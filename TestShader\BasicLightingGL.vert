#version 320 es

// Basic Lighting Vertex Shader - OpenGL Version
// Tests basic vertex transformations and lighting calculations

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;
layout(location = 3) in vec4 aColor;

uniform mat4 uViewProjectionMatrix;
uniform mat4 uWorldMatrix;
uniform mat4 uNormalMatrix;
uniform vec3 uLightDirection;
uniform vec3 uCameraPosition;
uniform float uTime;

out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;
out vec4 vColor;
out vec3 vViewDir;
out vec3 vLightDir;

void main()
{
    // Transform position to world space
    vec4 worldPos = uWorldMatrix * vec4(aPosition, 1.0);
    vWorldPos = worldPos.xyz;
    
    // Transform to clip space
    gl_Position = uViewProjectionMatrix * worldPos;
    
    // Transform normal to world space
    vNormal = normalize((uNormalMatrix * vec4(aNormal, 0.0)).xyz);
    
    // Pass through texture coordinates and color
    vTexCoord = aTexCoord;
    vColor = aColor;
    
    // Calculate view direction
    vViewDir = normalize(uCameraPosition - vWorldPos);
    
    // Calculate light direction
    vLightDir = normalize(-uLightDirection);
}
