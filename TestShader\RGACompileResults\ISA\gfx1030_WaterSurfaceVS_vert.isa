_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s30, s2, 0x90016                                 // 00000000000C: 939EFF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s30, 12                                     // 00000000002C: 8F028C1E
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v3, s3, 5, v1                                // 000000000040: D76F0003 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v3                            // 000000000048: 7D880601
	s_and_saveexec_b32 s31, vcc_lo                             // 00000000004C: BE9F3C6A
	s_cbranch_execz _L1                                        // 000000000050: BF8800D7
	s_getpc_b64 s[6:7]                                         // 000000000054: BE861F00
	v_add_nc_u32_e32 v1, s0, v5                                // 000000000058: 4A020A00
	s_mov_b32 s11, s7                                          // 00000000005C: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000060: BE850307
	s_clause 0x1                                               // 000000000064: BFA10001
	s_load_dwordx4 s[12:15], s[10:11], null                    // 000000000068: F4080305 FA000000
	s_load_dwordx4 s[0:3], s[10:11], 0x20                      // 000000000070: F4080005 FA000020
	s_waitcnt lgkmcnt(0)                                       // 000000000078: BF8CC07F
	tbuffer_load_format_xyz v[8:10], v1, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000007C: EA522000 80030801
	tbuffer_load_format_xy v[1:2], v1, s[0:3], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000084: EA012000 80000101
	s_load_dwordx4 s[16:19], s[4:5], null                      // 00000000008C: F4080402 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	s_clause 0x4                                               // 000000000098: BFA10004
	s_buffer_load_dwordx2 s[28:29], s[16:19], 0x90             // 00000000009C: F4240708 FA000090
	s_buffer_load_dwordx4 s[8:11], s[16:19], 0xa0              // 0000000000A4: F4280208 FA0000A0
	s_buffer_load_dwordx4 s[12:15], s[16:19], 0x60             // 0000000000AC: F4280308 FA000060
	s_buffer_load_dwordx8 s[0:7], s[16:19], 0x40               // 0000000000B4: F42C0008 FA000040
	s_buffer_load_dwordx4 s[24:27], s[16:19], 0x80             // 0000000000BC: F4280608 FA000080
	s_waitcnt lgkmcnt(0)                                       // 0000000000C4: BF8CC07F
	v_rcp_f32_e32 v5, s29                                      // 0000000000C8: 7E0A541D
	v_mul_f32_e64 v4, s9, s9                                   // 0000000000CC: D5080004 00001209
	v_mul_f32_e64 v6, s11, s11                                 // 0000000000D4: D5080006 0000160B
	v_mul_f32_e64 v17, 0x4116cbdc, s29                         // 0000000000DC: D5080011 00003AFF 4116CBDC
	v_mul_f32_e64 v16, 0x40c90fd0, s29                         // 0000000000E8: D5080010 00003AFF 40C90FD0
	v_mul_f32_e64 v21, s28, 0.5                                // 0000000000F4: D5080015 0001E01C
	v_fmac_f32_e64 v4, s8, s8                                  // 0000000000FC: D52B0004 00001008
	v_fmac_f32_e64 v6, s10, s10                                // 000000000104: D52B0006 0000140A
	v_mul_f32_e64 v19, 0x3e22f98c, s28                         // 00000000010C: D5080013 000038FF 3E22F98C
	v_mul_f32_e64 v22, 0x3d594cbb, s28                         // 000000000118: D5080016 000038FF 3D594CBB
	v_mul_f32_e32 v7, 0x3f851899, v5                           // 000000000124: 100E0AFF 3F851899
	v_rsq_f32_e32 v11, v4                                      // 00000000012C: 7E165D04
	v_mul_f32_e32 v4, 0x3fc7a4e5, v5                           // 000000000130: 10080AFF 3FC7A4E5
	v_rsq_f32_e32 v12, v6                                      // 000000000138: 7E185D06
	v_mul_f32_e32 v19, v19, v5                                 // 00000000013C: 10260B13
	v_sqrt_f32_e32 v7, v7                                      // 000000000140: 7E0E6707
	v_mul_f32_e32 v5, v22, v5                                  // 000000000144: 100A0B16
	v_sqrt_f32_e32 v13, v4                                     // 000000000148: 7E1A6704
	v_mul_legacy_f32_e32 v14, s9, v11                          // 00000000014C: 0E1C1609
	v_mul_legacy_f32_e32 v11, s8, v11                          // 000000000150: 0E161608
	v_mul_legacy_f32_e32 v15, s11, v12                         // 000000000154: 0E1E180B
	v_mul_legacy_f32_e32 v12, s10, v12                         // 000000000158: 0E18180A
	v_mul_f32_e32 v7, s27, v7                                  // 00000000015C: 100E0E1B
	v_mul_f32_e32 v13, s27, v13                                // 000000000160: 101A1A1B
	s_waitcnt vmcnt(1)                                         // 000000000164: BF8C3F71
	v_fma_f32 v6, s12, v8, s15                                 // 000000000168: D54B0006 003E100C
	v_fma_f32 v4, s0, v8, s3                                   // 000000000170: D54B0004 000E1000
	v_fma_f32 v8, s4, v8, s7                                   // 000000000178: D54B0008 001E1004
	v_fmac_f32_e32 v6, s13, v9                                 // 000000000180: 560C120D
	v_fmac_f32_e32 v4, s1, v9                                  // 000000000184: 56081201
	v_fmac_f32_e32 v8, s5, v9                                  // 000000000188: 56101205
	v_fmac_f32_e32 v6, s14, v10                                // 00000000018C: 560C140E
	v_fmac_f32_e32 v4, s2, v10                                 // 000000000190: 56081402
	s_clause 0x2                                               // 000000000194: BFA10002
	s_buffer_load_dword s2, s[16:19], 0xb8                     // 000000000198: F4200088 FA0000B8
	s_buffer_load_dwordx2 s[0:1], s[16:19], 0xb0               // 0000000001A0: F4240008 FA0000B0
	s_buffer_load_dwordx8 s[8:15], s[16:19], null              // 0000000001A8: F42C0208 FA000000
	v_fmac_f32_e32 v8, s6, v10                                 // 0000000001B0: 56101406
	s_buffer_load_dwordx8 s[16:23], s[16:19], 0x20             // 0000000001B4: F42C0408 FA000020
	v_fma_f32 v7, v15, v6, -v7                                 // 0000000001BC: D54B0007 841E0D0F
	v_fma_f32 v13, v14, v6, -v13                               // 0000000001C4: D54B000D 84360D0E
	v_fmac_f32_e32 v7, v12, v4                                 // 0000000001CC: 560E090C
	v_fmac_f32_e32 v13, v11, v4                                // 0000000001D0: 561A090B
	v_mul_f32_e32 v7, v17, v7                                  // 0000000001D4: 100E0F11
	v_mul_f32_e32 v13, v16, v13                                // 0000000001D8: 101A1B10
	v_add_f32_e64 v16, s27, s27                                // 0000000001DC: D5030010 0000361B
	v_add_f32_e32 v17, v6, v4                                  // 0000000001E4: 06220906
	v_mul_f32_e32 v7, 0.15915494, v7                           // 0000000001E8: 100E0EF8
	v_mul_f32_e32 v13, 0.15915494, v13                         // 0000000001EC: 101A1AF8
	v_fmac_f32_e32 v16, 0x3dcccccd, v17                        // 0000000001F0: 562022FF 3DCCCCCD
	v_sin_f32_e32 v18, v7                                      // 0000000001F8: 7E246B07
	v_sin_f32_e32 v20, v13                                     // 0000000001FC: 7E286B0D
	v_cos_f32_e32 v17, v7                                      // 000000000200: 7E226D07
	v_cos_f32_e32 v13, v13                                     // 000000000204: 7E1A6D0D
	v_mul_f32_e32 v16, 0.15915494, v16                         // 000000000208: 102020F8
	v_mul_f32_e64 v7, 0x3dcccccd, s27                          // 00000000020C: D5080007 000036FF 3DCCCCCD
	v_sin_f32_e32 v16, v16                                     // 000000000218: 7E206B10
	v_mul_f32_e32 v23, v18, v21                                // 00000000021C: 102E2B12
	v_mul_f32_e32 v24, s28, v20                                // 000000000220: 1030281C
	v_mul_f32_e32 v21, v17, v21                                // 000000000224: 102A2B11
	v_mul_f32_e32 v26, s28, v13                                // 000000000228: 10341A1C
	v_fmac_f32_e32 v8, v13, v19                                // 00000000022C: 5610270D
	v_mul_f32_e64 v25, v23, -v12                               // 000000000230: D5080019 40021917
	v_mul_f32_e64 v27, v24, -v11                               // 000000000238: D508001B 40021718
	v_mul_f32_e32 v28, v21, v12                                // 000000000240: 10381915
	v_mul_f32_e32 v24, v24, v14                                // 000000000244: 10301D18
	v_mul_f32_e32 v21, v21, v15                                // 000000000248: 102A1F15
	v_mul_f32_e32 v29, v25, v15                                // 00000000024C: 103A1F19
	v_fma_f32 v30, v27, v11, 1.0                               // 000000000250: D54B001E 03CA171B
	v_fmac_f32_e32 v28, v26, v11                               // 000000000258: 5638171A
	v_mul_f32_e32 v23, v23, v15                                // 00000000025C: 102E1F17
	v_fma_f32 v24, -v24, v14, 1.0                              // 000000000260: D54B0018 23CA1D18
	v_fmac_f32_e32 v29, v27, v14                               // 000000000268: 563A1D1B
	v_fmac_f32_e32 v21, v26, v14                               // 00000000026C: 562A1D1A
	v_fmac_f32_e32 v30, v25, v12                               // 000000000270: 563C1919
	v_mul_f32_e32 v25, v28, v28                                // 000000000274: 1032391C
	v_fma_f32 v9, -v23, v15, v24                               // 000000000278: D54B0009 24621F17
	v_mul_f32_e32 v26, v29, v29                                // 000000000280: 10343B1D
	s_waitcnt lgkmcnt(0)                                       // 000000000284: BF8CC07F
	v_mul_f32_e32 v10, s2, v16                                 // 000000000288: 10142002
	v_mul_f32_e32 v16, v20, v19                                // 00000000028C: 10202714
	v_fmac_f32_e32 v25, v30, v30                               // 000000000290: 56323D1E
	v_mul_f32_e32 v18, v18, v5                                 // 000000000294: 10240B12
	v_fmac_f32_e32 v26, v21, v21                               // 000000000298: 56342B15
	v_fmac_f32_e32 v8, v17, v5                                 // 00000000029C: 56100B11
	v_fmac_f32_e32 v4, v16, v11                                // 0000000002A0: 56081710
	v_fmac_f32_e32 v25, v29, v29                               // 0000000002A4: 56323B1D
	v_fmac_f32_e32 v6, v16, v14                                // 0000000002A8: 560C1D10
	v_fmac_f32_e32 v26, v9, v9                                 // 0000000002AC: 56341309
	v_mul_f32_e32 v20, s0, v10                                 // 0000000002B0: 10281400
	v_mul_f32_e32 v11, s1, v10                                 // 0000000002B4: 10161401
	v_rsq_f32_e32 v22, v25                                     // 0000000002B8: 7E2C5D19
	v_fma_f32 v13, v10, s1, s27                                // 0000000002BC: D54B000D 006C030A
	v_rsq_f32_e32 v23, v26                                     // 0000000002C4: 7E2E5D1A
	v_fmac_f32_e32 v4, v18, v12                                // 0000000002C8: 56081912
	v_fmac_f32_e32 v6, v18, v15                                // 0000000002CC: 560C1F12
	v_sub_f32_e32 v15, s25, v8                                 // 0000000002D0: 081E1019
	v_fmac_f32_e32 v7, 0x3d4ccccd, v20                         // 0000000002D4: 560E28FF 3D4CCCCD
	v_fmac_f32_e32 v4, 0x3dcccccd, v20                         // 0000000002DC: 560828FF 3DCCCCCD
	v_fmac_f32_e32 v6, 0x3dcccccd, v11                         // 0000000002E4: 560C16FF 3DCCCCCD
	v_mul_legacy_f32_e32 v5, v30, v22                          // 0000000002EC: 0E0A2D1E
	v_mul_legacy_f32_e32 v10, v29, v22                         // 0000000002F0: 0E142D1D
	v_mul_legacy_f32_e32 v9, v9, v23                           // 0000000002F4: 0E122F09
	v_mul_legacy_f32_e32 v12, v29, v23                         // 0000000002F8: 0E182F1D
	v_mul_legacy_f32_e32 v14, v21, v23                         // 0000000002FC: 0E1C2F15
	v_mul_legacy_f32_e32 v11, v28, v22                         // 000000000300: 0E162D1C
	v_sub_f32_e32 v19, s24, v4                                 // 000000000304: 08260818
	v_mul_f32_e32 v16, v5, v9                                  // 000000000308: 10201305
	v_sub_f32_e32 v20, s26, v6                                 // 00000000030C: 08280C1A
	v_mul_f32_e32 v17, v10, v14                                // 000000000310: 10221D0A
	v_fma_f32 v18, v10, v12, -v16                              // 000000000314: D54B0012 8442190A
	v_mul_f32_e32 v16, v15, v15                                // 00000000031C: 10201F0F
	v_mul_f32_e32 v10, v11, v12                                // 000000000320: 1014190B
	v_fma_f32 v17, v11, v9, -v17                               // 000000000324: D54B0011 8446130B
	v_fma_f32 v9, v8, s9, s11                                  // 00000000032C: D54B0009 002C1308
	v_mul_f32_e32 v21, v18, v18                                // 000000000334: 102A2512
	v_fmac_f32_e32 v16, v19, v19                               // 000000000338: 56202713
	v_fma_f32 v5, v5, v14, -v10                                // 00000000033C: D54B0005 842A1D05
	v_fma_f32 v10, v8, s13, s15                                // 000000000344: D54B000A 003C1B08
	v_fma_f32 v11, v8, s17, s19                                // 00000000034C: D54B000B 004C2308
	v_fmac_f32_e32 v21, v17, v17                               // 000000000354: 562A2311
	v_fmac_f32_e32 v16, v20, v20                               // 000000000358: 56202914
	v_fma_f32 v12, v8, s21, s23                                // 00000000035C: D54B000C 005C2B08
	v_fmac_f32_e32 v9, s8, v4                                  // 000000000364: 56120808
	v_fmac_f32_e32 v10, s12, v4                                // 000000000368: 5614080C
	v_fmac_f32_e32 v21, v5, v5                                 // 00000000036C: 562A0B05
	v_rsq_f32_e32 v22, v16                                     // 000000000370: 7E2C5D10
	v_fmac_f32_e32 v11, s16, v4                                // 000000000374: 56160810
	v_fmac_f32_e32 v12, s20, v4                                // 000000000378: 56180814
	v_mul_f32_e32 v14, 0x3d4ccccd, v13                         // 00000000037C: 101C1AFF 3D4CCCCD
	v_rsq_f32_e32 v21, v21                                     // 000000000384: 7E2A5D15
	v_fmac_f32_e32 v9, s10, v6                                 // 000000000388: 56120C0A
	v_fmac_f32_e32 v10, s14, v6                                // 00000000038C: 56140C0E
	v_fmac_f32_e32 v11, s18, v6                                // 000000000390: 56160C12
	v_fmac_f32_e32 v12, s22, v6                                // 000000000394: 56180C16
	v_mul_legacy_f32_e32 v13, v19, v22                         // 000000000398: 0E1A2D13
	v_mul_legacy_f32_e32 v15, v15, v22                         // 00000000039C: 0E1E2D0F
	v_mul_legacy_f32_e32 v16, v17, v21                         // 0000000003A0: 0E202B11
	v_mul_legacy_f32_e32 v17, v18, v21                         // 0000000003A4: 0E222B12
	v_mul_legacy_f32_e32 v19, v5, v21                          // 0000000003A8: 0E262B05
	v_mul_legacy_f32_e32 v18, v20, v22                         // 0000000003AC: 0E242D14
_L1:
	s_or_b32 exec_lo, exec_lo, s31                             // 0000000003B0: 887E1F7E
	s_mov_b32 s1, exec_lo                                      // 0000000003B4: BE81037E
	v_cmpx_gt_u32_e64 s30, v3                                  // 0000000003B8: D4D4007E 0002061E
	s_cbranch_execz _L2                                        // 0000000003C0: BF880002
	exp prim v0, off, off, off done                            // 0000000003C4: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000003CC: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000003D0: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000003D4: BE803C6A
	s_cbranch_execz _L3                                        // 0000000003D8: BF880011
	exp pos0 v9, v10, v11, v12 done                            // 0000000003DC: F80008CF 0C0B0A09
	exp param5 v9, v10, v11, v12                               // 0000000003E4: F800025F 0C0B0A09
	exp param3 v13, v15, v18, off                              // 0000000003EC: F8000237 00120F0D
	exp param1 v16, v17, v19, off                              // 0000000003F4: F8000217 00131110
	exp param6 v7, v14, off, off                               // 0000000003FC: F8000263 00000E07
	exp param4 v9, v10, v11, v12                               // 000000000404: F800024F 0C0B0A09
	s_waitcnt vmcnt(0)                                         // 00000000040C: BF8C3F70
	exp param2 v1, v2, off, off                                // 000000000410: F8000223 00000201
	exp param0 v4, v8, v6, off                                 // 000000000418: F8000207 00060804
_L3:
	s_endpgm                                                   // 000000000420: BF810000
