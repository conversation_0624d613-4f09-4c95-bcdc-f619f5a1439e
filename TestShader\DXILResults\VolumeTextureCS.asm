;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 2f5016382c06e0a6e39d0079b7c6c37f
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(4,4,4)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer ComputeParams
; {
;
;   struct ComputeParams
;   {
;
;       uint ParticleCount;                           ; Offset:    0
;       uint MaxParticles;                            ; Offset:    4
;       float DeltaTime;                              ; Offset:    8
;       float Time;                                   ; Offset:   12
;       float3 Gravity;                               ; Offset:   16
;       float Damping;                                ; Offset:   28
;       float3 EmitterPosition;                       ; Offset:   32
;       float EmissionRate;                           ; Offset:   44
;       float3 EmitterDirection;                      ; Offset:   48
;       float EmissionSpeed;                          ; Offset:   60
;       float2 LifetimeRange;                         ; Offset:   64
;       float2 SizeRange;                             ; Offset:   72
;       uint FrameCount;                              ; Offset:   80
;       float NoiseScale;                             ; Offset:   84
;       float NoiseStrength;                          ; Offset:   88
;       uint _padding;                                ; Offset:   92
;   
;   } ComputeParams;                                  ; Offset:    0 Size:    96
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; ComputeParams                     cbuffer      NA          NA     CB0            cb0     1
; VolumeTexture                         UAV     f32          3d      U0             u0     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.Dimensions = type { i32, i32, i32, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.RWTexture3D<vector<float, 4> >" = type { <4 x float> }
%ComputeParams = type { i32, i32, float, float, <3 x float>, float, <3 x float>, float, <3 x float>, float, <2 x float>, <2 x float>, i32, float, float, i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %4 = call i32 @dx.op.threadId.i32(i32 93, i32 1)  ; ThreadId(component)
  %5 = call i32 @dx.op.threadId.i32(i32 93, i32 2)  ; ThreadId(component)
  %6 = call %dx.types.Dimensions @dx.op.getDimensions(i32 72, %dx.types.Handle %1, i32 0)  ; GetDimensions(handle,mipLevel)
  %7 = extractvalue %dx.types.Dimensions %6, 0
  %8 = extractvalue %dx.types.Dimensions %6, 1
  %9 = extractvalue %dx.types.Dimensions %6, 2
  %10 = icmp uge i32 %3, %7
  %11 = icmp uge i32 %4, %8
  %12 = icmp uge i32 %5, %9
  %13 = or i1 %10, %11
  %14 = or i1 %12, %13
  br i1 %14, label %281, label %15

; <label>:15                                      ; preds = %0
  %16 = uitofp i32 %3 to float
  %17 = uitofp i32 %4 to float
  %18 = uitofp i32 %5 to float
  %19 = add i32 %7, -1
  %20 = add i32 %8, -1
  %21 = add i32 %9, -1
  %22 = uitofp i32 %19 to float
  %23 = uitofp i32 %20 to float
  %24 = uitofp i32 %21 to float
  %25 = fdiv fast float %16, %22
  %26 = fdiv fast float %17, %23
  %27 = fdiv fast float %18, %24
  %28 = fadd fast float %25, -5.000000e-01
  %29 = fadd fast float %26, -5.000000e-01
  %30 = fadd fast float %27, -5.000000e-01
  %31 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %32 = extractvalue %dx.types.CBufRet.f32 %31, 3
  %33 = fmul fast float %32, 0x3FB99999A0000000
  %34 = fadd fast float %33, %28
  %35 = fadd fast float %33, %29
  %36 = fadd fast float %33, %30
  %37 = call float @dx.op.unary.f32(i32 27, float %34)  ; Round_ni(value)
  %38 = call float @dx.op.unary.f32(i32 27, float %35)  ; Round_ni(value)
  %39 = call float @dx.op.unary.f32(i32 27, float %36)  ; Round_ni(value)
  %40 = call float @dx.op.unary.f32(i32 22, float %34)  ; Frc(value)
  %41 = call float @dx.op.unary.f32(i32 22, float %35)  ; Frc(value)
  %42 = call float @dx.op.unary.f32(i32 22, float %36)  ; Frc(value)
  %43 = fmul fast float %40, %40
  %44 = fmul fast float %41, %41
  %45 = fmul fast float %42, %42
  %46 = fmul fast float %40, 2.000000e+00
  %47 = fmul fast float %41, 2.000000e+00
  %48 = fmul fast float %42, 2.000000e+00
  %49 = fsub fast float 3.000000e+00, %46
  %50 = fsub fast float 3.000000e+00, %47
  %51 = fsub fast float 3.000000e+00, %48
  %52 = fmul fast float %43, %49
  %53 = fmul fast float %44, %50
  %54 = fmul fast float %45, %51
  %55 = fmul fast float %38, 5.700000e+01
  %56 = fadd fast float %55, %37
  %57 = fmul fast float %39, 1.130000e+02
  %58 = fadd fast float %56, %57
  %59 = fadd fast float %58, 1.710000e+02
  %60 = call float @dx.op.unary.f32(i32 13, float %59)  ; Sin(value)
  %61 = fmul fast float %60, 0x40E55DD180000000
  %62 = call float @dx.op.unary.f32(i32 22, float %61)  ; Frc(value)
  %63 = fadd fast float %58, 1.700000e+02
  %64 = call float @dx.op.unary.f32(i32 13, float %63)  ; Sin(value)
  %65 = fmul fast float %64, 0x40E55DD180000000
  %66 = call float @dx.op.unary.f32(i32 22, float %65)  ; Frc(value)
  %67 = fsub fast float %62, %66
  %68 = fmul fast float %67, %52
  %69 = fadd fast float %68, %66
  %70 = fadd fast float %58, 1.140000e+02
  %71 = call float @dx.op.unary.f32(i32 13, float %70)  ; Sin(value)
  %72 = fmul fast float %71, 0x40E55DD180000000
  %73 = call float @dx.op.unary.f32(i32 22, float %72)  ; Frc(value)
  %74 = fadd fast float %58, 1.130000e+02
  %75 = call float @dx.op.unary.f32(i32 13, float %74)  ; Sin(value)
  %76 = fmul fast float %75, 0x40E55DD180000000
  %77 = call float @dx.op.unary.f32(i32 22, float %76)  ; Frc(value)
  %78 = fsub fast float %73, %77
  %79 = fmul fast float %78, %52
  %80 = fadd fast float %79, %77
  %81 = fsub fast float %69, %80
  %82 = fmul fast float %81, %53
  %83 = fadd fast float %82, %80
  %84 = fadd fast float %58, 5.800000e+01
  %85 = call float @dx.op.unary.f32(i32 13, float %84)  ; Sin(value)
  %86 = fmul fast float %85, 0x40E55DD180000000
  %87 = call float @dx.op.unary.f32(i32 22, float %86)  ; Frc(value)
  %88 = fadd fast float %58, 5.700000e+01
  %89 = call float @dx.op.unary.f32(i32 13, float %88)  ; Sin(value)
  %90 = fmul fast float %89, 0x40E55DD180000000
  %91 = call float @dx.op.unary.f32(i32 22, float %90)  ; Frc(value)
  %92 = fsub fast float %87, %91
  %93 = fmul fast float %92, %52
  %94 = fadd fast float %93, %91
  %95 = fadd fast float %58, 1.000000e+00
  %96 = call float @dx.op.unary.f32(i32 13, float %95)  ; Sin(value)
  %97 = fmul fast float %96, 0x40E55DD180000000
  %98 = call float @dx.op.unary.f32(i32 22, float %97)  ; Frc(value)
  %99 = call float @dx.op.unary.f32(i32 13, float %58)  ; Sin(value)
  %100 = fmul fast float %99, 0x40E55DD180000000
  %101 = call float @dx.op.unary.f32(i32 22, float %100)  ; Frc(value)
  %102 = fsub fast float %98, %101
  %103 = fmul fast float %102, %52
  %104 = fadd fast float %103, %101
  %105 = fsub fast float %94, %104
  %106 = fmul fast float %105, %53
  %107 = fadd fast float %106, %104
  %108 = fsub fast float %83, %107
  %109 = fmul fast float %54, %108
  %110 = fadd fast float %109, %107
  %111 = fmul fast float %110, 5.000000e-01
  %112 = fmul fast float %28, 2.000000e+00
  %113 = fmul fast float %29, 2.000000e+00
  %114 = fmul fast float %30, 2.000000e+00
  %115 = fmul fast float %32, 0x3FA99999A0000000
  %116 = fadd fast float %115, %112
  %117 = fadd fast float %115, %113
  %118 = fadd fast float %115, %114
  %119 = call float @dx.op.unary.f32(i32 27, float %116)  ; Round_ni(value)
  %120 = call float @dx.op.unary.f32(i32 27, float %117)  ; Round_ni(value)
  %121 = call float @dx.op.unary.f32(i32 27, float %118)  ; Round_ni(value)
  %122 = call float @dx.op.unary.f32(i32 22, float %116)  ; Frc(value)
  %123 = call float @dx.op.unary.f32(i32 22, float %117)  ; Frc(value)
  %124 = call float @dx.op.unary.f32(i32 22, float %118)  ; Frc(value)
  %125 = fmul fast float %122, %122
  %126 = fmul fast float %123, %123
  %127 = fmul fast float %124, %124
  %128 = fmul fast float %122, 2.000000e+00
  %129 = fmul fast float %123, 2.000000e+00
  %130 = fmul fast float %124, 2.000000e+00
  %131 = fsub fast float 3.000000e+00, %128
  %132 = fsub fast float 3.000000e+00, %129
  %133 = fsub fast float 3.000000e+00, %130
  %134 = fmul fast float %125, %131
  %135 = fmul fast float %126, %132
  %136 = fmul fast float %127, %133
  %137 = fmul fast float %120, 5.700000e+01
  %138 = fadd fast float %137, %119
  %139 = fmul fast float %121, 1.130000e+02
  %140 = fadd fast float %138, %139
  %141 = fadd fast float %140, 1.710000e+02
  %142 = call float @dx.op.unary.f32(i32 13, float %141)  ; Sin(value)
  %143 = fmul fast float %142, 0x40E55DD180000000
  %144 = call float @dx.op.unary.f32(i32 22, float %143)  ; Frc(value)
  %145 = fadd fast float %140, 1.700000e+02
  %146 = call float @dx.op.unary.f32(i32 13, float %145)  ; Sin(value)
  %147 = fmul fast float %146, 0x40E55DD180000000
  %148 = call float @dx.op.unary.f32(i32 22, float %147)  ; Frc(value)
  %149 = fsub fast float %144, %148
  %150 = fmul fast float %149, %134
  %151 = fadd fast float %150, %148
  %152 = fadd fast float %140, 1.140000e+02
  %153 = call float @dx.op.unary.f32(i32 13, float %152)  ; Sin(value)
  %154 = fmul fast float %153, 0x40E55DD180000000
  %155 = call float @dx.op.unary.f32(i32 22, float %154)  ; Frc(value)
  %156 = fadd fast float %140, 1.130000e+02
  %157 = call float @dx.op.unary.f32(i32 13, float %156)  ; Sin(value)
  %158 = fmul fast float %157, 0x40E55DD180000000
  %159 = call float @dx.op.unary.f32(i32 22, float %158)  ; Frc(value)
  %160 = fsub fast float %155, %159
  %161 = fmul fast float %160, %134
  %162 = fadd fast float %161, %159
  %163 = fsub fast float %151, %162
  %164 = fmul fast float %163, %135
  %165 = fadd fast float %164, %162
  %166 = fadd fast float %140, 5.800000e+01
  %167 = call float @dx.op.unary.f32(i32 13, float %166)  ; Sin(value)
  %168 = fmul fast float %167, 0x40E55DD180000000
  %169 = call float @dx.op.unary.f32(i32 22, float %168)  ; Frc(value)
  %170 = fadd fast float %140, 5.700000e+01
  %171 = call float @dx.op.unary.f32(i32 13, float %170)  ; Sin(value)
  %172 = fmul fast float %171, 0x40E55DD180000000
  %173 = call float @dx.op.unary.f32(i32 22, float %172)  ; Frc(value)
  %174 = fsub fast float %169, %173
  %175 = fmul fast float %174, %134
  %176 = fadd fast float %175, %173
  %177 = fadd fast float %140, 1.000000e+00
  %178 = call float @dx.op.unary.f32(i32 13, float %177)  ; Sin(value)
  %179 = fmul fast float %178, 0x40E55DD180000000
  %180 = call float @dx.op.unary.f32(i32 22, float %179)  ; Frc(value)
  %181 = call float @dx.op.unary.f32(i32 13, float %140)  ; Sin(value)
  %182 = fmul fast float %181, 0x40E55DD180000000
  %183 = call float @dx.op.unary.f32(i32 22, float %182)  ; Frc(value)
  %184 = fsub fast float %180, %183
  %185 = fmul fast float %184, %134
  %186 = fadd fast float %185, %183
  %187 = fsub fast float %176, %186
  %188 = fmul fast float %187, %135
  %189 = fadd fast float %188, %186
  %190 = fsub fast float %165, %189
  %191 = fmul fast float %136, %190
  %192 = fadd fast float %191, %189
  %193 = fmul fast float %192, 0x3FD3333340000000
  %194 = fmul fast float %28, 4.000000e+00
  %195 = fmul fast float %29, 4.000000e+00
  %196 = fmul fast float %30, 4.000000e+00
  %197 = fmul fast float %32, 0x3F947AE140000000
  %198 = fadd fast float %197, %194
  %199 = fadd fast float %197, %195
  %200 = fadd fast float %197, %196
  %201 = call float @dx.op.unary.f32(i32 27, float %198)  ; Round_ni(value)
  %202 = call float @dx.op.unary.f32(i32 27, float %199)  ; Round_ni(value)
  %203 = call float @dx.op.unary.f32(i32 27, float %200)  ; Round_ni(value)
  %204 = call float @dx.op.unary.f32(i32 22, float %198)  ; Frc(value)
  %205 = call float @dx.op.unary.f32(i32 22, float %199)  ; Frc(value)
  %206 = call float @dx.op.unary.f32(i32 22, float %200)  ; Frc(value)
  %207 = fmul fast float %204, %204
  %208 = fmul fast float %205, %205
  %209 = fmul fast float %206, %206
  %210 = fmul fast float %204, 2.000000e+00
  %211 = fmul fast float %205, 2.000000e+00
  %212 = fmul fast float %206, 2.000000e+00
  %213 = fsub fast float 3.000000e+00, %210
  %214 = fsub fast float 3.000000e+00, %211
  %215 = fsub fast float 3.000000e+00, %212
  %216 = fmul fast float %207, %213
  %217 = fmul fast float %208, %214
  %218 = fmul fast float %209, %215
  %219 = fmul fast float %202, 5.700000e+01
  %220 = fadd fast float %219, %201
  %221 = fmul fast float %203, 1.130000e+02
  %222 = fadd fast float %220, %221
  %223 = fadd fast float %222, 1.710000e+02
  %224 = call float @dx.op.unary.f32(i32 13, float %223)  ; Sin(value)
  %225 = fmul fast float %224, 0x40E55DD180000000
  %226 = call float @dx.op.unary.f32(i32 22, float %225)  ; Frc(value)
  %227 = fadd fast float %222, 1.700000e+02
  %228 = call float @dx.op.unary.f32(i32 13, float %227)  ; Sin(value)
  %229 = fmul fast float %228, 0x40E55DD180000000
  %230 = call float @dx.op.unary.f32(i32 22, float %229)  ; Frc(value)
  %231 = fsub fast float %226, %230
  %232 = fmul fast float %231, %216
  %233 = fadd fast float %232, %230
  %234 = fadd fast float %222, 1.140000e+02
  %235 = call float @dx.op.unary.f32(i32 13, float %234)  ; Sin(value)
  %236 = fmul fast float %235, 0x40E55DD180000000
  %237 = call float @dx.op.unary.f32(i32 22, float %236)  ; Frc(value)
  %238 = fadd fast float %222, 1.130000e+02
  %239 = call float @dx.op.unary.f32(i32 13, float %238)  ; Sin(value)
  %240 = fmul fast float %239, 0x40E55DD180000000
  %241 = call float @dx.op.unary.f32(i32 22, float %240)  ; Frc(value)
  %242 = fsub fast float %237, %241
  %243 = fmul fast float %242, %216
  %244 = fadd fast float %243, %241
  %245 = fsub fast float %233, %244
  %246 = fmul fast float %245, %217
  %247 = fadd fast float %246, %244
  %248 = fadd fast float %222, 5.800000e+01
  %249 = call float @dx.op.unary.f32(i32 13, float %248)  ; Sin(value)
  %250 = fmul fast float %249, 0x40E55DD180000000
  %251 = call float @dx.op.unary.f32(i32 22, float %250)  ; Frc(value)
  %252 = fadd fast float %222, 5.700000e+01
  %253 = call float @dx.op.unary.f32(i32 13, float %252)  ; Sin(value)
  %254 = fmul fast float %253, 0x40E55DD180000000
  %255 = call float @dx.op.unary.f32(i32 22, float %254)  ; Frc(value)
  %256 = fsub fast float %251, %255
  %257 = fmul fast float %256, %216
  %258 = fadd fast float %257, %255
  %259 = fadd fast float %222, 1.000000e+00
  %260 = call float @dx.op.unary.f32(i32 13, float %259)  ; Sin(value)
  %261 = fmul fast float %260, 0x40E55DD180000000
  %262 = call float @dx.op.unary.f32(i32 22, float %261)  ; Frc(value)
  %263 = call float @dx.op.unary.f32(i32 13, float %222)  ; Sin(value)
  %264 = fmul fast float %263, 0x40E55DD180000000
  %265 = call float @dx.op.unary.f32(i32 22, float %264)  ; Frc(value)
  %266 = fsub fast float %262, %265
  %267 = fmul fast float %266, %216
  %268 = fadd fast float %267, %265
  %269 = fsub fast float %258, %268
  %270 = fmul fast float %269, %217
  %271 = fadd fast float %270, %268
  %272 = fsub fast float %247, %271
  %273 = fmul fast float %218, %272
  %274 = fadd fast float %273, %271
  %275 = fmul fast float %274, 0x3FC99999A0000000
  %276 = fadd fast float %111, 0xBFD3333340000000
  %277 = fadd fast float %276, %193
  %278 = fadd fast float %277, %275
  %279 = call float @dx.op.unary.f32(i32 7, float %278)  ; Saturate(value)
  %280 = fmul fast float %279, 2.000000e+00
  call void @dx.op.textureStore.f32(i32 67, %dx.types.Handle %1, i32 %3, i32 %4, i32 %5, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00, float %280, i8 15)  ; TextureStore(srv,coord0,coord1,coord2,value0,value1,value2,value3,mask)
  br label %281

; <label>:281                                     ; preds = %15, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readonly
declare %dx.types.Dimensions @dx.op.getDimensions(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind
declare void @dx.op.textureStore.f32(i32, %dx.types.Handle, i32, i32, i32, float, float, float, float, i8) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #1

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind readonly }
attributes #2 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!10}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{null, !5, !8, null}
!5 = !{!6}
!6 = !{i32 0, %"class.RWTexture3D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 4, i1 false, i1 false, i1 false, !7}
!7 = !{i32 0, i32 9}
!8 = !{!9}
!9 = !{i32 0, %ComputeParams* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!10 = !{void ()* @main, !"main", null, !4, !11}
!11 = !{i32 4, !12}
!12 = !{i32 4, i32 4, i32 4}
