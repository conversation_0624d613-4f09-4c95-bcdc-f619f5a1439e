﻿=== DXIL Compilation Log ===
Start time: 07/31/2025 18:47:31

Processing: AdvancedDataStructuresPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: AdvancedDataStructuresVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: BasicLightingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: BasicLightingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: DeferredGBufferPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: DeferredGBufferVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: DeferredLightingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: DeferredLightingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: GeometryTessellationPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: GeometryTessellationVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: ImageProcessingCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: InstancedRenderingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: InstancedRenderingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: MeshGenerationCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: MeshViewerPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: MeshViewerVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: ParallelReductionCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: ParticleSimulationCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: ParticleUpdateCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: PbrShadingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: PbrShadingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: PostProcessingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: PostProcessingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: RayTracingTestCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: ShadowMappingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: ShadowMappingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: SimplePS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: SimpleVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: StressTestCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: TestShaderPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: TestShaderVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: VolumeTextureCS.hlsl
  DXIL Compilation: SUCCESS (Profile: cs_6_1, Entry: main)

Processing: VolumetricRenderingPS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: VolumetricRenderingVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)

Processing: WaterSurfacePS.hlsl
  DXIL Compilation: SUCCESS (Profile: ps_6_1, Entry: main)

Processing: WaterSurfaceVS.hlsl
  DXIL Compilation: SUCCESS (Profile: vs_6_1, Entry: main)


=== Compilation Statistics ===
Total files processed: 36
Successful compilations: 36
Failed compilations: 0
Success rate: 100%
End time: 07/31/2025 18:47:49
=== Compilation Complete ===
