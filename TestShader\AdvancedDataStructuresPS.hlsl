// Advanced Data Structures Pixel Shader
// Tests complex HLSL data structures and texture operations

// Material data structure (matching VS)
struct MaterialData
{
    float4 Albedo;
    float Metallic;
    float Roughness;
    float AO;
    float Emission;
    float4 TilingOffset; // xy = tiling, zw = offset
    uint TextureFlags; // Bitfield for texture presence
    float _padding;
};

// Light data structure
struct LightData
{
    float3 Position;
    float Range;
    float3 Direction;
    float SpotAngle;
    float3 Color;
    float Intensity;
    uint Type; // 0=Directional, 1=Point, 2=Spot
    float3 _padding;
};

// Constant buffers
cbuffer PerFrame : register(b0)
{
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 ViewProjectionMatrix;
    float3 CameraPosition;
    float Time;
    float DeltaTime;
    uint FrameCount;
    float2 ScreenResolution;
};

cbuffer LightingParams : register(b1)
{
    uint NumLights;
    float AmbientIntensity;
    float3 AmbientColor;
    float ShadowBias;
    float ShadowNormalBias;
    float2 ShadowMapSize;
    float PCFRadius;
};

// Structured buffers
StructuredBuffer<MaterialData> Materials : register(t0);
StructuredBuffer<LightData> Lights : register(t1);

// Texture arrays and samplers
Texture2DArray AlbedoTextures : register(t2);
Texture2DArray NormalTextures : register(t3);
Texture2DArray MetallicRoughnessTextures : register(t4);
Texture2DArray AOTextures : register(t5);
Texture2DArray EmissionTextures : register(t6);
Texture2D LightmapTexture : register(t7);
TextureCube EnvironmentMap : register(t8);
Texture2D ShadowMap : register(t9);

SamplerState LinearSampler : register(s0);
SamplerState PointSampler : register(s1);
SamplerComparisonState ShadowSampler : register(s2);

// Input structure
struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float3 Tangent : TEXCOORD2;
    float3 Bitangent : TEXCOORD3;
    float2 TexCoord : TEXCOORD4;
    float2 LightmapUV : TEXCOORD5;
    float4 Color : TEXCOORD6;
    float3 ViewDir : TEXCOORD7;
    float4 PrevClipPos : TEXCOORD8;
    float4 CurrClipPos : TEXCOORD9;
    nointerpolation uint MaterialID : TEXCOORD10;
    float LODFade : TEXCOORD11;
};

// Output structure for multiple render targets
struct PSOutput
{
    float4 Color : SV_Target0;
    float4 Normal : SV_Target1; // World space normal + roughness
    float4 MotionVector : SV_Target2; // Motion vectors + depth
    float4 MaterialProps : SV_Target3; // Metallic, Roughness, AO, Emission
};

// Utility functions
float3 GetNormalFromMap(float3 normalMap, float3 normal, float3 tangent, float3 bitangent)
{
    // Convert from [0,1] to [-1,1]
    normalMap = normalMap * 2.0 - 1.0;
    
    // Construct TBN matrix
    float3x3 TBN = float3x3(tangent, bitangent, normal);
    
    // Transform normal from tangent space to world space
    return normalize(mul(normalMap, TBN));
}

float DistributionGGX(float3 N, float3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = 3.14159265359 * denom * denom;
    
    return num / denom;
}

float GeometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float GeometrySmith(float3 N, float3 V, float3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

float3 FresnelSchlick(float cosTheta, float3 F0)
{
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

float SampleShadowMap(float3 worldPos, float3 normal, float3 lightDir)
{
    // Transform world position to light space
    float4 lightSpacePos = mul(float4(worldPos, 1.0), ViewProjectionMatrix);
    
    // Perspective divide
    float3 projCoords = lightSpacePos.xyz / lightSpacePos.w;
    
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    
    // Check if position is in shadow map bounds
    if (projCoords.x < 0.0 || projCoords.x > 1.0 || 
        projCoords.y < 0.0 || projCoords.y > 1.0)
        return 1.0;
    
    // Apply bias
    float bias = max(ShadowNormalBias * (1.0 - dot(normal, lightDir)), ShadowBias);
    float currentDepth = projCoords.z - bias;
    
    // PCF sampling
    float shadow = 0.0;
    float2 texelSize = 1.0 / ShadowMapSize;
    
    for (int x = -1; x <= 1; ++x)
    {
        for (int y = -1; y <= 1; ++y)
        {
            float2 offset = float2(x, y) * texelSize * PCFRadius;
            shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, projCoords.xy + offset, currentDepth);
        }
    }
    
    return shadow / 9.0;
}

float3 CalculatePBRLighting(PSInput input, MaterialData material, float3 albedo, float3 normal, 
                           float metallic, float roughness, float ao)
{
    float3 V = normalize(input.ViewDir);
    float3 N = normal;
    
    // Calculate reflectance at normal incidence
    float3 F0 = lerp(float3(0.04, 0.04, 0.04), albedo, metallic);
    
    float3 Lo = float3(0.0, 0.0, 0.0);
    
    // Calculate lighting for each light
    for (uint i = 0; i < NumLights; ++i)
    {
        LightData light = Lights[i];
        
        float3 L;
        float attenuation = 1.0;
        
        if (light.Type == 0) // Directional light
        {
            L = normalize(-light.Direction);
        }
        else if (light.Type == 1) // Point light
        {
            L = normalize(light.Position - input.WorldPos);
            float distance = length(light.Position - input.WorldPos);
            attenuation = 1.0 / (1.0 + 0.09 * distance + 0.032 * distance * distance);
        }
        else if (light.Type == 2) // Spot light
        {
            L = normalize(light.Position - input.WorldPos);
            float distance = length(light.Position - input.WorldPos);
            float theta = dot(L, normalize(-light.Direction));
            float epsilon = cos(light.SpotAngle) - cos(light.SpotAngle * 1.2);
            float intensity = clamp((theta - cos(light.SpotAngle * 1.2)) / epsilon, 0.0, 1.0);
            attenuation = intensity / (1.0 + 0.09 * distance + 0.032 * distance * distance);
        }
        
        float3 H = normalize(V + L);
        float3 radiance = light.Color * light.Intensity * attenuation;
        
        // Calculate shadow
        float shadow = SampleShadowMap(input.WorldPos, N, L);
        radiance *= shadow;
        
        // Cook-Torrance BRDF
        float NDF = DistributionGGX(N, H, roughness);
        float G = GeometrySmith(N, V, L, roughness);
        float3 F = FresnelSchlick(max(dot(H, V), 0.0), F0);
        
        float3 kS = F;
        float3 kD = float3(1.0, 1.0, 1.0) - kS;
        kD *= 1.0 - metallic;
        
        float3 numerator = NDF * G * F;
        float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.0001;
        float3 specular = numerator / denominator;
        
        float NdotL = max(dot(N, L), 0.0);
        Lo += (kD * albedo / 3.14159265359 + specular) * radiance * NdotL;
    }
    
    // Ambient lighting
    float3 ambient = AmbientColor * AmbientIntensity * albedo * ao;
    
    return ambient + Lo;
}

PSOutput main(PSInput input)
{
    PSOutput output;
    
    // Get material data
    MaterialData material = Materials[input.MaterialID];
    
    // Sample textures with tiling and offset
    float2 tiledUV = input.TexCoord * material.TilingOffset.xy + material.TilingOffset.zw;
    
    // Sample material textures
    float3 albedo = material.Albedo.rgb;
    if (material.TextureFlags & 0x1) // Has albedo texture
    {
        albedo *= AlbedoTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb;
    }
    albedo *= input.Color.rgb;
    
    float3 normal = input.Normal;
    if (material.TextureFlags & 0x2) // Has normal texture
    {
        float3 normalMap = NormalTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb;
        normal = GetNormalFromMap(normalMap, input.Normal, input.Tangent, input.Bitangent);
    }
    
    float metallic = material.Metallic;
    float roughness = material.Roughness;
    if (material.TextureFlags & 0x4) // Has metallic/roughness texture
    {
        float2 metallicRoughness = MetallicRoughnessTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rg;
        metallic *= metallicRoughness.r;
        roughness *= metallicRoughness.g;
    }
    
    float ao = material.AO;
    if (material.TextureFlags & 0x8) // Has AO texture
    {
        ao *= AOTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).r;
    }
    
    float3 emission = float3(0, 0, 0);
    if (material.TextureFlags & 0x10) // Has emission texture
    {
        emission = EmissionTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb * material.Emission;
    }
    
    // Sample lightmap
    float3 lightmap = LightmapTexture.Sample(LinearSampler, input.LightmapUV).rgb;
    
    // Calculate PBR lighting
    float3 color = CalculatePBRLighting(input, material, albedo, normal, metallic, roughness, ao);
    
    // Add emission and lightmap
    color += emission + lightmap * albedo;
    
    // Apply LOD fade
    color *= input.LODFade;
    
    // Calculate motion vectors
    float2 currentPos = (input.CurrClipPos.xy / input.CurrClipPos.w) * 0.5 + 0.5;
    float2 prevPos = (input.PrevClipPos.xy / input.PrevClipPos.w) * 0.5 + 0.5;
    float2 motionVector = currentPos - prevPos;
    
    // Output to multiple render targets
    output.Color = float4(color, material.Albedo.a);
    output.Normal = float4(normal * 0.5 + 0.5, roughness);
    output.MotionVector = float4(motionVector, input.Position.z, 1.0);
    output.MaterialProps = float4(metallic, roughness, ao, material.Emission);
    
    return output;
}
