_amdgpu_cs_main:
	s_getpc_b64 s[4:5]                                         // 000000000000: BE841F00
	s_mov_b32 s0, s1                                           // 000000000004: BE800301
	s_mov_b32 s1, s5                                           // 000000000008: BE810305
	v_lshl_add_u32 v0, s2, 3, v0                               // 00000000000C: D7460000 04010602
	s_load_dwordx4 s[8:11], s[0:1], null                       // 000000000014: F4080200 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000001C: BF8CC07F
	s_buffer_load_dword s0, s[8:11], 0x20                      // 000000000020: F4200004 FA000020
	s_waitcnt lgkmcnt(0)                                       // 000000000028: BF8CC07F
	v_cmp_gt_u32_e32 vcc_lo, s0, v0                            // 00000000002C: 7D880000
	s_and_saveexec_b64 s[0:1], vcc                             // 000000000030: BE80246A
	s_cbranch_execz _L0                                        // 000000000034: BF880093
	v_lshlrev_b32_e32 v6, 6, v0                                // 000000000038: 340C0086
	buffer_load_dword v8, v6, s[8:11], 0 offen offset:28       // 00000000003C: E030101C 80020806
	v_or_b32_e32 v7, 28, v6                                    // 000000000044: 380E0C9C
	s_waitcnt vmcnt(0)                                         // 000000000048: BF8C3F70
	v_cmp_nge_f32_e32 vcc_lo, 0, v8                            // 00000000004C: 7C121080
	s_and_b64 exec, exec, vcc                                  // 000000000050: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000054: BF88008B
	s_clause 0x2                                               // 000000000058: BFA10002
	buffer_load_dwordx4 v[2:5], v6, s[8:11], 0 offen offset:12 // 00000000005C: E038100C 80020206
	buffer_load_dwordx3 v[9:11], v6, s[8:11], 0 offen offset:32// 000000000064: E03C1020 80020906
	buffer_load_dword v1, v6, s[8:11], 0 offen offset:4        // 00000000006C: E0301004 80020106
	s_buffer_load_dwordx8 s[0:7], s[8:11], null                // 000000000074: F42C0004 FA000000
	s_waitcnt vmcnt(1) lgkmcnt(0)                              // 00000000007C: BF8C0071
	v_fma_f32 v0, s2, v2, v10                                  // 000000000080: D54B0000 042A0402
	v_fma_f32 v9, s1, v2, v9                                   // 000000000088: D54B0009 04260401
	v_fmac_f32_e32 v11, s3, v2                                 // 000000000090: 56160403
	v_rcp_f32_e32 v10, v2                                      // 000000000094: 7E145502
	s_buffer_load_dword s1, s[8:11], 0x3c                      // 000000000098: F4200044 FA00003C
	v_add_f32_e32 v0, s5, v0                                   // 0000000000A0: 06000005
	v_add_f32_e32 v2, s4, v9                                   // 0000000000A4: 06041204
	v_add_f32_e32 v9, s6, v11                                  // 0000000000A8: 06121606
	s_mov_b64 s[2:3], exec                                     // 0000000000AC: BE82047E
	v_mul_f32_e32 v0, s0, v0                                   // 0000000000B0: 10000000
	v_mul_f32_e32 v2, s0, v2                                   // 0000000000B4: 10040400
	v_mul_f32_e32 v9, s0, v9                                   // 0000000000B8: 10121200
	v_fma_f32 v0, v0, v10, v4                                  // 0000000000BC: D54B0000 04121500
	v_fma_f32 v2, v2, v10, v3                                  // 0000000000C4: D54B0002 040E1502
	v_fmac_f32_e32 v5, v9, v10                                 // 0000000000CC: 560A1509
	v_add_nc_u32_e32 v9, 32, v6                                // 0000000000D0: 4A120CA0
	v_add_nc_u32_e32 v10, 16, v6                               // 0000000000D4: 4A140C90
	v_mul_f32_e32 v4, s7, v0                                   // 0000000000D8: 10080007
	v_mul_f32_e32 v2, s7, v2                                   // 0000000000DC: 10040407
	v_mul_f32_e32 v11, s7, v5                                  // 0000000000E0: 10160A07
	s_waitcnt vmcnt(0)                                         // 0000000000E4: BF8C3F70
	v_fmac_f32_e32 v1, s0, v4                                  // 0000000000E8: 56020800
	v_mov_b32_e32 v3, v2                                       // 0000000000EC: 7E060302
	v_mov_b32_e32 v5, v11                                      // 0000000000F0: 7E0A030B
	s_waitcnt lgkmcnt(0)                                       // 0000000000F4: BF8CC07F
	v_cmpx_ge_f32_e32 s1, v1                                   // 0000000000F8: 7C2C0201
	s_cbranch_execz _L1                                        // 0000000000FC: BF88000A
	s_buffer_load_dword s4, s[8:11], 0x40                      // 000000000100: F4200104 FA000040
	v_mul_f32_e32 v3, 0x3f4ccccd, v2                           // 000000000108: 100604FF 3F4CCCCD
	v_mul_f32_e32 v5, 0x3f4ccccd, v11                          // 000000000110: 100A16FF 3F4CCCCD
	v_mov_b32_e32 v1, s1                                       // 000000000118: 7E020201
	s_waitcnt lgkmcnt(0)                                       // 00000000011C: BF8CC07F
	v_mul_f32_e64 v4, s4, -v4                                  // 000000000120: D5080004 40020804
_L1:
	s_or_b64 exec, exec, s[2:3]                                // 000000000128: 88FE027E
	buffer_load_dword v0, v6, s[8:11], 0 offen                 // 00000000012C: E0301000 80020006
	s_buffer_load_dword s1, s[8:11], 0x24                      // 000000000134: F4200044 FA000024
	s_mov_b64 s[2:3], -1                                       // 00000000013C: BE8204C1
	s_mov_b64 s[4:5], exec                                     // 000000000140: BE84047E
	s_waitcnt vmcnt(0)                                         // 000000000144: BF8C3F70
	v_fmac_f32_e32 v0, s0, v2                                  // 000000000148: 56000400
	s_waitcnt lgkmcnt(0)                                       // 00000000014C: BF8CC07F
	v_cmpx_ngt_f32_e32 s1, v0                                  // 000000000150: 7C360001
	s_cbranch_execz _L2                                        // 000000000154: BF880005
	s_buffer_load_dword s2, s[8:11], 0x30                      // 000000000158: F4200084 FA000030
	s_waitcnt lgkmcnt(0)                                       // 000000000160: BF8CC07F
	v_cmp_lt_f32_e32 vcc_lo, s2, v0                            // 000000000164: 7C020002
	s_orn2_b64 s[2:3], vcc, exec                               // 000000000168: 8B827E6A
_L2:
	s_or_b64 exec, exec, s[4:5]                                // 00000000016C: 88FE047E
	s_and_saveexec_b64 s[4:5], s[2:3]                          // 000000000170: BE842402
	s_cbranch_execz _L3                                        // 000000000174: BF88000A
	s_clause 0x1                                               // 000000000178: BFA10001
	s_buffer_load_dword s2, s[8:11], 0x40                      // 00000000017C: F4200084 FA000040
	s_buffer_load_dword s3, s[8:11], 0x30                      // 000000000184: F42000C4 FA000030
	s_waitcnt lgkmcnt(0)                                       // 00000000018C: BF8CC07F
	v_mul_f32_e64 v3, s2, -v3                                  // 000000000190: D5080003 40020602
	v_med3_f32 v0, v0, s1, s3                                  // 000000000198: D5570000 000C0300
_L3:
	s_or_b64 exec, exec, s[4:5]                                // 0000000001A0: 88FE047E
	buffer_load_dword v2, v6, s[8:11], 0 offen offset:8        // 0000000001A4: E0301008 80020206
	s_buffer_load_dword s1, s[8:11], 0x2c                      // 0000000001AC: F4200044 FA00002C
	s_mov_b64 s[2:3], -1                                       // 0000000001B4: BE8204C1
	s_mov_b64 s[4:5], exec                                     // 0000000001B8: BE84047E
	s_waitcnt vmcnt(0)                                         // 0000000001BC: BF8C3F70
	v_fmac_f32_e32 v2, s0, v11                                 // 0000000001C0: 56041600
	s_waitcnt lgkmcnt(0)                                       // 0000000001C4: BF8CC07F
	v_cmpx_ngt_f32_e32 s1, v2                                  // 0000000001C8: 7C360401
	s_cbranch_execz _L4                                        // 0000000001CC: BF880005
	s_buffer_load_dword s2, s[8:11], 0x38                      // 0000000001D0: F4200084 FA000038
	s_waitcnt lgkmcnt(0)                                       // 0000000001D8: BF8CC07F
	v_cmp_lt_f32_e32 vcc_lo, s2, v2                            // 0000000001DC: 7C020402
	s_orn2_b64 s[2:3], vcc, exec                               // 0000000001E0: 8B827E6A
_L4:
	s_or_b64 exec, exec, s[4:5]                                // 0000000001E4: 88FE047E
	s_and_saveexec_b64 s[4:5], s[2:3]                          // 0000000001E8: BE842402
	s_cbranch_execz _L5                                        // 0000000001EC: BF88000A
	s_clause 0x1                                               // 0000000001F0: BFA10001
	s_buffer_load_dword s2, s[8:11], 0x40                      // 0000000001F4: F4200084 FA000040
	s_buffer_load_dword s3, s[8:11], 0x38                      // 0000000001FC: F42000C4 FA000038
	s_waitcnt lgkmcnt(0)                                       // 000000000204: BF8CC07F
	v_mul_f32_e64 v5, s2, -v5                                  // 000000000208: D5080005 40020A02
	v_med3_f32 v2, v2, s1, s3                                  // 000000000210: D5570002 000C0302
_L5:
	s_or_b64 exec, exec, s[4:5]                                // 000000000218: 88FE047E
	buffer_load_dwordx3 v[11:13], v6, s[8:11], 0 offen offset:48// 00000000021C: E03C1030 80020B06
	v_subrev_f32_e32 v8, s0, v8                                // 000000000224: 0A101000
	v_mov_b32_e32 v15, 0                                       // 000000000228: 7E1E0280
	s_mov_b32 s0, 0x3f666666                                   // 00000000022C: BE8003FF 3F666666
	v_mul_f32_e64 v14, 0x3e4ccccd, v8 clamp                    // 000000000234: D508800E 000210FF 3E4CCCCD
	v_mov_b32_e32 v16, v15                                     // 000000000240: 7E20030F
	v_mov_b32_e32 v17, v15                                     // 000000000244: 7E22030F
	v_fmaak_f32 v18, s0, v14, 0x3dcccccd                       // 000000000248: 5A241C00 3DCCCCCD
	buffer_store_dwordx3 v[0:2], v6, s[8:11], 0 offen          // 000000000250: E07C1000 80020006
	buffer_store_dwordx3 v[3:5], v10, s[8:11], 0 offen         // 000000000258: E07C1000 8002030A
	buffer_store_dword v8, v7, s[8:11], 0 offen                // 000000000260: E0701000 80020807
	buffer_store_dwordx3 v[15:17], v9, s[8:11], 0 offen        // 000000000268: E07C1000 80020F09
	buffer_store_dword v18, v6, s[8:11], 0 offen offset:44     // 000000000270: E070102C 80021206
	s_waitcnt vmcnt(0)                                         // 000000000278: BF8C3F70
	buffer_store_dwordx4 v[11:14], v6, s[8:11], 0 offen offset:48// 00000000027C: E0781030 80020B06
_L0:
	s_endpgm                                                   // 000000000284: BF810000
