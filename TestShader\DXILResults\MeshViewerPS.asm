;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyzw        1     NONE   float   xyzw
; NORMAL                   0   xyz         2     NONE   float   xyz 
; POSITION                 0   xyz         3     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 586334d92bbf69d391e5d10b27b0b4e2
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 4
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 4
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; NORMAL                   0                 linear       
; POSITION                 0                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer _MeshViewerPSCB
; {
;
;   struct _MeshViewerPSCB
;   {
;
;       struct struct.Struct__MeshViewerPSCB
;       {
;
;           float3 CameraPos;                         ; Offset:    0
;           int ViewMode;                             ; Offset:   12
;       
;       } _MeshViewerPSCB;                            ; Offset:    0
;
;   
;   } _MeshViewerPSCB;                                ; Offset:    0 Size:    16
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; _MeshViewerPSCB                   cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 15, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 8, 9, 10, 12, 13, 14 }
;   output 1 depends on inputs: { 5, 8, 9, 10, 12, 13, 14 }
;   output 2 depends on inputs: { 6, 8, 9, 10, 12, 13, 14 }
;   output 3 depends on inputs: { 7 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%_MeshViewerPSCB = type { %struct.Struct__MeshViewerPSCB }
%struct.Struct__MeshViewerPSCB = type { <3 x float>, i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %13 = extractvalue %dx.types.CBufRet.i32 %12, 3
  %14 = icmp eq i32 %13, 11
  br i1 %14, label %15, label %98

; <label>:15                                      ; preds = %0
  %16 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %17 = extractvalue %dx.types.CBufRet.f32 %16, 0
  %18 = extractvalue %dx.types.CBufRet.f32 %16, 1
  %19 = extractvalue %dx.types.CBufRet.f32 %16, 2
  %20 = fsub fast float %17, %2
  %21 = fsub fast float %18, %3
  %22 = fsub fast float %19, %4
  %23 = call float @dx.op.dot3.f32(i32 55, float %20, float %21, float %22, float %20, float %21, float %22)  ; Dot3(ax,ay,az,bx,by,bz)
  %24 = call float @dx.op.unary.f32(i32 25, float %23)  ; Rsqrt(value)
  %25 = fmul fast float %20, %24
  %26 = fmul fast float %21, %24
  %27 = fmul fast float %22, %24
  %28 = fsub fast float 2.000000e+00, %2
  %29 = fsub fast float 3.000000e+00, %3
  %30 = fsub fast float 2.000000e+00, %4
  %31 = call float @dx.op.dot3.f32(i32 55, float %28, float %29, float %30, float %28, float %29, float %30)  ; Dot3(ax,ay,az,bx,by,bz)
  %32 = call float @dx.op.unary.f32(i32 25, float %31)  ; Rsqrt(value)
  %33 = fmul fast float %32, %28
  %34 = fmul fast float %32, %29
  %35 = fmul fast float %32, %30
  %36 = fmul fast float %28, %28
  %37 = fmul fast float %29, %29
  %38 = fadd fast float %37, %36
  %39 = fmul fast float %30, %30
  %40 = fadd fast float %38, %39
  %41 = call float @dx.op.unary.f32(i32 24, float %40)  ; Sqrt(value)
  %42 = fmul fast float %41, %41
  %43 = fadd fast float %42, 1.000000e+00
  %44 = fdiv fast float 2.000000e+00, %43
  %45 = call float @dx.op.dot3.f32(i32 55, float %33, float %34, float %35, float %5, float %6, float %7)  ; Dot3(ax,ay,az,bx,by,bz)
  %46 = call float @dx.op.binary.f32(i32 35, float %45, float 0.000000e+00)  ; FMax(a,b)
  %47 = fmul fast float %44, %46
  %48 = fadd fast float %33, %25
  %49 = fadd fast float %34, %26
  %50 = fadd fast float %35, %27
  %51 = call float @dx.op.dot3.f32(i32 55, float %48, float %49, float %50, float %48, float %49, float %50)  ; Dot3(ax,ay,az,bx,by,bz)
  %52 = call float @dx.op.unary.f32(i32 25, float %51)  ; Rsqrt(value)
  %53 = fmul fast float %52, %48
  %54 = fmul fast float %52, %49
  %55 = fmul fast float %52, %50
  %56 = call float @dx.op.dot3.f32(i32 55, float %5, float %6, float %7, float %25, float %26, float %27)  ; Dot3(ax,ay,az,bx,by,bz)
  %57 = call float @dx.op.binary.f32(i32 35, float %56, float 0.000000e+00)  ; FMax(a,b)
  %58 = call float @dx.op.binary.f32(i32 36, float %57, float 1.000000e+00)  ; FMin(a,b)
  %59 = call float @dx.op.dot3.f32(i32 55, float %5, float %6, float %7, float %33, float %34, float %35)  ; Dot3(ax,ay,az,bx,by,bz)
  %60 = call float @dx.op.binary.f32(i32 35, float %59, float 0.000000e+00)  ; FMax(a,b)
  %61 = call float @dx.op.binary.f32(i32 36, float %60, float 1.000000e+00)  ; FMin(a,b)
  %62 = call float @dx.op.dot3.f32(i32 55, float %5, float %6, float %7, float %53, float %54, float %55)  ; Dot3(ax,ay,az,bx,by,bz)
  %63 = call float @dx.op.binary.f32(i32 35, float %62, float 0.000000e+00)  ; FMax(a,b)
  %64 = call float @dx.op.binary.f32(i32 36, float %63, float 1.000000e+00)  ; FMin(a,b)
  %65 = call float @dx.op.dot3.f32(i32 55, float %25, float %26, float %27, float %53, float %54, float %55)  ; Dot3(ax,ay,az,bx,by,bz)
  %66 = call float @dx.op.binary.f32(i32 35, float %65, float 0.000000e+00)  ; FMax(a,b)
  %67 = call float @dx.op.binary.f32(i32 36, float %66, float 1.000000e+00)  ; FMin(a,b)
  %68 = fsub fast float 1.000000e+00, %67
  %69 = call float @dx.op.unary.f32(i32 23, float %68)  ; Log(value)
  %70 = fmul fast float %69, 5.000000e+00
  %71 = call float @dx.op.unary.f32(i32 21, float %70)  ; Exp(value)
  %72 = fmul fast float %71, 0x3FEEB851E0000000
  %73 = fadd fast float %72, 0x3FA47AE140000000
  %74 = fmul fast float %64, %64
  %75 = fmul fast float %74, 0x3FEF2E48E0000000
  %76 = fsub fast float 1.000000e+00, %75
  %77 = fmul fast float %76, %76
  %78 = fdiv fast float 0x3F80B04880000000, %77
  %79 = call float @dx.op.binary.f32(i32 35, float %61, float 0x3F50624DE0000000)  ; FMax(a,b)
  %80 = fmul fast float %61, 0x3FED70A3E0000000
  %81 = fadd fast float %80, 0x3FB47AE160000000
  %82 = fdiv fast float %79, %81
  %83 = call float @dx.op.binary.f32(i32 35, float %58, float 0x3F50624DE0000000)  ; FMax(a,b)
  %84 = fmul fast float %58, 0x3FED70A3E0000000
  %85 = fadd fast float %84, 0x3FB47AE160000000
  %86 = fdiv fast float %83, %85
  %87 = fmul fast float %73, %78
  %88 = fmul fast float %87, %82
  %89 = fmul fast float %88, %86
  %90 = fmul fast float %79, 4.000000e+00
  %91 = fmul fast float %90, %83
  %92 = fdiv fast float %89, %91
  %93 = fmul fast float %71, 0x3FCF4A87C0000000
  %94 = fsub fast float 0x3FCF4A87C0000000, %93
  %95 = fadd fast float %94, %92
  %96 = fmul fast float %47, %95
  %97 = fadd fast float %96, 0x3F947AE140000000
  br label %98

; <label>:98                                      ; preds = %15, %0
  %99 = phi float [ %97, %15 ], [ %8, %0 ]
  %100 = phi float [ %97, %15 ], [ %9, %0 ]
  %101 = phi float [ %97, %15 ], [ %10, %0 ]
  %102 = phi float [ 1.000000e+00, %15 ], [ %11, %0 ]
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %99)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %100)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %101)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %102)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %_MeshViewerPSCB* undef, !"", i32 0, i32 0, i32 1, i32 16, null}
!7 = !{[17 x i32] [i32 15, i32 4, i32 0, i32 0, i32 0, i32 0, i32 1, i32 2, i32 4, i32 8, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !18, null}
!10 = !{!11, !13, !15, !17}
!11 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!12 = !{i32 0}
!13 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 4, i32 1, i8 0, !14}
!14 = !{i32 3, i32 15}
!15 = !{i32 2, !"NORMAL", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 2, i8 0, !16}
!16 = !{i32 3, i32 7}
!17 = !{i32 3, !"POSITION", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 3, i8 0, !16}
!18 = !{!19}
!19 = !{i32 0, !"SV_Target", i8 9, i8 16, !12, i8 0, i32 1, i8 4, i32 0, i8 0, !14}
