; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 533
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID %gl_LocalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_RWStructuredBuffer_Particle "type.RWStructuredBuffer.Particle"
               OpName %Particle "Particle"
               OpMemberName %Particle 0 "Position"
               OpMemberName %Particle 1 "Life"
               OpMemberName %Particle 2 "Velocity"
               OpMemberName %Particle 3 "Size"
               OpMemberName %Particle 4 "Color"
               OpMemberName %Particle 5 "Acceleration"
               OpMemberName %Particle 6 "Mass"
               OpMemberName %Particle 7 "Type"
               OpMemberName %Particle 8 "_padding"
               OpName %ParticleBuffer "ParticleBuffer"
               OpName %type_AppendStructuredBuffer_Particle "type.AppendStructuredBuffer.Particle"
               OpName %DeadParticleBuffer "DeadParticleBuffer"
               OpName %type_ACSBuffer_counter "type.ACSBuffer.counter"
               OpMemberName %type_ACSBuffer_counter 0 "counter"
               OpName %counter_var_DeadParticleBuffer "counter.var.DeadParticleBuffer"
               OpName %SharedPositions "SharedPositions"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %gl_LocalInvocationID BuiltIn LocalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %ParticleBuffer DescriptorSet 0
               OpDecorate %ParticleBuffer Binding 0
               OpDecorate %DeadParticleBuffer DescriptorSet 0
               OpDecorate %DeadParticleBuffer Binding 1
               OpDecorate %counter_var_DeadParticleBuffer DescriptorSet 0
               OpDecorate %counter_var_DeadParticleBuffer Binding 2
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
               OpMemberDecorate %Particle 0 Offset 0
               OpMemberDecorate %Particle 1 Offset 12
               OpMemberDecorate %Particle 2 Offset 16
               OpMemberDecorate %Particle 3 Offset 28
               OpMemberDecorate %Particle 4 Offset 32
               OpMemberDecorate %Particle 5 Offset 48
               OpMemberDecorate %Particle 6 Offset 60
               OpMemberDecorate %Particle 7 Offset 64
               OpMemberDecorate %Particle 8 Offset 68
               OpDecorate %_runtimearr_Particle ArrayStride 80
               OpMemberDecorate %type_RWStructuredBuffer_Particle 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_Particle BufferBlock
               OpMemberDecorate %type_AppendStructuredBuffer_Particle 0 Offset 0
               OpDecorate %type_AppendStructuredBuffer_Particle BufferBlock
               OpMemberDecorate %type_ACSBuffer_counter 0 Offset 0
               OpDecorate %type_ACSBuffer_counter BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_2 = OpConstant %int 2
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
      %int_4 = OpConstant %int 4
     %int_13 = OpConstant %int 13
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
     %int_14 = OpConstant %int 14
      %int_5 = OpConstant %int 5
    %float_2 = OpConstant %float 2
     %int_11 = OpConstant %int 11
    %v3float = OpTypeVector %float 3
         %33 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %float_5 = OpConstant %float 5
    %uint_64 = OpConstant %uint 64
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
     %uint_1 = OpConstant %uint 1
%float_0_0500000007 = OpConstant %float 0.0500000007
         %40 = OpConstantComposite %v3float %float_0_100000001 %float_0 %float_0
         %41 = OpConstantComposite %v3float %float_0 %float_0_100000001 %float_0
         %42 = OpConstantComposite %v3float %float_0 %float_0 %float_0_100000001
    %float_3 = OpConstant %float 3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
    %float_1 = OpConstant %float 1
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
    %v4float = OpTypeVector %float 4
   %Particle = OpTypeStruct %v3float %float %v3float %float %v4float %v3float %float %uint %v3float
%_runtimearr_Particle = OpTypeRuntimeArray %Particle
%type_RWStructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_RWStructuredBuffer_Particle = OpTypePointer Uniform %type_RWStructuredBuffer_Particle
%type_AppendStructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_AppendStructuredBuffer_Particle = OpTypePointer Uniform %type_AppendStructuredBuffer_Particle
%type_ACSBuffer_counter = OpTypeStruct %int
%_ptr_Uniform_type_ACSBuffer_counter = OpTypePointer Uniform %type_ACSBuffer_counter
%_arr_v3float_uint_64 = OpTypeArray %v3float %uint_64
%_ptr_Workgroup__arr_v3float_uint_64 = OpTypePointer Workgroup %_arr_v3float_uint_64
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %63 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Uniform_Particle = OpTypePointer Uniform %Particle
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Workgroup_v3float = OpTypePointer Workgroup %v3float
     %uint_2 = OpConstant %uint 2
   %uint_264 = OpConstant %uint 264
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%ParticleBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_Particle Uniform
%DeadParticleBuffer = OpVariable %_ptr_Uniform_type_AppendStructuredBuffer_Particle Uniform
%counter_var_DeadParticleBuffer = OpVariable %_ptr_Uniform_type_ACSBuffer_counter Uniform
%SharedPositions = OpVariable %_ptr_Workgroup__arr_v3float_uint_64 Workgroup
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%gl_LocalInvocationID = OpVariable %_ptr_Input_v3uint Input
         %72 = OpConstantComposite %v3float %float_5 %float_5 %float_5
  %float_0_5 = OpConstant %float 0.5
       %main = OpFunction %void None %63
         %74 = OpLabel
         %75 = OpLoad %v3uint %gl_GlobalInvocationID
         %76 = OpLoad %v3uint %gl_LocalInvocationID
               OpSelectionMerge %77 None
               OpSwitch %uint_0 %78
         %78 = OpLabel
         %79 = OpCompositeExtract %uint %75 0
         %80 = OpAccessChain %_ptr_Uniform_uint %ComputeParams %int_0
         %81 = OpLoad %uint %80
         %82 = OpUGreaterThanEqual %bool %79 %81
               OpSelectionMerge %83 None
               OpBranchConditional %82 %84 %83
         %84 = OpLabel
               OpBranch %77
         %83 = OpLabel
         %85 = OpAccessChain %_ptr_Uniform_Particle %ParticleBuffer %int_0 %79
         %86 = OpLoad %Particle %85
         %87 = OpCompositeExtract %v3float %86 0
         %88 = OpCompositeExtract %float %86 1
         %89 = OpCompositeExtract %v3float %86 2
         %90 = OpCompositeExtract %float %86 3
         %91 = OpCompositeExtract %v4float %86 4
         %92 = OpCompositeExtract %v3float %86 5
         %93 = OpCompositeExtract %float %86 6
         %94 = OpCompositeExtract %uint %86 7
         %95 = OpCompositeExtract %v3float %86 8
         %96 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_2
         %97 = OpLoad %float %96
         %98 = OpFSub %float %88 %97
         %99 = OpFOrdLessThanEqual %bool %98 %float_0
               OpSelectionMerge %100 None
               OpBranchConditional %99 %101 %100
        %101 = OpLabel
        %102 = OpAccessChain %_ptr_Uniform_int %counter_var_DeadParticleBuffer %uint_0
        %103 = OpAtomicIAdd %int %102 %uint_1 %uint_0 %int_1
        %104 = OpAccessChain %_ptr_Uniform_Particle %DeadParticleBuffer %uint_0 %103
        %105 = OpCompositeConstruct %Particle %87 %98 %89 %90 %91 %92 %93 %94 %95
               OpStore %104 %105
        %106 = OpCompositeConstruct %Particle %87 %98 %89 %90 %91 %92 %93 %uint_0 %95
               OpStore %85 %106
               OpBranch %77
        %100 = OpLabel
        %107 = OpAccessChain %_ptr_Uniform_v3float %ComputeParams %int_4
        %108 = OpLoad %v3float %107
        %109 = OpVectorTimesScalar %v3float %108 %93
        %110 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_13
        %111 = OpLoad %float %110
        %112 = OpVectorTimesScalar %v3float %87 %111
        %113 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
        %114 = OpLoad %float %113
        %115 = OpFMul %float %114 %float_0_100000001
        %116 = OpCompositeConstruct %v3float %115 %115 %115
        %117 = OpFAdd %v3float %112 %116
        %118 = OpFSub %v3float %117 %40
        %119 = OpExtInst %v3float %1 Floor %118
        %120 = OpExtInst %v3float %1 Fract %118
        %121 = OpFMul %v3float %120 %120
        %122 = OpVectorTimesScalar %v3float %121 %float_3
        %123 = OpVectorTimesScalar %v3float %120 %float_2
        %124 = OpFSub %v3float %122 %123
        %125 = OpCompositeExtract %float %119 0
        %126 = OpCompositeExtract %float %119 1
        %127 = OpFMul %float %126 %float_57
        %128 = OpFAdd %float %125 %127
        %129 = OpCompositeExtract %float %119 2
        %130 = OpFMul %float %float_113 %129
        %131 = OpFAdd %float %128 %130
        %132 = OpExtInst %float %1 Sin %131
        %133 = OpFMul %float %132 %float_43758_5469
        %134 = OpExtInst %float %1 Fract %133
        %135 = OpFAdd %float %131 %float_1
        %136 = OpExtInst %float %1 Sin %135
        %137 = OpFMul %float %136 %float_43758_5469
        %138 = OpExtInst %float %1 Fract %137
        %139 = OpCompositeExtract %float %124 0
        %140 = OpExtInst %float %1 FMix %134 %138 %139
        %141 = OpFAdd %float %131 %float_57
        %142 = OpExtInst %float %1 Sin %141
        %143 = OpFMul %float %142 %float_43758_5469
        %144 = OpExtInst %float %1 Fract %143
        %145 = OpFAdd %float %131 %float_58
        %146 = OpExtInst %float %1 Sin %145
        %147 = OpFMul %float %146 %float_43758_5469
        %148 = OpExtInst %float %1 Fract %147
        %149 = OpExtInst %float %1 FMix %144 %148 %139
        %150 = OpCompositeExtract %float %124 1
        %151 = OpExtInst %float %1 FMix %140 %149 %150
        %152 = OpFAdd %float %131 %float_113
        %153 = OpExtInst %float %1 Sin %152
        %154 = OpFMul %float %153 %float_43758_5469
        %155 = OpExtInst %float %1 Fract %154
        %156 = OpFAdd %float %131 %float_114
        %157 = OpExtInst %float %1 Sin %156
        %158 = OpFMul %float %157 %float_43758_5469
        %159 = OpExtInst %float %1 Fract %158
        %160 = OpExtInst %float %1 FMix %155 %159 %139
        %161 = OpFAdd %float %131 %float_170
        %162 = OpExtInst %float %1 Sin %161
        %163 = OpFMul %float %162 %float_43758_5469
        %164 = OpExtInst %float %1 Fract %163
        %165 = OpFAdd %float %131 %float_171
        %166 = OpExtInst %float %1 Sin %165
        %167 = OpFMul %float %166 %float_43758_5469
        %168 = OpExtInst %float %1 Fract %167
        %169 = OpExtInst %float %1 FMix %164 %168 %139
        %170 = OpExtInst %float %1 FMix %160 %169 %150
        %171 = OpCompositeExtract %float %124 2
        %172 = OpExtInst %float %1 FMix %151 %170 %171
        %173 = OpFAdd %v3float %117 %40
        %174 = OpExtInst %v3float %1 Floor %173
        %175 = OpExtInst %v3float %1 Fract %173
        %176 = OpFMul %v3float %175 %175
        %177 = OpVectorTimesScalar %v3float %176 %float_3
        %178 = OpVectorTimesScalar %v3float %175 %float_2
        %179 = OpFSub %v3float %177 %178
        %180 = OpCompositeExtract %float %174 0
        %181 = OpCompositeExtract %float %174 1
        %182 = OpFMul %float %181 %float_57
        %183 = OpFAdd %float %180 %182
        %184 = OpCompositeExtract %float %174 2
        %185 = OpFMul %float %float_113 %184
        %186 = OpFAdd %float %183 %185
        %187 = OpExtInst %float %1 Sin %186
        %188 = OpFMul %float %187 %float_43758_5469
        %189 = OpExtInst %float %1 Fract %188
        %190 = OpFAdd %float %186 %float_1
        %191 = OpExtInst %float %1 Sin %190
        %192 = OpFMul %float %191 %float_43758_5469
        %193 = OpExtInst %float %1 Fract %192
        %194 = OpCompositeExtract %float %179 0
        %195 = OpExtInst %float %1 FMix %189 %193 %194
        %196 = OpFAdd %float %186 %float_57
        %197 = OpExtInst %float %1 Sin %196
        %198 = OpFMul %float %197 %float_43758_5469
        %199 = OpExtInst %float %1 Fract %198
        %200 = OpFAdd %float %186 %float_58
        %201 = OpExtInst %float %1 Sin %200
        %202 = OpFMul %float %201 %float_43758_5469
        %203 = OpExtInst %float %1 Fract %202
        %204 = OpExtInst %float %1 FMix %199 %203 %194
        %205 = OpCompositeExtract %float %179 1
        %206 = OpExtInst %float %1 FMix %195 %204 %205
        %207 = OpFAdd %float %186 %float_113
        %208 = OpExtInst %float %1 Sin %207
        %209 = OpFMul %float %208 %float_43758_5469
        %210 = OpExtInst %float %1 Fract %209
        %211 = OpFAdd %float %186 %float_114
        %212 = OpExtInst %float %1 Sin %211
        %213 = OpFMul %float %212 %float_43758_5469
        %214 = OpExtInst %float %1 Fract %213
        %215 = OpExtInst %float %1 FMix %210 %214 %194
        %216 = OpFAdd %float %186 %float_170
        %217 = OpExtInst %float %1 Sin %216
        %218 = OpFMul %float %217 %float_43758_5469
        %219 = OpExtInst %float %1 Fract %218
        %220 = OpFAdd %float %186 %float_171
        %221 = OpExtInst %float %1 Sin %220
        %222 = OpFMul %float %221 %float_43758_5469
        %223 = OpExtInst %float %1 Fract %222
        %224 = OpExtInst %float %1 FMix %219 %223 %194
        %225 = OpExtInst %float %1 FMix %215 %224 %205
        %226 = OpCompositeExtract %float %179 2
        %227 = OpExtInst %float %1 FMix %206 %225 %226
        %228 = OpFSub %v3float %117 %41
        %229 = OpExtInst %v3float %1 Floor %228
        %230 = OpExtInst %v3float %1 Fract %228
        %231 = OpFMul %v3float %230 %230
        %232 = OpVectorTimesScalar %v3float %231 %float_3
        %233 = OpVectorTimesScalar %v3float %230 %float_2
        %234 = OpFSub %v3float %232 %233
        %235 = OpCompositeExtract %float %229 0
        %236 = OpCompositeExtract %float %229 1
        %237 = OpFMul %float %236 %float_57
        %238 = OpFAdd %float %235 %237
        %239 = OpCompositeExtract %float %229 2
        %240 = OpFMul %float %float_113 %239
        %241 = OpFAdd %float %238 %240
        %242 = OpExtInst %float %1 Sin %241
        %243 = OpFMul %float %242 %float_43758_5469
        %244 = OpExtInst %float %1 Fract %243
        %245 = OpFAdd %float %241 %float_1
        %246 = OpExtInst %float %1 Sin %245
        %247 = OpFMul %float %246 %float_43758_5469
        %248 = OpExtInst %float %1 Fract %247
        %249 = OpCompositeExtract %float %234 0
        %250 = OpExtInst %float %1 FMix %244 %248 %249
        %251 = OpFAdd %float %241 %float_57
        %252 = OpExtInst %float %1 Sin %251
        %253 = OpFMul %float %252 %float_43758_5469
        %254 = OpExtInst %float %1 Fract %253
        %255 = OpFAdd %float %241 %float_58
        %256 = OpExtInst %float %1 Sin %255
        %257 = OpFMul %float %256 %float_43758_5469
        %258 = OpExtInst %float %1 Fract %257
        %259 = OpExtInst %float %1 FMix %254 %258 %249
        %260 = OpCompositeExtract %float %234 1
        %261 = OpExtInst %float %1 FMix %250 %259 %260
        %262 = OpFAdd %float %241 %float_113
        %263 = OpExtInst %float %1 Sin %262
        %264 = OpFMul %float %263 %float_43758_5469
        %265 = OpExtInst %float %1 Fract %264
        %266 = OpFAdd %float %241 %float_114
        %267 = OpExtInst %float %1 Sin %266
        %268 = OpFMul %float %267 %float_43758_5469
        %269 = OpExtInst %float %1 Fract %268
        %270 = OpExtInst %float %1 FMix %265 %269 %249
        %271 = OpFAdd %float %241 %float_170
        %272 = OpExtInst %float %1 Sin %271
        %273 = OpFMul %float %272 %float_43758_5469
        %274 = OpExtInst %float %1 Fract %273
        %275 = OpFAdd %float %241 %float_171
        %276 = OpExtInst %float %1 Sin %275
        %277 = OpFMul %float %276 %float_43758_5469
        %278 = OpExtInst %float %1 Fract %277
        %279 = OpExtInst %float %1 FMix %274 %278 %249
        %280 = OpExtInst %float %1 FMix %270 %279 %260
        %281 = OpCompositeExtract %float %234 2
        %282 = OpExtInst %float %1 FMix %261 %280 %281
        %283 = OpFAdd %v3float %117 %41
        %284 = OpExtInst %v3float %1 Floor %283
        %285 = OpExtInst %v3float %1 Fract %283
        %286 = OpFMul %v3float %285 %285
        %287 = OpVectorTimesScalar %v3float %286 %float_3
        %288 = OpVectorTimesScalar %v3float %285 %float_2
        %289 = OpFSub %v3float %287 %288
        %290 = OpCompositeExtract %float %284 0
        %291 = OpCompositeExtract %float %284 1
        %292 = OpFMul %float %291 %float_57
        %293 = OpFAdd %float %290 %292
        %294 = OpCompositeExtract %float %284 2
        %295 = OpFMul %float %float_113 %294
        %296 = OpFAdd %float %293 %295
        %297 = OpExtInst %float %1 Sin %296
        %298 = OpFMul %float %297 %float_43758_5469
        %299 = OpExtInst %float %1 Fract %298
        %300 = OpFAdd %float %296 %float_1
        %301 = OpExtInst %float %1 Sin %300
        %302 = OpFMul %float %301 %float_43758_5469
        %303 = OpExtInst %float %1 Fract %302
        %304 = OpCompositeExtract %float %289 0
        %305 = OpExtInst %float %1 FMix %299 %303 %304
        %306 = OpFAdd %float %296 %float_57
        %307 = OpExtInst %float %1 Sin %306
        %308 = OpFMul %float %307 %float_43758_5469
        %309 = OpExtInst %float %1 Fract %308
        %310 = OpFAdd %float %296 %float_58
        %311 = OpExtInst %float %1 Sin %310
        %312 = OpFMul %float %311 %float_43758_5469
        %313 = OpExtInst %float %1 Fract %312
        %314 = OpExtInst %float %1 FMix %309 %313 %304
        %315 = OpCompositeExtract %float %289 1
        %316 = OpExtInst %float %1 FMix %305 %314 %315
        %317 = OpFAdd %float %296 %float_113
        %318 = OpExtInst %float %1 Sin %317
        %319 = OpFMul %float %318 %float_43758_5469
        %320 = OpExtInst %float %1 Fract %319
        %321 = OpFAdd %float %296 %float_114
        %322 = OpExtInst %float %1 Sin %321
        %323 = OpFMul %float %322 %float_43758_5469
        %324 = OpExtInst %float %1 Fract %323
        %325 = OpExtInst %float %1 FMix %320 %324 %304
        %326 = OpFAdd %float %296 %float_170
        %327 = OpExtInst %float %1 Sin %326
        %328 = OpFMul %float %327 %float_43758_5469
        %329 = OpExtInst %float %1 Fract %328
        %330 = OpFAdd %float %296 %float_171
        %331 = OpExtInst %float %1 Sin %330
        %332 = OpFMul %float %331 %float_43758_5469
        %333 = OpExtInst %float %1 Fract %332
        %334 = OpExtInst %float %1 FMix %329 %333 %304
        %335 = OpExtInst %float %1 FMix %325 %334 %315
        %336 = OpCompositeExtract %float %289 2
        %337 = OpExtInst %float %1 FMix %316 %335 %336
        %338 = OpFSub %v3float %117 %42
        %339 = OpExtInst %v3float %1 Floor %338
        %340 = OpExtInst %v3float %1 Fract %338
        %341 = OpFMul %v3float %340 %340
        %342 = OpVectorTimesScalar %v3float %341 %float_3
        %343 = OpVectorTimesScalar %v3float %340 %float_2
        %344 = OpFSub %v3float %342 %343
        %345 = OpCompositeExtract %float %339 0
        %346 = OpCompositeExtract %float %339 1
        %347 = OpFMul %float %346 %float_57
        %348 = OpFAdd %float %345 %347
        %349 = OpCompositeExtract %float %339 2
        %350 = OpFMul %float %float_113 %349
        %351 = OpFAdd %float %348 %350
        %352 = OpExtInst %float %1 Sin %351
        %353 = OpFMul %float %352 %float_43758_5469
        %354 = OpExtInst %float %1 Fract %353
        %355 = OpFAdd %float %351 %float_1
        %356 = OpExtInst %float %1 Sin %355
        %357 = OpFMul %float %356 %float_43758_5469
        %358 = OpExtInst %float %1 Fract %357
        %359 = OpCompositeExtract %float %344 0
        %360 = OpExtInst %float %1 FMix %354 %358 %359
        %361 = OpFAdd %float %351 %float_57
        %362 = OpExtInst %float %1 Sin %361
        %363 = OpFMul %float %362 %float_43758_5469
        %364 = OpExtInst %float %1 Fract %363
        %365 = OpFAdd %float %351 %float_58
        %366 = OpExtInst %float %1 Sin %365
        %367 = OpFMul %float %366 %float_43758_5469
        %368 = OpExtInst %float %1 Fract %367
        %369 = OpExtInst %float %1 FMix %364 %368 %359
        %370 = OpCompositeExtract %float %344 1
        %371 = OpExtInst %float %1 FMix %360 %369 %370
        %372 = OpFAdd %float %351 %float_113
        %373 = OpExtInst %float %1 Sin %372
        %374 = OpFMul %float %373 %float_43758_5469
        %375 = OpExtInst %float %1 Fract %374
        %376 = OpFAdd %float %351 %float_114
        %377 = OpExtInst %float %1 Sin %376
        %378 = OpFMul %float %377 %float_43758_5469
        %379 = OpExtInst %float %1 Fract %378
        %380 = OpExtInst %float %1 FMix %375 %379 %359
        %381 = OpFAdd %float %351 %float_170
        %382 = OpExtInst %float %1 Sin %381
        %383 = OpFMul %float %382 %float_43758_5469
        %384 = OpExtInst %float %1 Fract %383
        %385 = OpFAdd %float %351 %float_171
        %386 = OpExtInst %float %1 Sin %385
        %387 = OpFMul %float %386 %float_43758_5469
        %388 = OpExtInst %float %1 Fract %387
        %389 = OpExtInst %float %1 FMix %384 %388 %359
        %390 = OpExtInst %float %1 FMix %380 %389 %370
        %391 = OpCompositeExtract %float %344 2
        %392 = OpExtInst %float %1 FMix %371 %390 %391
        %393 = OpFAdd %v3float %117 %42
        %394 = OpExtInst %v3float %1 Floor %393
        %395 = OpExtInst %v3float %1 Fract %393
        %396 = OpFMul %v3float %395 %395
        %397 = OpVectorTimesScalar %v3float %396 %float_3
        %398 = OpVectorTimesScalar %v3float %395 %float_2
        %399 = OpFSub %v3float %397 %398
        %400 = OpCompositeExtract %float %394 0
        %401 = OpCompositeExtract %float %394 1
        %402 = OpFMul %float %401 %float_57
        %403 = OpFAdd %float %400 %402
        %404 = OpCompositeExtract %float %394 2
        %405 = OpFMul %float %float_113 %404
        %406 = OpFAdd %float %403 %405
        %407 = OpExtInst %float %1 Sin %406
        %408 = OpFMul %float %407 %float_43758_5469
        %409 = OpExtInst %float %1 Fract %408
        %410 = OpFAdd %float %406 %float_1
        %411 = OpExtInst %float %1 Sin %410
        %412 = OpFMul %float %411 %float_43758_5469
        %413 = OpExtInst %float %1 Fract %412
        %414 = OpCompositeExtract %float %399 0
        %415 = OpExtInst %float %1 FMix %409 %413 %414
        %416 = OpFAdd %float %406 %float_57
        %417 = OpExtInst %float %1 Sin %416
        %418 = OpFMul %float %417 %float_43758_5469
        %419 = OpExtInst %float %1 Fract %418
        %420 = OpFAdd %float %406 %float_58
        %421 = OpExtInst %float %1 Sin %420
        %422 = OpFMul %float %421 %float_43758_5469
        %423 = OpExtInst %float %1 Fract %422
        %424 = OpExtInst %float %1 FMix %419 %423 %414
        %425 = OpCompositeExtract %float %399 1
        %426 = OpExtInst %float %1 FMix %415 %424 %425
        %427 = OpFAdd %float %406 %float_113
        %428 = OpExtInst %float %1 Sin %427
        %429 = OpFMul %float %428 %float_43758_5469
        %430 = OpExtInst %float %1 Fract %429
        %431 = OpFAdd %float %406 %float_114
        %432 = OpExtInst %float %1 Sin %431
        %433 = OpFMul %float %432 %float_43758_5469
        %434 = OpExtInst %float %1 Fract %433
        %435 = OpExtInst %float %1 FMix %430 %434 %414
        %436 = OpFAdd %float %406 %float_170
        %437 = OpExtInst %float %1 Sin %436
        %438 = OpFMul %float %437 %float_43758_5469
        %439 = OpExtInst %float %1 Fract %438
        %440 = OpFAdd %float %406 %float_171
        %441 = OpExtInst %float %1 Sin %440
        %442 = OpFMul %float %441 %float_43758_5469
        %443 = OpExtInst %float %1 Fract %442
        %444 = OpExtInst %float %1 FMix %439 %443 %414
        %445 = OpExtInst %float %1 FMix %435 %444 %425
        %446 = OpCompositeExtract %float %399 2
        %447 = OpExtInst %float %1 FMix %426 %445 %446
        %448 = OpFSub %float %337 %282
        %449 = OpFSub %float %448 %447
        %450 = OpFAdd %float %449 %392
        %451 = OpFSub %float %447 %392
        %452 = OpFSub %float %451 %227
        %453 = OpFAdd %float %452 %172
        %454 = OpFSub %float %227 %172
        %455 = OpFSub %float %454 %337
        %456 = OpFAdd %float %455 %282
        %457 = OpCompositeConstruct %v3float %450 %453 %456
        %458 = OpFMul %v3float %457 %72
        %459 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_14
        %460 = OpLoad %float %459
        %461 = OpVectorTimesScalar %v3float %458 %460
        %462 = OpFAdd %v3float %109 %461
        %463 = OpCompositeConstruct %v3float %93 %93 %93
        %464 = OpFDiv %v3float %462 %463
        %465 = OpVectorTimesScalar %v3float %464 %97
        %466 = OpFAdd %v3float %89 %465
        %467 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_5
        %468 = OpLoad %float %467
        %469 = OpVectorTimesScalar %v3float %466 %468
        %470 = OpVectorTimesScalar %v3float %469 %97
        %471 = OpFAdd %v3float %87 %470
        %472 = OpFMul %float %98 %float_0_5
        %473 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_11 %int_0
        %474 = OpLoad %float %473
        %475 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_11 %int_1
        %476 = OpLoad %float %475
        %477 = OpExtInst %float %1 FMix %474 %476 %472
        %478 = OpCompositeInsert %v4float %472 %91 3
        %479 = OpCompositeExtract %uint %76 0
        %480 = OpAccessChain %_ptr_Workgroup_v3float %SharedPositions %479
               OpStore %480 %471
               OpControlBarrier %uint_2 %uint_2 %uint_264
               OpBranch %481
        %481 = OpLabel
        %482 = OpPhi %v3float %33 %100 %483 %484
        %485 = OpPhi %v3float %33 %100 %486 %484
        %487 = OpPhi %uint %uint_0 %100 %488 %484
        %489 = OpPhi %uint %uint_0 %100 %490 %484
        %491 = OpULessThan %bool %489 %uint_64
               OpLoopMerge %492 %484 None
               OpBranchConditional %491 %493 %492
        %493 = OpLabel
        %494 = OpIEqual %bool %489 %479
               OpSelectionMerge %495 None
               OpBranchConditional %494 %496 %495
        %496 = OpLabel
               OpBranch %484
        %495 = OpLabel
        %497 = OpAccessChain %_ptr_Workgroup_v3float %SharedPositions %489
        %498 = OpLoad %v3float %497
        %499 = OpFSub %v3float %471 %498
        %500 = OpExtInst %float %1 Length %499
        %501 = OpFOrdLessThan %bool %500 %float_5
               OpSelectionMerge %502 None
               OpBranchConditional %501 %503 %502
        %503 = OpLabel
        %504 = OpFOrdGreaterThan %bool %500 %float_0
               OpBranch %502
        %502 = OpLabel
        %505 = OpPhi %bool %false %495 %504 %503
               OpSelectionMerge %506 None
               OpBranchConditional %505 %507 %506
        %507 = OpLabel
        %508 = OpExtInst %v3float %1 Normalize %499
        %509 = OpCompositeConstruct %v3float %500 %500 %500
        %510 = OpFDiv %v3float %508 %509
        %511 = OpFAdd %v3float %482 %510
        %512 = OpFAdd %v3float %485 %498
        %513 = OpIAdd %uint %487 %uint_1
               OpBranch %506
        %506 = OpLabel
        %514 = OpPhi %v3float %482 %502 %511 %507
        %515 = OpPhi %v3float %485 %502 %512 %507
        %516 = OpPhi %uint %487 %502 %513 %507
               OpBranch %484
        %484 = OpLabel
        %483 = OpPhi %v3float %482 %496 %514 %506
        %486 = OpPhi %v3float %485 %496 %515 %506
        %488 = OpPhi %uint %487 %496 %516 %506
        %490 = OpIAdd %uint %489 %uint_1
               OpBranch %481
        %492 = OpLabel
        %517 = OpUGreaterThan %bool %487 %uint_0
               OpSelectionMerge %518 None
               OpBranchConditional %517 %519 %518
        %519 = OpLabel
        %520 = OpConvertUToF %float %487
        %521 = OpCompositeConstruct %v3float %520 %520 %520
        %522 = OpFDiv %v3float %485 %521
        %523 = OpFSub %v3float %522 %471
        %524 = OpExtInst %v3float %1 Normalize %523
        %525 = OpVectorTimesScalar %v3float %482 %float_0_100000001
        %526 = OpVectorTimesScalar %v3float %525 %97
        %527 = OpFAdd %v3float %469 %526
        %528 = OpVectorTimesScalar %v3float %524 %float_0_0500000007
        %529 = OpVectorTimesScalar %v3float %528 %97
        %530 = OpFAdd %v3float %527 %529
               OpBranch %518
        %518 = OpLabel
        %531 = OpPhi %v3float %469 %492 %530 %519
        %532 = OpCompositeConstruct %Particle %471 %98 %531 %477 %478 %464 %93 %94 %95
               OpStore %85 %532
               OpBranch %77
         %77 = OpLabel
               OpReturn
               OpFunctionEnd
