// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct Particle
{
  float3 Position;
  float Life;
  float3 Velocity;
  float Size;
  float4 Color;
  float3 Acceleration;
  float Mass;
  uint Type;
  float3 _padding;
};

cbuffer ComputeParams : register(b0)
{
  uint ParticleCount;
  uint MaxParticles;
  float DeltaTime;
  float Time;
  float3 Gravity;
  float Damping;
  float3 EmitterPosition;
  float EmissionRate;
  float3 EmitterDirection;
  float EmissionSpeed;
  float2 LifetimeRange;
  float2 SizeRange;
  uint FrameCount;
  float NoiseScale;
  float NoiseStrength;
  uint _padding;
}

StructuredBuffer<Particle> ParticleBuffer : register(t0);
RWByteAddressBuffer RawDataBuffer : register(u0);
groupshared float SharedDistances[64];
[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID, uint3 localId : SV_GroupThreadID)
{
  uint index = id.x;
  if (index < ParticleCount)
  {
    SharedDistances[localId.x] = length(ParticleBuffer[index].Position);
  }
  else
  {
    SharedDistances[localId.x] = 0.0f;
  }
  GroupMemoryBarrierWithGroupSync();
  for (uint stride = 32; stride > 0; stride >>= 1)
  {
    if (localId.x < stride)
    {
      SharedDistances[localId.x] = max(SharedDistances[localId.x], SharedDistances[localId.x + stride]);
    }
    GroupMemoryBarrierWithGroupSync();
  }
  if (localId.x == 0)
  {
    uint originalValue;
    RawDataBuffer.InterlockedMax(0, asuint(SharedDistances[0]), originalValue);
  }
}

