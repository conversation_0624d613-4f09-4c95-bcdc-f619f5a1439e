;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TANGENT                  0   xyz         2     NONE   float   xyz 
; TEXCOORD                 0   xy          3     NONE   float   xy  
; COLOR                    0   xyzw        4     NONE   float   xyzw
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5   xyzw        6     NONE   float   xyzw
; TEXCOORD                 6   xyz         7     NONE   float   xyz 
; TEXCOORD                 7   xyz         8     NONE   float   xyz 
;
; shader hash: d87d73f697489cb81eeaeb50c960e710
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 5
; SigOutputElements: 9
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 5
; SigOutputVectors[0]: 9
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TANGENT                  0                              
; TEXCOORD                 0                              
; COLOR                    0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
;
; Buffer Definitions:
;
; cbuffer MaterialConstants
; {
;
;   struct hostlayout.MaterialConstants
;   {
;
;       column_major float4x4 WorldMatrix;            ; Offset:    0
;       column_major float4x4 ViewMatrix;             ; Offset:   64
;       column_major float4x4 ProjectionMatrix;       ; Offset:  128
;       column_major float4x4 WorldViewProjectionMatrix;; Offset:  192
;       float4 LightPosition;                         ; Offset:  256
;       float4 LightColor;                            ; Offset:  272
;       float4 MaterialDiffuse;                       ; Offset:  288
;       float4 MaterialSpecular;                      ; Offset:  304
;       float4 CameraPosition;                        ; Offset:  320
;       float Time;                                   ; Offset:  336
;       float SpecularPower;                          ; Offset:  340
;       float2 TextureScale;                          ; Offset:  344
;   
;   } MaterialConstants;                              ; Offset:    0 Size:   352
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; MaterialConstants                 cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 20, outputs: 35
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2 }
;   output 1 depends on inputs: { 0, 1, 2 }
;   output 2 depends on inputs: { 0, 1, 2 }
;   output 3 depends on inputs: { 0, 1, 2 }
;   output 4 depends on inputs: { 0, 1, 2 }
;   output 5 depends on inputs: { 0, 1, 2 }
;   output 6 depends on inputs: { 0, 1, 2 }
;   output 8 depends on inputs: { 4, 5, 6 }
;   output 9 depends on inputs: { 4, 5, 6 }
;   output 10 depends on inputs: { 4, 5, 6 }
;   output 12 depends on inputs: { 8, 9, 10 }
;   output 13 depends on inputs: { 8, 9, 10 }
;   output 14 depends on inputs: { 8, 9, 10 }
;   output 16 depends on inputs: { 4, 5, 6, 8, 9, 10 }
;   output 17 depends on inputs: { 4, 5, 6, 8, 9, 10 }
;   output 18 depends on inputs: { 4, 5, 6, 8, 9, 10 }
;   output 20 depends on inputs: { 12 }
;   output 21 depends on inputs: { 13 }
;   output 24 depends on inputs: { 16 }
;   output 25 depends on inputs: { 17 }
;   output 26 depends on inputs: { 18 }
;   output 27 depends on inputs: { 19 }
;   output 28 depends on inputs: { 0, 1, 2 }
;   output 29 depends on inputs: { 0, 1, 2 }
;   output 30 depends on inputs: { 0, 1, 2 }
;   output 32 depends on inputs: { 0, 1, 2 }
;   output 33 depends on inputs: { 0, 1, 2 }
;   output 34 depends on inputs: { 0, 1, 2 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%hostlayout.MaterialConstants = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <4 x float>, <4 x float>, <4 x float>, <4 x float>, <4 x float>, float, float, <2 x float> }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %18 = extractvalue %dx.types.CBufRet.f32 %17, 0
  %19 = extractvalue %dx.types.CBufRet.f32 %17, 1
  %20 = extractvalue %dx.types.CBufRet.f32 %17, 2
  %21 = extractvalue %dx.types.CBufRet.f32 %17, 3
  %22 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %23 = extractvalue %dx.types.CBufRet.f32 %22, 0
  %24 = extractvalue %dx.types.CBufRet.f32 %22, 1
  %25 = extractvalue %dx.types.CBufRet.f32 %22, 2
  %26 = extractvalue %dx.types.CBufRet.f32 %22, 3
  %27 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %28 = extractvalue %dx.types.CBufRet.f32 %27, 0
  %29 = extractvalue %dx.types.CBufRet.f32 %27, 1
  %30 = extractvalue %dx.types.CBufRet.f32 %27, 2
  %31 = extractvalue %dx.types.CBufRet.f32 %27, 3
  %32 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %33 = extractvalue %dx.types.CBufRet.f32 %32, 0
  %34 = extractvalue %dx.types.CBufRet.f32 %32, 1
  %35 = extractvalue %dx.types.CBufRet.f32 %32, 2
  %36 = extractvalue %dx.types.CBufRet.f32 %32, 3
  %37 = fmul fast float %18, %14
  %38 = call float @dx.op.tertiary.f32(i32 46, float %15, float %19, float %37)  ; FMad(a,b,c)
  %39 = call float @dx.op.tertiary.f32(i32 46, float %16, float %20, float %38)  ; FMad(a,b,c)
  %40 = fadd fast float %39, %21
  %41 = fmul fast float %23, %14
  %42 = call float @dx.op.tertiary.f32(i32 46, float %15, float %24, float %41)  ; FMad(a,b,c)
  %43 = call float @dx.op.tertiary.f32(i32 46, float %16, float %25, float %42)  ; FMad(a,b,c)
  %44 = fadd fast float %43, %26
  %45 = fmul fast float %28, %14
  %46 = call float @dx.op.tertiary.f32(i32 46, float %15, float %29, float %45)  ; FMad(a,b,c)
  %47 = call float @dx.op.tertiary.f32(i32 46, float %16, float %30, float %46)  ; FMad(a,b,c)
  %48 = fadd fast float %47, %31
  %49 = fmul fast float %33, %14
  %50 = call float @dx.op.tertiary.f32(i32 46, float %15, float %34, float %49)  ; FMad(a,b,c)
  %51 = call float @dx.op.tertiary.f32(i32 46, float %16, float %35, float %50)  ; FMad(a,b,c)
  %52 = fadd fast float %51, %36
  %53 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %54 = extractvalue %dx.types.CBufRet.f32 %53, 0
  %55 = extractvalue %dx.types.CBufRet.f32 %53, 1
  %56 = extractvalue %dx.types.CBufRet.f32 %53, 2
  %57 = extractvalue %dx.types.CBufRet.f32 %53, 3
  %58 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %59 = extractvalue %dx.types.CBufRet.f32 %58, 0
  %60 = extractvalue %dx.types.CBufRet.f32 %58, 1
  %61 = extractvalue %dx.types.CBufRet.f32 %58, 2
  %62 = extractvalue %dx.types.CBufRet.f32 %58, 3
  %63 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %64 = extractvalue %dx.types.CBufRet.f32 %63, 0
  %65 = extractvalue %dx.types.CBufRet.f32 %63, 1
  %66 = extractvalue %dx.types.CBufRet.f32 %63, 2
  %67 = extractvalue %dx.types.CBufRet.f32 %63, 3
  %68 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 7)  ; CBufferLoadLegacy(handle,regIndex)
  %69 = extractvalue %dx.types.CBufRet.f32 %68, 0
  %70 = extractvalue %dx.types.CBufRet.f32 %68, 1
  %71 = extractvalue %dx.types.CBufRet.f32 %68, 2
  %72 = extractvalue %dx.types.CBufRet.f32 %68, 3
  %73 = fmul fast float %54, %40
  %74 = call float @dx.op.tertiary.f32(i32 46, float %44, float %55, float %73)  ; FMad(a,b,c)
  %75 = call float @dx.op.tertiary.f32(i32 46, float %48, float %56, float %74)  ; FMad(a,b,c)
  %76 = call float @dx.op.tertiary.f32(i32 46, float %52, float %57, float %75)  ; FMad(a,b,c)
  %77 = fmul fast float %59, %40
  %78 = call float @dx.op.tertiary.f32(i32 46, float %44, float %60, float %77)  ; FMad(a,b,c)
  %79 = call float @dx.op.tertiary.f32(i32 46, float %48, float %61, float %78)  ; FMad(a,b,c)
  %80 = call float @dx.op.tertiary.f32(i32 46, float %52, float %62, float %79)  ; FMad(a,b,c)
  %81 = fmul fast float %64, %40
  %82 = call float @dx.op.tertiary.f32(i32 46, float %44, float %65, float %81)  ; FMad(a,b,c)
  %83 = call float @dx.op.tertiary.f32(i32 46, float %48, float %66, float %82)  ; FMad(a,b,c)
  %84 = call float @dx.op.tertiary.f32(i32 46, float %52, float %67, float %83)  ; FMad(a,b,c)
  %85 = fmul fast float %69, %40
  %86 = call float @dx.op.tertiary.f32(i32 46, float %44, float %70, float %85)  ; FMad(a,b,c)
  %87 = call float @dx.op.tertiary.f32(i32 46, float %48, float %71, float %86)  ; FMad(a,b,c)
  %88 = call float @dx.op.tertiary.f32(i32 46, float %52, float %72, float %87)  ; FMad(a,b,c)
  %89 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %90 = extractvalue %dx.types.CBufRet.f32 %89, 0
  %91 = extractvalue %dx.types.CBufRet.f32 %89, 1
  %92 = extractvalue %dx.types.CBufRet.f32 %89, 2
  %93 = extractvalue %dx.types.CBufRet.f32 %89, 3
  %94 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %95 = extractvalue %dx.types.CBufRet.f32 %94, 0
  %96 = extractvalue %dx.types.CBufRet.f32 %94, 1
  %97 = extractvalue %dx.types.CBufRet.f32 %94, 2
  %98 = extractvalue %dx.types.CBufRet.f32 %94, 3
  %99 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %100 = extractvalue %dx.types.CBufRet.f32 %99, 0
  %101 = extractvalue %dx.types.CBufRet.f32 %99, 1
  %102 = extractvalue %dx.types.CBufRet.f32 %99, 2
  %103 = extractvalue %dx.types.CBufRet.f32 %99, 3
  %104 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %105 = extractvalue %dx.types.CBufRet.f32 %104, 0
  %106 = extractvalue %dx.types.CBufRet.f32 %104, 1
  %107 = extractvalue %dx.types.CBufRet.f32 %104, 2
  %108 = extractvalue %dx.types.CBufRet.f32 %104, 3
  %109 = fmul fast float %90, %76
  %110 = call float @dx.op.tertiary.f32(i32 46, float %80, float %91, float %109)  ; FMad(a,b,c)
  %111 = call float @dx.op.tertiary.f32(i32 46, float %84, float %92, float %110)  ; FMad(a,b,c)
  %112 = call float @dx.op.tertiary.f32(i32 46, float %88, float %93, float %111)  ; FMad(a,b,c)
  %113 = fmul fast float %95, %76
  %114 = call float @dx.op.tertiary.f32(i32 46, float %80, float %96, float %113)  ; FMad(a,b,c)
  %115 = call float @dx.op.tertiary.f32(i32 46, float %84, float %97, float %114)  ; FMad(a,b,c)
  %116 = call float @dx.op.tertiary.f32(i32 46, float %88, float %98, float %115)  ; FMad(a,b,c)
  %117 = fmul fast float %100, %76
  %118 = call float @dx.op.tertiary.f32(i32 46, float %80, float %101, float %117)  ; FMad(a,b,c)
  %119 = call float @dx.op.tertiary.f32(i32 46, float %84, float %102, float %118)  ; FMad(a,b,c)
  %120 = call float @dx.op.tertiary.f32(i32 46, float %88, float %103, float %119)  ; FMad(a,b,c)
  %121 = fmul fast float %105, %76
  %122 = call float @dx.op.tertiary.f32(i32 46, float %80, float %106, float %121)  ; FMad(a,b,c)
  %123 = call float @dx.op.tertiary.f32(i32 46, float %84, float %107, float %122)  ; FMad(a,b,c)
  %124 = call float @dx.op.tertiary.f32(i32 46, float %88, float %108, float %123)  ; FMad(a,b,c)
  %125 = fmul fast float %18, %11
  %126 = call float @dx.op.tertiary.f32(i32 46, float %12, float %19, float %125)  ; FMad(a,b,c)
  %127 = call float @dx.op.tertiary.f32(i32 46, float %13, float %20, float %126)  ; FMad(a,b,c)
  %128 = fmul fast float %23, %11
  %129 = call float @dx.op.tertiary.f32(i32 46, float %12, float %24, float %128)  ; FMad(a,b,c)
  %130 = call float @dx.op.tertiary.f32(i32 46, float %13, float %25, float %129)  ; FMad(a,b,c)
  %131 = fmul fast float %28, %11
  %132 = call float @dx.op.tertiary.f32(i32 46, float %12, float %29, float %131)  ; FMad(a,b,c)
  %133 = call float @dx.op.tertiary.f32(i32 46, float %13, float %30, float %132)  ; FMad(a,b,c)
  %134 = call float @dx.op.dot3.f32(i32 55, float %127, float %130, float %133, float %127, float %130, float %133)  ; Dot3(ax,ay,az,bx,by,bz)
  %135 = call float @dx.op.unary.f32(i32 25, float %134)  ; Rsqrt(value)
  %136 = fmul fast float %135, %127
  %137 = fmul fast float %135, %130
  %138 = fmul fast float %135, %133
  %139 = fmul fast float %18, %8
  %140 = call float @dx.op.tertiary.f32(i32 46, float %9, float %19, float %139)  ; FMad(a,b,c)
  %141 = call float @dx.op.tertiary.f32(i32 46, float %10, float %20, float %140)  ; FMad(a,b,c)
  %142 = fmul fast float %23, %8
  %143 = call float @dx.op.tertiary.f32(i32 46, float %9, float %24, float %142)  ; FMad(a,b,c)
  %144 = call float @dx.op.tertiary.f32(i32 46, float %10, float %25, float %143)  ; FMad(a,b,c)
  %145 = fmul fast float %28, %8
  %146 = call float @dx.op.tertiary.f32(i32 46, float %9, float %29, float %145)  ; FMad(a,b,c)
  %147 = call float @dx.op.tertiary.f32(i32 46, float %10, float %30, float %146)  ; FMad(a,b,c)
  %148 = call float @dx.op.dot3.f32(i32 55, float %141, float %144, float %147, float %141, float %144, float %147)  ; Dot3(ax,ay,az,bx,by,bz)
  %149 = call float @dx.op.unary.f32(i32 25, float %148)  ; Rsqrt(value)
  %150 = fmul fast float %149, %141
  %151 = fmul fast float %149, %144
  %152 = fmul fast float %149, %147
  %153 = fmul fast float %152, %137
  %154 = fmul fast float %151, %138
  %155 = fsub fast float %153, %154
  %156 = fmul fast float %150, %138
  %157 = fmul fast float %152, %136
  %158 = fsub fast float %156, %157
  %159 = fmul fast float %151, %136
  %160 = fmul fast float %150, %137
  %161 = fsub fast float %159, %160
  %162 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 21)  ; CBufferLoadLegacy(handle,regIndex)
  %163 = extractvalue %dx.types.CBufRet.f32 %162, 2
  %164 = extractvalue %dx.types.CBufRet.f32 %162, 3
  %165 = fmul fast float %163, %6
  %166 = fmul fast float %164, %7
  %167 = call float @dx.op.unary.f32(i32 23, float %2)  ; Log(value)
  %168 = call float @dx.op.unary.f32(i32 23, float %3)  ; Log(value)
  %169 = call float @dx.op.unary.f32(i32 23, float %4)  ; Log(value)
  %170 = call float @dx.op.unary.f32(i32 21, float %167)  ; Exp(value)
  %171 = call float @dx.op.unary.f32(i32 21, float %168)  ; Exp(value)
  %172 = call float @dx.op.unary.f32(i32 21, float %169)  ; Exp(value)
  %173 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 20)  ; CBufferLoadLegacy(handle,regIndex)
  %174 = extractvalue %dx.types.CBufRet.f32 %173, 0
  %175 = extractvalue %dx.types.CBufRet.f32 %173, 1
  %176 = extractvalue %dx.types.CBufRet.f32 %173, 2
  %177 = fsub fast float %174, %40
  %178 = fsub fast float %175, %44
  %179 = fsub fast float %176, %48
  %180 = call float @dx.op.dot3.f32(i32 55, float %177, float %178, float %179, float %177, float %178, float %179)  ; Dot3(ax,ay,az,bx,by,bz)
  %181 = call float @dx.op.unary.f32(i32 25, float %180)  ; Rsqrt(value)
  %182 = fmul fast float %177, %181
  %183 = fmul fast float %178, %181
  %184 = fmul fast float %179, %181
  %185 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 16)  ; CBufferLoadLegacy(handle,regIndex)
  %186 = extractvalue %dx.types.CBufRet.f32 %185, 0
  %187 = extractvalue %dx.types.CBufRet.f32 %185, 1
  %188 = extractvalue %dx.types.CBufRet.f32 %185, 2
  %189 = fsub fast float %186, %40
  %190 = fsub fast float %187, %44
  %191 = fsub fast float %188, %48
  %192 = call float @dx.op.dot3.f32(i32 55, float %189, float %190, float %191, float %189, float %190, float %191)  ; Dot3(ax,ay,az,bx,by,bz)
  %193 = call float @dx.op.unary.f32(i32 25, float %192)  ; Rsqrt(value)
  %194 = fmul fast float %189, %193
  %195 = fmul fast float %190, %193
  %196 = fmul fast float %191, %193
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %112)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %116)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %120)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %124)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %40)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %44)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %48)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %136)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %137)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %138)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %150)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %151)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %152)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %155)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %158)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 2, float %161)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %165)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %166)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %170)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 1, float %171)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 2, float %172)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 3, float %5)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %182)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 1, float %183)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 2, float %184)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 0, float %194)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 1, float %195)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 2, float %196)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %hostlayout.MaterialConstants* undef, !"", i32 0, i32 0, i32 1, i32 352, null}
!7 = !{[42 x i32] [i32 20, i32 35, i32 1879048319, i32 7, i32 1879048319, i32 7, i32 1879048319, i32 7, i32 0, i32 0, i32 460544, i32 0, i32 460544, i32 0, i32 460544, i32 0, i32 0, i32 0, i32 487424, i32 0, i32 487424, i32 0, i32 487424, i32 0, i32 0, i32 0, i32 1048576, i32 0, i32 2097152, i32 0, i32 0, i32 0, i32 0, i32 0, i32 16777216, i32 0, i32 33554432, i32 0, i32 67108864, i32 0, i32 134217728, i32 0]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !20, null}
!10 = !{!11, !14, !15, !16, !18}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"NORMAL", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 1, i8 0, !13}
!15 = !{i32 2, !"TANGENT", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 2, i8 0, !13}
!16 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 3, i8 0, !17}
!17 = !{i32 3, i32 3}
!18 = !{i32 4, !"COLOR", i8 9, i8 0, !12, i8 0, i32 1, i8 4, i32 4, i8 0, !19}
!19 = !{i32 3, i32 15}
!20 = !{!21, !22, !23, !25, !27, !29, !31, !33, !35}
!21 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !19}
!22 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 1, i8 0, !13}
!23 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 3, i32 2, i8 0, !13}
!24 = !{i32 1}
!25 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 3, i8 0, !13}
!26 = !{i32 2}
!27 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 3, i32 4, i8 0, !13}
!28 = !{i32 3}
!29 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 2, i32 5, i8 0, !17}
!30 = !{i32 4}
!31 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !32, i8 2, i32 1, i8 4, i32 6, i8 0, !19}
!32 = !{i32 5}
!33 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !34, i8 2, i32 1, i8 3, i32 7, i8 0, !13}
!34 = !{i32 6}
!35 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !36, i8 2, i32 1, i8 3, i32 8, i8 0, !13}
!36 = !{i32 7}
