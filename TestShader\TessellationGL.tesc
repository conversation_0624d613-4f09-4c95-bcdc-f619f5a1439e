#version 320 es

// Tessellation Control Shader - OpenGL ES Version
// Tests tessellation level control and patch processing

layout(vertices = 3) out;

in vec3 vWorldPos[];
in vec3 vNormal[];
in vec2 vTexCoord[];

out vec3 tcWorldPos[];
out vec3 tcNormal[];
out vec2 tcTexCoord[];

uniform vec3 uCameraPosition;
uniform float uTessellationFactor;
uniform float uMaxTessLevel;
uniform float uMinTessLevel;

float calculateTessLevel(vec3 worldPos0, vec3 worldPos1)
{
    // Calculate edge midpoint
    vec3 midpoint = (worldPos0 + worldPos1) * 0.5;
    
    // Calculate distance from camera
    float distance = length(uCameraPosition - midpoint);
    
    // Calculate tessellation level based on distance
    float tessLevel = uTessellationFactor / (1.0 + distance * 0.1);
    
    // Clamp to valid range
    return clamp(tessLevel, uMinTessLevel, uMaxTessLevel);
}

void main()
{
    // Pass through vertex data
    tcWorldPos[gl_InvocationID] = vWorldPos[gl_InvocationID];
    tcNormal[gl_InvocationID] = vNormal[gl_InvocationID];
    tcTexCoord[gl_InvocationID] = vTexCoord[gl_InvocationID];
    gl_out[gl_InvocationID].gl_Position = gl_in[gl_InvocationID].gl_Position;
    
    // Calculate tessellation levels (only for first invocation)
    if (gl_InvocationID == 0)
    {
        // Calculate edge tessellation levels
        gl_TessLevelOuter[0] = calculateTessLevel(vWorldPos[1], vWorldPos[2]);
        gl_TessLevelOuter[1] = calculateTessLevel(vWorldPos[2], vWorldPos[0]);
        gl_TessLevelOuter[2] = calculateTessLevel(vWorldPos[0], vWorldPos[1]);
        
        // Calculate inner tessellation level
        gl_TessLevelInner[0] = (gl_TessLevelOuter[0] + gl_TessLevelOuter[1] + gl_TessLevelOuter[2]) / 3.0;
    }
}
