; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 224
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "Albedo"
               OpMemberName %type_Material 1 "Metallic"
               OpMemberName %type_Material 2 "Roughness"
               OpMemberName %type_Material 3 "AO"
               OpMemberName %type_Material 4 "EmissiveColor"
               OpMemberName %type_Material 5 "EmissiveStrength"
               OpName %Material "Material"
               OpName %type_Lighting "type.Lighting"
               OpMemberName %type_Lighting 0 "LightPositions"
               OpMemberName %type_Lighting 1 "LightColors"
               OpMemberName %type_Lighting 2 "LightIntensities"
               OpMemberName %type_Lighting 3 "NumLights"
               OpName %Lighting "Lighting"
               OpName %type_2d_image "type.2d.image"
               OpName %AlbedoTexture "AlbedoTexture"
               OpName %NormalTexture "NormalTexture"
               OpName %MetallicTexture "MetallicTexture"
               OpName %RoughnessTexture "RoughnessTexture"
               OpName %AOTexture "AOTexture"
               OpName %EmissiveTexture "EmissiveTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %EnvironmentMap "EnvironmentMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %Lighting DescriptorSet 0
               OpDecorate %Lighting Binding 1
               OpDecorate %AlbedoTexture DescriptorSet 0
               OpDecorate %AlbedoTexture Binding 0
               OpDecorate %NormalTexture DescriptorSet 0
               OpDecorate %NormalTexture Binding 1
               OpDecorate %MetallicTexture DescriptorSet 0
               OpDecorate %MetallicTexture Binding 2
               OpDecorate %RoughnessTexture DescriptorSet 0
               OpDecorate %RoughnessTexture Binding 3
               OpDecorate %AOTexture DescriptorSet 0
               OpDecorate %AOTexture Binding 4
               OpDecorate %EmissiveTexture DescriptorSet 0
               OpDecorate %EmissiveTexture Binding 5
               OpDecorate %EnvironmentMap DescriptorSet 0
               OpDecorate %EnvironmentMap Binding 6
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 12
               OpMemberDecorate %type_Material 2 Offset 16
               OpMemberDecorate %type_Material 3 Offset 20
               OpMemberDecorate %type_Material 4 Offset 32
               OpMemberDecorate %type_Material 5 Offset 44
               OpDecorate %type_Material Block
               OpDecorate %_arr_v3float_uint_4 ArrayStride 16
               OpDecorate %_arr_float_uint_4 ArrayStride 16
               OpMemberDecorate %type_Lighting 0 Offset 0
               OpMemberDecorate %type_Lighting 1 Offset 64
               OpMemberDecorate %type_Lighting 2 Offset 128
               OpMemberDecorate %type_Lighting 3 Offset 192
               OpDecorate %type_Lighting Block
      %float = OpTypeFloat 32
%float_3_14159274 = OpConstant %float 3.14159274
        %int = OpTypeInt 32 1
      %int_5 = OpConstant %int 5
      %int_0 = OpConstant %int 0
%float_2_20000005 = OpConstant %float 2.20000005
    %v3float = OpTypeVector %float 3
         %36 = OpConstantComposite %v3float %float_2_20000005 %float_2_20000005 %float_2_20000005
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
%float_0_0399999991 = OpConstant %float 0.0399999991
         %42 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_0 = OpConstant %float 0
         %44 = OpConstantComposite %v3float %float_0 %float_0 %float_0
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
    %float_1 = OpConstant %float 1
         %48 = OpConstantComposite %v3float %float_1 %float_1 %float_1
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_0_454545468 = OpConstant %float 0.454545468
         %52 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_2 = OpConstant %float 2
    %float_5 = OpConstant %float 5
%type_Material = OpTypeStruct %v3float %float %float %float %v3float %float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
       %uint = OpTypeInt 32 0
     %uint_4 = OpConstant %uint 4
%_arr_v3float_uint_4 = OpTypeArray %v3float %uint_4
%_arr_float_uint_4 = OpTypeArray %float %uint_4
%type_Lighting = OpTypeStruct %_arr_v3float_uint_4 %_arr_v3float_uint_4 %_arr_float_uint_4 %int
%_ptr_Uniform_type_Lighting = OpTypePointer Uniform %type_Lighting
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %68 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%type_sampled_image_0 = OpTypeSampledImage %type_cube_image
%mat3v3float = OpTypeMatrix %v3float 3
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
   %Lighting = OpVariable %_ptr_Uniform_type_Lighting Uniform
%AlbedoTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%MetallicTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%RoughnessTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %AOTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EmissiveTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentMap = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_0_318309873 = OpConstant %float 0.318309873
         %74 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
       %main = OpFunction %void None %68
         %75 = OpLabel
         %76 = OpLoad %v3float %in_var_TEXCOORD0
         %77 = OpLoad %v3float %in_var_TEXCOORD1
         %78 = OpLoad %v3float %in_var_TEXCOORD2
         %79 = OpLoad %v3float %in_var_TEXCOORD3
         %80 = OpLoad %v2float %in_var_TEXCOORD4
         %81 = OpLoad %v3float %in_var_TEXCOORD6
         %82 = OpLoad %type_2d_image %AlbedoTexture
         %83 = OpLoad %type_sampler %LinearSampler
         %84 = OpSampledImage %type_sampled_image %82 %83
         %85 = OpImageSampleImplicitLod %v4float %84 %80 None
         %86 = OpVectorShuffle %v3float %85 %85 0 1 2
         %87 = OpAccessChain %_ptr_Uniform_v3float %Material %int_0
         %88 = OpLoad %v3float %87
         %89 = OpFMul %v3float %86 %88
         %90 = OpExtInst %v3float %1 Pow %89 %36
         %91 = OpLoad %type_2d_image %MetallicTexture
         %92 = OpLoad %type_sampler %LinearSampler
         %93 = OpSampledImage %type_sampled_image %91 %92
         %94 = OpImageSampleImplicitLod %v4float %93 %80 None
         %95 = OpCompositeExtract %float %94 0
         %96 = OpAccessChain %_ptr_Uniform_float %Material %int_1
         %97 = OpLoad %float %96
         %98 = OpFMul %float %95 %97
         %99 = OpLoad %type_2d_image %RoughnessTexture
        %100 = OpLoad %type_sampler %LinearSampler
        %101 = OpSampledImage %type_sampled_image %99 %100
        %102 = OpImageSampleImplicitLod %v4float %101 %80 None
        %103 = OpCompositeExtract %float %102 0
        %104 = OpAccessChain %_ptr_Uniform_float %Material %int_2
        %105 = OpLoad %float %104
        %106 = OpFMul %float %103 %105
        %107 = OpLoad %type_2d_image %AOTexture
        %108 = OpLoad %type_sampler %LinearSampler
        %109 = OpSampledImage %type_sampled_image %107 %108
        %110 = OpImageSampleImplicitLod %v4float %109 %80 None
        %111 = OpCompositeExtract %float %110 0
        %112 = OpAccessChain %_ptr_Uniform_float %Material %int_3
        %113 = OpLoad %float %112
        %114 = OpFMul %float %111 %113
        %115 = OpLoad %type_2d_image %EmissiveTexture
        %116 = OpLoad %type_sampler %LinearSampler
        %117 = OpSampledImage %type_sampled_image %115 %116
        %118 = OpImageSampleImplicitLod %v4float %117 %80 None
        %119 = OpVectorShuffle %v3float %118 %118 0 1 2
        %120 = OpAccessChain %_ptr_Uniform_v3float %Material %int_4
        %121 = OpLoad %v3float %120
        %122 = OpFMul %v3float %119 %121
        %123 = OpAccessChain %_ptr_Uniform_float %Material %int_5
        %124 = OpLoad %float %123
        %125 = OpVectorTimesScalar %v3float %122 %124
        %126 = OpLoad %type_2d_image %NormalTexture
        %127 = OpLoad %type_sampler %LinearSampler
        %128 = OpSampledImage %type_sampled_image %126 %127
        %129 = OpImageSampleImplicitLod %v4float %128 %80 None
        %130 = OpVectorShuffle %v3float %129 %129 0 1 2
        %131 = OpVectorTimesScalar %v3float %130 %float_2
        %132 = OpFSub %v3float %131 %48
        %133 = OpCompositeConstruct %mat3v3float %78 %79 %77
        %134 = OpMatrixTimesVector %v3float %133 %132
        %135 = OpExtInst %v3float %1 Normalize %134
        %136 = OpExtInst %v3float %1 Normalize %81
        %137 = OpCompositeConstruct %v3float %98 %98 %98
        %138 = OpExtInst %v3float %1 FMix %42 %90 %137
               OpBranch %139
        %139 = OpLabel
        %140 = OpPhi %v3float %44 %75 %141 %142
        %143 = OpPhi %int %int_0 %75 %144 %142
               OpLoopMerge %145 %142 None
               OpBranch %146
        %146 = OpLabel
        %147 = OpAccessChain %_ptr_Uniform_int %Lighting %int_3
        %148 = OpLoad %int %147
        %149 = OpSLessThan %bool %143 %148
               OpSelectionMerge %150 None
               OpBranchConditional %149 %151 %150
        %151 = OpLabel
        %152 = OpSLessThan %bool %143 %int_4
               OpBranch %150
        %150 = OpLabel
        %153 = OpPhi %bool %false %146 %152 %151
               OpBranchConditional %153 %142 %145
        %142 = OpLabel
        %154 = OpAccessChain %_ptr_Uniform_v3float %Lighting %int_0 %143
        %155 = OpLoad %v3float %154
        %156 = OpFSub %v3float %155 %76
        %157 = OpExtInst %v3float %1 Normalize %156
        %158 = OpFAdd %v3float %136 %157
        %159 = OpExtInst %v3float %1 Normalize %158
        %160 = OpExtInst %float %1 Length %156
        %161 = OpFMul %float %160 %160
        %162 = OpFDiv %float %float_1 %161
        %163 = OpAccessChain %_ptr_Uniform_v3float %Lighting %int_1 %143
        %164 = OpLoad %v3float %163
        %165 = OpAccessChain %_ptr_Uniform_float %Lighting %int_2 %143
        %166 = OpLoad %float %165
        %167 = OpVectorTimesScalar %v3float %164 %166
        %168 = OpVectorTimesScalar %v3float %167 %162
        %169 = OpFMul %float %106 %106
        %170 = OpFMul %float %169 %169
        %171 = OpDot %float %135 %159
        %172 = OpExtInst %float %1 NMax %171 %float_0
        %173 = OpFMul %float %172 %172
        %174 = OpFMul %float %173 %170
        %175 = OpFMul %float %float_3_14159274 %174
        %176 = OpFMul %float %175 %174
        %177 = OpFDiv %float %170 %176
        %178 = OpDot %float %135 %136
        %179 = OpExtInst %float %1 NMax %178 %float_0
        %180 = OpDot %float %135 %157
        %181 = OpExtInst %float %1 NMax %180 %float_0
        %182 = OpFDiv %float %179 %179
        %183 = OpFDiv %float %181 %181
        %184 = OpFMul %float %183 %182
        %185 = OpDot %float %159 %136
        %186 = OpExtInst %float %1 NMax %185 %float_0
        %187 = OpFSub %float %float_1 %186
        %188 = OpExtInst %float %1 FClamp %187 %float_0 %float_1
        %189 = OpExtInst %float %1 Pow %188 %float_5
        %190 = OpVectorTimesScalar %v3float %138 %189
        %191 = OpFSub %v3float %48 %190
        %192 = OpFAdd %v3float %138 %191
        %193 = OpFSub %v3float %48 %192
        %194 = OpFSub %float %float_1 %98
        %195 = OpVectorTimesScalar %v3float %193 %194
        %196 = OpFMul %float %177 %184
        %197 = OpVectorTimesScalar %v3float %192 %196
        %198 = OpFMul %float %float_4 %179
        %199 = OpFMul %float %198 %181
        %200 = OpFAdd %float %199 %float_9_99999975en05
        %201 = OpCompositeConstruct %v3float %200 %200 %200
        %202 = OpFDiv %v3float %197 %201
        %203 = OpFMul %v3float %195 %90
        %204 = OpFMul %v3float %203 %74
        %205 = OpFMul %v3float %202 %168
        %206 = OpFAdd %v3float %204 %205
        %207 = OpVectorTimesScalar %v3float %206 %181
        %141 = OpFAdd %v3float %140 %207
        %144 = OpIAdd %int %143 %int_1
               OpBranch %139
        %145 = OpLabel
        %208 = OpLoad %type_cube_image %EnvironmentMap
        %209 = OpLoad %type_sampler %LinearSampler
        %210 = OpSampledImage %type_sampled_image_0 %208 %209
        %211 = OpImageSampleImplicitLod %v4float %210 %135 None
        %212 = OpVectorShuffle %v3float %211 %211 0 1 2
        %213 = OpFMul %v3float %212 %90
        %214 = OpVectorTimesScalar %v3float %213 %114
        %215 = OpFAdd %v3float %214 %140
        %216 = OpFAdd %v3float %215 %125
        %217 = OpFDiv %v3float %216 %216
        %218 = OpFAdd %v3float %217 %48
        %219 = OpExtInst %v3float %1 Pow %218 %52
        %220 = OpCompositeExtract %float %219 0
        %221 = OpCompositeExtract %float %219 1
        %222 = OpCompositeExtract %float %219 2
        %223 = OpCompositeConstruct %v4float %220 %221 %222 %float_1
               OpStore %out_var_SV_TARGET %223
               OpReturn
               OpFunctionEnd
