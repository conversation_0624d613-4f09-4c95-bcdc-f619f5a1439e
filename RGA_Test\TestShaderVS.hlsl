// Vertex Shader - Ultra-comprehensive shader optimization test
// This shader contains numerous optimization opportunities across all categories

// Global constants and variables
static const float PI = 3.14159265359;
static const float TWO_PI = 2.0 * PI;
static const float HALF_PI = PI / 2.0;

// Constant buffer
cbuffer MaterialConstants : register(b0)
{
    float4x4 WorldMatrix;
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 WorldViewProjectionMatrix;
    float4 LightPosition;
    float4 LightColor;
    float4 MaterialDiffuse;
    float4 MaterialSpecular;
    float4 CameraPosition;
    float Time;
    float SpecularPower;
    float2 TextureScale;
};

// Vertex shader input
struct VSInput
{
    float3 position : POSITION;
    float3 normal : NORMAL;
    float3 tangent : TANGENT;
    float2 texcoord : TEXCOORD0;
    float4 color : COLOR0;
};

// Pixel shader input
struct PSInput
{
    float4 position : SV_POSITION;
    float3 worldPos : TEXCOORD0;
    float3 normal : TEXCOORD1;
    float3 tangent : TEXCOORD2;
    float3 binormal : TEXCOORD3;
    float2 texcoord : TEXCOORD4;
    float4 color : TEXCOORD5;
    float3 viewDir : TEXCOORD6;
    float3 lightDir : TEXCOORD7;
};

// Complex vertex shader with many optimization opportunities
PSInput main(VSInput input)
{
    PSInput output;

    // Matrix operations with redundant calculations
    float4 worldPos = mul(float4(input.position, 1.0), WorldMatrix);
    float4 viewPos = mul(worldPos, ViewMatrix);
    output.position = mul(viewPos, ProjectionMatrix);

    // Alternative: could be optimized to single matrix multiply
    // output.position = mul(float4(input.position, 1.0), WorldViewProjectionMatrix);

    output.worldPos = worldPos.xyz;

    // Normal transformation with redundant operations
    float3 worldNormal = mul(input.normal, (float3x3)WorldMatrix);
    output.normal = normalize(worldNormal + 0.0); // +0.0 should be optimized away

    // Tangent space calculations
    float3 worldTangent = mul(input.tangent, (float3x3)WorldMatrix);
    output.tangent = normalize(worldTangent * 1.0); // *1.0 should be optimized away
    output.binormal = cross(output.normal, output.tangent);

    // Texture coordinate transformations with redundant math
    output.texcoord = input.texcoord * TextureScale + 0.0; // +0.0 optimized away
    output.texcoord = output.texcoord - 0.0; // -0.0 optimized away

    // Color processing with mathematical optimizations
    output.color = input.color;
    output.color.rgb = pow(output.color.rgb, 1.0); // pow(x, 1.0) = x
    output.color.a = output.color.a * 1.0 + 0.0; // redundant operations

    // View and light direction calculations
    output.viewDir = normalize(CameraPosition.xyz - output.worldPos);
    output.lightDir = normalize(LightPosition.xyz - output.worldPos);

    return output;
}
