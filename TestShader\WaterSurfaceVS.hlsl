// Water Surface Vertex Shader
// Tests vertex displacement and wave simulation

cbuffer WaterParams : register(b0)
{
    float4x4 ViewProjectionMatrix;
    float4x4 WorldMatrix;
    float3 CameraPosition;
    float Time;
    float WaveAmplitude;
    float WaveFrequency;
    float WaveSpeed;
    float2 WaveDirection1;
    float2 WaveDirection2;
    float2 WindDirection;
    float WindStrength;
};

struct VSInput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float3 ViewDir : TEXCOORD3;
    float4 ReflectionPos : TEXCOORD4;
    float4 RefractionPos : TEXCOORD5;
    float2 WaveOffset : TEXCOORD6;
};

// Gerstner wave function
float3 GerstnerWave(float2 position, float2 direction, float amplitude, float frequency, float speed, float time, out float3 tangent, out float3 binormal)
{
    float2 d = normalize(direction);
    float k = 2.0 * 3.14159 * frequency;
    float c = sqrt(9.8 / k);
    float2 dir = d * k;
    float f = k * (dot(d, position) - c * time);
    float a = amplitude / k;
    
    tangent = float3(
        -d.x * d.x * (amplitude * sin(f)),
        d.x * (amplitude * cos(f)),
        -d.x * d.y * (amplitude * sin(f))
    );
    
    binormal = float3(
        -d.x * d.y * (amplitude * sin(f)),
        d.y * (amplitude * cos(f)),
        -d.y * d.y * (amplitude * sin(f))
    );
    
    return float3(
        d.x * (a * sin(f)),
        a * cos(f),
        d.y * (a * sin(f))
    );
}

VSOutput main(VSInput input)
{
    VSOutput output;
    
    float3 worldPos = mul(float4(input.Position, 1.0), WorldMatrix).xyz;
    
    // Calculate multiple wave contributions
    float3 tangent1, binormal1, tangent2, binormal2;
    float3 wave1 = GerstnerWave(worldPos.xz, WaveDirection1, WaveAmplitude, WaveFrequency, WaveSpeed, Time, tangent1, binormal1);
    float3 wave2 = GerstnerWave(worldPos.xz, WaveDirection2, WaveAmplitude * 0.5, WaveFrequency * 1.5, WaveSpeed * 0.8, Time, tangent2, binormal2);
    
    // Add wind effect
    float windEffect = sin(Time * 2.0 + worldPos.x * 0.1 + worldPos.z * 0.1) * WindStrength;
    float2 windOffset = WindDirection * windEffect * 0.1;
    
    // Combine waves
    worldPos += wave1 + wave2;
    worldPos.xz += windOffset;
    
    output.WorldPos = worldPos;
    output.Position = mul(float4(worldPos, 1.0), ViewProjectionMatrix);
    
    // Calculate normal from tangent and binormal
    float3 tangent = normalize(tangent1 + tangent2 + float3(1, 0, 0));
    float3 binormal = normalize(binormal1 + binormal2 + float3(0, 0, 1));
    output.Normal = normalize(cross(tangent, binormal));
    
    // Texture coordinates with wave animation
    output.TexCoord = input.TexCoord;
    output.WaveOffset = float2(Time * 0.1, Time * 0.05) + windOffset * 0.5;
    
    // View direction
    output.ViewDir = normalize(CameraPosition - worldPos);
    
    // Reflection and refraction positions (for water rendering)
    output.ReflectionPos = output.Position;
    output.RefractionPos = output.Position;
    
    return output;
}
