// Volume Texture Generation Compute Shader
// Tests 3D texture operations and volumetric rendering

// Compute constants
cbuffer ComputeParams : register(b0)
{
    uint ParticleCount;
    uint MaxParticles;
    float DeltaTime;
    float Time;
    float3 Gravity;
    float Damping;
    float3 EmitterPosition;
    float EmissionRate;
    float3 EmitterDirection;
    float EmissionSpeed;
    float2 LifetimeRange;
    float2 SizeRange;
    uint FrameCount;
    float NoiseScale;
    float NoiseStrength;
    uint _padding;
};

// 3D texture
RWTexture3D<float4> VolumeTexture : register(u0);

// Noise function
float Hash(float n)
{
    return frac(sin(n) * 43758.5453);
}

float Noise(float3 x)
{
    float3 p = floor(x);
    float3 f = frac(x);
    f = f * f * (3.0 - 2.0 * f);
    
    float n = p.x + p.y * 57.0 + 113.0 * p.z;
    return lerp(lerp(lerp(Hash(n + 0.0), Hash(n + 1.0), f.x),
                     lerp(Hash(n + 57.0), Hash(n + 58.0), f.x), f.y),
                lerp(lerp(Hash(n + 113.0), Hash(n + 114.0), f.x),
                     lerp(Hash(n + 170.0), Hash(n + 171.0), f.x), f.y), f.z);
}

[numthreads(4, 4, 4)]
void main(uint3 id : SV_DispatchThreadID)
{
    uint3 dimensions;
    VolumeTexture.GetDimensions(dimensions.x, dimensions.y, dimensions.z);
    
    if (any(id >= dimensions))
        return;
    
    float3 uvw = float3(id) / float3(dimensions - 1);
    float3 worldPos = (uvw - 0.5) * 100.0;
    
    // Generate 3D noise pattern
    float density = 0.0;
    density += Noise(worldPos * 0.01 + Time * 0.1) * 0.5;
    density += Noise(worldPos * 0.02 + Time * 0.05) * 0.3;
    density += Noise(worldPos * 0.04 + Time * 0.02) * 0.2;
    
    // Create cloud-like density
    density = saturate(density - 0.3) * 2.0;
    
    float4 color = float4(1.0, 1.0, 1.0, density);
    VolumeTexture[id] = color;
}
