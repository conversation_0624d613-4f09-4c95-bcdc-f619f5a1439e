// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float4 Color : TEXCOORD3;
  float4 LightSpacePos : TEXCOORD4;
  float3 LightDir : TEXCOORD5;
  float3 ViewDir : TEXCOORD6;
  float Depth : TEXCOORD7;
};

cbuffer Material : register(b0)
{
  float4 DiffuseColor;
  float4 SpecularColor;
  float SpecularPower;
  float3 AmbientColor;
  float ShadowBias;
  float ShadowStrength;
  int PCFSamples;
}

Texture2D DiffuseTexture : register(t0);
Texture2D ShadowMap : register(t1);
SamplerState LinearSampler : register(s0);
SamplerComparisonState ShadowSampler : register(s1);
float calculateShadow(float4 lightSpacePos, float bias)
{
  float3 projCoords = lightSpacePos.xyz / lightSpacePos.w;
  projCoords.xy = (projCoords.xy * 0.5f) + 0.5f;
  projCoords.y = 1.0f - projCoords.y;
  if (projCoords.x < 0.0f || projCoords.x > 1.0f || projCoords.y < 0.0f || projCoords.y > 1.0f)
    return 0.0f;
  float currentDepth = projCoords.z;
  float shadow = 0.0f;
  float2 texelSize = 1.0f / float2(2048.0f, 2048.0f);
  int halfSamples = PCFSamples / 2;
  for (int x = (-halfSamples); x <= halfSamples; (++x))
  {
    for (int y = (-halfSamples); y <= halfSamples; (++y))
    {
      float2 offset = (float2(x, y) * texelSize);
      shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, projCoords.xy + offset, currentDepth - bias);
    }
  }
  shadow /= float((PCFSamples + 1 * PCFSamples + 1));
  return shadow;
}

float4 main(PSInput input) : SV_TARGET
{
  float3 normal = normalize(input.Normal);
  float3 lightDir = normalize(input.LightDir);
  float3 viewDir = normalize(input.ViewDir);
  float4 texColor = DiffuseTexture.Sample(LinearSampler, input.TexCoord);
  float shadow = calculateShadow(input.LightSpacePos, ShadowBias);
  shadow = lerp(ShadowStrength, 1.0f, shadow);
  float3 ambient = (AmbientColor * texColor.rgb);
  float NdotL = max(0.0f, dot(normal, lightDir));
  float3 diffuse = (((DiffuseColor.rgb * texColor.rgb) * NdotL) * shadow);
  float3 halfDir = normalize(lightDir + viewDir);
  float NdotH = max(0.0f, dot(normal, halfDir));
  float3 specular = ((SpecularColor.rgb * pow(NdotH, SpecularPower)) * shadow);
  float3 finalColor = ambient + diffuse + specular;
  return float4(finalColor, (texColor.a * DiffuseColor.a));
}

