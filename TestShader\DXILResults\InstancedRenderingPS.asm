;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 5      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 6      w        2     NONE   float      w
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 3   xyzw        4     NONE   float   xyzw
; TEXCOORD                 4   xyz         5     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 69d32ab3b53d0081a2ced641d137c254
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 8
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 6
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer Material
; {
;
;   struct Material
;   {
;
;       float4 BaseColor;                             ; Offset:    0
;       float Metallic;                               ; Offset:   16
;       float Roughness;                              ; Offset:   20
;       float3 LightDirection;                        ; Offset:   32
;       float3 LightColor;                            ; Offset:   48
;       float3 AmbientColor;                          ; Offset:   64
;       float Time;                                   ; Offset:   76
;   
;   } Material;                                       ; Offset:    0 Size:    80
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; Material                          cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; DiffuseTextureArray               texture     f32     2darray      T0             t0     1
; NoiseTexture                      texture     f32          2d      T1             t2     1
;
;
; ViewId state:
;
; Number of inputs: 23, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 20, 21, 22 }
;   output 1 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 17, 20, 21, 22 }
;   output 2 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 18, 20, 21, 22 }
;   output 3 depends on inputs: { 7, 12, 13, 19 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%"class.Texture2DArray<vector<float, 4> >" = type { <4 x float>, %"class.Texture2DArray<vector<float, 4> >::mips_type" }
%"class.Texture2DArray<vector<float, 4> >::mips_type" = type { i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%Material = type { <4 x float>, float, float, <3 x float>, <3 x float>, <3 x float>, float }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.dot3.f32(i32 55, float %16, float %17, float %18, float %16, float %17, float %18)  ; Dot3(ax,ay,az,bx,by,bz)
  %23 = call float @dx.op.unary.f32(i32 25, float %22)  ; Rsqrt(value)
  %24 = fmul fast float %23, %16
  %25 = fmul fast float %23, %17
  %26 = fmul fast float %23, %18
  %27 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %28 = extractvalue %dx.types.CBufRet.f32 %27, 0
  %29 = extractvalue %dx.types.CBufRet.f32 %27, 1
  %30 = extractvalue %dx.types.CBufRet.f32 %27, 2
  %31 = fsub fast float -0.000000e+00, %28
  %32 = fsub fast float -0.000000e+00, %29
  %33 = fsub fast float -0.000000e+00, %30
  %34 = call float @dx.op.dot3.f32(i32 55, float %31, float %32, float %33, float %31, float %32, float %33)  ; Dot3(ax,ay,az,bx,by,bz)
  %35 = call float @dx.op.unary.f32(i32 25, float %34)  ; Rsqrt(value)
  %36 = fmul fast float %35, %31
  %37 = fmul fast float %35, %32
  %38 = fmul fast float %35, %33
  %39 = call float @dx.op.dot3.f32(i32 55, float %7, float %8, float %9, float %7, float %8, float %9)  ; Dot3(ax,ay,az,bx,by,bz)
  %40 = call float @dx.op.unary.f32(i32 25, float %39)  ; Rsqrt(value)
  %41 = fmul fast float %40, %7
  %42 = fmul fast float %40, %8
  %43 = fmul fast float %40, %9
  %44 = fptosi float %6 to i32
  %45 = sitofp i32 %44 to float
  %46 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %3, float %14, float %15, float %45, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %47 = extractvalue %dx.types.ResRet.f32 %46, 0
  %48 = extractvalue %dx.types.ResRet.f32 %46, 1
  %49 = extractvalue %dx.types.ResRet.f32 %46, 2
  %50 = extractvalue %dx.types.ResRet.f32 %46, 3
  %51 = fmul fast float %47, %10
  %52 = fmul fast float %48, %11
  %53 = fmul fast float %49, %12
  %54 = fmul fast float %50, %13
  %55 = fcmp fast olt float %6, 1.000000e+00
  br i1 %55, label %59, label %56

; <label>:56                                      ; preds = %0
  %57 = fcmp fast olt float %6, 2.000000e+00
  br i1 %57, label %59, label %58

; <label>:58                                      ; preds = %56
  br label %59

; <label>:59                                      ; preds = %58, %56, %0
  %60 = phi float [ 0x3FE99999A0000000, %58 ], [ 0x3FB99999A0000000, %0 ], [ 0.000000e+00, %56 ]
  %61 = phi float [ 0x3FC99999A0000000, %58 ], [ 0x3FE99999A0000000, %0 ], [ 0x3FECCCCCC0000000, %56 ]
  %62 = fadd fast float %14, %6
  %63 = fadd fast float %15, %6
  %64 = fmul fast float %62, 0x3FB99999A0000000
  %65 = fmul fast float %63, 0x3FB99999A0000000
  %66 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %3, float %64, float %65, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %67 = extractvalue %dx.types.ResRet.f32 %66, 0
  %68 = fadd fast float %67, -5.000000e-01
  %69 = fmul fast float %68, 0x3FC99999A0000000
  %70 = call float @dx.op.dot3.f32(i32 55, float %24, float %25, float %26, float %36, float %37, float %38)  ; Dot3(ax,ay,az,bx,by,bz)
  %71 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %70)  ; FMax(a,b)
  %72 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %73 = extractvalue %dx.types.CBufRet.f32 %72, 0
  %74 = extractvalue %dx.types.CBufRet.f32 %72, 1
  %75 = extractvalue %dx.types.CBufRet.f32 %72, 2
  %76 = fmul fast float %73, %71
  %77 = fmul fast float %74, %71
  %78 = fmul fast float %75, %71
  %79 = fadd fast float %41, %36
  %80 = fadd fast float %42, %37
  %81 = fadd fast float %43, %38
  %82 = call float @dx.op.dot3.f32(i32 55, float %79, float %80, float %81, float %79, float %80, float %81)  ; Dot3(ax,ay,az,bx,by,bz)
  %83 = call float @dx.op.unary.f32(i32 25, float %82)  ; Rsqrt(value)
  %84 = fmul fast float %83, %79
  %85 = fmul fast float %83, %80
  %86 = fmul fast float %83, %81
  %87 = call float @dx.op.dot3.f32(i32 55, float %24, float %25, float %26, float %84, float %85, float %86)  ; Dot3(ax,ay,az,bx,by,bz)
  %88 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %87)  ; FMax(a,b)
  %89 = fsub fast float 1.000000e+00, %61
  %90 = fsub fast float %89, %69
  %91 = fmul fast float %90, 2.240000e+02
  %92 = fadd fast float %91, 3.200000e+01
  %93 = call float @dx.op.unary.f32(i32 23, float %88)  ; Log(value)
  %94 = fmul fast float %93, %92
  %95 = call float @dx.op.unary.f32(i32 21, float %94)  ; Exp(value)
  %96 = fmul fast float %95, %60
  %97 = fmul fast float %96, %73
  %98 = fmul fast float %96, %74
  %99 = fmul fast float %96, %75
  %100 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %101 = extractvalue %dx.types.CBufRet.f32 %100, 0
  %102 = extractvalue %dx.types.CBufRet.f32 %100, 1
  %103 = extractvalue %dx.types.CBufRet.f32 %100, 2
  %104 = fadd fast float %101, %76
  %105 = fmul fast float %104, %51
  %106 = fadd fast float %105, %97
  %107 = fadd fast float %102, %77
  %108 = fmul fast float %107, %52
  %109 = fadd fast float %108, %98
  %110 = fadd fast float %103, %78
  %111 = fmul fast float %110, %53
  %112 = fadd fast float %111, %99
  br i1 %55, label %113, label %148

; <label>:113                                     ; preds = %59
  %114 = fsub fast float -0.000000e+00, %36
  %115 = fsub fast float -0.000000e+00, %37
  %116 = fsub fast float -0.000000e+00, %38
  %117 = call float @dx.op.dot3.f32(i32 55, float %114, float %115, float %116, float %24, float %25, float %26)  ; Dot3(ax,ay,az,bx,by,bz)
  %118 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %117)  ; FMax(a,b)
  %119 = call float @dx.op.dot3.f32(i32 55, float %41, float %42, float %43, float %114, float %115, float %116)  ; Dot3(ax,ay,az,bx,by,bz)
  %120 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %119)  ; FMax(a,b)
  %121 = call float @dx.op.unary.f32(i32 23, float %120)  ; Log(value)
  %122 = fmul fast float %121, 4.000000e+00
  %123 = call float @dx.op.unary.f32(i32 21, float %122)  ; Exp(value)
  %124 = fmul fast float %118, 5.000000e-01
  %125 = fmul fast float %124, %123
  %126 = fmul fast float %125, %53
  %127 = fadd fast float %126, %112
  %128 = fmul fast float %125, %52
  %129 = fadd fast float %128, %109
  %130 = fmul fast float %125, %51
  %131 = fadd fast float %130, %106
  %132 = extractvalue %dx.types.CBufRet.f32 %100, 3
  %133 = fadd fast float %132, %19
  %134 = fadd fast float %132, %21
  %135 = fmul fast float %133, 0x3F847AE160000000
  %136 = fmul fast float %134, 0x3F847AE160000000
  %137 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %3, float %135, float %136, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %138 = extractvalue %dx.types.ResRet.f32 %137, 0
  %139 = fmul fast float %132, 2.000000e+00
  %140 = fmul fast float %19, 0x3FB99999A0000000
  %141 = fadd fast float %139, %140
  %142 = call float @dx.op.unary.f32(i32 13, float %141)  ; Sin(value)
  %143 = fmul fast float %142, %138
  %144 = fmul fast float %143, 0x3FB99999A0000000
  %145 = fadd fast float %129, %144
  %146 = fmul fast float %143, 0x3FA99999A0000000
  %147 = fsub fast float %127, %146
  br label %148

; <label>:148                                     ; preds = %113, %59
  %149 = phi float [ %106, %59 ], [ %131, %113 ]
  %150 = phi float [ %109, %59 ], [ %145, %113 ]
  %151 = phi float [ %112, %59 ], [ %147, %113 ]
  %152 = fcmp fast olt float %5, 1.000000e+00
  br i1 %152, label %153, label %168

; <label>:153                                     ; preds = %148
  %154 = fmul fast float %149, %5
  %155 = fmul fast float %150, %5
  %156 = fmul fast float %151, %5
  %157 = fadd fast float %154, %149
  %158 = fmul fast float %157, 5.000000e-01
  %159 = fadd fast float %155, %150
  %160 = fmul fast float %159, 5.000000e-01
  %161 = fadd fast float %156, %151
  %162 = fmul fast float %161, 5.000000e-01
  %163 = fcmp fast olt float %5, 0x3FD3333340000000
  br i1 %163, label %164, label %168

; <label>:164                                     ; preds = %153
  %165 = fsub fast float 0x3FD3333340000000, %5
  %166 = fmul fast float %165, 2.000000e+00
  %167 = fadd fast float %158, %166
  br label %168

; <label>:168                                     ; preds = %164, %153, %148
  %169 = phi float [ %167, %164 ], [ %158, %153 ], [ %149, %148 ]
  %170 = phi float [ %160, %164 ], [ %160, %153 ], [ %150, %148 ]
  %171 = phi float [ %162, %164 ], [ %162, %153 ], [ %151, %148 ]
  %172 = fmul fast float %19, %19
  %173 = fmul fast float %20, %20
  %174 = fadd fast float %173, %172
  %175 = fmul fast float %21, %21
  %176 = fadd fast float %174, %175
  %177 = call float @dx.op.unary.f32(i32 24, float %176)  ; Sqrt(value)
  %178 = fmul fast float %177, 0xBF8D8BE060000000
  %179 = call float @dx.op.unary.f32(i32 21, float %178)  ; Exp(value)
  %180 = fadd fast float %169, 0xBFE6666660000000
  %181 = fadd fast float %170, 0xBFE99999A0000000
  %182 = fadd fast float %171, 0xBFECCCCCC0000000
  %183 = fmul fast float %179, %180
  %184 = fmul fast float %179, %181
  %185 = fmul fast float %179, %182
  %186 = fadd fast float %183, 0x3FE6666660000000
  %187 = fadd fast float %184, 0x3FE99999A0000000
  %188 = fadd fast float %185, 0x3FECCCCCC0000000
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %186)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %187)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %188)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %54)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!13}
!dx.entryPoints = !{!14}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !9, !11}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 7, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!9 = !{!10}
!10 = !{i32 0, %Material* undef, !"", i32 0, i32 0, i32 1, i32 80, null}
!11 = !{!12}
!12 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!13 = !{[25 x i32] [i32 23, i32 4, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 15, i32 7, i32 7, i32 7, i32 7, i32 15, i32 15, i32 0, i32 0, i32 1, i32 2, i32 4, i32 8, i32 7, i32 7, i32 7]}
!14 = !{void ()* @main, !"main", !15, !4, null}
!15 = !{!16, !36, null}
!16 = !{!17, !19, !21, !23, !26, !29, !31, !34}
!17 = !{i32 0, !"SV_Position", i8 9, i8 3, !18, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!18 = !{i32 0}
!19 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !18, i8 2, i32 1, i8 3, i32 1, i8 0, !20}
!20 = !{i32 3, i32 7}
!21 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !22, i8 2, i32 1, i8 3, i32 2, i8 0, !20}
!22 = !{i32 1}
!23 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 2, i32 3, i8 0, !25}
!24 = !{i32 2}
!25 = !{i32 3, i32 3}
!26 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !27, i8 2, i32 1, i8 4, i32 4, i8 0, !28}
!27 = !{i32 3}
!28 = !{i32 3, i32 15}
!29 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 3, i32 5, i8 0, !20}
!30 = !{i32 4}
!31 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !32, i8 2, i32 1, i8 1, i32 1, i8 3, !33}
!32 = !{i32 5}
!33 = !{i32 3, i32 1}
!34 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 1, i32 2, i8 3, !33}
!35 = !{i32 6}
!36 = !{!37}
!37 = !{i32 0, !"SV_Target", i8 9, i8 16, !18, i8 0, i32 1, i8 4, i32 0, i8 0, !28}
