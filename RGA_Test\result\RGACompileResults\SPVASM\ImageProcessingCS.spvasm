; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 352
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ProcessingParams "type.ProcessingParams"
               OpMemberName %type_ProcessingParams 0 "ImageWidth"
               OpMemberName %type_ProcessingParams 1 "ImageHeight"
               OpMemberName %type_ProcessingParams 2 "BlurRadius"
               OpMemberName %type_ProcessingParams 3 "Brightness"
               OpMemberName %type_ProcessingParams 4 "Contrast"
               OpMemberName %type_ProcessingParams 5 "Saturation"
               OpMemberName %type_ProcessingParams 6 "Gamma"
               OpMemberName %type_ProcessingParams 7 "FilterType"
               OpName %ProcessingParams "ProcessingParams"
               OpName %type_2d_image "type.2d.image"
               OpName %InputTexture "InputTexture"
               OpName %type_2d_image_0 "type.2d.image"
               OpName %OutputTexture "OutputTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %GaussianKernel "GaussianKernel"
               OpName %SharpenKernel "SharpenKernel"
               OpName %EdgeKernelX "EdgeKernelX"
               OpName %EdgeKernelY "EdgeKernelY"
               OpName %EmbossKernel "EmbossKernel"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ProcessingParams DescriptorSet 0
               OpDecorate %ProcessingParams Binding 0
               OpDecorate %InputTexture DescriptorSet 0
               OpDecorate %InputTexture Binding 0
               OpDecorate %OutputTexture DescriptorSet 0
               OpDecorate %OutputTexture Binding 0
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_ProcessingParams 0 Offset 0
               OpMemberDecorate %type_ProcessingParams 1 Offset 4
               OpMemberDecorate %type_ProcessingParams 2 Offset 8
               OpMemberDecorate %type_ProcessingParams 3 Offset 12
               OpMemberDecorate %type_ProcessingParams 4 Offset 16
               OpMemberDecorate %type_ProcessingParams 5 Offset 20
               OpMemberDecorate %type_ProcessingParams 6 Offset 24
               OpMemberDecorate %type_ProcessingParams 7 Offset 28
               OpDecorate %type_ProcessingParams Block
      %float = OpTypeFloat 32
%float_0_00376500003 = OpConstant %float 0.00376500003
%float_0_0150189996 = OpConstant %float 0.0150189996
%float_0_0237920005 = OpConstant %float 0.0237920005
%float_0_0599119999 = OpConstant %float 0.0599119999
%float_0_0949070007 = OpConstant %float 0.0949070007
%float_0_150342003 = OpConstant %float 0.150342003
    %float_0 = OpConstant %float 0
   %float_n1 = OpConstant %float -1
    %float_5 = OpConstant %float 5
    %float_1 = OpConstant %float 1
   %float_n2 = OpConstant %float -2
    %float_2 = OpConstant %float 2
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
      %int_1 = OpConstant %int 1
      %int_7 = OpConstant %int 7
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
    %v4float = OpTypeVector %float 4
         %40 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
    %v2float = OpTypeVector %float 2
         %42 = OpConstantComposite %v2float %float_1 %float_1
     %int_n2 = OpConstant %int -2
      %int_2 = OpConstant %int 2
     %int_10 = OpConstant %int 10
     %uint_1 = OpConstant %uint 1
     %int_n1 = OpConstant %int -1
      %int_3 = OpConstant %int 3
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
  %float_0_5 = OpConstant %float 0.5
         %52 = OpConstantComposite %v4float %float_0_5 %float_0_5 %float_0_5 %float_0_5
      %int_4 = OpConstant %int 4
    %v3float = OpTypeVector %float 3
         %55 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
      %int_5 = OpConstant %int 5
      %int_6 = OpConstant %int 6
         %58 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
%float_n0_333333343 = OpConstant %float -0.333333343
%float_0_666666687 = OpConstant %float 0.666666687
%float_1_00000001en10 = OpConstant %float 1.00000001e-10
    %float_6 = OpConstant %float 6
%float_0_333333343 = OpConstant %float 0.333333343
    %float_3 = OpConstant %float 3
         %65 = OpConstantComposite %v3float %float_0 %float_0 %float_0
         %66 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%type_ProcessingParams = OpTypeStruct %uint %uint %float %float %float %float %float %uint
%_ptr_Uniform_type_ProcessingParams = OpTypePointer Uniform %type_ProcessingParams
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_2d_image_0 = OpTypeImage %float 2D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_2d_image_0 = OpTypePointer UniformConstant %type_2d_image_0
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %uint_25 = OpConstant %uint 25
%_arr_float_uint_25 = OpTypeArray %float %uint_25
     %uint_9 = OpConstant %uint 9
%_arr_float_uint_9 = OpTypeArray %float %uint_9
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %78 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
     %v2uint = OpTypeVector %uint 2
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Function_float = OpTypePointer Function %float
%ProcessingParams = OpVariable %_ptr_Uniform_type_ProcessingParams Uniform
%InputTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%OutputTexture = OpVariable %_ptr_UniformConstant_type_2d_image_0 UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%_ptr_Function__arr_float_uint_25 = OpTypePointer Function %_arr_float_uint_25
%_ptr_Function__arr_float_uint_9 = OpTypePointer Function %_arr_float_uint_9
         %85 = OpConstantComposite %_arr_float_uint_25 %float_0_00376500003 %float_0_0150189996 %float_0_0237920005 %float_0_0150189996 %float_0_00376500003 %float_0_0150189996 %float_0_0599119999 %float_0_0949070007 %float_0_0599119999 %float_0_0150189996 %float_0_0237920005 %float_0_0949070007 %float_0_150342003 %float_0_0949070007 %float_0_0237920005 %float_0_0150189996 %float_0_0599119999 %float_0_0949070007 %float_0_0599119999 %float_0_0150189996 %float_0_00376500003 %float_0_0150189996 %float_0_0237920005 %float_0_0150189996 %float_0_00376500003
         %86 = OpConstantComposite %_arr_float_uint_9 %float_0 %float_n1 %float_0 %float_n1 %float_5 %float_n1 %float_0 %float_n1 %float_0
         %87 = OpConstantComposite %_arr_float_uint_9 %float_n1 %float_0 %float_1 %float_n2 %float_0 %float_2 %float_n1 %float_0 %float_1
         %88 = OpConstantComposite %_arr_float_uint_9 %float_n1 %float_n2 %float_n1 %float_0 %float_0 %float_0 %float_1 %float_2 %float_1
         %89 = OpConstantComposite %_arr_float_uint_9 %float_n2 %float_n1 %float_0 %float_n1 %float_1 %float_1 %float_0 %float_1 %float_2
         %90 = OpConstantComposite %v3float %float_1 %float_0_666666687 %float_0_333333343
         %91 = OpConstantComposite %v3float %float_3 %float_3 %float_3
         %92 = OpConstantNull %v4float
       %main = OpFunction %void None %78
         %93 = OpLabel
%EmbossKernel = OpVariable %_ptr_Function__arr_float_uint_9 Function
%EdgeKernelY = OpVariable %_ptr_Function__arr_float_uint_9 Function
%EdgeKernelX = OpVariable %_ptr_Function__arr_float_uint_9 Function
%SharpenKernel = OpVariable %_ptr_Function__arr_float_uint_9 Function
%GaussianKernel = OpVariable %_ptr_Function__arr_float_uint_25 Function
               OpStore %GaussianKernel %85
               OpStore %SharpenKernel %86
               OpStore %EdgeKernelX %87
               OpStore %EdgeKernelY %88
               OpStore %EmbossKernel %89
         %94 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %95 None
               OpSwitch %uint_0 %96
         %96 = OpLabel
         %97 = OpCompositeExtract %uint %94 0
         %98 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_0
         %99 = OpLoad %uint %98
        %100 = OpUGreaterThanEqual %bool %97 %99
        %101 = OpLogicalNot %bool %100
               OpSelectionMerge %102 None
               OpBranchConditional %101 %103 %102
        %103 = OpLabel
        %104 = OpCompositeExtract %uint %94 1
        %105 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_1
        %106 = OpLoad %uint %105
        %107 = OpUGreaterThanEqual %bool %104 %106
               OpBranch %102
        %102 = OpLabel
        %108 = OpPhi %bool %true %96 %107 %103
               OpSelectionMerge %109 None
               OpBranchConditional %108 %110 %109
        %110 = OpLabel
               OpBranch %95
        %109 = OpLabel
        %111 = OpVectorShuffle %v2uint %94 %94 0 1
        %112 = OpConvertUToF %v2float %111
        %113 = OpConvertUToF %float %99
        %114 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_1
        %115 = OpLoad %uint %114
        %116 = OpConvertUToF %float %115
        %117 = OpCompositeConstruct %v2float %113 %116
        %118 = OpFDiv %v2float %112 %117
        %119 = OpLoad %type_2d_image %InputTexture
        %120 = OpLoad %type_sampler %LinearSampler
        %121 = OpSampledImage %type_sampled_image %119 %120
        %122 = OpImageSampleExplicitLod %v4float %121 %118 Lod %float_0
        %123 = OpAccessChain %_ptr_Uniform_uint %ProcessingParams %int_7
        %124 = OpLoad %uint %123
        %125 = OpIEqual %bool %124 %uint_0
               OpSelectionMerge %126 None
               OpBranchConditional %125 %127 %128
        %127 = OpLabel
        %129 = OpFDiv %v2float %42 %117
               OpBranch %130
        %130 = OpLabel
        %131 = OpPhi %v4float %40 %127 %132 %133
        %134 = OpPhi %int %int_n2 %127 %135 %133
        %136 = OpSLessThanEqual %bool %134 %int_2
               OpLoopMerge %137 %133 None
               OpBranchConditional %136 %138 %137
        %138 = OpLabel
               OpBranch %139
        %139 = OpLabel
        %132 = OpPhi %v4float %131 %138 %140 %141
        %142 = OpPhi %int %int_n2 %138 %143 %141
        %144 = OpSLessThanEqual %bool %142 %int_2
               OpLoopMerge %145 %141 None
               OpBranchConditional %144 %141 %145
        %141 = OpLabel
        %146 = OpConvertSToF %float %142
        %147 = OpConvertSToF %float %134
        %148 = OpCompositeConstruct %v2float %146 %147
        %149 = OpFMul %v2float %148 %129
        %150 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_2
        %151 = OpLoad %float %150
        %152 = OpVectorTimesScalar %v2float %149 %151
        %153 = OpFAdd %v2float %118 %152
        %154 = OpLoad %type_2d_image %InputTexture
        %155 = OpLoad %type_sampler %LinearSampler
        %156 = OpSampledImage %type_sampled_image %154 %155
        %157 = OpImageSampleExplicitLod %v4float %156 %153 Lod %float_0
        %158 = OpIAdd %int %134 %int_10
        %159 = OpIAdd %int %158 %142
        %160 = OpIAdd %int %159 %int_2
        %161 = OpAccessChain %_ptr_Function_float %GaussianKernel %160
        %162 = OpLoad %float %161
        %163 = OpVectorTimesScalar %v4float %157 %162
        %140 = OpFAdd %v4float %132 %163
        %143 = OpIAdd %int %142 %int_1
               OpBranch %139
        %145 = OpLabel
               OpBranch %133
        %133 = OpLabel
        %135 = OpIAdd %int %134 %int_1
               OpBranch %130
        %137 = OpLabel
               OpBranch %126
        %128 = OpLabel
        %164 = OpIEqual %bool %124 %uint_1
               OpSelectionMerge %165 None
               OpBranchConditional %164 %166 %167
        %166 = OpLabel
        %168 = OpFDiv %v2float %42 %117
               OpBranch %169
        %169 = OpLabel
        %170 = OpPhi %v4float %40 %166 %171 %172
        %173 = OpPhi %int %int_n1 %166 %174 %172
        %175 = OpSLessThanEqual %bool %173 %int_1
               OpLoopMerge %176 %172 None
               OpBranchConditional %175 %177 %176
        %177 = OpLabel
               OpBranch %178
        %178 = OpLabel
        %171 = OpPhi %v4float %170 %177 %179 %180
        %181 = OpPhi %int %int_n1 %177 %182 %180
        %183 = OpSLessThanEqual %bool %181 %int_1
               OpLoopMerge %184 %180 None
               OpBranchConditional %183 %180 %184
        %180 = OpLabel
        %185 = OpConvertSToF %float %181
        %186 = OpConvertSToF %float %173
        %187 = OpCompositeConstruct %v2float %185 %186
        %188 = OpFMul %v2float %187 %168
        %189 = OpFAdd %v2float %118 %188
        %190 = OpLoad %type_2d_image %InputTexture
        %191 = OpLoad %type_sampler %LinearSampler
        %192 = OpSampledImage %type_sampled_image %190 %191
        %193 = OpImageSampleExplicitLod %v4float %192 %189 Lod %float_0
        %194 = OpIAdd %int %173 %int_3
        %195 = OpIAdd %int %194 %181
        %196 = OpIAdd %int %195 %int_1
        %197 = OpAccessChain %_ptr_Function_float %SharpenKernel %196
        %198 = OpLoad %float %197
        %199 = OpVectorTimesScalar %v4float %193 %198
        %179 = OpFAdd %v4float %171 %199
        %182 = OpIAdd %int %181 %int_1
               OpBranch %178
        %184 = OpLabel
               OpBranch %172
        %172 = OpLabel
        %174 = OpIAdd %int %173 %int_1
               OpBranch %169
        %176 = OpLabel
               OpBranch %165
        %167 = OpLabel
        %200 = OpIEqual %bool %124 %uint_2
               OpSelectionMerge %201 None
               OpBranchConditional %200 %202 %203
        %202 = OpLabel
        %204 = OpFDiv %v2float %42 %117
               OpBranch %205
        %205 = OpLabel
        %206 = OpPhi %v4float %40 %202 %207 %208
        %209 = OpPhi %v4float %40 %202 %210 %208
        %211 = OpPhi %int %int_n1 %202 %212 %208
        %213 = OpSLessThanEqual %bool %211 %int_1
               OpLoopMerge %214 %208 None
               OpBranchConditional %213 %215 %214
        %215 = OpLabel
               OpBranch %216
        %216 = OpLabel
        %207 = OpPhi %v4float %206 %215 %217 %218
        %210 = OpPhi %v4float %209 %215 %219 %218
        %220 = OpPhi %int %int_n1 %215 %221 %218
        %222 = OpSLessThanEqual %bool %220 %int_1
               OpLoopMerge %223 %218 None
               OpBranchConditional %222 %218 %223
        %218 = OpLabel
        %224 = OpConvertSToF %float %220
        %225 = OpConvertSToF %float %211
        %226 = OpCompositeConstruct %v2float %224 %225
        %227 = OpFMul %v2float %226 %204
        %228 = OpFAdd %v2float %118 %227
        %229 = OpLoad %type_2d_image %InputTexture
        %230 = OpLoad %type_sampler %LinearSampler
        %231 = OpSampledImage %type_sampled_image %229 %230
        %232 = OpImageSampleExplicitLod %v4float %231 %228 Lod %float_0
        %233 = OpIAdd %int %211 %int_3
        %234 = OpIAdd %int %233 %220
        %235 = OpIAdd %int %234 %int_1
        %236 = OpAccessChain %_ptr_Function_float %EdgeKernelX %235
        %237 = OpLoad %float %236
        %238 = OpVectorTimesScalar %v4float %232 %237
        %219 = OpFAdd %v4float %210 %238
        %239 = OpAccessChain %_ptr_Function_float %EdgeKernelY %235
        %240 = OpLoad %float %239
        %241 = OpVectorTimesScalar %v4float %232 %240
        %217 = OpFAdd %v4float %207 %241
        %221 = OpIAdd %int %220 %int_1
               OpBranch %216
        %223 = OpLabel
               OpBranch %208
        %208 = OpLabel
        %212 = OpIAdd %int %211 %int_1
               OpBranch %205
        %214 = OpLabel
        %242 = OpFMul %v4float %209 %209
        %243 = OpFMul %v4float %206 %206
        %244 = OpFAdd %v4float %242 %243
        %245 = OpExtInst %v4float %1 Sqrt %244
               OpBranch %201
        %203 = OpLabel
        %246 = OpIEqual %bool %124 %uint_3
               OpSelectionMerge %247 None
               OpBranchConditional %246 %248 %247
        %248 = OpLabel
        %249 = OpFDiv %v2float %42 %117
               OpBranch %250
        %250 = OpLabel
        %251 = OpPhi %v4float %40 %248 %252 %253
        %254 = OpPhi %int %int_n1 %248 %255 %253
        %256 = OpSLessThanEqual %bool %254 %int_1
               OpLoopMerge %257 %253 None
               OpBranchConditional %256 %258 %257
        %258 = OpLabel
               OpBranch %259
        %259 = OpLabel
        %252 = OpPhi %v4float %251 %258 %260 %261
        %262 = OpPhi %int %int_n1 %258 %263 %261
        %264 = OpSLessThanEqual %bool %262 %int_1
               OpLoopMerge %265 %261 None
               OpBranchConditional %264 %261 %265
        %261 = OpLabel
        %266 = OpConvertSToF %float %262
        %267 = OpConvertSToF %float %254
        %268 = OpCompositeConstruct %v2float %266 %267
        %269 = OpFMul %v2float %268 %249
        %270 = OpFAdd %v2float %118 %269
        %271 = OpLoad %type_2d_image %InputTexture
        %272 = OpLoad %type_sampler %LinearSampler
        %273 = OpSampledImage %type_sampled_image %271 %272
        %274 = OpImageSampleExplicitLod %v4float %273 %270 Lod %float_0
        %275 = OpIAdd %int %254 %int_3
        %276 = OpIAdd %int %275 %262
        %277 = OpIAdd %int %276 %int_1
        %278 = OpAccessChain %_ptr_Function_float %EmbossKernel %277
        %279 = OpLoad %float %278
        %280 = OpVectorTimesScalar %v4float %274 %279
        %260 = OpFAdd %v4float %252 %280
        %263 = OpIAdd %int %262 %int_1
               OpBranch %259
        %265 = OpLabel
               OpBranch %253
        %253 = OpLabel
        %255 = OpIAdd %int %254 %int_1
               OpBranch %250
        %257 = OpLabel
        %281 = OpFAdd %v4float %251 %52
               OpBranch %247
        %247 = OpLabel
        %282 = OpPhi %v4float %122 %203 %281 %257
               OpBranch %201
        %201 = OpLabel
        %283 = OpPhi %v4float %245 %214 %282 %247
               OpBranch %165
        %165 = OpLabel
        %284 = OpPhi %v4float %170 %176 %283 %201
               OpBranch %126
        %126 = OpLabel
        %285 = OpPhi %v4float %131 %137 %284 %165
        %286 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_3
        %287 = OpLoad %float %286
        %288 = OpVectorShuffle %v3float %285 %285 0 1 2
        %289 = OpVectorTimesScalar %v3float %288 %287
        %290 = OpVectorShuffle %v3float %289 %92 0 1 2
        %291 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_4
        %292 = OpLoad %float %291
        %293 = OpFMul %float %float_0_5 %292
        %294 = OpCompositeConstruct %v3float %293 %293 %293
        %295 = OpFSub %v3float %290 %294
        %296 = OpFAdd %v3float %295 %55
        %297 = OpCompositeExtract %float %296 2
        %298 = OpCompositeExtract %float %296 1
        %299 = OpCompositeConstruct %v4float %297 %298 %float_n1 %float_0_666666687
        %300 = OpCompositeConstruct %v4float %298 %297 %float_0 %float_n0_333333343
        %301 = OpExtInst %float %1 Step %297 %298
        %302 = OpCompositeConstruct %v4float %301 %301 %301 %301
        %303 = OpExtInst %v4float %1 FMix %299 %300 %302
        %304 = OpCompositeExtract %float %296 0
        %305 = OpCompositeExtract %float %303 0
        %306 = OpCompositeExtract %float %303 1
        %307 = OpCompositeExtract %float %303 3
        %308 = OpCompositeConstruct %v4float %305 %306 %307 %304
        %309 = OpCompositeExtract %float %303 2
        %310 = OpCompositeConstruct %v4float %304 %306 %309 %305
        %311 = OpExtInst %float %1 Step %305 %304
        %312 = OpCompositeConstruct %v4float %311 %311 %311 %311
        %313 = OpExtInst %v4float %1 FMix %308 %310 %312
        %314 = OpCompositeExtract %float %313 0
        %315 = OpCompositeExtract %float %313 3
        %316 = OpCompositeExtract %float %313 1
        %317 = OpExtInst %float %1 NMin %315 %316
        %318 = OpFSub %float %314 %317
        %319 = OpCompositeExtract %float %313 2
        %320 = OpFAdd %float %319 %315
        %321 = OpFMul %float %float_6 %318
        %322 = OpFDiv %float %316 %321
        %323 = OpFSub %float %320 %322
        %324 = OpFAdd %float %323 %float_1_00000001en10
        %325 = OpExtInst %float %1 FAbs %324
        %326 = OpFDiv %float %318 %314
        %327 = OpFAdd %float %326 %float_1_00000001en10
        %328 = OpCompositeConstruct %v3float %325 %327 %314
        %329 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_5
        %330 = OpLoad %float %329
        %331 = OpFMul %float %327 %330
        %332 = OpVectorShuffle %v3float %328 %328 0 0 0
        %333 = OpFAdd %v3float %332 %90
        %334 = OpExtInst %v3float %1 Fract %333
        %335 = OpVectorTimesScalar %v3float %334 %float_6
        %336 = OpFSub %v3float %335 %91
        %337 = OpExtInst %v3float %1 FAbs %336
        %338 = OpFSub %v3float %337 %66
        %339 = OpExtInst %v3float %1 FClamp %338 %65 %66
        %340 = OpCompositeConstruct %v3float %331 %331 %331
        %341 = OpExtInst %v3float %1 FMix %66 %339 %340
        %342 = OpVectorTimesScalar %v3float %341 %314
        %343 = OpVectorShuffle %v3float %342 %92 0 1 2
        %344 = OpExtInst %v3float %1 FAbs %343
        %345 = OpAccessChain %_ptr_Uniform_float %ProcessingParams %int_6
        %346 = OpLoad %float %345
        %347 = OpCompositeConstruct %v3float %346 %346 %346
        %348 = OpExtInst %v3float %1 Pow %344 %347
        %349 = OpVectorShuffle %v4float %285 %348 4 5 6 3
        %350 = OpExtInst %v4float %1 FClamp %349 %40 %58
        %351 = OpLoad %type_2d_image_0 %OutputTexture
               OpImageWrite %351 %111 %350 None
               OpBranch %95
         %95 = OpLabel
               OpReturn
               OpFunctionEnd
