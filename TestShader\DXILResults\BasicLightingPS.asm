;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyz         1     NONE   float       
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 3   xyzw        4     NONE   float       
; TEXCOORD                 4   xyz         5     NONE   float   xyz 
; TEXCOORD                 5   xyz         6     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 170e5c3c2615b05a0958827ae66ab6ac
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 7
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 7
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer Material
; {
;
;   struct Material
;   {
;
;       float4 DiffuseColor;                          ; Offset:    0
;       float4 SpecularColor;                         ; Offset:   16
;       float SpecularPower;                          ; Offset:   32
;       float3 AmbientColor;                          ; Offset:   36
;   
;   } Material;                                       ; Offset:    0 Size:    48
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; Material                          cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; DiffuseTexture                    texture     f32          2d      T0             t0     1
;
;
; ViewId state:
;
; Number of inputs: 27, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 24, 25, 26 }
;   output 1 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 24, 25, 26 }
;   output 2 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 24, 25, 26 }
;   output 3 depends on inputs: { 12, 13 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%Material = type { <4 x float>, <4 x float>, float, <3 x float> }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.dot3.f32(i32 55, float %12, float %13, float %14, float %12, float %13, float %14)  ; Dot3(ax,ay,az,bx,by,bz)
  %16 = call float @dx.op.unary.f32(i32 25, float %15)  ; Rsqrt(value)
  %17 = fmul fast float %16, %12
  %18 = fmul fast float %16, %13
  %19 = fmul fast float %16, %14
  %20 = call float @dx.op.dot3.f32(i32 55, float %4, float %5, float %6, float %4, float %5, float %6)  ; Dot3(ax,ay,az,bx,by,bz)
  %21 = call float @dx.op.unary.f32(i32 25, float %20)  ; Rsqrt(value)
  %22 = fmul fast float %21, %4
  %23 = fmul fast float %21, %5
  %24 = fmul fast float %21, %6
  %25 = call float @dx.op.dot3.f32(i32 55, float %7, float %8, float %9, float %7, float %8, float %9)  ; Dot3(ax,ay,az,bx,by,bz)
  %26 = call float @dx.op.unary.f32(i32 25, float %25)  ; Rsqrt(value)
  %27 = fmul fast float %26, %7
  %28 = fmul fast float %26, %8
  %29 = fmul fast float %26, %9
  %30 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %2, float %10, float %11, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %31 = extractvalue %dx.types.ResRet.f32 %30, 0
  %32 = extractvalue %dx.types.ResRet.f32 %30, 1
  %33 = extractvalue %dx.types.ResRet.f32 %30, 2
  %34 = extractvalue %dx.types.ResRet.f32 %30, 3
  %35 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %36 = extractvalue %dx.types.CBufRet.f32 %35, 1
  %37 = extractvalue %dx.types.CBufRet.f32 %35, 2
  %38 = extractvalue %dx.types.CBufRet.f32 %35, 3
  %39 = call float @dx.op.dot3.f32(i32 55, float %17, float %18, float %19, float %22, float %23, float %24)  ; Dot3(ax,ay,az,bx,by,bz)
  %40 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %39)  ; FMax(a,b)
  %41 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %42 = extractvalue %dx.types.CBufRet.f32 %41, 0
  %43 = extractvalue %dx.types.CBufRet.f32 %41, 1
  %44 = extractvalue %dx.types.CBufRet.f32 %41, 2
  %45 = fmul fast float %42, %40
  %46 = fmul fast float %43, %40
  %47 = fmul fast float %44, %40
  %48 = fsub fast float -0.000000e+00, %22
  %49 = fsub fast float -0.000000e+00, %23
  %50 = fsub fast float -0.000000e+00, %24
  %51 = call float @dx.op.dot3.f32(i32 55, float %48, float %49, float %50, float %17, float %18, float %19)  ; Dot3(ax,ay,az,bx,by,bz)
  %52 = fmul fast float %51, 2.000000e+00
  %53 = fmul fast float %52, %17
  %54 = fmul fast float %52, %18
  %55 = fmul fast float %52, %19
  %56 = fsub fast float %48, %53
  %57 = fsub fast float %49, %54
  %58 = fsub fast float %50, %55
  %59 = call float @dx.op.dot3.f32(i32 55, float %56, float %57, float %58, float %27, float %28, float %29)  ; Dot3(ax,ay,az,bx,by,bz)
  %60 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %59)  ; FMax(a,b)
  %61 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %62 = extractvalue %dx.types.CBufRet.f32 %61, 0
  %63 = extractvalue %dx.types.CBufRet.f32 %61, 1
  %64 = extractvalue %dx.types.CBufRet.f32 %61, 2
  %65 = extractvalue %dx.types.CBufRet.f32 %35, 0
  %66 = call float @dx.op.unary.f32(i32 23, float %60)  ; Log(value)
  %67 = fmul fast float %66, %65
  %68 = call float @dx.op.unary.f32(i32 21, float %67)  ; Exp(value)
  %69 = fmul fast float %68, %62
  %70 = fmul fast float %68, %63
  %71 = fmul fast float %68, %64
  %72 = fadd fast float %45, %36
  %73 = fmul fast float %72, %31
  %74 = fadd fast float %69, %73
  %75 = fadd fast float %46, %37
  %76 = fmul fast float %75, %32
  %77 = fadd fast float %70, %76
  %78 = fadd fast float %47, %38
  %79 = fmul fast float %78, %33
  %80 = fadd fast float %71, %79
  %81 = extractvalue %dx.types.CBufRet.f32 %41, 3
  %82 = fmul fast float %81, %34
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %74)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %77)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %80)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %82)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!12}
!dx.entryPoints = !{!13}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !8, !10}
!5 = !{!6}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{!9}
!9 = !{i32 0, %Material* undef, !"", i32 0, i32 0, i32 1, i32 48, null}
!10 = !{!11}
!11 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!12 = !{[29 x i32] [i32 27, i32 4, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 15, i32 15, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7]}
!13 = !{void ()* @main, !"main", !14, !4, null}
!14 = !{!15, !31, null}
!15 = !{!16, !18, !19, !22, !25, !27, !29}
!16 = !{i32 0, !"SV_Position", i8 9, i8 3, !17, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!17 = !{i32 0}
!18 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !17, i8 2, i32 1, i8 3, i32 1, i8 0, null}
!19 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !20, i8 2, i32 1, i8 3, i32 2, i8 0, !21}
!20 = !{i32 1}
!21 = !{i32 3, i32 7}
!22 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !23, i8 2, i32 1, i8 2, i32 3, i8 0, !24}
!23 = !{i32 2}
!24 = !{i32 3, i32 3}
!25 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 4, i32 4, i8 0, null}
!26 = !{i32 3}
!27 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 3, i32 5, i8 0, !21}
!28 = !{i32 4}
!29 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 3, i32 6, i8 0, !21}
!30 = !{i32 5}
!31 = !{!32}
!32 = !{i32 0, !"SV_Target", i8 9, i8 16, !17, i8 0, i32 1, i8 4, i32 0, i8 0, !33}
!33 = !{i32 3, i32 15}
