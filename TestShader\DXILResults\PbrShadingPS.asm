;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5   xyzw        6     NONE   float       
; TEXCOORD                 6   xyz         7     NONE   float   xyz 
; TEXCOORD                 7   xyzw        8     NONE   float       
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: c641f58e6aed716944ba0a5352e93679
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 9
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 9
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer Material
; {
;
;   struct Material
;   {
;
;       float3 Albedo;                                ; Offset:    0
;       float Metallic;                               ; Offset:   12
;       float Roughness;                              ; Offset:   16
;       float AO;                                     ; Offset:   20
;       float3 EmissiveColor;                         ; Offset:   32
;       float EmissiveStrength;                       ; Offset:   44
;   
;   } Material;                                       ; Offset:    0 Size:    48
;
; }
;
; cbuffer Lighting
; {
;
;   struct Lighting
;   {
;
;       float3 LightPositions[4];                     ; Offset:    0
;       float3 LightColors[4];                        ; Offset:   64
;       float LightIntensities[4];                    ; Offset:  128
;       int NumLights;                                ; Offset:  180
;   
;   } Lighting;                                       ; Offset:    0 Size:   184
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; Material                          cbuffer      NA          NA     CB0            cb0     1
; Lighting                          cbuffer      NA          NA     CB1            cb1     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; AlbedoTexture                     texture     f32          2d      T0             t0     1
; NormalTexture                     texture     f32          2d      T1             t1     1
; MetallicTexture                   texture     f32          2d      T2             t2     1
; RoughnessTexture                  texture     f32          2d      T3             t3     1
; AOTexture                         texture     f32          2d      T4             t4     1
; EmissiveTexture                   texture     f32          2d      T5             t5     1
; EnvironmentMap                    texture     f32        cube      T6             t6     1
;
;
; ViewId state:
;
; Number of inputs: 36, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 5, 6, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30 }
;   output 1 depends on inputs: { 4, 5, 6, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30 }
;   output 2 depends on inputs: { 4, 5, 6, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.TextureCube<vector<float, 4> >" = type { <4 x float> }
%Material = type { <3 x float>, float, float, float, <3 x float>, float }
%Lighting = type { [4 x <3 x float>], [4 x <3 x float>], [4 x float], i32 }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 6, i32 6, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 5, i32 5, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 4, i32 4, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %9 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %10 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %27 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %28 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %29 = extractvalue %dx.types.ResRet.f32 %28, 0
  %30 = extractvalue %dx.types.ResRet.f32 %28, 1
  %31 = extractvalue %dx.types.ResRet.f32 %28, 2
  %32 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %10, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %33 = extractvalue %dx.types.CBufRet.f32 %32, 0
  %34 = extractvalue %dx.types.CBufRet.f32 %32, 1
  %35 = extractvalue %dx.types.CBufRet.f32 %32, 2
  %36 = fmul fast float %33, %29
  %37 = fmul fast float %34, %30
  %38 = fmul fast float %35, %31
  %39 = call float @dx.op.unary.f32(i32 23, float %36)  ; Log(value)
  %40 = call float @dx.op.unary.f32(i32 23, float %37)  ; Log(value)
  %41 = call float @dx.op.unary.f32(i32 23, float %38)  ; Log(value)
  %42 = fmul fast float %39, 0x40019999A0000000
  %43 = fmul fast float %40, 0x40019999A0000000
  %44 = fmul fast float %41, 0x40019999A0000000
  %45 = call float @dx.op.unary.f32(i32 21, float %42)  ; Exp(value)
  %46 = call float @dx.op.unary.f32(i32 21, float %43)  ; Exp(value)
  %47 = call float @dx.op.unary.f32(i32 21, float %44)  ; Exp(value)
  %48 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %49 = extractvalue %dx.types.ResRet.f32 %48, 0
  %50 = extractvalue %dx.types.CBufRet.f32 %32, 3
  %51 = fmul fast float %49, %50
  %52 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %53 = extractvalue %dx.types.ResRet.f32 %52, 0
  %54 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %10, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %55 = extractvalue %dx.types.CBufRet.f32 %54, 0
  %56 = fmul fast float %55, %53
  %57 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %58 = extractvalue %dx.types.ResRet.f32 %57, 0
  %59 = extractvalue %dx.types.CBufRet.f32 %54, 1
  %60 = fmul fast float %58, %59
  %61 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %62 = extractvalue %dx.types.ResRet.f32 %61, 0
  %63 = extractvalue %dx.types.ResRet.f32 %61, 1
  %64 = extractvalue %dx.types.ResRet.f32 %61, 2
  %65 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %10, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %66 = extractvalue %dx.types.CBufRet.f32 %65, 0
  %67 = extractvalue %dx.types.CBufRet.f32 %65, 1
  %68 = extractvalue %dx.types.CBufRet.f32 %65, 2
  %69 = fmul fast float %66, %62
  %70 = fmul fast float %67, %63
  %71 = fmul fast float %68, %64
  %72 = extractvalue %dx.types.CBufRet.f32 %65, 3
  %73 = fmul fast float %69, %72
  %74 = fmul fast float %70, %72
  %75 = fmul fast float %71, %72
  %76 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %8, float %14, float %15, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %77 = extractvalue %dx.types.ResRet.f32 %76, 0
  %78 = extractvalue %dx.types.ResRet.f32 %76, 1
  %79 = extractvalue %dx.types.ResRet.f32 %76, 2
  %80 = fmul fast float %77, 2.000000e+00
  %81 = fmul fast float %78, 2.000000e+00
  %82 = fmul fast float %79, 2.000000e+00
  %83 = fadd fast float %80, -1.000000e+00
  %84 = fadd fast float %81, -1.000000e+00
  %85 = fadd fast float %82, -1.000000e+00
  %86 = fmul fast float %83, %19
  %87 = call float @dx.op.tertiary.f32(i32 46, float %84, float %16, float %86)  ; FMad(a,b,c)
  %88 = call float @dx.op.tertiary.f32(i32 46, float %85, float %22, float %87)  ; FMad(a,b,c)
  %89 = fmul fast float %83, %20
  %90 = call float @dx.op.tertiary.f32(i32 46, float %84, float %17, float %89)  ; FMad(a,b,c)
  %91 = call float @dx.op.tertiary.f32(i32 46, float %85, float %23, float %90)  ; FMad(a,b,c)
  %92 = fmul fast float %83, %21
  %93 = call float @dx.op.tertiary.f32(i32 46, float %84, float %18, float %92)  ; FMad(a,b,c)
  %94 = call float @dx.op.tertiary.f32(i32 46, float %85, float %24, float %93)  ; FMad(a,b,c)
  %95 = call float @dx.op.dot3.f32(i32 55, float %88, float %91, float %94, float %88, float %91, float %94)  ; Dot3(ax,ay,az,bx,by,bz)
  %96 = call float @dx.op.unary.f32(i32 25, float %95)  ; Rsqrt(value)
  %97 = fmul fast float %96, %88
  %98 = fmul fast float %96, %91
  %99 = fmul fast float %96, %94
  %100 = call float @dx.op.dot3.f32(i32 55, float %11, float %12, float %13, float %11, float %12, float %13)  ; Dot3(ax,ay,az,bx,by,bz)
  %101 = call float @dx.op.unary.f32(i32 25, float %100)  ; Rsqrt(value)
  %102 = fmul fast float %101, %11
  %103 = fmul fast float %101, %12
  %104 = fmul fast float %101, %13
  %105 = fadd fast float %45, 0xBFA47AE140000000
  %106 = fadd fast float %46, 0xBFA47AE140000000
  %107 = fadd fast float %47, 0xBFA47AE140000000
  %108 = fmul fast float %51, %105
  %109 = fmul fast float %51, %106
  %110 = fmul fast float %51, %107
  %111 = fadd fast float %108, 0x3FA47AE140000000
  %112 = fadd fast float %109, 0x3FA47AE140000000
  %113 = fadd fast float %110, 0x3FA47AE140000000
  %114 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %9, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %115 = extractvalue %dx.types.CBufRet.i32 %114, 1
  %116 = icmp sgt i32 %115, 0
  br i1 %116, label %117, label %244

; <label>:117                                     ; preds = %0
  br label %118

; <label>:118                                     ; preds = %118, %117
  %119 = phi i32 [ %237, %118 ], [ 0, %117 ]
  %120 = phi float [ %236, %118 ], [ 0.000000e+00, %117 ]
  %121 = phi float [ %235, %118 ], [ 0.000000e+00, %117 ]
  %122 = phi float [ %234, %118 ], [ 0.000000e+00, %117 ]
  %123 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %9, i32 %119)  ; CBufferLoadLegacy(handle,regIndex)
  %124 = extractvalue %dx.types.CBufRet.f32 %123, 0
  %125 = extractvalue %dx.types.CBufRet.f32 %123, 1
  %126 = extractvalue %dx.types.CBufRet.f32 %123, 2
  %127 = fsub fast float %124, %25
  %128 = fsub fast float %125, %26
  %129 = fsub fast float %126, %27
  %130 = call float @dx.op.dot3.f32(i32 55, float %127, float %128, float %129, float %127, float %128, float %129)  ; Dot3(ax,ay,az,bx,by,bz)
  %131 = call float @dx.op.unary.f32(i32 25, float %130)  ; Rsqrt(value)
  %132 = fmul fast float %127, %131
  %133 = fmul fast float %128, %131
  %134 = fmul fast float %129, %131
  %135 = fadd fast float %132, %102
  %136 = fadd fast float %133, %103
  %137 = fadd fast float %134, %104
  %138 = call float @dx.op.dot3.f32(i32 55, float %135, float %136, float %137, float %135, float %136, float %137)  ; Dot3(ax,ay,az,bx,by,bz)
  %139 = call float @dx.op.unary.f32(i32 25, float %138)  ; Rsqrt(value)
  %140 = fmul fast float %135, %139
  %141 = fmul fast float %136, %139
  %142 = fmul fast float %137, %139
  %143 = fmul fast float %127, %127
  %144 = fmul fast float %128, %128
  %145 = fadd fast float %143, %144
  %146 = fmul fast float %129, %129
  %147 = fadd fast float %145, %146
  %148 = call float @dx.op.unary.f32(i32 24, float %147)  ; Sqrt(value)
  %149 = fmul fast float %148, %148
  %150 = fdiv fast float 1.000000e+00, %149
  %151 = add nuw nsw i32 %119, 4
  %152 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %9, i32 %151)  ; CBufferLoadLegacy(handle,regIndex)
  %153 = extractvalue %dx.types.CBufRet.f32 %152, 0
  %154 = extractvalue %dx.types.CBufRet.f32 %152, 1
  %155 = extractvalue %dx.types.CBufRet.f32 %152, 2
  %156 = add nuw nsw i32 %119, 8
  %157 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %9, i32 %156)  ; CBufferLoadLegacy(handle,regIndex)
  %158 = extractvalue %dx.types.CBufRet.f32 %157, 0
  %159 = fmul fast float %56, %56
  %160 = fmul fast float %159, %159
  %161 = call float @dx.op.dot3.f32(i32 55, float %97, float %98, float %99, float %140, float %141, float %142)  ; Dot3(ax,ay,az,bx,by,bz)
  %162 = call float @dx.op.binary.f32(i32 35, float %161, float 0.000000e+00)  ; FMax(a,b)
  %163 = fadd fast float %160, -1.000000e+00
  %164 = fmul fast float %162, %162
  %165 = fmul fast float %164, %163
  %166 = fadd fast float %165, 1.000000e+00
  %167 = fmul fast float %166, %166
  %168 = fmul fast float %167, 0x400921FB60000000
  %169 = fdiv fast float %160, %168
  %170 = call float @dx.op.dot3.f32(i32 55, float %97, float %98, float %99, float %102, float %103, float %104)  ; Dot3(ax,ay,az,bx,by,bz)
  %171 = call float @dx.op.binary.f32(i32 35, float %170, float 0.000000e+00)  ; FMax(a,b)
  %172 = call float @dx.op.dot3.f32(i32 55, float %97, float %98, float %99, float %132, float %133, float %134)  ; Dot3(ax,ay,az,bx,by,bz)
  %173 = call float @dx.op.binary.f32(i32 35, float %172, float 0.000000e+00)  ; FMax(a,b)
  %174 = fadd fast float %56, 1.000000e+00
  %175 = fmul fast float %174, %174
  %176 = fmul fast float %175, 1.250000e-01
  %177 = fsub fast float 1.000000e+00, %176
  %178 = fmul fast float %171, %177
  %179 = fadd fast float %178, %176
  %180 = fdiv fast float %171, %179
  %181 = fmul fast float %173, %177
  %182 = fadd fast float %181, %176
  %183 = fdiv fast float %173, %182
  %184 = call float @dx.op.dot3.f32(i32 55, float %140, float %141, float %142, float %102, float %103, float %104)  ; Dot3(ax,ay,az,bx,by,bz)
  %185 = call float @dx.op.binary.f32(i32 35, float %184, float 0.000000e+00)  ; FMax(a,b)
  %186 = fsub fast float 0x3FEEB851E0000000, %108
  %187 = fsub fast float 0x3FEEB851E0000000, %109
  %188 = fsub fast float 0x3FEEB851E0000000, %110
  %189 = fsub fast float 1.000000e+00, %185
  %190 = call float @dx.op.binary.f32(i32 35, float %189, float 0.000000e+00)  ; FMax(a,b)
  %191 = call float @dx.op.binary.f32(i32 36, float %190, float 1.000000e+00)  ; FMin(a,b)
  %192 = call float @dx.op.unary.f32(i32 23, float %191)  ; Log(value)
  %193 = fmul fast float %192, 5.000000e+00
  %194 = call float @dx.op.unary.f32(i32 21, float %193)  ; Exp(value)
  %195 = fmul fast float %194, %186
  %196 = fmul fast float %194, %187
  %197 = fmul fast float %194, %188
  %198 = fadd fast float %111, %195
  %199 = fadd fast float %112, %196
  %200 = fadd fast float %113, %197
  %201 = fsub fast float 1.000000e+00, %198
  %202 = fsub fast float 1.000000e+00, %199
  %203 = fsub fast float 1.000000e+00, %200
  %204 = fsub fast float 1.000000e+00, %51
  %205 = fmul fast float %180, %169
  %206 = fmul fast float %205, %183
  %207 = fmul fast float %198, %206
  %208 = fmul fast float %199, %206
  %209 = fmul fast float %200, %206
  %210 = fmul fast float %171, 4.000000e+00
  %211 = fmul fast float %210, %173
  %212 = fadd fast float %211, 0x3F1A36E2E0000000
  %213 = fdiv fast float %207, %212
  %214 = fdiv fast float %208, %212
  %215 = fdiv fast float %209, %212
  %216 = fmul fast float %204, 0x3FD45F3060000000
  %217 = fmul fast float %216, %45
  %218 = fmul fast float %217, %201
  %219 = fmul fast float %216, %46
  %220 = fmul fast float %219, %202
  %221 = fmul fast float %216, %47
  %222 = fmul fast float %221, %203
  %223 = fadd fast float %218, %213
  %224 = fadd fast float %220, %214
  %225 = fadd fast float %222, %215
  %226 = fmul fast float %158, %150
  %227 = fmul fast float %173, %226
  %228 = fmul fast float %227, %153
  %229 = fmul fast float %228, %223
  %230 = fmul fast float %227, %154
  %231 = fmul fast float %230, %224
  %232 = fmul fast float %227, %155
  %233 = fmul fast float %232, %225
  %234 = fadd fast float %229, %122
  %235 = fadd fast float %231, %121
  %236 = fadd fast float %233, %120
  %237 = add nuw nsw i32 %119, 1
  %238 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %9, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %239 = extractvalue %dx.types.CBufRet.i32 %238, 1
  %240 = icmp slt i32 %237, %239
  %241 = icmp slt i32 %237, 4
  %242 = and i1 %241, %240
  br i1 %242, label %118, label %243

; <label>:243                                     ; preds = %118
  br label %244

; <label>:244                                     ; preds = %243, %0
  %245 = phi float [ 0.000000e+00, %0 ], [ %236, %243 ]
  %246 = phi float [ 0.000000e+00, %0 ], [ %235, %243 ]
  %247 = phi float [ 0.000000e+00, %0 ], [ %234, %243 ]
  %248 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %8, float %97, float %98, float %99, float undef, i32 undef, i32 undef, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %249 = extractvalue %dx.types.ResRet.f32 %248, 0
  %250 = extractvalue %dx.types.ResRet.f32 %248, 1
  %251 = extractvalue %dx.types.ResRet.f32 %248, 2
  %252 = fmul fast float %60, %45
  %253 = fmul fast float %252, %249
  %254 = fmul fast float %60, %46
  %255 = fmul fast float %254, %250
  %256 = fmul fast float %60, %47
  %257 = fmul fast float %256, %251
  %258 = fadd fast float %247, %73
  %259 = fadd fast float %258, %253
  %260 = fadd fast float %246, %74
  %261 = fadd fast float %260, %255
  %262 = fadd fast float %245, %75
  %263 = fadd fast float %262, %257
  %264 = fadd fast float %259, 1.000000e+00
  %265 = fadd fast float %261, 1.000000e+00
  %266 = fadd fast float %263, 1.000000e+00
  %267 = fdiv fast float %259, %264
  %268 = fdiv fast float %261, %265
  %269 = fdiv fast float %263, %266
  %270 = call float @dx.op.unary.f32(i32 23, float %267)  ; Log(value)
  %271 = call float @dx.op.unary.f32(i32 23, float %268)  ; Log(value)
  %272 = call float @dx.op.unary.f32(i32 23, float %269)  ; Log(value)
  %273 = fmul fast float %270, 0x3FDD1745E0000000
  %274 = fmul fast float %271, 0x3FDD1745E0000000
  %275 = fmul fast float %272, 0x3FDD1745E0000000
  %276 = call float @dx.op.unary.f32(i32 21, float %273)  ; Exp(value)
  %277 = call float @dx.op.unary.f32(i32 21, float %274)  ; Exp(value)
  %278 = call float @dx.op.unary.f32(i32 21, float %275)  ; Exp(value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %276)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %277)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %278)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float 1.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!19}
!dx.entryPoints = !{!20}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !14, !17}
!5 = !{!6, !8, !9, !10, !11, !12, !13}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{i32 2, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 2, i32 0, !7}
!11 = !{i32 4, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 4, i32 1, i32 2, i32 0, !7}
!12 = !{i32 5, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 5, i32 1, i32 2, i32 0, !7}
!13 = !{i32 6, %"class.TextureCube<vector<float, 4> >"* undef, !"", i32 0, i32 6, i32 1, i32 5, i32 0, !7}
!14 = !{!15, !16}
!15 = !{i32 0, %Material* undef, !"", i32 0, i32 0, i32 1, i32 48, null}
!16 = !{i32 1, %Lighting* undef, !"", i32 0, i32 1, i32 1, i32 184, null}
!17 = !{!18}
!18 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!19 = !{[38 x i32] [i32 36, i32 4, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 0, i32 0, i32 0, i32 0]}
!20 = !{void ()* @main, !"main", !21, !4, null}
!21 = !{!22, !42, null}
!22 = !{!23, !25, !27, !29, !31, !33, !36, !38, !40}
!23 = !{i32 0, !"SV_Position", i8 9, i8 3, !24, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!24 = !{i32 0}
!25 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 3, i32 1, i8 0, !26}
!26 = !{i32 3, i32 7}
!27 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 3, i32 2, i8 0, !26}
!28 = !{i32 1}
!29 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 3, i32 3, i8 0, !26}
!30 = !{i32 2}
!31 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !32, i8 2, i32 1, i8 3, i32 4, i8 0, !26}
!32 = !{i32 3}
!33 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !34, i8 2, i32 1, i8 2, i32 5, i8 0, !35}
!34 = !{i32 4}
!35 = !{i32 3, i32 3}
!36 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !37, i8 2, i32 1, i8 4, i32 6, i8 0, null}
!37 = !{i32 5}
!38 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !39, i8 2, i32 1, i8 3, i32 7, i8 0, !26}
!39 = !{i32 6}
!40 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !41, i8 2, i32 1, i8 4, i32 8, i8 0, null}
!41 = !{i32 7}
!42 = !{!43}
!43 = !{i32 0, !"SV_Target", i8 9, i8 16, !24, i8 0, i32 1, i8 4, i32 0, i8 0, !44}
!44 = !{i32 3, i32 15}
