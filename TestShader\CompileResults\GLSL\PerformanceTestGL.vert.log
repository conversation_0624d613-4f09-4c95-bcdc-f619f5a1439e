﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Vertex  Main shader ===========  Position variant ----------------  Work registers: 32 (100% used at 100% occupancy) Uniform registers: 62 (48% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    4.20    2.00    0.00        A Shortest path cycles:        1.46    2.00    0.00       LS Longest path cycles:          N/A     N/A     N/A      N/A  A = Arithmetic, LS = Load/Store, T = Texture  Varying variant ---------------  Work registers: 30 (93% used at 100% occupancy) Uniform registers: 54 (42% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    3.70   11.00    0.00       LS Shortest path cycles:        1.08   11.00    0.00       LS Longest path cycles:          N/A     N/A     N/A      N/A  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: false  Recommended attribute streams =============================  Position attributes  - aPosition (location=0)  - aNormal (location=1)  Non-position attributes  - aTexCoord (location=2) 
