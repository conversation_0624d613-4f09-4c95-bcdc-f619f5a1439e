;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyz         1     NONE   float       
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5   xyzw        6     NONE   float       
; TEXCOORD                 6   xyz         7     NONE   float   xyz 
; TEXCOORD                 7   xyz         8     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 6808a993a57d391e4cab644e9f58f647
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 9
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 9
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer MaterialConstants
; {
;
;   struct hostlayout.MaterialConstants
;   {
;
;       column_major float4x4 WorldMatrix;            ; Offset:    0
;       column_major float4x4 ViewMatrix;             ; Offset:   64
;       column_major float4x4 ProjectionMatrix;       ; Offset:  128
;       column_major float4x4 WorldViewProjectionMatrix;; Offset:  192
;       float4 LightPosition;                         ; Offset:  256
;       float4 LightColor;                            ; Offset:  272
;       float4 MaterialDiffuse;                       ; Offset:  288
;       float4 MaterialSpecular;                      ; Offset:  304
;       float4 CameraPosition;                        ; Offset:  320
;       float Time;                                   ; Offset:  336
;       float SpecularPower;                          ; Offset:  340
;       float2 TextureScale;                          ; Offset:  344
;   
;   } MaterialConstants;                              ; Offset:    0 Size:   352
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; MaterialConstants                 cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; DiffuseTexture                    texture     f32          2d      T0             t0     1
; NormalTexture                     texture     f32          2d      T1             t1     1
; SpecularTexture                   texture     f32          2d      T2             t2     1
; EmissiveTexture                   texture     f32          2d      T3             t3     1
; EnvironmentTexture                texture     f32        cube      T4             t4     1
;
;
; ViewId state:
;
; Number of inputs: 35, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30, 32, 33, 34 }
;   output 1 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30, 32, 33, 34 }
;   output 2 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 28, 29, 30, 32, 33, 34 }
;   output 3 depends on inputs: { 20, 21 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.TextureCube<vector<float, 4> >" = type { <4 x float> }
%hostlayout.MaterialConstants = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <4 x float>, <4 x float>, <4 x float>, <4 x float>, <4 x float>, float, float, <2 x float> }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 4, i32 4, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 21)  ; CBufferLoadLegacy(handle,regIndex)
  %26 = extractvalue %dx.types.CBufRet.f32 %25, 0
  %27 = fmul fast float %26, 5.000000e-01
  %28 = call float @dx.op.unary.f32(i32 13, float %27)  ; Sin(value)
  %29 = fmul fast float %28, 0x3F847AE140000000
  %30 = fadd fast float %29, %14
  %31 = fadd fast float %29, %15
  %32 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %6, float %30, float %31, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %33 = extractvalue %dx.types.ResRet.f32 %32, 0
  %34 = extractvalue %dx.types.ResRet.f32 %32, 1
  %35 = extractvalue %dx.types.ResRet.f32 %32, 2
  %36 = extractvalue %dx.types.ResRet.f32 %32, 3
  %37 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %30, float %31, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %38 = extractvalue %dx.types.ResRet.f32 %37, 0
  %39 = extractvalue %dx.types.ResRet.f32 %37, 1
  %40 = extractvalue %dx.types.ResRet.f32 %37, 2
  %41 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %6, float %30, float %31, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %42 = extractvalue %dx.types.ResRet.f32 %41, 0
  %43 = extractvalue %dx.types.ResRet.f32 %41, 1
  %44 = extractvalue %dx.types.ResRet.f32 %41, 2
  %45 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %6, float %30, float %31, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %46 = extractvalue %dx.types.ResRet.f32 %45, 0
  %47 = extractvalue %dx.types.ResRet.f32 %45, 1
  %48 = fmul fast float %38, 2.000000e+00
  %49 = fmul fast float %39, 2.000000e+00
  %50 = fmul fast float %40, 2.000000e+00
  %51 = fadd fast float %48, -1.000000e+00
  %52 = fadd fast float %49, -1.000000e+00
  %53 = fadd fast float %50, -1.000000e+00
  %54 = call float @dx.op.dot3.f32(i32 55, float %51, float %52, float %53, float %51, float %52, float %53)  ; Dot3(ax,ay,az,bx,by,bz)
  %55 = call float @dx.op.unary.f32(i32 25, float %54)  ; Rsqrt(value)
  %56 = fmul fast float %55, %51
  %57 = fmul fast float %55, %52
  %58 = fmul fast float %55, %53
  %59 = call float @dx.op.dot3.f32(i32 55, float %19, float %20, float %21, float %19, float %20, float %21)  ; Dot3(ax,ay,az,bx,by,bz)
  %60 = call float @dx.op.unary.f32(i32 25, float %59)  ; Rsqrt(value)
  %61 = call float @dx.op.dot3.f32(i32 55, float %16, float %17, float %18, float %16, float %17, float %18)  ; Dot3(ax,ay,az,bx,by,bz)
  %62 = call float @dx.op.unary.f32(i32 25, float %61)  ; Rsqrt(value)
  %63 = fmul fast float %62, %16
  %64 = fmul fast float %62, %17
  %65 = fmul fast float %62, %18
  %66 = call float @dx.op.dot3.f32(i32 55, float %22, float %23, float %24, float %22, float %23, float %24)  ; Dot3(ax,ay,az,bx,by,bz)
  %67 = call float @dx.op.unary.f32(i32 25, float %66)  ; Rsqrt(value)
  %68 = fmul fast float %67, %22
  %69 = fmul fast float %67, %23
  %70 = fmul fast float %67, %24
  %71 = fmul fast float %56, %60
  %72 = fmul fast float %71, %19
  %73 = call float @dx.op.tertiary.f32(i32 46, float %57, float %63, float %72)  ; FMad(a,b,c)
  %74 = call float @dx.op.tertiary.f32(i32 46, float %58, float %68, float %73)  ; FMad(a,b,c)
  %75 = fmul fast float %71, %20
  %76 = call float @dx.op.tertiary.f32(i32 46, float %57, float %64, float %75)  ; FMad(a,b,c)
  %77 = call float @dx.op.tertiary.f32(i32 46, float %58, float %69, float %76)  ; FMad(a,b,c)
  %78 = fmul fast float %71, %21
  %79 = call float @dx.op.tertiary.f32(i32 46, float %57, float %65, float %78)  ; FMad(a,b,c)
  %80 = call float @dx.op.tertiary.f32(i32 46, float %58, float %70, float %79)  ; FMad(a,b,c)
  %81 = call float @dx.op.dot3.f32(i32 55, float %74, float %77, float %80, float %74, float %77, float %80)  ; Dot3(ax,ay,az,bx,by,bz)
  %82 = call float @dx.op.unary.f32(i32 25, float %81)  ; Rsqrt(value)
  %83 = fmul fast float %82, %74
  %84 = fmul fast float %82, %77
  %85 = fmul fast float %82, %80
  %86 = call float @dx.op.dot3.f32(i32 55, float %8, float %9, float %10, float %8, float %9, float %10)  ; Dot3(ax,ay,az,bx,by,bz)
  %87 = call float @dx.op.unary.f32(i32 25, float %86)  ; Rsqrt(value)
  %88 = fmul fast float %87, %8
  %89 = fmul fast float %87, %9
  %90 = fmul fast float %87, %10
  %91 = call float @dx.op.dot3.f32(i32 55, float %11, float %12, float %13, float %11, float %12, float %13)  ; Dot3(ax,ay,az,bx,by,bz)
  %92 = call float @dx.op.unary.f32(i32 25, float %91)  ; Rsqrt(value)
  %93 = fmul fast float %92, %11
  %94 = fmul fast float %92, %12
  %95 = fmul fast float %92, %13
  %96 = fadd fast float %93, %88
  %97 = fadd fast float %94, %89
  %98 = fadd fast float %95, %90
  %99 = call float @dx.op.dot3.f32(i32 55, float %96, float %97, float %98, float %96, float %97, float %98)  ; Dot3(ax,ay,az,bx,by,bz)
  %100 = call float @dx.op.unary.f32(i32 25, float %99)  ; Rsqrt(value)
  %101 = fmul fast float %96, %100
  %102 = fmul fast float %97, %100
  %103 = fmul fast float %98, %100
  %104 = call float @dx.op.dot3.f32(i32 55, float %83, float %84, float %85, float %88, float %89, float %90)  ; Dot3(ax,ay,az,bx,by,bz)
  %105 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %104)  ; FMax(a,b)
  %106 = call float @dx.op.dot3.f32(i32 55, float %83, float %84, float %85, float %101, float %102, float %103)  ; Dot3(ax,ay,az,bx,by,bz)
  %107 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %106)  ; FMax(a,b)
  %108 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 21)  ; CBufferLoadLegacy(handle,regIndex)
  %109 = extractvalue %dx.types.CBufRet.f32 %108, 1
  %110 = call float @dx.op.unary.f32(i32 23, float %107)  ; Log(value)
  %111 = fmul fast float %110, %109
  %112 = call float @dx.op.unary.f32(i32 21, float %111)  ; Exp(value)
  %113 = call float @dx.op.dot3.f32(i32 55, float %93, float %94, float %95, float %83, float %84, float %85)  ; Dot3(ax,ay,az,bx,by,bz)
  %114 = fsub fast float 1.000000e+00, %113
  %115 = call float @dx.op.unary.f32(i32 23, float %114)  ; Log(value)
  %116 = fmul fast float %115, 5.000000e+00
  %117 = call float @dx.op.unary.f32(i32 21, float %116)  ; Exp(value)
  %118 = fsub fast float -0.000000e+00, %93
  %119 = fsub fast float -0.000000e+00, %94
  %120 = fsub fast float -0.000000e+00, %95
  %121 = call float @dx.op.dot3.f32(i32 55, float %118, float %119, float %120, float %83, float %84, float %85)  ; Dot3(ax,ay,az,bx,by,bz)
  %122 = fmul fast float %121, 2.000000e+00
  %123 = fmul fast float %122, %83
  %124 = fmul fast float %122, %84
  %125 = fmul fast float %122, %85
  %126 = fsub fast float %118, %123
  %127 = fsub fast float %119, %124
  %128 = fsub fast float %120, %125
  %129 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %6, float %126, float %127, float %128, float undef, i32 undef, i32 undef, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %130 = extractvalue %dx.types.ResRet.f32 %129, 0
  %131 = extractvalue %dx.types.ResRet.f32 %129, 1
  %132 = extractvalue %dx.types.ResRet.f32 %129, 2
  %133 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 18)  ; CBufferLoadLegacy(handle,regIndex)
  %134 = extractvalue %dx.types.CBufRet.f32 %133, 0
  %135 = extractvalue %dx.types.CBufRet.f32 %133, 1
  %136 = extractvalue %dx.types.CBufRet.f32 %133, 2
  %137 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 17)  ; CBufferLoadLegacy(handle,regIndex)
  %138 = extractvalue %dx.types.CBufRet.f32 %137, 0
  %139 = extractvalue %dx.types.CBufRet.f32 %137, 1
  %140 = extractvalue %dx.types.CBufRet.f32 %137, 2
  %141 = fmul fast float %33, 0x3FE99999A0000000
  %142 = fmul fast float %141, %105
  %143 = fmul fast float %142, %134
  %144 = fmul fast float %34, 0x3FE3333340000000
  %145 = fmul fast float %144, %105
  %146 = fmul fast float %145, %135
  %147 = fmul fast float %35, 0x3FD99999A0000000
  %148 = fmul fast float %147, %105
  %149 = fmul fast float %148, %136
  %150 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 19)  ; CBufferLoadLegacy(handle,regIndex)
  %151 = extractvalue %dx.types.CBufRet.f32 %150, 0
  %152 = extractvalue %dx.types.CBufRet.f32 %150, 1
  %153 = extractvalue %dx.types.CBufRet.f32 %150, 2
  %154 = fmul fast float %112, %42
  %155 = fmul fast float %154, %151
  %156 = fmul fast float %112, %43
  %157 = fmul fast float %156, %152
  %158 = fmul fast float %112, %44
  %159 = fmul fast float %158, %153
  %160 = fmul fast float %46, 0x3FB99999A0000000
  %161 = fmul fast float %47, 0x3FA99999A0000000
  %162 = fmul fast float %160, %134
  %163 = fmul fast float %161, %135
  %164 = fmul fast float %117, 0x3FD3333340000000
  %165 = fmul fast float %130, %164
  %166 = fmul fast float %131, %164
  %167 = fmul fast float %132, %164
  %168 = fadd fast float %155, %143
  %169 = fmul fast float %168, %138
  %170 = fadd fast float %162, %165
  %171 = fadd fast float %170, %169
  %172 = fadd fast float %157, %146
  %173 = fmul fast float %172, %139
  %174 = fadd fast float %163, %166
  %175 = fadd fast float %174, %173
  %176 = fadd fast float %159, %149
  %177 = fmul fast float %176, %140
  %178 = fadd fast float %177, %167
  %179 = fadd fast float %171, 2.000000e+00
  %180 = fadd fast float %175, 0x40023D70C0000000
  %181 = fadd fast float %178, 3.700000e+01
  %182 = fadd fast float %171, 3.000000e+00
  %183 = fadd fast float %175, 0x400A3D70C0000000
  %184 = fadd fast float %178, 3.800000e+01
  %185 = fdiv fast float %179, %182
  %186 = fdiv fast float %180, %183
  %187 = fdiv fast float %181, %184
  %188 = call float @dx.op.unary.f32(i32 23, float %185)  ; Log(value)
  %189 = call float @dx.op.unary.f32(i32 23, float %186)  ; Log(value)
  %190 = call float @dx.op.unary.f32(i32 23, float %187)  ; Log(value)
  %191 = fmul fast float %188, 0x3FDD1745E0000000
  %192 = fmul fast float %189, 0x3FDD1745E0000000
  %193 = fmul fast float %190, 0x3FDD1745E0000000
  %194 = call float @dx.op.unary.f32(i32 21, float %191)  ; Exp(value)
  %195 = call float @dx.op.unary.f32(i32 21, float %192)  ; Exp(value)
  %196 = call float @dx.op.unary.f32(i32 21, float %193)  ; Exp(value)
  %197 = extractvalue %dx.types.CBufRet.f32 %133, 3
  %198 = fmul fast float %197, %36
  %199 = fmul fast float %194, 0x3FEDAC3400000000
  %200 = fmul fast float %195, 0x3FEDAC3400000000
  %201 = fmul fast float %196, 0x3FEDAC3400000000
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %199)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %200)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %201)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %198)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!16}
!dx.entryPoints = !{!17}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !12, !14}
!5 = !{!6, !8, !9, !10, !11}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{i32 2, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 2, i32 0, !7}
!11 = !{i32 4, %"class.TextureCube<vector<float, 4> >"* undef, !"", i32 0, i32 4, i32 1, i32 5, i32 0, !7}
!12 = !{!13}
!13 = !{i32 0, %hostlayout.MaterialConstants* undef, !"", i32 0, i32 0, i32 1, i32 352, null}
!14 = !{!15}
!15 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!16 = !{[37 x i32] [i32 35, i32 4, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 0, i32 15, i32 15, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7]}
!17 = !{void ()* @main, !"main", !18, !4, null}
!18 = !{!19, !39, null}
!19 = !{!20, !22, !23, !26, !28, !30, !33, !35, !37}
!20 = !{i32 0, !"SV_Position", i8 9, i8 3, !21, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!21 = !{i32 0}
!22 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 1, i8 3, i32 1, i8 0, null}
!23 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 3, i32 2, i8 0, !25}
!24 = !{i32 1}
!25 = !{i32 3, i32 7}
!26 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !27, i8 2, i32 1, i8 3, i32 3, i8 0, !25}
!27 = !{i32 2}
!28 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !29, i8 2, i32 1, i8 3, i32 4, i8 0, !25}
!29 = !{i32 3}
!30 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !31, i8 2, i32 1, i8 2, i32 5, i8 0, !32}
!31 = !{i32 4}
!32 = !{i32 3, i32 3}
!33 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !34, i8 2, i32 1, i8 4, i32 6, i8 0, null}
!34 = !{i32 5}
!35 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !36, i8 2, i32 1, i8 3, i32 7, i8 0, !25}
!36 = !{i32 6}
!37 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !38, i8 2, i32 1, i8 3, i32 8, i8 0, !25}
!38 = !{i32 7}
!39 = !{!40}
!40 = !{i32 0, !"SV_Target", i8 9, i8 16, !21, i8 0, i32 1, i8 4, i32 0, i8 0, !41}
!41 = !{i32 3, i32 15}
