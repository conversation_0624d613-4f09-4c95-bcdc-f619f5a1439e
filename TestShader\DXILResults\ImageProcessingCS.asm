;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 53de78f1b8d505bae5971a6efa121d96
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(8,8,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer ProcessingParams
; {
;
;   struct ProcessingParams
;   {
;
;       uint ImageWidth;                              ; Offset:    0
;       uint ImageHeight;                             ; Offset:    4
;       float BlurRadius;                             ; Offset:    8
;       float Brightness;                             ; Offset:   12
;       float Contrast;                               ; Offset:   16
;       float Saturation;                             ; Offset:   20
;       float Gamma;                                  ; Offset:   24
;       uint FilterType;                              ; Offset:   28
;   
;   } ProcessingParams;                               ; Offset:    0 Size:    32
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; ProcessingParams                  cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; InputTexture                      texture     f32          2d      T0             t0     1
; OutputTexture                         UAV     f32          2d      U0             u0     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.RWTexture2D<vector<float, 4> >" = type { <4 x float> }
%ProcessingParams = type { i32, i32, float, float, float, float, float, i32 }
%struct.SamplerState = type { i32 }

@GaussianKernel = internal unnamed_addr constant [25 x float] [float 0x3F6ED7C700000000, float 0x3F8EC24800000000, float 0x3F985CEE20000000, float 0x3F8EC24800000000, float 0x3F6ED7C700000000, float 0x3F8EC24800000000, float 0x3FAEACC920000000, float 0x3FB84BD340000000, float 0x3FAEACC920000000, float 0x3F8EC24800000000, float 0x3F985CEE20000000, float 0x3FB84BD340000000, float 0x3FC33E6820000000, float 0x3FB84BD340000000, float 0x3F985CEE20000000, float 0x3F8EC24800000000, float 0x3FAEACC920000000, float 0x3FB84BD340000000, float 0x3FAEACC920000000, float 0x3F8EC24800000000, float 0x3F6ED7C700000000, float 0x3F8EC24800000000, float 0x3F985CEE20000000, float 0x3F8EC24800000000, float 0x3F6ED7C700000000], align 4
@SharpenKernel = internal unnamed_addr constant [9 x float] [float 0.000000e+00, float -1.000000e+00, float 0.000000e+00, float -1.000000e+00, float 5.000000e+00, float -1.000000e+00, float 0.000000e+00, float -1.000000e+00, float 0.000000e+00], align 4
@EdgeKernelX = internal unnamed_addr constant [9 x float] [float -1.000000e+00, float 0.000000e+00, float 1.000000e+00, float -2.000000e+00, float 0.000000e+00, float 2.000000e+00, float -1.000000e+00, float 0.000000e+00, float 1.000000e+00], align 4
@EdgeKernelY = internal unnamed_addr constant [9 x float] [float -1.000000e+00, float -2.000000e+00, float -1.000000e+00, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float 1.000000e+00, float 2.000000e+00, float 1.000000e+00], align 4
@EmbossKernel = internal unnamed_addr constant [9 x float] [float -2.000000e+00, float -1.000000e+00, float 0.000000e+00, float -1.000000e+00, float 1.000000e+00, float 1.000000e+00, float 0.000000e+00, float 1.000000e+00, float 2.000000e+00], align 4

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %6 = call i32 @dx.op.threadId.i32(i32 93, i32 1)  ; ThreadId(component)
  %7 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %8 = extractvalue %dx.types.CBufRet.i32 %7, 0
  %9 = icmp ult i32 %5, %8
  br i1 %9, label %10, label %363

; <label>:10                                      ; preds = %0
  %11 = extractvalue %dx.types.CBufRet.i32 %7, 1
  %12 = icmp ult i32 %6, %11
  br i1 %12, label %13, label %363

; <label>:13                                      ; preds = %10
  %14 = uitofp i32 %5 to float
  %15 = uitofp i32 %6 to float
  %16 = uitofp i32 %8 to float
  %17 = uitofp i32 %11 to float
  %18 = fdiv fast float %14, %16
  %19 = fdiv fast float %15, %17
  %20 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %18, float %19, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %21 = extractvalue %dx.types.ResRet.f32 %20, 0
  %22 = extractvalue %dx.types.ResRet.f32 %20, 1
  %23 = extractvalue %dx.types.ResRet.f32 %20, 2
  %24 = extractvalue %dx.types.ResRet.f32 %20, 3
  %25 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %26 = extractvalue %dx.types.CBufRet.i32 %25, 3
  %27 = icmp eq i32 %26, 0
  br i1 %27, label %28, label %76

; <label>:28                                      ; preds = %13
  %29 = fdiv fast float 1.000000e+00, %16
  %30 = fdiv fast float 1.000000e+00, %17
  br label %31

; <label>:31                                      ; preds = %73, %28
  %32 = phi float [ 0.000000e+00, %28 ], [ %67, %73 ]
  %33 = phi float [ 0.000000e+00, %28 ], [ %68, %73 ]
  %34 = phi float [ 0.000000e+00, %28 ], [ %69, %73 ]
  %35 = phi float [ 0.000000e+00, %28 ], [ %70, %73 ]
  %36 = phi i32 [ -2, %28 ], [ %74, %73 ]
  br label %37

; <label>:37                                      ; preds = %37, %31
  %38 = phi float [ %32, %31 ], [ %67, %37 ]
  %39 = phi float [ %33, %31 ], [ %68, %37 ]
  %40 = phi float [ %34, %31 ], [ %69, %37 ]
  %41 = phi float [ %35, %31 ], [ %70, %37 ]
  %42 = phi i32 [ -2, %31 ], [ %71, %37 ]
  %43 = sitofp i32 %42 to float
  %44 = sitofp i32 %36 to float
  %45 = fmul fast float %43, %29
  %46 = fmul fast float %44, %30
  %47 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %48 = extractvalue %dx.types.CBufRet.f32 %47, 2
  %49 = fmul fast float %45, %48
  %50 = fmul fast float %46, %48
  %51 = fadd fast float %49, %18
  %52 = fadd fast float %50, %19
  %53 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %51, float %52, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %54 = extractvalue %dx.types.ResRet.f32 %53, 0
  %55 = extractvalue %dx.types.ResRet.f32 %53, 1
  %56 = extractvalue %dx.types.ResRet.f32 %53, 2
  %57 = extractvalue %dx.types.ResRet.f32 %53, 3
  %58 = mul nsw i32 %36, 5
  %59 = add nsw i32 %58, 12
  %60 = add nsw i32 %59, %42
  %61 = getelementptr inbounds [25 x float], [25 x float]* @GaussianKernel, i32 0, i32 %60
  %62 = load float, float* %61, align 4, !tbaa !17
  %63 = fmul fast float %54, %62
  %64 = fmul fast float %55, %62
  %65 = fmul fast float %56, %62
  %66 = fmul fast float %57, %62
  %67 = fadd fast float %63, %38
  %68 = fadd fast float %64, %39
  %69 = fadd fast float %65, %40
  %70 = fadd fast float %66, %41
  %71 = add nsw i32 %42, 1
  %72 = icmp eq i32 %71, 3
  br i1 %72, label %73, label %37

; <label>:73                                      ; preds = %37
  %74 = add nsw i32 %36, 1
  %75 = icmp eq i32 %74, 3
  br i1 %75, label %254, label %31

; <label>:76                                      ; preds = %13
  %77 = icmp eq i32 %26, 1
  br i1 %77, label %78, label %122

; <label>:78                                      ; preds = %76
  %79 = fdiv fast float 1.000000e+00, %16
  %80 = fdiv fast float 1.000000e+00, %17
  br label %81

; <label>:81                                      ; preds = %119, %78
  %82 = phi float [ 0.000000e+00, %78 ], [ %113, %119 ]
  %83 = phi float [ 0.000000e+00, %78 ], [ %114, %119 ]
  %84 = phi float [ 0.000000e+00, %78 ], [ %115, %119 ]
  %85 = phi float [ 0.000000e+00, %78 ], [ %116, %119 ]
  %86 = phi i32 [ -1, %78 ], [ %120, %119 ]
  br label %87

; <label>:87                                      ; preds = %87, %81
  %88 = phi float [ %82, %81 ], [ %113, %87 ]
  %89 = phi float [ %83, %81 ], [ %114, %87 ]
  %90 = phi float [ %84, %81 ], [ %115, %87 ]
  %91 = phi float [ %85, %81 ], [ %116, %87 ]
  %92 = phi i32 [ -1, %81 ], [ %117, %87 ]
  %93 = sitofp i32 %92 to float
  %94 = sitofp i32 %86 to float
  %95 = fmul fast float %93, %79
  %96 = fmul fast float %94, %80
  %97 = fadd fast float %95, %18
  %98 = fadd fast float %96, %19
  %99 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %97, float %98, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %100 = extractvalue %dx.types.ResRet.f32 %99, 0
  %101 = extractvalue %dx.types.ResRet.f32 %99, 1
  %102 = extractvalue %dx.types.ResRet.f32 %99, 2
  %103 = extractvalue %dx.types.ResRet.f32 %99, 3
  %104 = mul nsw i32 %86, 3
  %105 = add nsw i32 %104, 4
  %106 = add nsw i32 %105, %92
  %107 = getelementptr inbounds [9 x float], [9 x float]* @SharpenKernel, i32 0, i32 %106
  %108 = load float, float* %107, align 4, !tbaa !17
  %109 = fmul fast float %100, %108
  %110 = fmul fast float %101, %108
  %111 = fmul fast float %102, %108
  %112 = fmul fast float %103, %108
  %113 = fadd fast float %109, %88
  %114 = fadd fast float %110, %89
  %115 = fadd fast float %111, %90
  %116 = fadd fast float %112, %91
  %117 = add nsw i32 %92, 1
  %118 = icmp eq i32 %117, 2
  br i1 %118, label %119, label %87

; <label>:119                                     ; preds = %87
  %120 = add nsw i32 %86, 1
  %121 = icmp eq i32 %120, 2
  br i1 %121, label %255, label %81

; <label>:122                                     ; preds = %76
  %123 = icmp eq i32 %26, 2
  br i1 %123, label %124, label %203

; <label>:124                                     ; preds = %122
  %125 = fdiv fast float 1.000000e+00, %16
  %126 = fdiv fast float 1.000000e+00, %17
  br label %127

; <label>:127                                     ; preds = %183, %124
  %128 = phi float [ 0.000000e+00, %124 ], [ %167, %183 ]
  %129 = phi float [ 0.000000e+00, %124 ], [ %168, %183 ]
  %130 = phi float [ 0.000000e+00, %124 ], [ %169, %183 ]
  %131 = phi float [ 0.000000e+00, %124 ], [ %170, %183 ]
  %132 = phi float [ 0.000000e+00, %124 ], [ %177, %183 ]
  %133 = phi float [ 0.000000e+00, %124 ], [ %178, %183 ]
  %134 = phi float [ 0.000000e+00, %124 ], [ %179, %183 ]
  %135 = phi float [ 0.000000e+00, %124 ], [ %180, %183 ]
  %136 = phi i32 [ -1, %124 ], [ %184, %183 ]
  br label %137

; <label>:137                                     ; preds = %137, %127
  %138 = phi float [ %128, %127 ], [ %167, %137 ]
  %139 = phi float [ %129, %127 ], [ %168, %137 ]
  %140 = phi float [ %130, %127 ], [ %169, %137 ]
  %141 = phi float [ %131, %127 ], [ %170, %137 ]
  %142 = phi float [ %132, %127 ], [ %177, %137 ]
  %143 = phi float [ %133, %127 ], [ %178, %137 ]
  %144 = phi float [ %134, %127 ], [ %179, %137 ]
  %145 = phi float [ %135, %127 ], [ %180, %137 ]
  %146 = phi i32 [ -1, %127 ], [ %181, %137 ]
  %147 = sitofp i32 %146 to float
  %148 = sitofp i32 %136 to float
  %149 = fmul fast float %147, %125
  %150 = fmul fast float %148, %126
  %151 = fadd fast float %149, %18
  %152 = fadd fast float %150, %19
  %153 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %151, float %152, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %154 = extractvalue %dx.types.ResRet.f32 %153, 0
  %155 = extractvalue %dx.types.ResRet.f32 %153, 1
  %156 = extractvalue %dx.types.ResRet.f32 %153, 2
  %157 = extractvalue %dx.types.ResRet.f32 %153, 3
  %158 = mul nsw i32 %136, 3
  %159 = add i32 %158, 4
  %160 = add i32 %159, %146
  %161 = getelementptr inbounds [9 x float], [9 x float]* @EdgeKernelX, i32 0, i32 %160
  %162 = load float, float* %161, align 4, !tbaa !17
  %163 = fmul fast float %154, %162
  %164 = fmul fast float %155, %162
  %165 = fmul fast float %156, %162
  %166 = fmul fast float %157, %162
  %167 = fadd fast float %163, %138
  %168 = fadd fast float %164, %139
  %169 = fadd fast float %165, %140
  %170 = fadd fast float %166, %141
  %171 = getelementptr inbounds [9 x float], [9 x float]* @EdgeKernelY, i32 0, i32 %160
  %172 = load float, float* %171, align 4, !tbaa !17
  %173 = fmul fast float %172, %154
  %174 = fmul fast float %172, %155
  %175 = fmul fast float %172, %156
  %176 = fmul fast float %172, %157
  %177 = fadd fast float %173, %142
  %178 = fadd fast float %174, %143
  %179 = fadd fast float %175, %144
  %180 = fadd fast float %176, %145
  %181 = add nsw i32 %146, 1
  %182 = icmp eq i32 %181, 2
  br i1 %182, label %183, label %137

; <label>:183                                     ; preds = %137
  %184 = add nsw i32 %136, 1
  %185 = icmp eq i32 %184, 2
  br i1 %185, label %186, label %127

; <label>:186                                     ; preds = %183
  %187 = fmul fast float %167, %167
  %188 = fmul fast float %168, %168
  %189 = fmul fast float %169, %169
  %190 = fmul fast float %170, %170
  %191 = fmul fast float %177, %177
  %192 = fmul fast float %178, %178
  %193 = fmul fast float %179, %179
  %194 = fmul fast float %180, %180
  %195 = fadd fast float %191, %187
  %196 = fadd fast float %192, %188
  %197 = fadd fast float %193, %189
  %198 = fadd fast float %194, %190
  %199 = call float @dx.op.unary.f32(i32 24, float %195)  ; Sqrt(value)
  %200 = call float @dx.op.unary.f32(i32 24, float %196)  ; Sqrt(value)
  %201 = call float @dx.op.unary.f32(i32 24, float %197)  ; Sqrt(value)
  %202 = call float @dx.op.unary.f32(i32 24, float %198)  ; Sqrt(value)
  br label %256

; <label>:203                                     ; preds = %122
  %204 = icmp eq i32 %26, 3
  br i1 %204, label %205, label %256

; <label>:205                                     ; preds = %203
  %206 = fdiv fast float 1.000000e+00, %16
  %207 = fdiv fast float 1.000000e+00, %17
  br label %208

; <label>:208                                     ; preds = %246, %205
  %209 = phi float [ 0.000000e+00, %205 ], [ %240, %246 ]
  %210 = phi float [ 0.000000e+00, %205 ], [ %241, %246 ]
  %211 = phi float [ 0.000000e+00, %205 ], [ %242, %246 ]
  %212 = phi float [ 0.000000e+00, %205 ], [ %243, %246 ]
  %213 = phi i32 [ -1, %205 ], [ %247, %246 ]
  br label %214

; <label>:214                                     ; preds = %214, %208
  %215 = phi float [ %209, %208 ], [ %240, %214 ]
  %216 = phi float [ %210, %208 ], [ %241, %214 ]
  %217 = phi float [ %211, %208 ], [ %242, %214 ]
  %218 = phi float [ %212, %208 ], [ %243, %214 ]
  %219 = phi i32 [ -1, %208 ], [ %244, %214 ]
  %220 = sitofp i32 %219 to float
  %221 = sitofp i32 %213 to float
  %222 = fmul fast float %220, %206
  %223 = fmul fast float %221, %207
  %224 = fadd fast float %222, %18
  %225 = fadd fast float %223, %19
  %226 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %224, float %225, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %227 = extractvalue %dx.types.ResRet.f32 %226, 0
  %228 = extractvalue %dx.types.ResRet.f32 %226, 1
  %229 = extractvalue %dx.types.ResRet.f32 %226, 2
  %230 = extractvalue %dx.types.ResRet.f32 %226, 3
  %231 = mul nsw i32 %213, 3
  %232 = add nsw i32 %231, 4
  %233 = add nsw i32 %232, %219
  %234 = getelementptr inbounds [9 x float], [9 x float]* @EmbossKernel, i32 0, i32 %233
  %235 = load float, float* %234, align 4, !tbaa !17
  %236 = fmul fast float %227, %235
  %237 = fmul fast float %228, %235
  %238 = fmul fast float %229, %235
  %239 = fmul fast float %230, %235
  %240 = fadd fast float %236, %215
  %241 = fadd fast float %237, %216
  %242 = fadd fast float %238, %217
  %243 = fadd fast float %239, %218
  %244 = add nsw i32 %219, 1
  %245 = icmp eq i32 %244, 2
  br i1 %245, label %246, label %214

; <label>:246                                     ; preds = %214
  %247 = add nsw i32 %213, 1
  %248 = icmp eq i32 %247, 2
  br i1 %248, label %249, label %208

; <label>:249                                     ; preds = %246
  %250 = fadd fast float %240, 5.000000e-01
  %251 = fadd fast float %241, 5.000000e-01
  %252 = fadd fast float %242, 5.000000e-01
  %253 = fadd fast float %243, 5.000000e-01
  br label %256

; <label>:254                                     ; preds = %73
  br label %256

; <label>:255                                     ; preds = %119
  br label %256

; <label>:256                                     ; preds = %255, %254, %249, %203, %186
  %257 = phi float [ %199, %186 ], [ %250, %249 ], [ %21, %203 ], [ %67, %254 ], [ %113, %255 ]
  %258 = phi float [ %200, %186 ], [ %251, %249 ], [ %22, %203 ], [ %68, %254 ], [ %114, %255 ]
  %259 = phi float [ %201, %186 ], [ %252, %249 ], [ %23, %203 ], [ %69, %254 ], [ %115, %255 ]
  %260 = phi float [ %202, %186 ], [ %253, %249 ], [ %24, %203 ], [ %70, %254 ], [ %116, %255 ]
  %261 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %262 = extractvalue %dx.types.CBufRet.f32 %261, 3
  %263 = fmul fast float %262, %257
  %264 = fmul fast float %262, %258
  %265 = fmul fast float %262, %259
  %266 = fadd fast float %263, -5.000000e-01
  %267 = fadd fast float %264, -5.000000e-01
  %268 = fadd fast float %265, -5.000000e-01
  %269 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %270 = extractvalue %dx.types.CBufRet.f32 %269, 0
  %271 = fmul fast float %266, %270
  %272 = fmul fast float %267, %270
  %273 = fmul fast float %268, %270
  %274 = fadd fast float %271, 5.000000e-01
  %275 = fadd fast float %272, 5.000000e-01
  %276 = fadd fast float %273, 5.000000e-01
  %277 = fcmp fast olt float %275, %276
  %278 = select i1 %277, float 0.000000e+00, float 1.000000e+00
  %279 = fsub fast float %275, %276
  %280 = fsub fast float %276, %275
  %281 = fmul fast float %278, %279
  %282 = fmul fast float %278, %280
  %283 = fadd fast float %281, %276
  %284 = fadd fast float %282, %275
  %285 = fadd fast float %278, -1.000000e+00
  %286 = fsub fast float 0x3FE5555560000000, %278
  %287 = fcmp fast olt float %274, %283
  %288 = select i1 %287, float 0.000000e+00, float 1.000000e+00
  %289 = fsub fast float %274, %283
  %290 = fsub fast float %285, %286
  %291 = fsub fast float %283, %274
  %292 = fmul fast float %288, %289
  %293 = fmul fast float %288, %290
  %294 = fmul fast float %288, %291
  %295 = fadd fast float %292, %283
  %296 = fadd fast float %294, %274
  %297 = call float @dx.op.binary.f32(i32 36, float %296, float %284)  ; FMin(a,b)
  %298 = fsub fast float %295, %297
  %299 = fsub fast float %296, %284
  %300 = fmul fast float %298, 6.000000e+00
  %301 = fadd fast float %300, 0x3DDB7CDFE0000000
  %302 = fdiv fast float %299, %301
  %303 = fadd fast float %286, %302
  %304 = fadd fast float %303, %293
  %305 = call float @dx.op.unary.f32(i32 6, float %304)  ; FAbs(value)
  %306 = fadd fast float %295, 0x3DDB7CDFE0000000
  %307 = fdiv fast float %298, %306
  %308 = extractvalue %dx.types.CBufRet.f32 %269, 1
  %309 = fmul fast float %307, %308
  %310 = fadd fast float %305, 1.000000e+00
  %311 = fadd fast float %305, 0x3FE5555560000000
  %312 = fadd fast float %305, 0x3FD5555560000000
  %313 = call float @dx.op.unary.f32(i32 22, float %310)  ; Frc(value)
  %314 = call float @dx.op.unary.f32(i32 22, float %311)  ; Frc(value)
  %315 = call float @dx.op.unary.f32(i32 22, float %312)  ; Frc(value)
  %316 = fmul fast float %313, 6.000000e+00
  %317 = fmul fast float %314, 6.000000e+00
  %318 = fmul fast float %315, 6.000000e+00
  %319 = fadd fast float %316, -3.000000e+00
  %320 = fadd fast float %317, -3.000000e+00
  %321 = fadd fast float %318, -3.000000e+00
  %322 = call float @dx.op.unary.f32(i32 6, float %319)  ; FAbs(value)
  %323 = call float @dx.op.unary.f32(i32 6, float %320)  ; FAbs(value)
  %324 = call float @dx.op.unary.f32(i32 6, float %321)  ; FAbs(value)
  %325 = fadd fast float %322, -1.000000e+00
  %326 = fadd fast float %323, -1.000000e+00
  %327 = fadd fast float %324, -1.000000e+00
  %328 = call float @dx.op.binary.f32(i32 35, float %325, float 0.000000e+00)  ; FMax(a,b)
  %329 = call float @dx.op.binary.f32(i32 35, float %326, float 0.000000e+00)  ; FMax(a,b)
  %330 = call float @dx.op.binary.f32(i32 35, float %327, float 0.000000e+00)  ; FMax(a,b)
  %331 = call float @dx.op.binary.f32(i32 36, float %328, float 1.000000e+00)  ; FMin(a,b)
  %332 = call float @dx.op.binary.f32(i32 36, float %329, float 1.000000e+00)  ; FMin(a,b)
  %333 = call float @dx.op.binary.f32(i32 36, float %330, float 1.000000e+00)  ; FMin(a,b)
  %334 = fadd fast float %331, -1.000000e+00
  %335 = fadd fast float %332, -1.000000e+00
  %336 = fadd fast float %333, -1.000000e+00
  %337 = fmul fast float %334, %309
  %338 = fmul fast float %335, %309
  %339 = fmul fast float %336, %309
  %340 = fadd fast float %337, 1.000000e+00
  %341 = fadd fast float %338, 1.000000e+00
  %342 = fadd fast float %339, 1.000000e+00
  %343 = fmul fast float %340, %295
  %344 = fmul fast float %341, %295
  %345 = fmul fast float %342, %295
  %346 = extractvalue %dx.types.CBufRet.f32 %269, 2
  %347 = call float @dx.op.unary.f32(i32 6, float %343)  ; FAbs(value)
  %348 = call float @dx.op.unary.f32(i32 6, float %344)  ; FAbs(value)
  %349 = call float @dx.op.unary.f32(i32 6, float %345)  ; FAbs(value)
  %350 = call float @dx.op.unary.f32(i32 23, float %347)  ; Log(value)
  %351 = call float @dx.op.unary.f32(i32 23, float %348)  ; Log(value)
  %352 = call float @dx.op.unary.f32(i32 23, float %349)  ; Log(value)
  %353 = fmul fast float %350, %346
  %354 = fmul fast float %351, %346
  %355 = fmul fast float %352, %346
  %356 = call float @dx.op.unary.f32(i32 21, float %353)  ; Exp(value)
  %357 = call float @dx.op.unary.f32(i32 21, float %354)  ; Exp(value)
  %358 = call float @dx.op.unary.f32(i32 21, float %355)  ; Exp(value)
  %359 = call float @dx.op.unary.f32(i32 7, float %356)  ; Saturate(value)
  %360 = call float @dx.op.unary.f32(i32 7, float %357)  ; Saturate(value)
  %361 = call float @dx.op.unary.f32(i32 7, float %358)  ; Saturate(value)
  %362 = call float @dx.op.unary.f32(i32 7, float %260)  ; Saturate(value)
  call void @dx.op.textureStore.f32(i32 67, %dx.types.Handle %1, i32 %5, i32 %6, i32 undef, float %359, float %360, float %361, float %362, i8 15)  ; TextureStore(srv,coord0,coord1,coord2,value0,value1,value2,value3,mask)
  br label %363

; <label>:363                                     ; preds = %256, %10, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind
declare void @dx.op.textureStore.f32(i32, %dx.types.Handle, i32, i32, i32, float, float, float, float, i8) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #1

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind readonly }
attributes #2 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!14}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{!5, !8, !10, !12}
!5 = !{!6}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{!9}
!9 = !{i32 0, %"class.RWTexture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i1 false, i1 false, i1 false, !7}
!10 = !{!11}
!11 = !{i32 0, %ProcessingParams* undef, !"", i32 0, i32 0, i32 1, i32 32, null}
!12 = !{!13}
!13 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!14 = !{void ()* @main, !"main", null, !4, !15}
!15 = !{i32 4, !16}
!16 = !{i32 8, i32 8, i32 1}
!17 = !{!18, !18, i64 0}
!18 = !{!"float", !19, i64 0}
!19 = !{!"omnipotent char", !20, i64 0}
!20 = !{!"Simple C/C++ TBAA"}
