// PBR Shading Pixel Shader
// Tests physically based rendering with Cook-Torrance BRDF

cbuffer Material : register(b0)
{
    float3 Albedo;
    float Metallic;
    float Roughness;
    float AO;
    float3 EmissiveColor;
    float EmissiveStrength;
};

cbuffer Lighting : register(b1)
{
    float3 LightPositions[4];
    float3 LightColors[4];
    float LightIntensities[4];
    int NumLights;
};

Texture2D AlbedoTexture : register(t0);
Texture2D NormalTexture : register(t1);
Texture2D MetallicTexture : register(t2);
Texture2D RoughnessTexture : register(t3);
Texture2D AOTexture : register(t4);
Texture2D EmissiveTexture : register(t5);
TextureCube EnvironmentMap : register(t6);
SamplerState LinearSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float3 Tangent : TEXCOORD2;
    float3 Bitangent : TEXCOORD3;
    float2 TexCoord : TEXCOORD4;
    float4 Color : TEXCOORD5;
    float3 ViewDir : TEXCOORD6;
    float4 LightSpacePos : TEXCOORD7;
};

static const float PI = 3.14159265359;

float3 getNormalFromMap(float2 texCoord, float3 worldNormal, float3 worldTangent, float3 worldBitangent)
{
    float3 tangentNormal = NormalTexture.Sample(LinearSampler, texCoord).xyz * 2.0 - 1.0;
    
    float3x3 TBN = float3x3(worldTangent, worldBitangent, worldNormal);
    return normalize(mul(tangentNormal, TBN));
}

float DistributionGGX(float3 N, float3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;
    
    return num / denom;
}

float GeometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float GeometrySmith(float3 N, float3 V, float3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

float3 fresnelSchlick(float cosTheta, float3 F0)
{
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

float4 main(PSInput input) : SV_TARGET
{
    // Sample material textures
    float3 albedo = pow(AlbedoTexture.Sample(LinearSampler, input.TexCoord).rgb * Albedo, 2.2);
    float metallic = MetallicTexture.Sample(LinearSampler, input.TexCoord).r * Metallic;
    float roughness = RoughnessTexture.Sample(LinearSampler, input.TexCoord).r * Roughness;
    float ao = AOTexture.Sample(LinearSampler, input.TexCoord).r * AO;
    float3 emissive = EmissiveTexture.Sample(LinearSampler, input.TexCoord).rgb * EmissiveColor * EmissiveStrength;
    
    // Get normal from normal map
    float3 N = getNormalFromMap(input.TexCoord, input.Normal, input.Tangent, input.Bitangent);
    float3 V = normalize(input.ViewDir);
    
    // Calculate reflectance at normal incidence
    float3 F0 = lerp(float3(0.04, 0.04, 0.04), albedo, metallic);
    
    // Reflectance equation
    float3 Lo = float3(0.0, 0.0, 0.0);
    
    // Calculate lighting for each light source
    for(int i = 0; i < NumLights && i < 4; ++i)
    {
        float3 L = normalize(LightPositions[i] - input.WorldPos);
        float3 H = normalize(V + L);
        float distance = length(LightPositions[i] - input.WorldPos);
        float attenuation = 1.0 / (distance * distance);
        float3 radiance = LightColors[i] * LightIntensities[i] * attenuation;
        
        // Cook-Torrance BRDF
        float NDF = DistributionGGX(N, H, roughness);
        float G = GeometrySmith(N, V, L, roughness);
        float3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
        
        float3 kS = F;
        float3 kD = float3(1.0, 1.0, 1.0) - kS;
        kD *= 1.0 - metallic;
        
        float3 numerator = NDF * G * F;
        float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.0001;
        float3 specular = numerator / denominator;
        
        float NdotL = max(dot(N, L), 0.0);
        Lo += (kD * albedo / PI + specular) * radiance * NdotL;
    }
    
    // Ambient lighting (simplified IBL)
    float3 ambient = EnvironmentMap.Sample(LinearSampler, N).rgb * albedo * ao;
    
    float3 color = ambient + Lo + emissive;
    
    // HDR tonemapping
    color = color / (color + float3(1.0, 1.0, 1.0));
    
    // Gamma correction
    color = pow(color, 1.0/2.2);
    
    return float4(color, 1.0);
}
