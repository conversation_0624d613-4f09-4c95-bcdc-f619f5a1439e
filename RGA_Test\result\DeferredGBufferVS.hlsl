// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float3 Tangent : TANGENT;
  float3 Bitangent : BITANGENT;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float4 Color : TEXCOORD5;
  float Depth : TEXCOORD6;
};

cbuffer PerFrame : register(b0)
{
  float4x4 WorldMatrix;
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 WorldViewProjectionMatrix;
  float4x4 NormalMatrix;
  float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4 worldPos = mul(float4(input.Position, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  output.Position = mul(float4(input.Position, 1.0f), WorldViewProjectionMatrix);
  output.Normal = normalize(mul(input.Normal, (float3x3)NormalMatrix));
  output.Tangent = normalize(mul(input.Tangent, (float3x3)NormalMatrix));
  output.Bitangent = normalize(mul(input.Bitangent, (float3x3)NormalMatrix));
  output.TexCoord = input.TexCoord;
  output.Color = input.Color;
  output.Depth = output.Position.z / output.Position.w;
  return output;
}

