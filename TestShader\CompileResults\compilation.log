﻿=== Shader Compilation Log ===
Start time: 07/31/2025 15:03:14

Processing HLSL: AdvancedDataStructuresPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: AdvancedDataStructuresVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: BasicLightingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: BasicLightingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: DeferredGBufferPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: DeferredGBufferVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: DeferredLightingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: DeferredLightingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: GeometryTessellationPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: GeometryTessellationVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ImageProcessingCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: InstancedRenderingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: InstancedRenderingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: MeshGenerationCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: MeshViewerPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: MeshViewerVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ParallelReductionCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ParticleSimulationCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ParticleUpdateCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: PbrShadingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: PbrShadingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: PostProcessingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: PostProcessingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: RayTracingTestCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ShadowMappingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: ShadowMappingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: SimplePS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: SimpleVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: StressTestCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: TestShaderPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: TestShaderVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: VolumeTextureCS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: VolumetricRenderingPS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: VolumetricRenderingVS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: WaterSurfacePS.hlsl
  HLSL Compilation: SUCCESS

Processing HLSL: WaterSurfaceVS.hlsl
  HLSL Compilation: SUCCESS

Processing GLSL: BasicLightingGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: BasicLightingGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: ComplexGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: ComplexGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: GeometryExpansionGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: GeometryExpansionGL.geom
  GLSL Analysis: SUCCESS

Processing GLSL: GeometryExpansionGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: ParticleSimulationGL.comp
  GLSL Analysis: SUCCESS

Processing GLSL: PbrAdvancedGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: PbrAdvancedGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: PerformanceTestGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: PerformanceTestGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: ShadowMappingGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: ShadowMappingGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: SimpleGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: SimpleGL.vert
  GLSL Analysis: SUCCESS

Processing GLSL: TessellationGL.frag
  GLSL Analysis: SUCCESS

Processing GLSL: TessellationGL.tesc
  GLSL Analysis: SUCCESS

Processing GLSL: TessellationGL.tese
  GLSL Analysis: SUCCESS

Processing GLSL: TessellationGL.vert
  GLSL Analysis: SUCCESS


=== Compilation Statistics ===
Total files processed: 56

HLSL files: 36
  - Successful: 36
  - Failed: 0
  - Success rate: 100%

GLSL files: 20
  - Successful: 20
  - Failed: 0
  - Success rate: 100%

Overall success rate: 100%
End time: 07/31/2025 15:03:39
=== Compilation Complete ===
