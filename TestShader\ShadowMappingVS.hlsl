// Shadow Mapping Vertex Shader
// Tests shadow map generation and depth rendering

cbuffer PerFrame : register(b0)
{
    float4x4 WorldMatrix;
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 LightViewProjectionMatrix;
    float3 LightPosition;
    float3 CameraPosition;
};

struct VSInput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float2 TexCoord : TEXCOORD0;
    float4 Color : COLOR0;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float4 Color : TEXCOORD3;
    float4 LightSpacePos : TEXCOORD4;
    float3 LightDir : TEXCOORD5;
    float3 ViewDir : TEXCOORD6;
    float Depth : TEXCOORD7;
};

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Transform to world space
    float4 worldPos = mul(float4(input.Position, 1.0), WorldMatrix);
    output.WorldPos = worldPos.xyz;
    
    // Transform to clip space
    float4 viewPos = mul(worldPos, ViewMatrix);
    output.Position = mul(viewPos, ProjectionMatrix);
    
    // Transform normal to world space
    output.Normal = normalize(mul(input.Normal, (float3x3)WorldMatrix));
    
    // Pass through texture coordinates and color
    output.TexCoord = input.TexCoord;
    output.Color = input.Color;
    
    // Calculate light space position for shadow mapping
    output.LightSpacePos = mul(worldPos, LightViewProjectionMatrix);
    
    // Calculate light direction
    output.LightDir = normalize(LightPosition - output.WorldPos);
    
    // Calculate view direction
    output.ViewDir = normalize(CameraPosition - output.WorldPos);
    
    // Store depth for shadow comparison
    output.Depth = output.Position.z / output.Position.w;
    
    return output;
}
