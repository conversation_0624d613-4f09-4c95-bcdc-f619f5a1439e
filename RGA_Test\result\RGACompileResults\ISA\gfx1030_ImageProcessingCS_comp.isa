_amdgpu_cs_main:
	s_mov_b32 s12, s1                                          // 000000000000: BE8C0301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v4, s2, 3, v0                               // 000000000008: D7460004 04010602
	s_mov_b32 s13, s1                                          // 000000000010: BE8D0301
	s_mov_b64 s[4:5], exec                                     // 000000000014: BE84047E
	s_load_dwordx4 s[8:11], s[12:13], null                     // 000000000018: F4080206 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s0, s[8:11], null                      // 000000000024: F4200004 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cmpx_gt_u32_e64 s0, v4                                   // 000000000030: D4D4007E 00020800
	s_cbranch_execz _L0                                        // 000000000038: BF880308
	s_buffer_load_dword s1, s[8:11], 0x4                       // 00000000003C: F4200044 FA000004
	v_lshl_add_u32 v5, s3, 3, v1                               // 000000000044: D7460005 04050603
	s_waitcnt lgkmcnt(0)                                       // 00000000004C: BF8CC07F
	v_cmp_gt_u32_e32 vcc_lo, s1, v5                            // 000000000050: 7D880A01
	s_and_b64 exec, exec, vcc                                  // 000000000054: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000058: BF880300
	s_buffer_load_dword s16, s[8:11], 0x1c                     // 00000000005C: F4200404 FA00001C
	v_cvt_f32_u32_e32 v8, s0                                   // 000000000064: 7E100C00
	v_cvt_f32_u32_e32 v9, s1                                   // 000000000068: 7E120C01
	s_clause 0x1                                               // 00000000006C: BFA10001
	s_load_dwordx8 s[0:7], s[12:13], null                      // 000000000070: F40C0006 FA000000
	s_load_dwordx4 s[12:15], s[12:13], 0x20                    // 000000000078: F4080306 FA000020
	v_cvt_f32_u32_e32 v2, v4                                   // 000000000080: 7E040D04
	v_cvt_f32_u32_e32 v3, v5                                   // 000000000084: 7E060D05
	v_rcp_iflag_f32_e32 v0, v8                                 // 000000000088: 7E005708
	v_rcp_iflag_f32_e32 v1, v9                                 // 00000000008C: 7E025709
	v_mul_f32_e32 v6, v2, v0                                   // 000000000090: 100C0102
	v_mul_f32_e32 v7, v3, v1                                   // 000000000094: 100E0303
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	s_cmp_lg_u32 s16, 0                                        // 00000000009C: BF078010
	s_cbranch_scc0 _L1                                         // 0000000000A0: BF84005C
	s_cmp_lg_u32 s16, 1                                        // 0000000000A4: BF078110
	s_cbranch_scc0 _L2                                         // 0000000000A8: BF84005B
	s_cmp_lg_u32 s16, 2                                        // 0000000000AC: BF078210
	s_cbranch_scc0 _L3                                         // 0000000000B0: BF84005A
	image_sample_lz v[0:3], v[6:7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000B4: F09C0F08 00600006
	s_cmp_lg_u32 s16, 3                                        // 0000000000BC: BF078310
	s_cbranch_scc1 _L4                                         // 0000000000C0: BF850052
	v_rcp_f32_e32 v18, v8                                      // 0000000000C4: 7E245508
	v_rcp_f32_e32 v14, v9                                      // 0000000000C8: 7E1C5509
	v_sub_f32_e32 v21, v6, v18                                 // 0000000000CC: 082A2506
	v_fma_f32 v23, 0, v14, v7                                  // 0000000000D0: D54B0017 041E1C80
	v_sub_f32_e32 v22, v7, v14                                 // 0000000000D8: 082C1D07
	v_fma_f32 v24, 0, v18, v6                                  // 0000000000DC: D54B0018 041A2480
	v_add_f32_e32 v25, v14, v7                                 // 0000000000E4: 06320F0E
	s_clause 0x1                                               // 0000000000E8: BFA10001
	image_sample_lz  v[0:3], [v21, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000EC: F09C0F0A 00600015 00000017
	image_sample_lz  v[10:13], [v24, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000F8: F09C0F0A 00600A18 00000016
	v_add_f32_e32 v26, v18, v6                                 // 000000000104: 06340D12
	s_clause 0x1                                               // 000000000108: BFA10001
	image_sample_lz v[14:17], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000010C: F09C0F08 00600E15
	image_sample_lz  v[18:21], [v21, v25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000114: F09C0F0A 00601215 00000019
	s_waitcnt vmcnt(2)                                         // 000000000120: BF8C3F72
	v_add_f32_e32 v27, v10, v0                                 // 000000000124: 0636010A
	v_add_f32_e32 v28, v11, v1                                 // 000000000128: 0638030B
	v_add_f32_e32 v29, v12, v2                                 // 00000000012C: 063A050C
	v_add_f32_e32 v30, v13, v3                                 // 000000000130: 063C070D
	s_clause 0x1                                               // 000000000134: BFA10001
	image_sample_lz  v[0:3], [v26, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000138: F09C0F0A 0060001A 00000016
	image_sample_lz  v[10:13], [v26, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000144: F09C0F0A 00600A1A 00000017
	s_waitcnt vmcnt(1)                                         // 000000000150: BF8C3F71
	v_fmac_f32_e32 v3, 0, v17                                  // 000000000154: 56062280
	v_fma_f32 v14, 0, v14, v0                                  // 000000000158: D54B000E 04021C80
	v_fma_f32 v22, 0, v15, v1                                  // 000000000160: D54B0016 04061E80
	v_fma_f32 v31, 0, v16, v2                                  // 000000000168: D54B001F 040A2080
	v_sub_f32_e32 v30, v3, v30                                 // 000000000170: 083C3D03
	image_sample_lz  v[0:3], [v24, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000174: F09C0F0A 00600018 00000017
	v_sub_f32_e32 v27, v14, v27                                // 000000000180: 0836370E
	image_sample_lz v[14:17], v[24:25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000184: F09C0F08 00600E18
	v_sub_f32_e32 v28, v22, v28                                // 00000000018C: 08383916
	image_sample_lz  v[22:25], [v26, v25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000190: F09C0F0A 0060161A 00000019
	v_sub_f32_e32 v29, v31, v29                                // 00000000019C: 083A3B1F
	s_waitcnt vmcnt(2)                                         // 0000000001A0: BF8C3F72
	v_add_f32_e32 v0, v0, v27                                  // 0000000001A4: 06003700
	v_add_f32_e32 v1, v1, v28                                  // 0000000001A8: 06023901
	v_add_f32_e32 v2, v2, v29                                  // 0000000001AC: 06043B02
	v_add_f32_e32 v3, v3, v30                                  // 0000000001B0: 06063D03
	v_add_f32_e32 v0, v10, v0                                  // 0000000001B4: 0600010A
	v_add_f32_e32 v1, v11, v1                                  // 0000000001B8: 0602030B
	v_add_f32_e32 v2, v12, v2                                  // 0000000001BC: 0604050C
	v_add_f32_e32 v3, v13, v3                                  // 0000000001C0: 0606070D
	v_add_f32_e32 v0, v18, v0                                  // 0000000001C4: 06000112
	v_add_f32_e32 v1, v19, v1                                  // 0000000001C8: 06020313
	v_add_f32_e32 v2, v20, v2                                  // 0000000001CC: 06040514
	v_add_f32_e32 v3, v21, v3                                  // 0000000001D0: 06060715
	s_waitcnt vmcnt(1)                                         // 0000000001D4: BF8C3F71
	v_add_f32_e32 v0, v14, v0                                  // 0000000001D8: 0600010E
	v_add_f32_e32 v1, v15, v1                                  // 0000000001DC: 0602030F
	v_add_f32_e32 v2, v16, v2                                  // 0000000001E0: 06040510
	v_add_f32_e32 v3, v17, v3                                  // 0000000001E4: 06060711
	s_waitcnt vmcnt(0)                                         // 0000000001E8: BF8C3F70
	v_fmac_f32_e32 v0, 0, v22                                  // 0000000001EC: 56002C80
	v_fmac_f32_e32 v1, 0, v23                                  // 0000000001F0: 56022E80
	v_fmac_f32_e32 v2, 0, v24                                  // 0000000001F4: 56043080
	v_fmac_f32_e32 v3, 0, v25                                  // 0000000001F8: 56063280
	v_add_f32_e32 v0, 0.5, v0                                  // 0000000001FC: 060000F0
	v_add_f32_e32 v1, 0.5, v1                                  // 000000000200: 060202F0
	v_add_f32_e32 v2, 0.5, v2                                  // 000000000204: 060404F0
	v_add_f32_e32 v3, 0.5, v3                                  // 000000000208: 060606F0
_L4:
	s_mov_b64 s[16:17], 0                                      // 00000000020C: BE900480
	s_branch _L5                                               // 000000000210: BF820003
_L1:
	s_branch _L6                                               // 000000000214: BF8200E2
_L2:
	s_branch _L7                                               // 000000000218: BF820083
_L3:
	s_mov_b64 s[16:17], -1                                     // 00000000021C: BE9004C1
_L5:
	s_andn2_b64 vcc, exec, s[16:17]                            // 000000000220: 8AEA107E
	s_cbranch_vccnz _L8                                        // 000000000224: BF87007F
	v_rcp_f32_e32 v14, v8                                      // 000000000228: 7E1C5508
	v_rcp_f32_e32 v16, v9                                      // 00000000022C: 7E205509
	v_sub_f32_e32 v15, v6, v14                                 // 000000000230: 081E1D06
	v_sub_f32_e32 v24, v7, v16                                 // 000000000234: 08302107
	v_fma_f32 v23, 0, v14, v6                                  // 000000000238: D54B0017 041A1C80
	v_add_f32_e32 v17, v14, v6                                 // 000000000240: 06220D0E
	v_fma_f32 v18, 0, v16, v7                                  // 000000000244: D54B0012 041E2080
	v_add_f32_e32 v16, v16, v7                                 // 00000000024C: 06200F10
	s_clause 0x1                                               // 000000000250: BFA10001
	image_sample_lz  v[0:3], [v15, v24], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000254: F09C0F0A 0060000F 00000018
	image_sample_lz v[10:13], v[23:24], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000260: F09C0F08 00600A17
	s_waitcnt vmcnt(0)                                         // 000000000268: BF8C3F70
	v_fma_f32 v19, -2.0, v10, v0                               // 00000000026C: D54B0013 040214F5
	v_fma_f32 v20, -2.0, v11, v1                               // 000000000274: D54B0014 040616F5
	v_fma_f32 v21, -2.0, v12, v2                               // 00000000027C: D54B0015 040A18F5
	v_fma_f32 v22, -2.0, v13, v3                               // 000000000284: D54B0016 040E1AF5
	v_fma_f32 v10, v10, 0, -v0                                 // 00000000028C: D54B000A 8401010A
	v_fma_f32 v11, v11, 0, -v1                                 // 000000000294: D54B000B 8405010B
	v_fma_f32 v12, v12, 0, -v2                                 // 00000000029C: D54B000C 8409010C
	v_fma_f32 v13, v13, 0, -v3                                 // 0000000002A4: D54B000D 840D010D
	image_sample_lz  v[0:3], [v17, v24], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000002AC: F09C0F0A 00600011 00000018
	s_waitcnt vmcnt(0)                                         // 0000000002B8: BF8C3F70
	v_fmac_f32_e32 v19, 0, v0                                  // 0000000002BC: 56260080
	v_fmac_f32_e32 v20, 0, v1                                  // 0000000002C0: 56280280
	v_fmac_f32_e32 v21, 0, v2                                  // 0000000002C4: 562A0480
	v_fmac_f32_e32 v22, 0, v3                                  // 0000000002C8: 562C0680
	v_fmac_f32_e32 v10, 0, v0                                  // 0000000002CC: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 0000000002D0: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 0000000002D4: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 0000000002D8: 561A0680
	image_sample_lz  v[0:3], [v15, v18], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000002DC: F09C0F0A 0060000F 00000012
	s_waitcnt vmcnt(0)                                         // 0000000002E8: BF8C3F70
	v_fmac_f32_e32 v19, -2.0, v0                               // 0000000002EC: 562600F5
	v_fmac_f32_e32 v20, -2.0, v1                               // 0000000002F0: 562802F5
	v_fmac_f32_e32 v21, -2.0, v2                               // 0000000002F4: 562A04F5
	v_fmac_f32_e32 v22, -2.0, v3                               // 0000000002F8: 562C06F5
	v_fmac_f32_e32 v10, 0, v0                                  // 0000000002FC: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 000000000300: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 000000000304: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 000000000308: 561A0680
	image_sample_lz  v[0:3], [v23, v18], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000030C: F09C0F0A 00600017 00000012
	s_waitcnt vmcnt(0)                                         // 000000000318: BF8C3F70
	v_fmac_f32_e32 v19, 0, v0                                  // 00000000031C: 56260080
	v_fmac_f32_e32 v20, 0, v1                                  // 000000000320: 56280280
	v_fmac_f32_e32 v21, 0, v2                                  // 000000000324: 562A0480
	v_fmac_f32_e32 v22, 0, v3                                  // 000000000328: 562C0680
	v_fmac_f32_e32 v10, 0, v0                                  // 00000000032C: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 000000000330: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 000000000334: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 000000000338: 561A0680
	image_sample_lz v[0:3], v[17:18], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000033C: F09C0F08 00600011
	s_waitcnt vmcnt(0)                                         // 000000000344: BF8C3F70
	v_fmac_f32_e32 v19, 2.0, v0                                // 000000000348: 562600F4
	v_fmac_f32_e32 v20, 2.0, v1                                // 00000000034C: 562802F4
	v_fmac_f32_e32 v21, 2.0, v2                                // 000000000350: 562A04F4
	v_fmac_f32_e32 v22, 2.0, v3                                // 000000000354: 562C06F4
	v_fmac_f32_e32 v10, 0, v0                                  // 000000000358: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 00000000035C: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 000000000360: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 000000000364: 561A0680
	image_sample_lz v[0:3], v[15:16], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000368: F09C0F08 0060000F
	s_waitcnt vmcnt(0)                                         // 000000000370: BF8C3F70
	v_fmac_f32_e32 v19, 0, v0                                  // 000000000374: 56260080
	v_fmac_f32_e32 v20, 0, v1                                  // 000000000378: 56280280
	v_fmac_f32_e32 v21, 0, v2                                  // 00000000037C: 562A0480
	v_fmac_f32_e32 v22, 0, v3                                  // 000000000380: 562C0680
	v_fmac_f32_e32 v10, 0, v0                                  // 000000000384: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 000000000388: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 00000000038C: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 000000000390: 561A0680
	image_sample_lz  v[0:3], [v23, v16], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000394: F09C0F0A 00600017 00000010
	s_waitcnt vmcnt(0)                                         // 0000000003A0: BF8C3F70
	v_fmac_f32_e32 v19, 2.0, v0                                // 0000000003A4: 562600F4
	v_fmac_f32_e32 v20, 2.0, v1                                // 0000000003A8: 562802F4
	v_fmac_f32_e32 v21, 2.0, v2                                // 0000000003AC: 562A04F4
	v_fmac_f32_e32 v22, 2.0, v3                                // 0000000003B0: 562C06F4
	v_fmac_f32_e32 v10, 0, v0                                  // 0000000003B4: 56140080
	v_fmac_f32_e32 v11, 0, v1                                  // 0000000003B8: 56160280
	v_fmac_f32_e32 v12, 0, v2                                  // 0000000003BC: 56180480
	v_fmac_f32_e32 v13, 0, v3                                  // 0000000003C0: 561A0680
	image_sample_lz  v[0:3], [v17, v16], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000003C4: F09C0F0A 00600011 00000010
	s_waitcnt vmcnt(0)                                         // 0000000003D0: BF8C3F70
	v_sub_f32_e32 v14, v19, v0                                 // 0000000003D4: 081C0113
	v_sub_f32_e32 v15, v20, v1                                 // 0000000003D8: 081E0314
	v_sub_f32_e32 v16, v21, v2                                 // 0000000003DC: 08200515
	v_sub_f32_e32 v17, v22, v3                                 // 0000000003E0: 08220716
	v_add_f32_e32 v0, v0, v10                                  // 0000000003E4: 06001500
	v_add_f32_e32 v1, v1, v11                                  // 0000000003E8: 06021701
	v_add_f32_e32 v2, v2, v12                                  // 0000000003EC: 06041902
	v_add_f32_e32 v3, v3, v13                                  // 0000000003F0: 06061B03
	v_mul_f32_e32 v0, v0, v0                                   // 0000000003F4: 10000100
	v_mul_f32_e32 v1, v1, v1                                   // 0000000003F8: 10020301
	v_mul_f32_e32 v2, v2, v2                                   // 0000000003FC: 10040502
	v_mul_f32_e32 v3, v3, v3                                   // 000000000400: 10060703
	v_fmac_f32_e32 v0, v14, v14                                // 000000000404: 56001D0E
	v_fmac_f32_e32 v1, v15, v15                                // 000000000408: 56021F0F
	v_fmac_f32_e32 v2, v16, v16                                // 00000000040C: 56042110
	v_fmac_f32_e32 v3, v17, v17                                // 000000000410: 56062311
	v_sqrt_f32_e32 v0, v0                                      // 000000000414: 7E006700
	v_sqrt_f32_e32 v1, v1                                      // 000000000418: 7E026701
	v_sqrt_f32_e32 v2, v2                                      // 00000000041C: 7E046702
	v_sqrt_f32_e32 v3, v3                                      // 000000000420: 7E066703
_L8:
	s_cbranch_execnz _L9                                       // 000000000424: BF89005D
_L7:
	v_rcp_f32_e32 v14, v8                                      // 000000000428: 7E1C5508
	v_rcp_f32_e32 v15, v9                                      // 00000000042C: 7E1E5509
	v_sub_f32_e32 v18, v6, v14                                 // 000000000430: 08241D06
	v_fma_f32 v20, 0, v15, v7                                  // 000000000434: D54B0014 041E1E80
	v_sub_f32_e32 v19, v7, v15                                 // 00000000043C: 08261F07
	v_fma_f32 v21, 0, v14, v6                                  // 000000000440: D54B0015 041A1C80
	v_add_f32_e32 v22, v15, v7                                 // 000000000448: 062C0F0F
	s_clause 0x1                                               // 00000000044C: BFA10001
	image_sample_lz  v[0:3], [v18, v20], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000450: F09C0F0A 00600012 00000014
	image_sample_lz  v[10:13], [v21, v19], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000045C: F09C0F0A 00600A15 00000013
	v_add_f32_e32 v23, v14, v6                                 // 000000000468: 062E0D0E
	image_sample_lz  v[14:17], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000046C: F09C0F0A 00600E12 00000016
	s_waitcnt vmcnt(1)                                         // 000000000478: BF8C3F71
	v_add_f32_e32 v24, v10, v0                                 // 00000000047C: 0630010A
	v_add_f32_e32 v25, v11, v1                                 // 000000000480: 0632030B
	v_add_f32_e32 v26, v12, v2                                 // 000000000484: 0634050C
	v_add_f32_e32 v27, v13, v3                                 // 000000000488: 0636070D
	s_clause 0x1                                               // 00000000048C: BFA10001
	image_sample_lz  v[0:3], [v23, v19], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000490: F09C0F0A 00600017 00000013
	image_sample_lz  v[10:13], [v23, v20], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000049C: F09C0F0A 00600A17 00000014
	s_waitcnt vmcnt(1)                                         // 0000000004A8: BF8C3F71
	v_mul_f32_e32 v28, 0x40a00000, v0                          // 0000000004AC: 103800FF 40A00000
	v_mul_f32_e32 v29, 0x40a00000, v1                          // 0000000004B4: 103A02FF 40A00000
	v_mul_f32_e32 v30, 0x40a00000, v2                          // 0000000004BC: 103C04FF 40A00000
	v_mul_f32_e32 v31, 0x40a00000, v3                          // 0000000004C4: 103E06FF 40A00000
	image_sample_lz v[0:3], v[18:19], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004CC: F09C0F08 00600012
	s_waitcnt vmcnt(1)                                         // 0000000004D4: BF8C3F71
	v_add_f32_e32 v24, v24, v10                                // 0000000004D8: 06301518
	v_add_f32_e32 v25, v25, v11                                // 0000000004DC: 06321719
	v_add_f32_e32 v26, v26, v12                                // 0000000004E0: 0634191A
	v_add_f32_e32 v27, v27, v13                                // 0000000004E4: 06361B1B
	s_waitcnt vmcnt(0)                                         // 0000000004E8: BF8C3F70
	v_fmac_f32_e32 v28, 0, v0                                  // 0000000004EC: 56380080
	v_fmac_f32_e32 v29, 0, v1                                  // 0000000004F0: 563A0280
	v_fmac_f32_e32 v30, 0, v2                                  // 0000000004F4: 563C0480
	v_fmac_f32_e32 v31, 0, v3                                  // 0000000004F8: 563E0680
	s_clause 0x2                                               // 0000000004FC: BFA10002
	image_sample_lz  v[0:3], [v21, v20], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000500: F09C0F0A 00600015 00000014
	image_sample_lz v[10:13], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000050C: F09C0F08 00600A15
	image_sample_lz  v[18:21], [v23, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000514: F09C0F0A 00601217 00000016
	s_waitcnt vmcnt(2)                                         // 000000000520: BF8C3F72
	v_fmac_f32_e32 v28, 0x40a00000, v0                         // 000000000524: 563800FF 40A00000
	v_fmac_f32_e32 v29, 0x40a00000, v1                         // 00000000052C: 563A02FF 40A00000
	v_fmac_f32_e32 v30, 0x40a00000, v2                         // 000000000534: 563C04FF 40A00000
	v_fmac_f32_e32 v31, 0x40a00000, v3                         // 00000000053C: 563E06FF 40A00000
	s_waitcnt vmcnt(1)                                         // 000000000544: BF8C3F71
	v_add_f32_e32 v0, v24, v10                                 // 000000000548: 06001518
	v_add_f32_e32 v1, v25, v11                                 // 00000000054C: 06021719
	v_add_f32_e32 v2, v26, v12                                 // 000000000550: 0604191A
	v_add_f32_e32 v3, v27, v13                                 // 000000000554: 06061B1B
	v_fmac_f32_e32 v28, 0x40a00000, v14                        // 000000000558: 56381CFF 40A00000
	v_fmac_f32_e32 v29, 0x40a00000, v15                        // 000000000560: 563A1EFF 40A00000
	v_fmac_f32_e32 v30, 0x40a00000, v16                        // 000000000568: 563C20FF 40A00000
	v_fmac_f32_e32 v31, 0x40a00000, v17                        // 000000000570: 563E22FF 40A00000
	v_sub_f32_e32 v0, v28, v0                                  // 000000000578: 0800011C
	v_sub_f32_e32 v1, v29, v1                                  // 00000000057C: 0802031D
	v_sub_f32_e32 v2, v30, v2                                  // 000000000580: 0804051E
	v_sub_f32_e32 v3, v31, v3                                  // 000000000584: 0806071F
	s_waitcnt vmcnt(0)                                         // 000000000588: BF8C3F70
	v_fmac_f32_e32 v0, 0, v18                                  // 00000000058C: 56002480
	v_fmac_f32_e32 v1, 0, v19                                  // 000000000590: 56022680
	v_fmac_f32_e32 v2, 0, v20                                  // 000000000594: 56042880
	v_fmac_f32_e32 v3, 0, v21                                  // 000000000598: 56062A80
_L9:
	s_cbranch_execnz _L10                                      // 00000000059C: BF890145
_L6:
	s_buffer_load_dword s16, s[8:11], 0x8                      // 0000000005A0: F4200404 FA000008
	v_rcp_f32_e32 v12, v8                                      // 0000000005A8: 7E185508
	v_rcp_f32_e32 v14, v9                                      // 0000000005AC: 7E1C5509
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000005B0: BF8C0070
	v_mul_f32_e64 v0, s16, -2.0                                // 0000000005B4: D5080000 0001EA10
	v_fma_f32 v16, -s16, v12, v6                               // 0000000005BC: D54B0010 241A1810
	v_mul_f32_e64 v17, s16, 0                                  // 0000000005C4: D5080011 00010010
	v_fma_f32 v19, s16, v12, v6                                // 0000000005CC: D54B0013 041A1810
	v_add_f32_e64 v20, s16, s16                                // 0000000005D4: D5030014 00002010
	v_fma_f32 v21, v0, v12, v6                                 // 0000000005DC: D54B0015 041A1900
	v_fma_f32 v22, v0, v14, v7                                 // 0000000005E4: D54B0016 041E1D00
	v_fma_f32 v18, v17, v12, v6                                // 0000000005EC: D54B0012 041A1911
	v_fmac_f32_e32 v6, v20, v12                                // 0000000005F4: 560C1914
	s_clause 0x1                                               // 0000000005F8: BFA10001
	image_sample_lz v[0:3], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000005FC: F09C0F08 00600015
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000604: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(1)                                         // 000000000610: BF8C3F71
	v_mul_f32_e32 v0, 0x3d756649, v0                           // 000000000614: 100000FF 3D756649
	v_mul_f32_e32 v1, 0x3d756649, v1                           // 00000000061C: 100202FF 3D756649
	v_mul_f32_e32 v2, 0x3d756649, v2                           // 000000000624: 100404FF 3D756649
	v_mul_f32_e32 v3, 0x3d756649, v3                           // 00000000062C: 100606FF 3D756649
	s_waitcnt vmcnt(0)                                         // 000000000634: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000638: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000640: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000648: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000650: 560616FF 3C761240
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000658: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000664: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 000000000668: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000670: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000678: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000680: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000688: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000694: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000698: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 0000000006A0: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 0000000006A8: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 0000000006B0: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000006B8: F09C0F0A 00600806 00000016
	v_fma_f32 v22, -s16, v14, v7                               // 0000000006C4: D54B0016 241E1C10
	s_waitcnt vmcnt(0)                                         // 0000000006CC: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 0000000006D0: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 0000000006D8: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 0000000006E0: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 0000000006E8: 560616FF 3E19F341
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000006F0: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 0000000006F8: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000006FC: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000704: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 00000000070C: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000714: 560616FF 3C761240
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000071C: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 000000000728: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 00000000072C: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000734: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 00000000073C: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000744: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000074C: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000758: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 00000000075C: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000764: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 00000000076C: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000774: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000077C: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000788: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 00000000078C: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 000000000794: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 00000000079C: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 0000000007A4: 560616FF 3E19F341
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000007AC: F09C0F0A 00600806 00000016
	v_fma_f32 v22, v17, v14, v7                                // 0000000007B8: D54B0016 041E1D11
	s_waitcnt vmcnt(0)                                         // 0000000007C0: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 0000000007C4: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 0000000007CC: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 0000000007D4: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 0000000007DC: 560616FF 3DC25E9A
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000007E4: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 0000000007EC: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 0000000007F0: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 0000000007F8: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000800: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000808: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000810: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 00000000081C: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000820: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000828: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000830: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000838: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000840: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 00000000084C: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 000000000850: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 000000000858: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 000000000860: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 000000000868: 560616FF 3E19F341
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000870: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 00000000087C: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000880: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000888: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000890: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000898: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000008A0: F09C0F0A 00600806 00000016
	v_fma_f32 v22, s16, v14, v7                                // 0000000008AC: D54B0016 041E1C10
	v_fmac_f32_e32 v7, v20, v14                                // 0000000008B4: 560E1D14
	s_waitcnt vmcnt(0)                                         // 0000000008B8: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 0000000008BC: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 0000000008C4: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 0000000008CC: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 0000000008D4: 560616FF 3CC2E771
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000008DC: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 0000000008E4: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 0000000008E8: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 0000000008F0: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 0000000008F8: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000900: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000908: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 000000000914: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 000000000918: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 000000000920: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 000000000928: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 000000000930: 560616FF 3E19F341
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000938: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000944: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000948: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000950: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000958: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000960: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000968: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000974: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 000000000978: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000980: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000988: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000990: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000998: F09C0F0A 00600806 00000016
	s_waitcnt vmcnt(0)                                         // 0000000009A4: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000009A8: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 0000000009B0: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 0000000009B8: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 0000000009C0: 560616FF 3C761240
	image_sample_lz  v[8:11], [v21, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000009C8: F09C0F0A 00600815 00000007
	s_waitcnt vmcnt(0)                                         // 0000000009D4: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 0000000009D8: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 0000000009E0: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 0000000009E8: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 0000000009F0: 560616FF 3E19F341
	image_sample_lz  v[8:11], [v16, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000009F8: F09C0F0A 00600810 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A04: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000A08: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000A10: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000A18: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000A20: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v18, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A28: F09C0F0A 00600812 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A34: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 000000000A38: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000A40: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000A48: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000A50: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v19, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A58: F09C0F0A 00600813 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A64: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000A68: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000A70: 560212FF 3C761240
	image_sample_lz v[6:9], v[6:7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A78: F09C0F08 00600606
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000A80: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000A88: 560616FF 3C761240
	s_waitcnt vmcnt(0)                                         // 000000000A90: BF8C3F70
	v_fmac_f32_e32 v0, 0x3d756649, v6                          // 000000000A94: 56000CFF 3D756649
	v_fmac_f32_e32 v1, 0x3d756649, v7                          // 000000000A9C: 56020EFF 3D756649
	v_fmac_f32_e32 v2, 0x3d756649, v8                          // 000000000AA4: 560410FF 3D756649
	v_fmac_f32_e32 v3, 0x3d756649, v9                          // 000000000AAC: 560612FF 3D756649
_L10:
	s_buffer_load_dwordx4 s[8:11], s[8:11], 0xc                // 000000000AB4: F4280204 FA00000C
	s_waitcnt vmcnt(0)                                         // 000000000ABC: BF8C3F70
	v_max_f32_e64 v3, v3, v3 clamp                             // 000000000AC0: D5108003 00020703
	s_waitcnt lgkmcnt(0)                                       // 000000000AC8: BF8CC07F
	v_mul_f32_e64 v6, s9, 0.5                                  // 000000000ACC: D5080006 0001E009
	v_fma_f32 v7, v1, s8, -v6                                  // 000000000AD4: D54B0007 84181101
	v_fma_f32 v8, v2, s8, -v6                                  // 000000000ADC: D54B0008 84181102
	v_mul_f32_e32 v1, s8, v1                                   // 000000000AE4: 10020208
	v_fma_f32 v0, v0, s8, -v6                                  // 000000000AE8: D54B0000 84181100
	v_add_f32_e32 v7, 0.5, v7                                  // 000000000AF0: 060E0EF0
	v_add_f32_e32 v8, 0.5, v8                                  // 000000000AF4: 061010F0
	v_fma_f32 v6, -v2, s8, v1                                  // 000000000AF8: D54B0006 24041102
	v_add_f32_e32 v0, 0.5, v0                                  // 000000000B00: 060000F0
	v_fma_f32 v1, v2, s8, -v1                                  // 000000000B04: D54B0001 84041102
	s_mov_b32 s8, 0x40c00000                                   // 000000000B0C: BE8803FF 40C00000
	v_cmp_lt_f32_e32 vcc_lo, v7, v8                            // 000000000B14: 7C021107
	v_cndmask_b32_e64 v9, 1.0, 0, vcc_lo                       // 000000000B18: D5010009 01A900F2
	v_fmac_f32_e32 v8, v9, v6                                  // 000000000B20: 56100D09
	v_fmac_f32_e32 v7, v9, v1                                  // 000000000B24: 560E0309
	v_cmp_lt_f32_e32 vcc_lo, v0, v8                            // 000000000B28: 7C021100
	v_sub_f32_e32 v6, v8, v0                                   // 000000000B2C: 080C0108
	v_sub_f32_e32 v1, v0, v8                                   // 000000000B30: 08021100
	v_cndmask_b32_e64 v2, 1.0, 0, vcc_lo                       // 000000000B34: D5010002 01A900F2
	v_fmac_f32_e32 v0, v2, v6                                  // 000000000B3C: 56000D02
	v_add_f32_e32 v6, -1.0, v9                                 // 000000000B40: 060C12F3
	v_sub_f32_e32 v9, 0x3f2aaaab, v9                           // 000000000B44: 081212FF 3F2AAAAB
	v_fmac_f32_e32 v8, v2, v1                                  // 000000000B4C: 56100302
	v_min_f32_e32 v1, v0, v7                                   // 000000000B50: 1E020F00
	v_add_f32_e32 v0, 0x2edbe6ff, v0                           // 000000000B54: 060000FF 2EDBE6FF
	v_sub_f32_e32 v6, v6, v9                                   // 000000000B5C: 080C1306
	v_sub_f32_e32 v1, v8, v1                                   // 000000000B60: 08020308
	v_fmac_f32_e32 v9, v2, v6                                  // 000000000B64: 56120D02
	v_mul_f32_e32 v6, 0xbe2aaaab, v7                           // 000000000B68: 100C0EFF BE2AAAAB
	v_rcp_f32_e32 v7, v8                                       // 000000000B70: 7E0E5508
	v_rcp_f32_e32 v2, v1                                       // 000000000B74: 7E045501
	v_add_f32_e32 v0, v0, v9                                   // 000000000B78: 06001300
	v_fmaak_f32 v1, v1, v7, 0x2edbe6ff                         // 000000000B7C: 5A020F01 2EDBE6FF
	v_fmac_f32_e32 v0, v6, v2                                  // 000000000B84: 56000506
	v_mul_f32_e32 v1, s10, v1                                  // 000000000B88: 1002020A
	v_add_f32_e64 v2, |v0|, 1.0                                // 000000000B8C: D5030102 0001E500
	v_add_f32_e64 v6, 0x3f2aaaab, |v0|                         // 000000000B94: D5030206 000200FF 3F2AAAAB
	v_add_f32_e64 v0, 0x3eaaaaab, |v0|                         // 000000000BA0: D5030200 000200FF 3EAAAAAB
	v_fract_f32_e32 v2, v2                                     // 000000000BAC: 7E044102
	v_fract_f32_e32 v6, v6                                     // 000000000BB0: 7E0C4106
	v_fract_f32_e32 v0, v0                                     // 000000000BB4: 7E004100
	v_fmaak_f32 v2, s8, v2, 0xc0400000                         // 000000000BB8: 5A040408 C0400000
	v_fmaak_f32 v6, s8, v6, 0xc0400000                         // 000000000BC0: 5A0C0C08 C0400000
	v_fmaak_f32 v0, s8, v0, 0xc0400000                         // 000000000BC8: 5A000008 C0400000
	v_add_f32_e64 v2, |v2|, -1.0 clamp                         // 000000000BD0: D5038102 0001E702
	v_add_f32_e64 v6, |v6|, -1.0 clamp                         // 000000000BD8: D5038106 0001E706
	v_add_f32_e64 v0, |v0|, -1.0 clamp                         // 000000000BE0: D5038100 0001E700
	v_add_f32_e32 v2, -1.0, v2                                 // 000000000BE8: 060404F3
	v_add_f32_e32 v6, -1.0, v6                                 // 000000000BEC: 060C0CF3
	v_add_f32_e32 v0, -1.0, v0                                 // 000000000BF0: 060000F3
	v_fma_f32 v2, v2, v1, 1.0                                  // 000000000BF4: D54B0002 03CA0302
	v_fma_f32 v6, v6, v1, 1.0                                  // 000000000BFC: D54B0006 03CA0306
	v_fma_f32 v0, v0, v1, 1.0                                  // 000000000C04: D54B0000 03CA0300
	v_mul_f32_e32 v1, v2, v8                                   // 000000000C0C: 10021102
	v_mul_f32_e32 v2, v6, v8                                   // 000000000C10: 10041106
	v_mul_f32_e32 v0, v0, v8                                   // 000000000C14: 10001100
	v_log_f32_e64 v1, |v1|                                     // 000000000C18: D5A70101 00000101
	v_log_f32_e64 v2, |v2|                                     // 000000000C20: D5A70102 00000102
	v_log_f32_e64 v0, |v0|                                     // 000000000C28: D5A70100 00000100
	v_mul_legacy_f32_e32 v1, s11, v1                           // 000000000C30: 0E02020B
	v_mul_legacy_f32_e32 v2, s11, v2                           // 000000000C34: 0E04040B
	v_mul_legacy_f32_e32 v6, s11, v0                           // 000000000C38: 0E0C000B
	v_exp_f32_e64 v0, v1 clamp                                 // 000000000C3C: D5A58000 00000101
	v_exp_f32_e64 v1, v2 clamp                                 // 000000000C44: D5A58001 00000102
	v_exp_f32_e64 v2, v6 clamp                                 // 000000000C4C: D5A58002 00000106
	image_store v[0:3], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000C54: F0201F08 00000004
_L0:
	s_endpgm                                                   // 000000000C5C: BF810000
