;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xyz         1     NONE   float       
; TEXCOORD                 7      w        1     NONE   float       
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 3   xyzw        4     NONE   float       
; TEXCOORD                 4   xyzw        5     NONE   float   xyzw
; TEXCOORD                 5   xyz         6     NONE   float   xyz 
; TEXCOORD                 6   xyz         7     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 4036ecf180005c00b9471e2ace2e2986
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 9
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 8
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer Material
; {
;
;   struct Material
;   {
;
;       float4 DiffuseColor;                          ; Offset:    0
;       float4 SpecularColor;                         ; Offset:   16
;       float SpecularPower;                          ; Offset:   32
;       float3 AmbientColor;                          ; Offset:   36
;       float ShadowBias;                             ; Offset:   48
;       float ShadowStrength;                         ; Offset:   52
;       int PCFSamples;                               ; Offset:   56
;   
;   } Material;                                       ; Offset:    0 Size:    60
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; Material                          cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; ShadowSampler                     sampler      NA          NA      S1             s1     1
; DiffuseTexture                    texture     f32          2d      T0             t0     1
; ShadowMap                         texture     f32          2d      T1             t1     1
;
;
; ViewId state:
;
; Number of inputs: 31, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30 }
;   output 1 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30 }
;   output 2 depends on inputs: { 8, 9, 10, 12, 13, 20, 21, 22, 23, 24, 25, 26, 28, 29, 30 }
;   output 3 depends on inputs: { 12, 13 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%Material = type { <4 x float>, <4 x float>, float, <3 x float>, float, float, i32 }
%struct.SamplerState = type { i32 }
%struct.SamplerComparisonState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.dot3.f32(i32 55, float %18, float %19, float %20, float %18, float %19, float %20)  ; Dot3(ax,ay,az,bx,by,bz)
  %22 = call float @dx.op.unary.f32(i32 25, float %21)  ; Rsqrt(value)
  %23 = fmul fast float %22, %18
  %24 = fmul fast float %22, %19
  %25 = fmul fast float %22, %20
  %26 = call float @dx.op.dot3.f32(i32 55, float %9, float %10, float %11, float %9, float %10, float %11)  ; Dot3(ax,ay,az,bx,by,bz)
  %27 = call float @dx.op.unary.f32(i32 25, float %26)  ; Rsqrt(value)
  %28 = fmul fast float %27, %9
  %29 = fmul fast float %27, %10
  %30 = fmul fast float %27, %11
  %31 = call float @dx.op.dot3.f32(i32 55, float %6, float %7, float %8, float %6, float %7, float %8)  ; Dot3(ax,ay,az,bx,by,bz)
  %32 = call float @dx.op.unary.f32(i32 25, float %31)  ; Rsqrt(value)
  %33 = fmul fast float %32, %6
  %34 = fmul fast float %32, %7
  %35 = fmul fast float %32, %8
  %36 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %4, float %16, float %17, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %37 = extractvalue %dx.types.ResRet.f32 %36, 0
  %38 = extractvalue %dx.types.ResRet.f32 %36, 1
  %39 = extractvalue %dx.types.ResRet.f32 %36, 2
  %40 = extractvalue %dx.types.ResRet.f32 %36, 3
  %41 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %42 = extractvalue %dx.types.CBufRet.f32 %41, 0
  %43 = fdiv fast float %12, %15
  %44 = fdiv fast float %13, %15
  %45 = fdiv fast float %14, %15
  %46 = fmul fast float %43, 5.000000e-01
  %47 = fmul fast float %44, 5.000000e-01
  %48 = fadd fast float %46, 5.000000e-01
  %49 = fsub fast float 5.000000e-01, %47
  %50 = fcmp fast olt float %48, 0.000000e+00
  %51 = fcmp fast ogt float %48, 1.000000e+00
  %52 = or i1 %50, %51
  %53 = fcmp fast olt float %49, 0.000000e+00
  %54 = or i1 %52, %53
  %55 = fcmp fast ogt float %49, 1.000000e+00
  %56 = or i1 %55, %54
  br i1 %56, label %92, label %57

; <label>:57                                      ; preds = %0
  %58 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %5, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %59 = extractvalue %dx.types.CBufRet.i32 %58, 2
  %60 = sdiv i32 %59, 2
  %61 = sub nsw i32 0, %60
  %62 = icmp slt i32 %60, %61
  br i1 %62, label %86, label %63

; <label>:63                                      ; preds = %57
  br label %64

; <label>:64                                      ; preds = %82, %63
  %65 = phi float [ %79, %82 ], [ 0.000000e+00, %63 ]
  %66 = phi i32 [ %83, %82 ], [ %61, %63 ]
  br label %67

; <label>:67                                      ; preds = %67, %64
  %68 = phi float [ %79, %67 ], [ %65, %64 ]
  %69 = phi i32 [ %80, %67 ], [ %61, %64 ]
  %70 = sitofp i32 %66 to float
  %71 = sitofp i32 %69 to float
  %72 = fmul fast float %70, 0x3F40000000000000
  %73 = fmul fast float %71, 0x3F40000000000000
  %74 = fsub fast float %45, %42
  %75 = fadd fast float %72, %48
  %76 = fadd fast float %73, %49
  %77 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %3, float %75, float %76, float undef, float undef, i32 0, i32 0, i32 undef, float %74)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %78 = extractvalue %dx.types.ResRet.f32 %77, 0
  %79 = fadd fast float %78, %68
  %80 = add nsw i32 %69, 1
  %81 = icmp slt i32 %69, %60
  br i1 %81, label %67, label %82

; <label>:82                                      ; preds = %67
  %83 = add nsw i32 %66, 1
  %84 = icmp slt i32 %66, %60
  br i1 %84, label %64, label %85

; <label>:85                                      ; preds = %82
  br label %86

; <label>:86                                      ; preds = %85, %57
  %87 = phi float [ 0.000000e+00, %57 ], [ %79, %85 ]
  %88 = add nsw i32 %59, 1
  %89 = mul nsw i32 %88, %88
  %90 = sitofp i32 %89 to float
  %91 = fdiv fast float %87, %90
  br label %92

; <label>:92                                      ; preds = %86, %0
  %93 = phi float [ %91, %86 ], [ 0.000000e+00, %0 ]
  %94 = extractvalue %dx.types.CBufRet.f32 %41, 1
  %95 = fsub fast float 1.000000e+00, %94
  %96 = fmul fast float %93, %95
  %97 = fadd fast float %96, %94
  %98 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %99 = extractvalue %dx.types.CBufRet.f32 %98, 1
  %100 = extractvalue %dx.types.CBufRet.f32 %98, 2
  %101 = extractvalue %dx.types.CBufRet.f32 %98, 3
  %102 = fmul fast float %99, %37
  %103 = fmul fast float %100, %38
  %104 = fmul fast float %101, %39
  %105 = call float @dx.op.dot3.f32(i32 55, float %23, float %24, float %25, float %28, float %29, float %30)  ; Dot3(ax,ay,az,bx,by,bz)
  %106 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %105)  ; FMax(a,b)
  %107 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %108 = extractvalue %dx.types.CBufRet.f32 %107, 0
  %109 = extractvalue %dx.types.CBufRet.f32 %107, 1
  %110 = extractvalue %dx.types.CBufRet.f32 %107, 2
  %111 = fmul fast float %106, %37
  %112 = fmul fast float %111, %108
  %113 = fmul fast float %106, %38
  %114 = fmul fast float %113, %109
  %115 = fmul fast float %106, %39
  %116 = fmul fast float %115, %110
  %117 = fadd fast float %33, %28
  %118 = fadd fast float %34, %29
  %119 = fadd fast float %35, %30
  %120 = call float @dx.op.dot3.f32(i32 55, float %117, float %118, float %119, float %117, float %118, float %119)  ; Dot3(ax,ay,az,bx,by,bz)
  %121 = call float @dx.op.unary.f32(i32 25, float %120)  ; Rsqrt(value)
  %122 = fmul fast float %121, %117
  %123 = fmul fast float %121, %118
  %124 = fmul fast float %121, %119
  %125 = call float @dx.op.dot3.f32(i32 55, float %23, float %24, float %25, float %122, float %123, float %124)  ; Dot3(ax,ay,az,bx,by,bz)
  %126 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %125)  ; FMax(a,b)
  %127 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %128 = extractvalue %dx.types.CBufRet.f32 %127, 0
  %129 = extractvalue %dx.types.CBufRet.f32 %127, 1
  %130 = extractvalue %dx.types.CBufRet.f32 %127, 2
  %131 = extractvalue %dx.types.CBufRet.f32 %98, 0
  %132 = call float @dx.op.unary.f32(i32 23, float %126)  ; Log(value)
  %133 = fmul fast float %132, %131
  %134 = call float @dx.op.unary.f32(i32 21, float %133)  ; Exp(value)
  %135 = fmul fast float %134, %128
  %136 = fmul fast float %134, %129
  %137 = fmul fast float %134, %130
  %138 = fadd fast float %135, %112
  %139 = fmul fast float %138, %97
  %140 = fadd fast float %139, %102
  %141 = fadd fast float %136, %114
  %142 = fmul fast float %141, %97
  %143 = fadd fast float %142, %103
  %144 = fadd fast float %137, %116
  %145 = fmul fast float %144, %97
  %146 = fadd fast float %145, %104
  %147 = extractvalue %dx.types.CBufRet.f32 %107, 3
  %148 = fmul fast float %147, %40
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %140)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %143)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %146)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %148)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!14}
!dx.entryPoints = !{!15}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !9, !11}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{!10}
!10 = !{i32 0, %Material* undef, !"", i32 0, i32 0, i32 1, i32 60, null}
!11 = !{!12, !13}
!12 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!13 = !{i32 1, %struct.SamplerComparisonState* undef, !"", i32 0, i32 1, i32 1, i32 1, null}
!14 = !{[33 x i32] [i32 31, i32 4, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 15, i32 15, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 7, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7]}
!15 = !{void ()* @main, !"main", !16, !4, null}
!16 = !{!17, !38, null}
!17 = !{!18, !20, !21, !24, !27, !29, !32, !34, !36}
!18 = !{i32 0, !"SV_Position", i8 9, i8 3, !19, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!19 = !{i32 0}
!20 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !19, i8 2, i32 1, i8 3, i32 1, i8 0, null}
!21 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !22, i8 2, i32 1, i8 3, i32 2, i8 0, !23}
!22 = !{i32 1}
!23 = !{i32 3, i32 7}
!24 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !25, i8 2, i32 1, i8 2, i32 3, i8 0, !26}
!25 = !{i32 2}
!26 = !{i32 3, i32 3}
!27 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 4, i32 4, i8 0, null}
!28 = !{i32 3}
!29 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 4, i32 5, i8 0, !31}
!30 = !{i32 4}
!31 = !{i32 3, i32 15}
!32 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !33, i8 2, i32 1, i8 3, i32 6, i8 0, !23}
!33 = !{i32 5}
!34 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 3, i32 7, i8 0, !23}
!35 = !{i32 6}
!36 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !37, i8 2, i32 1, i8 1, i32 1, i8 3, null}
!37 = !{i32 7}
!38 = !{!39}
!39 = !{i32 0, !"SV_Target", i8 9, i8 16, !19, i8 0, i32 1, i8 4, i32 0, i8 0, !31}
