#version 320 es

// Geometry Expansion Fragment Shader - OpenGL ES Version
// Tests fragment processing for geometry shader output

precision highp float;

in vec3 gWorldPos;
in vec3 gNormal;
in vec2 gTexCoord;
in vec4 gColor;
in float gExpansionFactor;

out vec4 fragColor;

uniform vec3 uLightDirection;
uniform vec3 uLightColor;
uniform vec3 uCameraPosition;
uniform sampler2D uTexture;
uniform float uTime;

void main()
{
    // Normalize interpolated normal
    vec3 normal = normalize(gNormal);
    vec3 lightDir = normalize(-uLightDirection);
    vec3 viewDir = normalize(uCameraPosition - gWorldPos);
    
    // Sample texture
    vec4 texColor = texture(uTexture, gTexCoord);
    
    // Basic lighting
    float NdotL = max(0.0, dot(normal, lightDir));
    vec3 diffuse = uLightColor * NdotL;
    
    // Specular
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32.0);
    vec3 specular = uLightColor * spec * 0.5;
    
    // Color based on expansion factor
    vec3 expansionColor = mix(vec3(1.0), vec3(0.2, 0.8, 1.0), gExpansionFactor);
    
    // Combine lighting
    vec3 finalColor = (diffuse + specular) * texColor.rgb * gColor.rgb * expansionColor;
    
    // Add some animation based on time and expansion
    float pulse = sin(uTime * 3.0 + gExpansionFactor * 10.0) * 0.1 + 0.9;
    finalColor *= pulse;
    
    fragColor = vec4(finalColor, texColor.a * gColor.a);
}
