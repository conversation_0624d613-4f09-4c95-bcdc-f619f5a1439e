// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VertexData
{
  float3 Position;
  float3 Normal;
  float2 TexCoord;
  float4 Color;
  uint MaterialID;
  float3 Tangent;
  float3 Bitangent;
  float4 BoneWeights;
  uint4 BoneIndices;
};

struct MaterialData
{
  float4 Albedo;
  float Metallic;
  float Roughness;
  float AO;
  float Emission;
  float4 TilingOffset;
  uint TextureFlags;
  float _padding;
};

struct BoneTransform
{
  float4x4 Transform;
  float4x4 InverseBindPose;
};

struct LightData
{
  float3 Position;
  float Range;
  float3 Direction;
  float SpotAngle;
  float3 Color;
  float Intensity;
  uint Type;
  float3 _padding;
};

struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float3 Tangent : TANGENT;
  float3 Bitangent : BITANGENT;
  float2 TexCoord : TEXCOORD0;
  float2 TexCoord2 : TEXCOORD1;
  float4 Color : COLOR0;
  float4 BoneWeights : BLENDWEIGHT;
  uint4 BoneIndices : BLENDINDICES;
  uint InstanceID : SV_InstanceID;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float2 LightmapUV : TEXCOORD5;
  float4 Color : TEXCOORD6;
  float3 ViewDir : TEXCOORD7;
  float4 PrevClipPos : TEXCOORD8;
  float4 CurrClipPos : TEXCOORD9;
  nointerpolation uint MaterialID : TEXCOORD10;
  float LODFade : TEXCOORD11;
};

cbuffer PerFrame : register(b0)
{
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 ViewProjectionMatrix;
  float3 CameraPosition;
  float Time;
  float DeltaTime;
  uint FrameCount;
  float2 ScreenResolution;
}

cbuffer PerObject : register(b1)
{
  float4x4 WorldMatrix;
  float4x4 NormalMatrix;
  float4x4 PrevWorldMatrix;
  uint MaterialIndex;
  float3 BoundingBoxMin;
  float3 BoundingBoxMax;
  float LODLevel;
}

StructuredBuffer<BoneTransform> BoneTransforms : register(t1);
StructuredBuffer<float4> InstanceTransforms : register(t3);
float4x4 GetBoneTransform(uint4 boneIndices, float4 boneWeights)
{
  float4x4 boneTransform = 0;
  for (int i = 0; i < 4; (i++))
  {
    if (boneWeights[i] > 0.0f)
    {
      boneTransform += (BoneTransforms[boneIndices[i]].Transform * boneWeights[i]);
    }
  }
  return boneTransform;
}

float3 ApplyBoneTransformToVector(float3 inputVector, uint4 boneIndices, float4 boneWeights)
{
  float3 result = 0;
  for (int i = 0; i < 4; (i++))
  {
    if (boneWeights[i] > 0.0f)
    {
      result += (mul(inputVector, (float3x3)BoneTransforms[boneIndices[i]].Transform) * boneWeights[i]);
    }
  }
  return result;
}

float CalculateLODFade(float3 worldPos)
{
  float distance = length(CameraPosition - worldPos);
  float fadeStart = 50.0f;
  float fadeEnd = 100.0f;
  return saturate(fadeEnd - distance / fadeEnd - fadeStart);
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4x4 instanceTransform = float4x4(InstanceTransforms[(input.InstanceID * 4) + 0], InstanceTransforms[(input.InstanceID * 4) + 1], InstanceTransforms[(input.InstanceID * 4) + 2], InstanceTransforms[(input.InstanceID * 4) + 3]);
  float3 skinnedPosition = input.Position;
  float3 skinnedNormal = input.Normal;
  float3 skinnedTangent = input.Tangent;
  if (dot(input.BoneWeights, 1.0f) > 0.0f)
  {
    float4 normalizedWeights = input.BoneWeights / dot(input.BoneWeights, 1.0f);
    float4x4 boneTransform = GetBoneTransform(input.BoneIndices, normalizedWeights);
    skinnedPosition = mul(float4(input.Position, 1.0f), boneTransform).xyz;
    skinnedNormal = ApplyBoneTransformToVector(input.Normal, input.BoneIndices, normalizedWeights);
    skinnedTangent = ApplyBoneTransformToVector(input.Tangent, input.BoneIndices, normalizedWeights);
  }
  float4 instancedPosition = mul(float4(skinnedPosition, 1.0f), instanceTransform);
  float4 worldPos = mul(instancedPosition, WorldMatrix);
  output.WorldPos = worldPos.xyz;
  float4 prevWorldPos = mul(instancedPosition, PrevWorldMatrix);
  output.PrevClipPos = mul(prevWorldPos, ViewProjectionMatrix);
  output.Position = mul(worldPos, ViewProjectionMatrix);
  output.CurrClipPos = output.Position;
  output.Normal = normalize(mul(skinnedNormal, (float3x3)NormalMatrix));
  output.Tangent = normalize(mul(skinnedTangent, (float3x3)NormalMatrix));
  output.Bitangent = normalize(mul(input.Bitangent, (float3x3)NormalMatrix));
  output.TexCoord = input.TexCoord;
  output.LightmapUV = input.TexCoord2;
  output.Color = input.Color;
  output.ViewDir = normalize(CameraPosition - output.WorldPos);
  output.MaterialID = MaterialIndex;
  output.LODFade = CalculateLODFade(output.WorldPos);
  return output;
}

