// Post Processing Vertex Shader
// Tests full-screen quad rendering for post-processing effects

struct VSInput
{
    float3 Position : POSITION;
    float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float2 TexCoord : TEXCOORD0;
    float2 TexCoordOffset[8] : TEXCOORD1; // For multi-tap sampling
};

cbuffer PostProcessParams : register(b0)
{
    float2 TexelSize;
    float BlurRadius;
    float Time;
};

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Full-screen quad
    output.Position = float4(input.Position, 1.0);
    output.TexCoord = input.TexCoord;
    
    // Pre-calculate offset coordinates for blur sampling
    float2 offsets[8] = {
        float2(-1.0, -1.0), float2(0.0, -1.0), float2(1.0, -1.0),
        float2(-1.0,  0.0),                     float2(1.0,  0.0),
        float2(-1.0,  1.0), float2(0.0,  1.0), float2(1.0,  1.0)
    };
    
    for(int i = 0; i < 8; i++)
    {
        output.TexCoordOffset[i] = input.TexCoord + offsets[i] * TexelSize * BlurRadius;
    }
    
    return output;
}
