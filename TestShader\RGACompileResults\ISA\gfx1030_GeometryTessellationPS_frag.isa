_amdgpu_ps_main:
	s_mov_b64 s[26:27], exec                                   // 000000000000: BE9A047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s24, s1                                          // 000000000008: BE980301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s25, s1                                          // 000000000014: BE990301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx8 s[4:11], s[24:25], 0x20                     // 00000000001C: F40C010C FA000020
	s_load_dwordx8 s[12:19], s[24:25], 0x90                    // 000000000024: F40C030C FA000090
	v_interp_p1_f32_e32 v7, v0, attr3.x                        // 00000000002C: C81C0C00
	v_interp_p1_f32_e32 v8, v0, attr3.y                        // 000000000030: C8200D00
	v_interp_p1_f32_e32 v6, v0, attr6.x                        // 000000000034: C8181800
	v_interp_p1_f32_e32 v2, v0, attr5.x                        // 000000000038: C8081400
	s_mov_b32 s1, 0x40200000                                   // 00000000003C: BE8103FF 40200000
	v_interp_p2_f32_e32 v7, v1, attr3.x                        // 000000000044: C81D0C01
	v_interp_p2_f32_e32 v8, v1, attr3.y                        // 000000000048: C8210D01
	v_interp_p2_f32_e32 v6, v1, attr6.x                        // 00000000004C: C8191801
	v_interp_p2_f32_e32 v2, v1, attr5.x                        // 000000000050: C8091401
	v_mov_b32_e32 v21, 1.0                                     // 000000000054: 7E2A02F2
	v_mov_b32_e32 v20, 0                                       // 000000000058: 7E280280
	v_mov_b32_e32 v19, 0                                       // 00000000005C: 7E260280
	v_fmaak_f32 v9, s1, v6, 0xbf400000                         // 000000000060: 5A120C01 BF400000
	v_mul_f32_e32 v15, 0x40555555, v2                          // 000000000068: 101E04FF 40555555
	v_fma_f32 v6, v6, -2.0, 1.0 clamp                          // 000000000070: D54B8006 03C9EB06
	v_max_f32_e64 v9, v9, v9 clamp                             // 000000000078: D5108009 00021309
	s_waitcnt lgkmcnt(0)                                       // 000000000080: BF8CC07F
	image_sample v[11:14], v[7:8], s[12:19], s[4:7] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000084: F0800F08 00230B07
	s_load_dwordx4 s[4:7], s[24:25], null                      // 00000000008C: F408010C FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	s_buffer_load_dword s0, s[4:7], 0xe8                       // 000000000098: F4200002 FA0000E8
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	v_rcp_f32_e32 v3, s0                                       // 0000000000A4: 7E065400
	v_fmamk_f32 v10, s0, 0xbf333333, v2                        // 0000000000A8: 58140400 BF333333
	s_mov_b64 s[0:1], exec                                     // 0000000000B0: BE80047E
	v_mul_f32_e32 v16, 0x40555555, v10                         // 0000000000B4: 102014FF 40555555
	v_fma_f32 v15, -v15, v3, 1.0 clamp                         // 0000000000BC: D54B800F 23CA070F
	s_waitcnt vmcnt(0)                                         // 0000000000C4: BF8C3F70
	v_mul_f32_e32 v10, v11, v6                                 // 0000000000C8: 10140D0B
	v_mul_f32_e32 v11, v12, v9                                 // 0000000000CC: 1016130C
	v_mul_f32_e64 v6, v16, v3 clamp                            // 0000000000D0: D5088006 00020710
	v_mul_f32_e32 v12, v15, v14                                // 0000000000D8: 10181D0F
	v_add_f32_e32 v9, v10, v11                                 // 0000000000DC: 0612170A
	v_mul_f32_e32 v13, v6, v13                                 // 0000000000E0: 101A1B06
	v_add_f32_e32 v6, v9, v12                                  // 0000000000E4: 060C1909
	v_mov_b32_e32 v9, 0                                        // 0000000000E8: 7E120280
	v_add_f32_e32 v14, v6, v13                                 // 0000000000EC: 061C1B06
	v_mov_b32_e32 v6, 0                                        // 0000000000F0: 7E0C0280
	v_cmpx_lt_f32_e32 0, v14                                   // 0000000000F4: 7C221C80
	v_rcp_f32_e32 v6, v14                                      // 0000000000F8: 7E0C550E
	v_mul_f32_e32 v21, v6, v10                                 // 0000000000FC: 102A1506
	v_mul_f32_e32 v20, v6, v11                                 // 000000000100: 10281706
	v_mul_f32_e32 v19, v6, v13                                 // 000000000104: 10261B06
	v_mul_f32_e32 v6, v6, v12                                  // 000000000108: 100C1906
	s_or_b64 exec, exec, s[0:1]                                // 00000000010C: 88FE007E
	v_mov_b32_e32 v10, 0                                       // 000000000110: 7E140280
	v_mov_b32_e32 v11, 0                                       // 000000000114: 7E160280
	v_mov_b32_e32 v17, 0                                       // 000000000118: 7E220280
	v_mov_b32_e32 v18, 0                                       // 00000000011C: 7E240280
	v_mov_b32_e32 v16, 0                                       // 000000000120: 7E200280
	v_mov_b32_e32 v13, 0                                       // 000000000124: 7E1A0280
	v_mov_b32_e32 v14, 0                                       // 000000000128: 7E1C0280
	v_mov_b32_e32 v15, 0                                       // 00000000012C: 7E1E0280
	v_mov_b32_e32 v12, 0                                       // 000000000130: 7E180280
	s_mov_b64 s[0:1], exec                                     // 000000000134: BE80047E
	v_cmpx_lt_f32_e32 0, v21                                   // 000000000138: 7C222A80
	s_cbranch_execz _L0                                        // 00000000013C: BF880047
	s_clause 0x1                                               // 000000000140: BFA10001
	s_buffer_load_dwordx8 s[12:19], s[8:11], null              // 000000000144: F42C0304 FA000000
	s_buffer_load_dwordx2 s[28:29], s[8:11], 0x20              // 00000000014C: F4240704 FA000020
	s_buffer_load_dword s3, s[4:7], 0xcc                       // 000000000154: F42000C2 FA0000CC
	s_clause 0x1                                               // 00000000015C: BFA10001
	s_buffer_load_dwordx2 s[30:31], s[8:11], 0xd8              // 000000000160: F4240784 FA0000D8
	s_buffer_load_dword s33, s[8:11], 0xe0                     // 000000000168: F4200844 FA0000E0
	s_clause 0x3                                               // 000000000170: BFA10003
	s_load_dwordx4 s[20:23], s[24:25], 0x50                    // 000000000174: F408050C FA000050
	s_load_dwordx8 s[36:43], s[24:25], 0x30                    // 00000000017C: F40C090C FA000030
	s_load_dwordx8 s[44:51], s[24:25], 0x60                    // 000000000184: F40C0B0C FA000060
	s_load_dwordx8 s[52:59], s[24:25], null                    // 00000000018C: F40C0D0C FA000000
	v_mov_b32_e32 v24, 0                                       // 000000000194: 7E300280
	s_waitcnt lgkmcnt(0)                                       // 000000000198: BF8CC07F
	v_mul_f32_e32 v15, s13, v21                                // 00000000019C: 101E2A0D
	v_fma_f32 v22, v7, s19, s28                                // 0000000001A0: D54B0016 00702707
	v_mov_b32_e32 v9, s3                                       // 0000000001A8: 7E120203
	v_fma_f32 v23, v8, s19, s29                                // 0000000001AC: D54B0017 00742708
	v_mul_f32_e32 v26, s14, v21                                // 0000000001B4: 10342A0E
	v_mul_f32_e32 v27, s15, v21                                // 0000000001B8: 10362A0F
	v_mul_f32_e32 v28, s18, v21                                // 0000000001BC: 10382A12
	v_fmamk_f32 v9, v22, 0x41200000, v9                        // 0000000001C0: 58121316 41200000
	v_mul_f32_e32 v9, 0.15915494, v9                           // 0000000001C8: 101212F8
	v_sin_f32_e32 v9, v9                                       // 0000000001CC: 7E126B09
	v_mul_f32_e32 v9, s33, v9                                  // 0000000001D0: 10121221
	v_mul_f32_e32 v17, 0x3c23d70a, v9                          // 0000000001D4: 102212FF 3C23D70A
	image_sample v[9:11], v[22:24], s[36:43], s[20:23] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000001DC: F0800728 00A90916
	image_sample v[12:13], v[22:24], s[44:51], s[20:23] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000001E4: F0800328 00AB0C16
	v_fma_f32 v14, s30, v17, v22                               // 0000000001EC: D54B000E 045A221E
	v_fma_f32 v16, s31, v17, v23                               // 0000000001F4: D54B0010 045E221F
	image_sample  v[22:25], [v14, v16, v24], s[52:59], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D_ARRAY// 0000000001FC: F0800F2A 00AD160E 00001810
	v_mul_f32_e32 v14, s12, v21                                // 000000000208: 101C2A0C
	v_mul_f32_e32 v16, s17, v21                                // 00000000020C: 10202A11
	s_waitcnt vmcnt(2)                                         // 000000000210: BF8C3F72
	v_fma_f32 v9, v9, 2.0, -1.0                                // 000000000214: D54B0009 03CDE909
	v_fma_f32 v18, v10, 2.0, -1.0                              // 00000000021C: D54B0012 03CDE90A
	v_fma_f32 v29, v11, 2.0, -1.0                              // 000000000224: D54B001D 03CDE90B
	s_waitcnt vmcnt(1)                                         // 00000000022C: BF8C3F71
	v_mul_f32_e32 v11, v16, v12                                // 000000000230: 10161910
	v_mul_f32_e32 v10, v13, v21                                // 000000000234: 10142B0D
	v_mul_f32_e32 v17, v28, v9                                 // 000000000238: 1022131C
	v_mul_f32_e32 v18, v28, v18                                // 00000000023C: 1024251C
	v_mul_f32_e32 v16, v28, v29                                // 000000000240: 10203B1C
	v_mul_f32_e32 v9, s16, v21                                 // 000000000244: 10122A10
	s_waitcnt vmcnt(0)                                         // 000000000248: BF8C3F70
	v_mul_f32_e32 v13, v14, v22                                // 00000000024C: 101A2D0E
	v_mul_f32_e32 v14, v15, v23                                // 000000000250: 101C2F0F
	v_mul_f32_e32 v15, v26, v24                                // 000000000254: 101E311A
	v_mul_f32_e32 v12, v27, v25                                // 000000000258: 1018331B
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 00000000025C: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 000000000260: BE80047E
	v_cmpx_lt_f32_e32 0, v20                                   // 000000000264: 7C222880
	s_cbranch_execz _L1                                        // 000000000268: BF880034
	s_clause 0x1                                               // 00000000026C: BFA10001
	s_buffer_load_dwordx8 s[12:19], s[8:11], 0x30              // 000000000270: F42C0304 FA000030
	s_buffer_load_dwordx2 s[28:29], s[8:11], 0x50              // 000000000278: F4240704 FA000050
	s_clause 0x1                                               // 000000000280: BFA10001
	s_load_dwordx4 s[20:23], s[24:25], 0x50                    // 000000000284: F408050C FA000050
	s_load_dwordx8 s[36:43], s[24:25], null                    // 00000000028C: F40C090C FA000000
	v_mov_b32_e32 v27, 1.0                                     // 000000000294: 7E3602F2
	s_load_dwordx8 s[44:51], s[24:25], 0x60                    // 000000000298: F40C0B0C FA000060
	s_waitcnt lgkmcnt(0)                                       // 0000000002A0: BF8CC07F
	v_mul_f32_e32 v28, s12, v20                                // 0000000002A4: 1038280C
	v_fma_f32 v25, v7, s19, s28                                // 0000000002A8: D54B0019 00702707
	v_fma_f32 v26, v8, s19, s29                                // 0000000002B0: D54B001A 00742708
	v_fmac_f32_e32 v9, s16, v20                                // 0000000002B8: 56122810
	image_sample v[21:24], v[25:27], s[36:43], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D_ARRAY// 0000000002BC: F0800F28 00A91519
	s_load_dwordx8 s[36:43], s[24:25], 0x30                    // 0000000002C4: F40C090C FA000030
	s_waitcnt vmcnt(0)                                         // 0000000002CC: BF8C3F70
	v_fmac_f32_e32 v13, v28, v21                               // 0000000002D0: 561A2B1C
	v_mul_f32_e32 v21, s13, v20                                // 0000000002D4: 102A280D
	v_fmac_f32_e32 v14, v21, v22                               // 0000000002D8: 561C2D15
	v_mul_f32_e32 v21, s14, v20                                // 0000000002DC: 102A280E
	v_fmac_f32_e32 v15, v21, v23                               // 0000000002E0: 561E2F15
	v_mul_f32_e32 v21, s15, v20                                // 0000000002E4: 102A280F
	v_fmac_f32_e32 v12, v21, v24                               // 0000000002E8: 56183115
	s_waitcnt lgkmcnt(0)                                       // 0000000002EC: BF8CC07F
	image_sample v[21:23], v[25:27], s[36:43], s[20:23] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000002F0: F0800728 00A91519
	image_sample v[24:25], v[25:27], s[44:51], s[20:23] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000002F8: F0800328 00AB1819
	v_mul_f32_e32 v26, s17, v20                                // 000000000300: 10342811
	v_mul_f32_e32 v27, s18, v20                                // 000000000304: 10362812
	s_waitcnt vmcnt(1)                                         // 000000000308: BF8C3F71
	v_fma_f32 v21, v21, 2.0, -1.0                              // 00000000030C: D54B0015 03CDE915
	v_fma_f32 v22, v22, 2.0, -1.0                              // 000000000314: D54B0016 03CDE916
	v_fma_f32 v23, v23, 2.0, -1.0                              // 00000000031C: D54B0017 03CDE917
	s_waitcnt vmcnt(0)                                         // 000000000324: BF8C3F70
	v_fmac_f32_e32 v11, v26, v24                               // 000000000328: 5616311A
	v_fmac_f32_e32 v10, v25, v20                               // 00000000032C: 56142919
	v_fmac_f32_e32 v17, v27, v21                               // 000000000330: 56222B1B
	v_fmac_f32_e32 v18, v27, v22                               // 000000000334: 56242D1B
	v_fmac_f32_e32 v16, v27, v23                               // 000000000338: 56202F1B
_L1:
	s_or_b64 exec, exec, s[0:1]                                // 00000000033C: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 000000000340: BE80047E
	v_cmpx_lt_f32_e32 0, v19                                   // 000000000344: 7C222680
	s_cbranch_execz _L2                                        // 000000000348: BF880034
	s_clause 0x1                                               // 00000000034C: BFA10001
	s_buffer_load_dwordx8 s[12:19], s[8:11], 0x60              // 000000000350: F42C0304 FA000060
	s_buffer_load_dwordx2 s[28:29], s[8:11], 0x80              // 000000000358: F4240704 FA000080
	s_clause 0x1                                               // 000000000360: BFA10001
	s_load_dwordx4 s[20:23], s[24:25], 0x50                    // 000000000364: F408050C FA000050
	s_load_dwordx8 s[36:43], s[24:25], null                    // 00000000036C: F40C090C FA000000
	v_mov_b32_e32 v26, 2.0                                     // 000000000374: 7E3402F4
	s_load_dwordx8 s[44:51], s[24:25], 0x60                    // 000000000378: F40C0B0C FA000060
	s_waitcnt lgkmcnt(0)                                       // 000000000380: BF8CC07F
	v_mul_f32_e32 v27, s12, v19                                // 000000000384: 1036260C
	v_fma_f32 v24, v7, s19, s28                                // 000000000388: D54B0018 00702707
	v_fma_f32 v25, v8, s19, s29                                // 000000000390: D54B0019 00742708
	v_fmac_f32_e32 v9, s16, v19                                // 000000000398: 56122610
	image_sample v[20:23], v[24:26], s[36:43], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D_ARRAY// 00000000039C: F0800F28 00A91418
	s_load_dwordx8 s[36:43], s[24:25], 0x30                    // 0000000003A4: F40C090C FA000030
	s_waitcnt vmcnt(0)                                         // 0000000003AC: BF8C3F70
	v_fmac_f32_e32 v13, v27, v20                               // 0000000003B0: 561A291B
	v_mul_f32_e32 v20, s13, v19                                // 0000000003B4: 1028260D
	v_fmac_f32_e32 v14, v20, v21                               // 0000000003B8: 561C2B14
	v_mul_f32_e32 v20, s14, v19                                // 0000000003BC: 1028260E
	v_fmac_f32_e32 v15, v20, v22                               // 0000000003C0: 561E2D14
	v_mul_f32_e32 v20, s15, v19                                // 0000000003C4: 1028260F
	v_fmac_f32_e32 v12, v20, v23                               // 0000000003C8: 56182F14
	s_waitcnt lgkmcnt(0)                                       // 0000000003CC: BF8CC07F
	image_sample v[20:22], v[24:26], s[36:43], s[20:23] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000003D0: F0800728 00A91418
	image_sample v[23:24], v[24:26], s[44:51], s[20:23] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000003D8: F0800328 00AB1718
	v_mul_f32_e32 v25, s17, v19                                // 0000000003E0: 10322611
	v_mul_f32_e32 v26, s18, v19                                // 0000000003E4: 10342612
	s_waitcnt vmcnt(1)                                         // 0000000003E8: BF8C3F71
	v_fma_f32 v20, v20, 2.0, -1.0                              // 0000000003EC: D54B0014 03CDE914
	v_fma_f32 v21, v21, 2.0, -1.0                              // 0000000003F4: D54B0015 03CDE915
	v_fma_f32 v22, v22, 2.0, -1.0                              // 0000000003FC: D54B0016 03CDE916
	s_waitcnt vmcnt(0)                                         // 000000000404: BF8C3F70
	v_fmac_f32_e32 v11, v25, v23                               // 000000000408: 56162F19
	v_fmac_f32_e32 v10, v24, v19                               // 00000000040C: 56142718
	v_fmac_f32_e32 v17, v26, v20                               // 000000000410: 5622291A
	v_fmac_f32_e32 v18, v26, v21                               // 000000000414: 56242B1A
	v_fmac_f32_e32 v16, v26, v22                               // 000000000418: 56202D1A
_L2:
	s_or_b64 exec, exec, s[0:1]                                // 00000000041C: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 000000000420: BE80047E
	v_cmpx_lt_f32_e32 0, v6                                    // 000000000424: 7C220C80
	s_cbranch_execz _L3                                        // 000000000428: BF880034
	s_clause 0x1                                               // 00000000042C: BFA10001
	s_buffer_load_dwordx8 s[12:19], s[8:11], 0x90              // 000000000430: F42C0304 FA000090
	s_buffer_load_dwordx2 s[28:29], s[8:11], 0xb0              // 000000000438: F4240704 FA0000B0
	s_clause 0x3                                               // 000000000440: BFA10003
	s_load_dwordx4 s[20:23], s[24:25], 0x50                    // 000000000444: F408050C FA000050
	s_load_dwordx8 s[36:43], s[24:25], null                    // 00000000044C: F40C090C FA000000
	s_load_dwordx8 s[44:51], s[24:25], 0x30                    // 000000000454: F40C0B0C FA000030
	s_load_dwordx8 s[52:59], s[24:25], 0x60                    // 00000000045C: F40C0D0C FA000060
	v_mov_b32_e32 v28, 0x40400000                              // 000000000464: 7E3802FF 40400000
	s_waitcnt lgkmcnt(0)                                       // 00000000046C: BF8CC07F
	v_mul_f32_e32 v29, s15, v6                                 // 000000000470: 103A0C0F
	v_fma_f32 v26, v7, s19, s28                                // 000000000474: D54B001A 00702707
	v_fma_f32 v27, v8, s19, s29                                // 00000000047C: D54B001B 00742708
	v_mul_f32_e32 v30, s17, v6                                 // 000000000484: 103C0C11
	v_mul_f32_e32 v31, s18, v6                                 // 000000000488: 103E0C12
	v_fmac_f32_e32 v9, s16, v6                                 // 00000000048C: 56120C10
	image_sample v[19:22], v[26:28], s[36:43], s[20:23] dmask:0xf dim:SQ_RSRC_IMG_2D_ARRAY// 000000000490: F0800F28 00A9131A
	image_sample v[23:25], v[26:28], s[44:51], s[20:23] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 000000000498: F0800728 00AB171A
	image_sample v[7:8], v[26:28], s[52:59], s[20:23] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000004A0: F0800328 00AD071A
	v_mul_f32_e32 v26, s12, v6                                 // 0000000004A8: 10340C0C
	v_mul_f32_e32 v27, s13, v6                                 // 0000000004AC: 10360C0D
	v_mul_f32_e32 v28, s14, v6                                 // 0000000004B0: 10380C0E
	s_waitcnt vmcnt(2)                                         // 0000000004B4: BF8C3F72
	v_fmac_f32_e32 v13, v26, v19                               // 0000000004B8: 561A271A
	v_fmac_f32_e32 v14, v27, v20                               // 0000000004BC: 561C291B
	v_fmac_f32_e32 v15, v28, v21                               // 0000000004C0: 561E2B1C
	s_waitcnt vmcnt(1)                                         // 0000000004C4: BF8C3F71
	v_fma_f32 v19, v23, 2.0, -1.0                              // 0000000004C8: D54B0013 03CDE917
	v_fma_f32 v20, v24, 2.0, -1.0                              // 0000000004D0: D54B0014 03CDE918
	v_fma_f32 v21, v25, 2.0, -1.0                              // 0000000004D8: D54B0015 03CDE919
	v_fmac_f32_e32 v12, v29, v22                               // 0000000004E0: 56182D1D
	s_waitcnt vmcnt(0)                                         // 0000000004E4: BF8C3F70
	v_fmac_f32_e32 v11, v30, v7                                // 0000000004E8: 56160F1E
	v_fmac_f32_e32 v10, v8, v6                                 // 0000000004EC: 56140D08
	v_fmac_f32_e32 v17, v31, v19                               // 0000000004F0: 5622271F
	v_fmac_f32_e32 v18, v31, v20                               // 0000000004F4: 5624291F
	v_fmac_f32_e32 v16, v31, v21                               // 0000000004F8: 56202B1F
_L3:
	s_or_b64 exec, exec, s[0:1]                                // 0000000004FC: 88FE007E
	s_clause 0x1                                               // 000000000500: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0xb0                    // 000000000504: F40C040C FA0000B0
	s_load_dwordx4 s[12:15], s[24:25], 0x50                    // 00000000050C: F408030C FA000050
	s_mov_b32 m0, s2                                           // 000000000514: BEFC0302
	v_interp_p1_f32_e32 v6, v0, attr4.x                        // 000000000518: C8181000
	v_interp_p1_f32_e32 v7, v0, attr4.y                        // 00000000051C: C81C1100
	v_interp_p2_f32_e32 v6, v1, attr4.x                        // 000000000520: C8191001
	v_interp_p2_f32_e32 v7, v1, attr4.y                        // 000000000524: C81D1101
	s_and_b64 exec, exec, s[26:27]                             // 000000000528: 87FE1A7E
	s_waitcnt lgkmcnt(0)                                       // 00000000052C: BF8CC07F
	image_sample v[6:8], v[6:7], s[16:23], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000530: F0800708 00640606
	v_interp_p1_f32_e32 v19, v0, attr8.w                       // 000000000538: C84C2300
	v_interp_p1_f32_e32 v20, v0, attr8.x                       // 00000000053C: C8502000
	v_interp_p2_f32_e32 v19, v1, attr8.w                       // 000000000540: C84D2301
	v_interp_p2_f32_e32 v20, v1, attr8.x                       // 000000000544: C8512001
	v_rcp_f32_e64 v25, v19 div:2                               // 000000000548: D5AA0019 18000113
	v_interp_p1_f32_e32 v19, v0, attr8.y                       // 000000000550: C84C2100
	v_interp_p2_f32_e32 v19, v1, attr8.y                       // 000000000554: C84D2101
	v_mul_f32_e32 v24, v25, v20                                // 000000000558: 10302919
	v_mul_f32_e32 v23, v25, v19                                // 00000000055C: 102E2719
	v_add_f32_e32 v21, 0.5, v24                                // 000000000560: 062A30F0
	v_add_f32_e32 v22, 0.5, v23                                // 000000000564: 062C2EF0
	v_min_f32_e32 v19, v21, v22                                // 000000000568: 1E262D15
	v_max_f32_e32 v20, v21, v22                                // 00000000056C: 20282D15
	v_cmp_ngt_f32_e32 vcc_lo, 0, v19                           // 000000000570: 7C162680
	v_cmp_nlt_f32_e64 s0, 1.0, v20                             // 000000000574: D40E0000 000228F2
	v_mov_b32_e32 v19, 1.0                                     // 00000000057C: 7E2602F2
	v_mov_b32_e32 v20, 1.0                                     // 000000000580: 7E2802F2
	s_and_b64 s[16:17], s[0:1], vcc                            // 000000000584: 87906A00
	s_and_saveexec_b64 s[0:1], s[16:17]                        // 000000000588: BE802410
	s_cbranch_execz _L4                                        // 00000000058C: BF88003F
	s_mov_b32 m0, s2                                           // 000000000590: BEFC0302
	s_clause 0x1                                               // 000000000594: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0xf0                    // 000000000598: F40C040C FA0000F0
	s_load_dwordx4 s[28:31], s[24:25], 0x80                    // 0000000005A0: F408070C FA000080
	v_interp_p1_f32_e32 v20, v0, attr8.z                       // 0000000005A8: C8502200
	v_add_f32_e32 v26, 0x3effc000, v23                         // 0000000005AC: 06342EFF 3EFFC000
	v_add_f32_e32 v23, 0x3f002000, v23                         // 0000000005B4: 062E2EFF 3F002000
	v_interp_p2_f32_e32 v20, v1, attr8.z                       // 0000000005BC: C8512201
	v_mul_f32_e32 v20, v25, v20                                // 0000000005C0: 10282919
	v_add_f32_e32 v25, 0x3effc000, v24                         // 0000000005C4: 063230FF 3EFFC000
	v_add_f32_e32 v24, 0x3f002000, v24                         // 0000000005CC: 063030FF 3F002000
	v_add_f32_e32 v20, 0.5, v20                                // 0000000005D4: 062828F0
	s_waitcnt lgkmcnt(0)                                       // 0000000005D8: BF8CC07F
	s_clause 0x8                                               // 0000000005DC: BFA10008
	image_sample_c_lz  v27, [v20, v25, v22], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000005E0: F0BC010A 00E41B14 00001619
	image_sample_c_lz  v28, [v20, v24, v22], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000005EC: F0BC010A 00E41C14 00001618
	image_sample_c_lz  v29, [v20, v25, v26], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000005F8: F0BC010A 00E41D14 00001A19
	image_sample_c_lz  v25, [v20, v25, v23], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000604: F0BC010A 00E41914 00001719
	image_sample_c_lz  v30, [v20, v21, v26], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000610: F0BC010A 00E41E14 00001A15
	image_sample_c_lz v22, v[20:22], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000061C: F0BC0108 00E41614
	image_sample_c_lz  v21, [v20, v21, v23], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000624: F0BC010A 00E41514 00001715
	image_sample_c_lz  v26, [v20, v24, v26], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000630: F0BC010A 00E41A14 00001A18
	image_sample_c_lz  v20, [v20, v24, v23], s[16:23], s[28:31] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000063C: F0BC010A 00E41414 00001718
	s_waitcnt vmcnt(6)                                         // 000000000648: BF8C3F76
	v_add_f32_e32 v23, v27, v29                                // 00000000064C: 062E3B1B
	s_waitcnt vmcnt(5)                                         // 000000000650: BF8C3F75
	v_add_f32_e32 v23, v25, v23                                // 000000000654: 062E2F19
	s_waitcnt vmcnt(4)                                         // 000000000658: BF8C3F74
	v_add_f32_e32 v23, v30, v23                                // 00000000065C: 062E2F1E
	s_waitcnt vmcnt(3)                                         // 000000000660: BF8C3F73
	v_add_f32_e32 v22, v22, v23                                // 000000000664: 062C2F16
	s_waitcnt vmcnt(2)                                         // 000000000668: BF8C3F72
	v_add_f32_e32 v21, v21, v22                                // 00000000066C: 062A2D15
	s_waitcnt vmcnt(1)                                         // 000000000670: BF8C3F71
	v_add_f32_e32 v21, v26, v21                                // 000000000674: 062A2B1A
	v_add_f32_e32 v21, v28, v21                                // 000000000678: 062A2B1C
	s_waitcnt vmcnt(0)                                         // 00000000067C: BF8C3F70
	v_add_f32_e32 v20, v20, v21                                // 000000000680: 06282B14
	v_mul_f32_e32 v20, 0x3de38e39, v20                         // 000000000684: 102828FF 3DE38E39
_L4:
	s_or_b64 exec, exec, s[0:1]                                // 00000000068C: 88FE007E
	s_waitcnt vmcnt(0)                                         // 000000000690: BF8C3F70
	v_fma_f32 v7, v7, -2.0, 1.0                                // 000000000694: D54B0007 03C9EB07
	v_add_f32_e32 v21, 1.0, v16                                // 00000000069C: 062A20F2
	v_fma_f32 v6, v6, -2.0, 1.0                                // 0000000006A0: D54B0006 03C9EB06
	v_fma_f32 v8, v8, 2.0, -1.0                                // 0000000006A8: D54B0008 03CDE908
	s_mov_b32 m0, s2                                           // 0000000006B0: BEFC0302
	v_mul_f32_e32 v22, v7, v18                                 // 0000000006B4: 102C2507
	v_rcp_f32_e32 v23, v21                                     // 0000000006B8: 7E2E5515
	v_add_f32_e32 v7, v18, v7                                  // 0000000006BC: 060E0F12
	v_interp_p1_f32_e32 v24, v0, attr2.y                       // 0000000006C0: C8600900
	v_interp_p1_f32_e32 v25, v0, attr2.x                       // 0000000006C4: C8640800
	v_fmac_f32_e32 v22, v6, v17                                // 0000000006C8: 562C2306
	v_add_f32_e32 v6, v17, v6                                  // 0000000006CC: 060C0D11
	s_load_dwordx8 s[16:23], s[24:25], 0xd0                    // 0000000006D0: F40C040C FA0000D0
	v_interp_p2_f32_e32 v24, v1, attr2.y                       // 0000000006D8: C8610901
	v_interp_p2_f32_e32 v25, v1, attr2.x                       // 0000000006DC: C8650801
	v_fmac_f32_e32 v22, v8, v21                                // 0000000006E0: 562C2B08
	v_mul_f32_e32 v26, v23, v18                                // 0000000006E4: 10342517
	v_mul_f32_e32 v27, v23, v17                                // 0000000006E8: 10362317
	v_mul_f32_e32 v21, v23, v21                                // 0000000006EC: 102A2B17
	v_add_f32_e32 v8, v16, v8                                  // 0000000006F0: 06101110
	v_interp_p1_f32_e32 v23, v0, attr2.z                       // 0000000006F4: C85C0A00
	v_fma_f32 v7, v26, v22, -v7                                // 0000000006F8: D54B0007 841E2D1A
	v_interp_p1_f32_e32 v26, v0, attr1.y                       // 000000000700: C8680500
	v_fma_f32 v6, v27, v22, -v6                                // 000000000704: D54B0006 841A2D1B
	v_interp_p1_f32_e32 v27, v0, attr1.x                       // 00000000070C: C86C0400
	v_fma_f32 v8, v21, v22, -v8                                // 000000000710: D54B0008 84222D15
	v_fmac_f32_e32 v18, 0.5, v7                                // 000000000718: 56240EF0
	v_interp_p2_f32_e32 v26, v1, attr1.y                       // 00000000071C: C8690501
	v_fmac_f32_e32 v17, 0.5, v6                                // 000000000720: 56220CF0
	v_mul_f32_e32 v21, v24, v24                                // 000000000724: 102A3118
	v_interp_p1_f32_e32 v7, v0, attr1.z                        // 000000000728: C81C0600
	v_mul_f32_e32 v6, v18, v18                                 // 00000000072C: 100C2512
	v_interp_p2_f32_e32 v27, v1, attr1.x                       // 000000000730: C86D0401
	v_mul_f32_e32 v22, v26, v26                                // 000000000734: 102C351A
	v_fmac_f32_e32 v16, 0.5, v8                                // 000000000738: 562010F0
	v_interp_p2_f32_e32 v23, v1, attr2.z                       // 00000000073C: C85D0A01
	v_fmac_f32_e32 v6, v17, v17                                // 000000000740: 560C2311
	v_fmac_f32_e32 v21, v25, v25                               // 000000000744: 562A3319
	v_interp_p2_f32_e32 v7, v1, attr1.z                        // 000000000748: C81D0601
	v_fmac_f32_e32 v22, v27, v27                               // 00000000074C: 562C371B
	v_rcp_f32_e32 v5, v5                                       // 000000000750: 7E0A5505
	v_fmac_f32_e32 v6, v16, v16                                // 000000000754: 560C2110
	v_fmac_f32_e32 v21, v23, v23                               // 000000000758: 562A2F17
	v_mul_f32_e32 v2, v2, v3                                   // 00000000075C: 10040702
	v_fmac_f32_e32 v22, v7, v7                                 // 000000000760: 562C0F07
	v_rsq_f32_e32 v8, v6                                       // 000000000764: 7E105D06
	v_cmp_neq_f32_e32 vcc_lo, 0, v6                            // 000000000768: 7C1A0C80
	v_rsq_f32_e32 v6, v21                                      // 00000000076C: 7E0C5D15
	v_cmp_neq_f32_e64 s0, 0, v21                               // 000000000770: D40D0000 00022A80
	v_rsq_f32_e32 v21, v22                                     // 000000000778: 7E2A5D16
	v_cndmask_b32_e32 v8, 0, v8, vcc_lo                        // 00000000077C: 02101080
	v_cndmask_b32_e64 v6, 0, v6, s0                            // 000000000780: D5010006 00020C80
	v_cmp_neq_f32_e64 s0, 0, v22                               // 000000000788: D40D0000 00022C80
	v_interp_p1_f32_e32 v22, v0, attr0.y                       // 000000000790: C8580100
	v_mul_f32_e32 v18, v8, v18                                 // 000000000794: 10242508
	v_mul_f32_e32 v17, v8, v17                                 // 000000000798: 10222308
	v_mul_f32_e32 v25, v6, v25                                 // 00000000079C: 10323306
	v_cndmask_b32_e64 v21, 0, v21, s0                          // 0000000007A0: D5010015 00022A80
	v_mul_f32_e32 v24, v6, v24                                 // 0000000007A8: 10303106
	v_mul_f32_e32 v6, v6, v23                                  // 0000000007AC: 100C2F06
	v_interp_p1_f32_e32 v23, v0, attr0.x                       // 0000000007B0: C85C0000
	v_interp_p2_f32_e32 v22, v1, attr0.y                       // 0000000007B4: C8590101
	v_mul_f32_e32 v27, v21, v27                                // 0000000007B8: 10363715
	v_mul_f32_e32 v26, v21, v26                                // 0000000007BC: 10343515
	v_mul_f32_e32 v7, v21, v7                                  // 0000000007C0: 100E0F15
	v_interp_p1_f32_e32 v21, v0, attr0.z                       // 0000000007C4: C8540200
	v_interp_p2_f32_e32 v23, v1, attr0.x                       // 0000000007C8: C85D0001
	v_mul_f32_e32 v25, v18, v25                                // 0000000007CC: 10323312
	v_mul_f32_e32 v24, v18, v24                                // 0000000007D0: 10303112
	v_mul_f32_e32 v6, v18, v6                                  // 0000000007D4: 100C0D12
	v_mul_f32_e32 v18, v22, v22                                // 0000000007D8: 10242D16
	v_interp_p2_f32_e32 v21, v1, attr0.z                       // 0000000007DC: C8550201
	v_fmac_f32_e32 v25, v17, v27                               // 0000000007E0: 56323711
	v_fmac_f32_e32 v24, v17, v26                               // 0000000007E4: 56303511
	v_fmac_f32_e32 v6, v17, v7                                 // 0000000007E8: 560C0F11
	v_fmac_f32_e32 v18, v23, v23                               // 0000000007EC: 56242F17
	v_interp_p1_f32_e32 v7, v0, attr7.y                        // 0000000007F0: C81C1D00
	v_mul_f32_e32 v8, v8, v16                                  // 0000000007F4: 10102108
	v_mul_f32_e32 v26, 0x40e00000, v11                         // 0000000007F8: 103416FF 40E00000
	v_fmac_f32_e32 v18, v21, v21                               // 000000000800: 56242B15
	v_interp_p2_f32_e32 v7, v1, attr7.y                        // 000000000804: C81D1D01
	v_rsq_f32_e32 v27, v18                                     // 000000000808: 7E365D12
	v_cmp_neq_f32_e32 vcc_lo, 0, v18                           // 00000000080C: 7C1A2480
	v_cndmask_b32_e32 v18, 0, v27, vcc_lo                      // 000000000810: 02243680
	v_mul_f32_e32 v16, v18, v22                                // 000000000814: 10202D12
	v_interp_p1_f32_e32 v22, v0, attr7.x                       // 000000000818: C8581C00
	v_mul_f32_e32 v17, v18, v23                                // 00000000081C: 10222F12
	v_mul_f32_e32 v18, v18, v21                                // 000000000820: 10242B12
	v_mul_f32_e32 v21, v7, v7                                  // 000000000824: 102A0F07
	v_fmac_f32_e32 v24, v8, v16                                // 000000000828: 56302108
	v_interp_p1_f32_e32 v16, v0, attr7.z                       // 00000000082C: C8401E00
	v_interp_p2_f32_e32 v22, v1, attr7.x                       // 000000000830: C8591C01
	v_fmac_f32_e32 v25, v8, v17                                // 000000000834: 56322308
	v_fmac_f32_e32 v6, v8, v18                                 // 000000000838: 560C2508
	v_mul_f32_e32 v17, v24, v24                                // 00000000083C: 10223118
	v_interp_p2_f32_e32 v16, v1, attr7.z                       // 000000000840: C8411E01
	v_fmac_f32_e32 v21, v22, v22                               // 000000000844: 562A2D16
	v_interp_p1_f32_e32 v0, v0, attr9.x                        // 000000000848: C8002400
	v_fmac_f32_e32 v17, v25, v25                               // 00000000084C: 56223319
	v_fmac_f32_e32 v21, v16, v16                               // 000000000850: 562A2110
	v_interp_p2_f32_e32 v0, v1, attr9.x                        // 000000000854: C8012401
	v_fmac_f32_e32 v17, v6, v6                                 // 000000000858: 56220D06
	v_rsq_f32_e32 v8, v21                                      // 00000000085C: 7E105D15
	v_cmp_neq_f32_e32 vcc_lo, 0, v21                           // 000000000860: 7C1A2A80
	v_rsq_f32_e32 v18, v17                                     // 000000000864: 7E245D11
	v_cndmask_b32_e32 v8, 0, v8, vcc_lo                        // 000000000868: 02101080
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 00000000086C: 7C1A2280
	v_mul_f32_e64 v7, v8, -v7                                  // 000000000870: D5080007 40020F08
	v_cndmask_b32_e32 v17, 0, v18, vcc_lo                      // 000000000878: 02222480
	v_mul_f32_e64 v21, v8, -v22                                // 00000000087C: D5080015 40022D08
	v_mul_f32_e64 v8, v8, -v16                                 // 000000000884: D5080008 40022108
	v_mul_f32_e32 v18, v17, v24                                // 00000000088C: 10243111
	v_mul_f32_e32 v22, v17, v25                                // 000000000890: 102C3311
	v_mul_f32_e32 v16, v17, v6                                 // 000000000894: 10200D11
	v_mul_f32_e32 v23, v18, v7                                 // 000000000898: 102E0F12
	v_fmac_f32_e32 v23, v22, v21                               // 00000000089C: 562E2B16
	v_fma_f32 v6, v16, v8, v23 mul:2                           // 0000000008A0: D54B0006 0C5E1110
	v_fma_f32 v17, -v22, v6, v21                               // 0000000008A8: D54B0011 24560D16
	v_fma_f32 v7, -v18, v6, v7                                 // 0000000008B0: D54B0007 241E0D12
	v_fma_f32 v6, -v16, v6, v8                                 // 0000000008B8: D54B0006 24220D10
	v_cubema_f32 v8, v17, v7, v6                               // 0000000008C0: D5470008 041A0F11
	v_cubeid_f32 v21, v17, v7, v6                              // 0000000008C8: D5440015 041A0F11
	v_cubesc_f32 v23, v17, v7, v6                              // 0000000008D0: D5450017 041A0F11
	v_cubetc_f32 v6, v17, v7, v6                               // 0000000008D8: D5460006 041A0F11
	v_rcp_f32_e64 v8, |v8|                                     // 0000000008E0: D5AA0108 00000108
	v_rndne_f32_e32 v25, v21                                   // 0000000008E8: 7E324715
	v_fmaak_f32 v23, v8, v23, 0x3fc00000                       // 0000000008EC: 5A2E2F08 3FC00000
	v_fmaak_f32 v24, v8, v6, 0x3fc00000                        // 0000000008F4: 5A300D08 3FC00000
	s_waitcnt lgkmcnt(0)                                       // 0000000008FC: BF8CC07F
	image_sample_l v[6:8], v[23:26], s[16:23], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_CUBE// 000000000900: F0900718 00640617
	s_clause 0x1                                               // 000000000908: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[4:7], 0xd0                 // 00000000090C: F4240002 FA0000D0
	s_buffer_load_dword s2, s[4:7], 0xd8                       // 000000000914: F4200082 FA0000D8
	s_buffer_load_dwordx2 s[4:5], s[8:11], 0xd0                // 00000000091C: F4240104 FA0000D0
	v_mul_f32_e32 v24, 0x3dcccccd, v10                         // 000000000924: 103014FF 3DCCCCCD
	s_waitcnt lgkmcnt(0)                                       // 00000000092C: BF8CC07F
	v_mul_f32_e64 v17, s1, s1                                  // 000000000930: D5080011 00000201
	v_fmac_f32_e64 v17, s0, s0                                 // 000000000938: D52B0011 00000000
	v_fmac_f32_e64 v17, s2, s2                                 // 000000000940: D52B0011 00000402
	v_rsq_f32_e32 v21, v17                                     // 000000000948: 7E2A5D11
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 00000000094C: 7C1A2280
	v_cndmask_b32_e32 v17, 0, v21, vcc_lo                      // 000000000950: 02222A80
	v_mul_f32_e32 v21, s1, v17                                 // 000000000954: 102A2201
	v_mul_f32_e64 v23, v17, -s0                                // 000000000958: D5080017 40000111
	v_mul_f32_e32 v17, s2, v17                                 // 000000000960: 10222202
	s_buffer_load_dwordx4 s[0:3], s[8:11], 0xc0                // 000000000964: F4280004 FA0000C0
	v_subrev_f32_e32 v0, s4, v0                                // 00000000096C: 0A000004
	v_mul_f32_e32 v21, v18, v21                                // 000000000970: 102A2B12
	v_fma_f32 v21, v22, v23, -v21                              // 000000000974: D54B0015 84562F16
	v_sub_f32_e64 v23, s5, s4                                  // 00000000097C: D5040017 00000805
	v_fma_f32 v17, -v16, v17, v21                              // 000000000984: D54B0011 24562310
	v_sub_f32_e32 v21, 1.0, v11                                // 00000000098C: 082A16F2
	v_rcp_f32_e32 v1, v23                                      // 000000000990: 7E025517
	v_mul_f32_e32 v23, 0x3e19999a, v10                         // 000000000994: 102E14FF 3E19999A
	v_max_f32_e32 v17, 0, v17                                  // 00000000099C: 20222280
	v_mul_f32_e32 v21, v21, v9                                 // 0000000009A0: 102A1315
	v_mul_f32_e32 v17, v20, v17                                // 0000000009A4: 10222314
	v_mul_f32_e64 v0, v0, v1 clamp                             // 0000000009A8: D5088000 00020300
	v_rcp_f32_e32 v1, v5                                       // 0000000009B0: 7E025505
	s_waitcnt lgkmcnt(0)                                       // 0000000009B4: BF8CC07F
	v_mul_f32_e32 v0, s3, v0                                   // 0000000009B8: 10000003
	v_mul_f32_e32 v1, v4, v1                                   // 0000000009BC: 10020304
	s_waitcnt vmcnt(0)                                         // 0000000009C0: BF8C3F70
	v_fma_f32 v6, v6, v21, v24                                 // 0000000009C4: D54B0006 04622B06
	v_fmac_f32_e32 v24, v7, v21                                // 0000000009CC: 56302B07
	v_fmac_f32_e32 v23, v8, v21                                // 0000000009D0: 562E2B08
	v_fma_f32 v8, v22, 0.5, 0.5                                // 0000000009D4: D54B0008 03C1E116
	v_fmac_f32_e32 v6, v17, v13                                // 0000000009DC: 560C1B11
	v_fmac_f32_e32 v24, v17, v14                               // 0000000009E0: 56301D11
	v_fmac_f32_e32 v23, v17, v15                               // 0000000009E4: 562E1F11
	v_fma_f32 v13, v18, 0.5, 0.5                               // 0000000009E8: D54B000D 03C1E112
	v_sub_f32_e32 v3, s0, v6                                   // 0000000009F0: 08060C00
	v_sub_f32_e32 v5, s1, v24                                  // 0000000009F4: 080A3001
	v_sub_f32_e32 v7, s2, v23                                  // 0000000009F8: 080E2E02
	v_fmac_f32_e32 v6, v0, v3                                  // 0000000009FC: 560C0700
	v_fmac_f32_e32 v24, v0, v5                                 // 000000000A00: 56300B00
	v_fmac_f32_e32 v23, v0, v7                                 // 000000000A04: 562E0F00
	v_fma_f32 v0, v16, 0.5, 0.5                                // 000000000A08: D54B0000 03C1E110
	v_mov_b32_e32 v3, 0                                        // 000000000A10: 7E060280
	exp mrt0 v6, v24, v23, v12 vm                              // 000000000A14: F800100F 0C171806
	exp mrt1 v8, v13, v0, v11 vm                               // 000000000A1C: F800101F 0B000D08
	exp mrt2 v9, v11, v10, v2 vm                               // 000000000A24: F800102F 020A0B09
	exp mrt3 v3, v3, v1, v19 done vm                           // 000000000A2C: F800183F 13010303
	s_endpgm                                                   // 000000000A34: BF810000
