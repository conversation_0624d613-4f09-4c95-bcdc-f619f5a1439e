// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float3 ViewDir : TEXCOORD3;
  float4 ReflectionPos : TEXCOORD4;
  float4 RefractionPos : TEXCOORD5;
  float2 WaveOffset : TEXCOORD6;
};

cbuffer WaterMaterial : register(b0)
{
  float3 WaterColor;
  float WaterAlpha;
  float FresnelStrength;
  float ReflectionStrength;
  float RefractionStrength;
  float NormalStrength;
  float SpecularPower;
  float3 SpecularColor;
  float FoamThreshold;
  float3 FoamColor;
  float Time;
}

Texture2D WaterNormalTexture : register(t0);
Texture2D ReflectionTexture : register(t1);
Texture2D RefractionTexture : register(t2);
Texture2D FoamTexture : register(t3);
Texture2D DepthTexture : register(t4);
SamplerState LinearSampler : register(s0);
float3 calculateFresnel(float3 viewDir, float3 normal, float fresnelStrength)
{
  float fresnel = dot(viewDir, normal);
  fresnel = saturate(1.0f - fresnel);
  fresnel = pow(fresnel, fresnelStrength);
  return float3(fresnel, fresnel, fresnel);
}

float4 main(PSInput input) : SV_TARGET
{
  float3 normal = normalize(input.Normal);
  float3 viewDir = normalize(input.ViewDir);
  float2 normalCoord1 = (input.TexCoord * 4.0f) + input.WaveOffset;
  float2 normalCoord2 = (input.TexCoord * 2.0f) - (input.WaveOffset * 0.5f);
  float3 normalMap1 = (WaterNormalTexture.Sample(LinearSampler, normalCoord1).rgb * 2.0f) - 1.0f;
  float3 normalMap2 = (WaterNormalTexture.Sample(LinearSampler, normalCoord2).rgb * 2.0f) - 1.0f;
  float3 combinedNormal = normalize(normalMap1 + normalMap2);
  combinedNormal = normalize(normal + (combinedNormal * NormalStrength));
  float2 reflectionCoords = (input.ReflectionPos.xy / input.ReflectionPos.w * 0.5f) + 0.5f;
  float2 refractionCoords = (input.RefractionPos.xy / input.RefractionPos.w * 0.5f) + 0.5f;
  reflectionCoords += (combinedNormal.xz * 0.02f);
  refractionCoords += (combinedNormal.xz * 0.01f);
  float3 reflectionColor = ReflectionTexture.Sample(LinearSampler, reflectionCoords).rgb;
  float3 refractionColor = RefractionTexture.Sample(LinearSampler, refractionCoords).rgb;
  float3 fresnel = calculateFresnel(viewDir, combinedNormal, FresnelStrength);
  float3 waterSurfaceColor = lerp((refractionColor * RefractionStrength), (reflectionColor * ReflectionStrength), fresnel);
  waterSurfaceColor = lerp(waterSurfaceColor, WaterColor, 0.3f);
  float3 lightDir = normalize(float3(0.5f, 1.0f, 0.3f));
  float3 halfDir = normalize(lightDir + viewDir);
  float specular = pow(max(0.0f, dot(combinedNormal, halfDir)), SpecularPower);
  waterSurfaceColor += (SpecularColor * specular);
  float depth = DepthTexture.Sample(LinearSampler, refractionCoords).r;
  float waterDepth = input.Position.z / input.Position.w;
  float depthDifference = abs(depth - waterDepth);
  float foamFactor = 1.0f - saturate(depthDifference / FoamThreshold);
  float2 foamCoord = (input.TexCoord * 8.0f) + (input.WaveOffset * 2.0f);
  float foamNoise = FoamTexture.Sample(LinearSampler, foamCoord).r;
  foamFactor *= foamNoise;
  waterSurfaceColor = lerp(waterSurfaceColor, FoamColor, foamFactor);
  float finalAlpha = WaterAlpha + (foamFactor * 0.5f);
  finalAlpha = saturate(finalAlpha);
  return float4(waterSurfaceColor, finalAlpha);
}

