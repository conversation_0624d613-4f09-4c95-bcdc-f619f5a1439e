;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TANGENT                  0   xyz         2     NONE   float       
; TEXCOORD                 0   xy          3     NONE   float   xy  
; COLOR                    0   xyzw        4     NONE   float      w
; SV_VertexID              0   x           5   VERTID    uint       
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; TEXCOORD                 3      w        0     NONE   float      w
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TEXCOORD                 4      w        1     NONE   float      w
; TANGENT                  0   xyz         2     NONE   float   xyz 
; TEXCOORD                 5      w        2     NONE   float      w
; BITANGENT                0   xyz         3     NONE   float   xyz 
; TEXCOORD                 0   xy          4     NONE   float   xy  
; TEXCOORD                 1     zw        4     NONE   float     zw
; COLOR                    0   xyzw        5     NONE   float   xyzw
; TEXCOORD                 2   xyz         6     NONE   float   xyz 
;
; shader hash: df6ac8b5fb2870042c02add0a99be019
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 6
; SigOutputElements: 11
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 6
; SigOutputVectors[0]: 7
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TANGENT                  0                              
; TEXCOORD                 0                              
; COLOR                    0                              
; SV_VertexID              0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                 linear       
; NORMAL                   0                 linear       
; TANGENT                  0                 linear       
; BITANGENT                0                 linear       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; COLOR                    0                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 ViewMatrix;             ; Offset:    0
;       column_major float4x4 ProjectionMatrix;       ; Offset:   64
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:  128
;       float3 CameraPosition;                        ; Offset:  192
;       float Time;                                   ; Offset:  204
;       float3 LightDirection;                        ; Offset:  208
;       float TessellationLevel;                      ; Offset:  220
;       float2 HeightmapSize;                         ; Offset:  224
;       float HeightScale;                            ; Offset:  232
;       float DetailScale;                            ; Offset:  236
;       float LODDistance;                            ; Offset:  240
;   
;   } PerFrame;                                       ; Offset:    0 Size:   244
;
; }
;
; cbuffer PerObject
; {
;
;   struct hostlayout.PerObject
;   {
;
;       column_major float4x4 WorldMatrix;            ; Offset:    0
;       column_major float4x4 NormalMatrix;           ; Offset:   64
;       float3 BoundingBoxMin;                        ; Offset:  128
;       float3 BoundingBoxMax;                        ; Offset:  144
;       float2 TextureTiling;                         ; Offset:  160
;       float DisplacementStrength;                   ; Offset:  168
;       float _padding;                               ; Offset:  172
;   
;   } PerObject;                                      ; Offset:    0 Size:   176
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
; PerObject                         cbuffer      NA          NA     CB1            cb1     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; HeightmapTexture                  texture     f32          2d      T0             t0     1
; DetailHeightTexture               texture     f32          2d      T1             t2     1
;
;
; ViewId state:
;
; Number of inputs: 21, outputs: 27
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0 }
;   output 1 depends on inputs: { 1, 12, 13 }
;   output 2 depends on inputs: { 2 }
;   output 3 depends on inputs: { 0, 1, 2, 4, 5, 6, 12, 13 }
;   output 4 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 5 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 6 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 7 depends on inputs: { 12, 13 }
;   output 8 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 9 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 10 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 11 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 12 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 13 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 14 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 16 depends on inputs: { 12 }
;   output 17 depends on inputs: { 13 }
;   output 18 depends on inputs: { 12 }
;   output 19 depends on inputs: { 13 }
;   output 20 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 21 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 22 depends on inputs: { 4, 5, 6, 12, 13 }
;   output 23 depends on inputs: { 19 }
;   output 24 depends on inputs: { 0, 1, 2, 12, 13 }
;   output 25 depends on inputs: { 0, 1, 2, 12, 13 }
;   output 26 depends on inputs: { 0, 1, 2, 12, 13 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%hostlayout.PerFrame = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, <3 x float>, float, <2 x float>, float, float, float }
%hostlayout.PerObject = type { [4 x <4 x float>], [4 x <4 x float>], <3 x float>, <3 x float>, <2 x float>, float, float }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %16 = extractvalue %dx.types.CBufRet.f32 %15, 0
  %17 = extractvalue %dx.types.CBufRet.f32 %15, 1
  %18 = fmul fast float %16, %7
  %19 = fmul fast float %17, %8
  %20 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %18, float %19, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %21 = extractvalue %dx.types.ResRet.f32 %20, 0
  %22 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 14)  ; CBufferLoadLegacy(handle,regIndex)
  %23 = extractvalue %dx.types.CBufRet.f32 %22, 3
  %24 = fmul fast float %23, %18
  %25 = fmul fast float %23, %19
  %26 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %24, float %25, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %27 = extractvalue %dx.types.ResRet.f32 %26, 0
  %28 = extractvalue %dx.types.CBufRet.f32 %22, 2
  %29 = fmul fast float %28, %21
  %30 = fadd fast float %27, -5.000000e-01
  %31 = extractvalue %dx.types.CBufRet.f32 %15, 2
  %32 = fmul fast float %30, %31
  %33 = fadd fast float %32, %29
  %34 = fadd fast float %33, %13
  %35 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %36 = extractvalue %dx.types.CBufRet.f32 %35, 0
  %37 = extractvalue %dx.types.CBufRet.f32 %35, 1
  %38 = extractvalue %dx.types.CBufRet.f32 %35, 2
  %39 = extractvalue %dx.types.CBufRet.f32 %35, 3
  %40 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %41 = extractvalue %dx.types.CBufRet.f32 %40, 0
  %42 = extractvalue %dx.types.CBufRet.f32 %40, 1
  %43 = extractvalue %dx.types.CBufRet.f32 %40, 2
  %44 = extractvalue %dx.types.CBufRet.f32 %40, 3
  %45 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %46 = extractvalue %dx.types.CBufRet.f32 %45, 0
  %47 = extractvalue %dx.types.CBufRet.f32 %45, 1
  %48 = extractvalue %dx.types.CBufRet.f32 %45, 2
  %49 = extractvalue %dx.types.CBufRet.f32 %45, 3
  %50 = fmul fast float %36, %12
  %51 = call float @dx.op.tertiary.f32(i32 46, float %34, float %37, float %50)  ; FMad(a,b,c)
  %52 = call float @dx.op.tertiary.f32(i32 46, float %14, float %38, float %51)  ; FMad(a,b,c)
  %53 = fadd fast float %52, %39
  %54 = fmul fast float %41, %12
  %55 = call float @dx.op.tertiary.f32(i32 46, float %34, float %42, float %54)  ; FMad(a,b,c)
  %56 = call float @dx.op.tertiary.f32(i32 46, float %14, float %43, float %55)  ; FMad(a,b,c)
  %57 = fadd fast float %56, %44
  %58 = fmul fast float %46, %12
  %59 = call float @dx.op.tertiary.f32(i32 46, float %34, float %47, float %58)  ; FMad(a,b,c)
  %60 = call float @dx.op.tertiary.f32(i32 46, float %14, float %48, float %59)  ; FMad(a,b,c)
  %61 = fadd fast float %60, %49
  %62 = extractvalue %dx.types.CBufRet.f32 %22, 0
  %63 = extractvalue %dx.types.CBufRet.f32 %22, 1
  %64 = fdiv fast float 1.000000e+00, %62
  %65 = fdiv fast float 1.000000e+00, %63
  %66 = fsub fast float %18, %64
  %67 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %66, float %19, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %68 = extractvalue %dx.types.ResRet.f32 %67, 0
  %69 = fmul fast float %66, %23
  %70 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %69, float %25, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %71 = extractvalue %dx.types.ResRet.f32 %70, 0
  %72 = fmul fast float %68, %28
  %73 = fadd fast float %71, -5.000000e-01
  %74 = fmul fast float %73, %31
  %75 = fadd fast float %64, %18
  %76 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %75, float %19, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %77 = extractvalue %dx.types.ResRet.f32 %76, 0
  %78 = fmul fast float %75, %23
  %79 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %78, float %25, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %80 = extractvalue %dx.types.ResRet.f32 %79, 0
  %81 = fmul fast float %77, %28
  %82 = fadd fast float %80, -5.000000e-01
  %83 = fmul fast float %82, %31
  %84 = fsub fast float %19, %65
  %85 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %18, float %84, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %86 = extractvalue %dx.types.ResRet.f32 %85, 0
  %87 = fmul fast float %84, %23
  %88 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %24, float %87, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %89 = extractvalue %dx.types.ResRet.f32 %88, 0
  %90 = fmul fast float %86, %28
  %91 = fadd fast float %89, -5.000000e-01
  %92 = fmul fast float %91, %31
  %93 = fadd fast float %65, %19
  %94 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %18, float %93, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %95 = extractvalue %dx.types.ResRet.f32 %94, 0
  %96 = fmul fast float %93, %23
  %97 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %24, float %96, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %98 = extractvalue %dx.types.ResRet.f32 %97, 0
  %99 = fmul fast float %95, %28
  %100 = fadd fast float %98, -5.000000e-01
  %101 = fmul fast float %100, %31
  %102 = fadd fast float %74, %72
  %103 = fsub fast float %102, %81
  %104 = fsub fast float %103, %83
  %105 = fmul fast float %64, 2.000000e+00
  %106 = fdiv fast float %104, %105
  %107 = fadd fast float %92, %90
  %108 = fsub fast float %107, %99
  %109 = fsub fast float %108, %101
  %110 = fmul fast float %65, 2.000000e+00
  %111 = fdiv fast float %109, %110
  %112 = call float @dx.op.dot3.f32(i32 55, float %106, float 1.000000e+00, float %111, float %106, float 1.000000e+00, float %111)  ; Dot3(ax,ay,az,bx,by,bz)
  %113 = call float @dx.op.unary.f32(i32 25, float %112)  ; Rsqrt(value)
  %114 = fmul fast float %113, %106
  %115 = fmul fast float %113, %111
  %116 = fsub fast float %114, %9
  %117 = fsub fast float %113, %10
  %118 = fsub fast float %115, %11
  %119 = fmul fast float %116, 0x3FE99999A0000000
  %120 = fmul fast float %117, 0x3FE99999A0000000
  %121 = fmul fast float %118, 0x3FE99999A0000000
  %122 = fadd fast float %119, %9
  %123 = fadd fast float %120, %10
  %124 = fadd fast float %121, %11
  %125 = call float @dx.op.dot3.f32(i32 55, float %122, float %123, float %124, float %122, float %123, float %124)  ; Dot3(ax,ay,az,bx,by,bz)
  %126 = call float @dx.op.unary.f32(i32 25, float %125)  ; Rsqrt(value)
  %127 = fmul fast float %122, %126
  %128 = fmul fast float %123, %126
  %129 = fmul fast float %124, %126
  %130 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %131 = extractvalue %dx.types.CBufRet.f32 %130, 0
  %132 = extractvalue %dx.types.CBufRet.f32 %130, 1
  %133 = extractvalue %dx.types.CBufRet.f32 %130, 2
  %134 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %135 = extractvalue %dx.types.CBufRet.f32 %134, 0
  %136 = extractvalue %dx.types.CBufRet.f32 %134, 1
  %137 = extractvalue %dx.types.CBufRet.f32 %134, 2
  %138 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %139 = extractvalue %dx.types.CBufRet.f32 %138, 0
  %140 = extractvalue %dx.types.CBufRet.f32 %138, 1
  %141 = extractvalue %dx.types.CBufRet.f32 %138, 2
  %142 = fmul fast float %127, %131
  %143 = call float @dx.op.tertiary.f32(i32 46, float %128, float %132, float %142)  ; FMad(a,b,c)
  %144 = call float @dx.op.tertiary.f32(i32 46, float %129, float %133, float %143)  ; FMad(a,b,c)
  %145 = fmul fast float %127, %135
  %146 = call float @dx.op.tertiary.f32(i32 46, float %128, float %136, float %145)  ; FMad(a,b,c)
  %147 = call float @dx.op.tertiary.f32(i32 46, float %129, float %137, float %146)  ; FMad(a,b,c)
  %148 = fmul fast float %139, %127
  %149 = call float @dx.op.tertiary.f32(i32 46, float %128, float %140, float %148)  ; FMad(a,b,c)
  %150 = call float @dx.op.tertiary.f32(i32 46, float %129, float %141, float %149)  ; FMad(a,b,c)
  %151 = call float @dx.op.dot3.f32(i32 55, float %144, float %147, float %150, float %144, float %147, float %150)  ; Dot3(ax,ay,az,bx,by,bz)
  %152 = call float @dx.op.unary.f32(i32 25, float %151)  ; Rsqrt(value)
  %153 = fmul fast float %152, %144
  %154 = fmul fast float %152, %147
  %155 = fmul fast float %152, %150
  %156 = fsub fast float -0.000000e+00, %29
  %157 = fsub fast float %156, %32
  %158 = fadd fast float %157, %81
  %159 = fadd fast float %158, %83
  %160 = call float @dx.op.dot3.f32(i32 55, float %64, float %159, float 0.000000e+00, float %64, float %159, float 0.000000e+00)  ; Dot3(ax,ay,az,bx,by,bz)
  %161 = call float @dx.op.unary.f32(i32 25, float %160)  ; Rsqrt(value)
  %162 = fmul fast float %161, %64
  %163 = fmul fast float %161, %159
  %164 = call float @dx.op.dot3.f32(i32 55, float %162, float %163, float 0.000000e+00, float %127, float %128, float %129)  ; Dot3(ax,ay,az,bx,by,bz)
  %165 = fmul fast float %164, %127
  %166 = fmul fast float %164, %128
  %167 = fsub fast float %162, %165
  %168 = fsub fast float %163, %166
  %169 = fmul fast float %129, %164
  %170 = fsub fast float -0.000000e+00, %169
  %171 = call float @dx.op.dot3.f32(i32 55, float %167, float %168, float %170, float %167, float %168, float %170)  ; Dot3(ax,ay,az,bx,by,bz)
  %172 = call float @dx.op.unary.f32(i32 25, float %171)  ; Rsqrt(value)
  %173 = fmul fast float %167, %172
  %174 = fmul fast float %168, %172
  %175 = fmul fast float %172, %170
  %176 = fmul fast float %173, %131
  %177 = call float @dx.op.tertiary.f32(i32 46, float %174, float %132, float %176)  ; FMad(a,b,c)
  %178 = call float @dx.op.tertiary.f32(i32 46, float %175, float %133, float %177)  ; FMad(a,b,c)
  %179 = fmul fast float %173, %135
  %180 = call float @dx.op.tertiary.f32(i32 46, float %174, float %136, float %179)  ; FMad(a,b,c)
  %181 = call float @dx.op.tertiary.f32(i32 46, float %175, float %137, float %180)  ; FMad(a,b,c)
  %182 = fmul fast float %173, %139
  %183 = call float @dx.op.tertiary.f32(i32 46, float %174, float %140, float %182)  ; FMad(a,b,c)
  %184 = call float @dx.op.tertiary.f32(i32 46, float %175, float %141, float %183)  ; FMad(a,b,c)
  %185 = call float @dx.op.dot3.f32(i32 55, float %178, float %181, float %184, float %178, float %181, float %184)  ; Dot3(ax,ay,az,bx,by,bz)
  %186 = call float @dx.op.unary.f32(i32 25, float %185)  ; Rsqrt(value)
  %187 = fmul fast float %186, %178
  %188 = fmul fast float %186, %181
  %189 = fmul fast float %186, %184
  %190 = fmul fast float %189, %154
  %191 = fmul fast float %188, %155
  %192 = fsub fast float %190, %191
  %193 = fmul fast float %187, %155
  %194 = fmul fast float %189, %153
  %195 = fsub fast float %193, %194
  %196 = fmul fast float %188, %153
  %197 = fmul fast float %187, %154
  %198 = fsub fast float %196, %197
  %199 = call float @dx.op.dot3.f32(i32 55, float %192, float %195, float %198, float %192, float %195, float %198)  ; Dot3(ax,ay,az,bx,by,bz)
  %200 = call float @dx.op.unary.f32(i32 25, float %199)  ; Rsqrt(value)
  %201 = fmul fast float %192, %200
  %202 = fmul fast float %195, %200
  %203 = fmul fast float %198, %200
  %204 = call float @dx.op.dot3.f32(i32 55, float %153, float %154, float %155, float 0.000000e+00, float 1.000000e+00, float 0.000000e+00)  ; Dot3(ax,ay,az,bx,by,bz)
  %205 = fsub fast float 1.000000e+00, %204
  %206 = fdiv fast float %33, %28
  %207 = call float @dx.op.unary.f32(i32 7, float %206)  ; Saturate(value)
  %208 = fmul fast float %207, 0x3FE3333340000000
  %209 = fmul fast float %207, 0x3FD99999A0000000
  %210 = fmul fast float %207, 0x3FE9999980000000
  %211 = fadd fast float %208, 0x3FC99999A0000000
  %212 = fadd fast float %209, 0x3FD99999A0000000
  %213 = fadd fast float %210, 0x3FB99999A0000000
  %214 = fcmp fast ogt float %205, 5.000000e-01
  br i1 %214, label %215, label %226

; <label>:215                                     ; preds = %0
  %216 = fsub fast float 5.000000e-01, %204
  %217 = fmul fast float %216, 2.000000e+00
  %218 = fsub fast float 0x3FD3333340000000, %208
  %219 = fsub fast float 0x3FC99999C0000000, %210
  %220 = fmul fast float %218, %217
  %221 = fmul fast float %209, %217
  %222 = fmul fast float %219, %217
  %223 = fadd fast float %220, %211
  %224 = fsub fast float %212, %221
  %225 = fadd fast float %222, %213
  br label %226

; <label>:226                                     ; preds = %215, %0
  %227 = phi float [ %223, %215 ], [ %211, %0 ]
  %228 = phi float [ %224, %215 ], [ %212, %0 ]
  %229 = phi float [ %225, %215 ], [ %213, %0 ]
  %230 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %231 = extractvalue %dx.types.CBufRet.f32 %230, 0
  %232 = extractvalue %dx.types.CBufRet.f32 %230, 1
  %233 = extractvalue %dx.types.CBufRet.f32 %230, 2
  %234 = fsub fast float %231, %53
  %235 = fsub fast float %232, %57
  %236 = fsub fast float %233, %61
  %237 = fmul fast float %234, %234
  %238 = fmul fast float %235, %235
  %239 = fadd fast float %237, %238
  %240 = fmul fast float %236, %236
  %241 = fadd fast float %239, %240
  %242 = call float @dx.op.unary.f32(i32 24, float %241)  ; Sqrt(value)
  %243 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 13)  ; CBufferLoadLegacy(handle,regIndex)
  %244 = extractvalue %dx.types.CBufRet.f32 %243, 3
  %245 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %5, i32 15)  ; CBufferLoadLegacy(handle,regIndex)
  %246 = extractvalue %dx.types.CBufRet.f32 %245, 0
  %247 = call float @dx.op.binary.f32(i32 35, float %242, float 1.000000e+00)  ; FMax(a,b)
  %248 = fdiv fast float %246, %247
  %249 = fmul fast float %248, %244
  %250 = call float @dx.op.binary.f32(i32 35, float %249, float 1.000000e+00)  ; FMax(a,b)
  %251 = call float @dx.op.binary.f32(i32 36, float %250, float 6.400000e+01)  ; FMin(a,b)
  %252 = fmul fast float %205, 2.000000e+00
  %253 = fadd fast float %252, 1.000000e+00
  %254 = fmul fast float %251, %253
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %12)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %34)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %14)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %153)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %154)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %155)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %187)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %188)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %189)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %201)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %202)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %203)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %18)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %19)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %24)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %25)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %227)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 1, float %228)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 2, float %229)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 3, float %6)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %53)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 1, float %57)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 2, float %61)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 0, float %254)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 9, i32 0, i8 0, float %33)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 10, i32 0, i8 0, float %205)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!14}
!dx.entryPoints = !{!15}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{!5, null, !9, !12}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!9 = !{!10, !11}
!10 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 244, null}
!11 = !{i32 1, %hostlayout.PerObject* undef, !"", i32 0, i32 1, i32 1, i32 176, null}
!12 = !{!13}
!13 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!14 = !{[23 x i32] [i32 21, i32 27, i32 117440521, i32 117440522, i32 117440524, i32 0, i32 7372664, i32 7372664, i32 7372664, i32 0, i32 0, i32 0, i32 0, i32 0, i32 125140986, i32 125468666, i32 0, i32 0, i32 0, i32 0, i32 0, i32 8388608, i32 0]}
!15 = !{void ()* @main, !"main", !16, !4, null}
!16 = !{!17, !28, null}
!17 = !{!18, !21, !22, !23, !25, !27}
!18 = !{i32 0, !"POSITION", i8 9, i8 0, !19, i8 0, i32 1, i8 3, i32 0, i8 0, !20}
!19 = !{i32 0}
!20 = !{i32 3, i32 7}
!21 = !{i32 1, !"NORMAL", i8 9, i8 0, !19, i8 0, i32 1, i8 3, i32 1, i8 0, !20}
!22 = !{i32 2, !"TANGENT", i8 9, i8 0, !19, i8 0, i32 1, i8 3, i32 2, i8 0, null}
!23 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !19, i8 0, i32 1, i8 2, i32 3, i8 0, !24}
!24 = !{i32 3, i32 3}
!25 = !{i32 4, !"COLOR", i8 9, i8 0, !19, i8 0, i32 1, i8 4, i32 4, i8 0, !26}
!26 = !{i32 3, i32 8}
!27 = !{i32 5, !"SV_VertexID", i8 5, i8 1, !19, i8 0, i32 1, i8 1, i32 5, i8 0, null}
!28 = !{!29, !30, !31, !32, !33, !34, !36, !38, !40, !43, !45}
!29 = !{i32 0, !"POSITION", i8 9, i8 0, !19, i8 2, i32 1, i8 3, i32 0, i8 0, !20}
!30 = !{i32 1, !"NORMAL", i8 9, i8 0, !19, i8 2, i32 1, i8 3, i32 1, i8 0, !20}
!31 = !{i32 2, !"TANGENT", i8 9, i8 0, !19, i8 2, i32 1, i8 3, i32 2, i8 0, !20}
!32 = !{i32 3, !"BITANGENT", i8 9, i8 0, !19, i8 2, i32 1, i8 3, i32 3, i8 0, !20}
!33 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !19, i8 2, i32 1, i8 2, i32 4, i8 0, !24}
!34 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 2, i32 4, i8 2, !24}
!35 = !{i32 1}
!36 = !{i32 6, !"COLOR", i8 9, i8 0, !19, i8 2, i32 1, i8 4, i32 5, i8 0, !37}
!37 = !{i32 3, i32 15}
!38 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !39, i8 2, i32 1, i8 3, i32 6, i8 0, !20}
!39 = !{i32 2}
!40 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !41, i8 2, i32 1, i8 1, i32 0, i8 3, !42}
!41 = !{i32 3}
!42 = !{i32 3, i32 1}
!43 = !{i32 9, !"TEXCOORD", i8 9, i8 0, !44, i8 2, i32 1, i8 1, i32 1, i8 3, !42}
!44 = !{i32 4}
!45 = !{i32 10, !"TEXCOORD", i8 9, i8 0, !46, i8 2, i32 1, i8 1, i32 2, i8 3, !42}
!46 = !{i32 5}
