// Water Surface Pixel Shader
// Tests complex water rendering with reflections and refractions

cbuffer WaterMaterial : register(b0)
{
    float3 WaterColor;
    float WaterAlpha;
    float FresnelStrength;
    float ReflectionStrength;
    float RefractionStrength;
    float NormalStrength;
    float SpecularPower;
    float3 SpecularColor;
    float FoamThreshold;
    float3 FoamColor;
    float Time;
};

Texture2D WaterNormalTexture : register(t0);
Texture2D ReflectionTexture : register(t1);
Texture2D RefractionTexture : register(t2);
Texture2D FoamTexture : register(t3);
Texture2D DepthTexture : register(t4);
SamplerState LinearSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float3 ViewDir : TEXCOORD3;
    float4 ReflectionPos : TEXCOORD4;
    float4 RefractionPos : TEXCOORD5;
    float2 WaveOffset : TEXCOORD6;
};

float3 calculateFresnel(float3 viewDir, float3 normal, float fresnelStrength)
{
    float fresnel = dot(viewDir, normal);
    fresnel = saturate(1.0 - fresnel);
    fresnel = pow(fresnel, fresnelStrength);
    return float3(fresnel, fresnel, fresnel);
}

float4 main(PSInput input) : SV_TARGET
{
    // Normalize interpolated vectors
    float3 normal = normalize(input.Normal);
    float3 viewDir = normalize(input.ViewDir);
    
    // Sample normal maps with different scales and offsets for variation
    float2 normalCoord1 = input.TexCoord * 4.0 + input.WaveOffset;
    float2 normalCoord2 = input.TexCoord * 2.0 - input.WaveOffset * 0.5;
    
    float3 normalMap1 = WaterNormalTexture.Sample(LinearSampler, normalCoord1).rgb * 2.0 - 1.0;
    float3 normalMap2 = WaterNormalTexture.Sample(LinearSampler, normalCoord2).rgb * 2.0 - 1.0;
    
    // Combine normal maps
    float3 combinedNormal = normalize(normalMap1 + normalMap2);
    combinedNormal = normalize(normal + combinedNormal * NormalStrength);
    
    // Calculate reflection and refraction coordinates
    float2 reflectionCoords = (input.ReflectionPos.xy / input.ReflectionPos.w) * 0.5 + 0.5;
    float2 refractionCoords = (input.RefractionPos.xy / input.RefractionPos.w) * 0.5 + 0.5;
    
    // Apply normal distortion to reflection/refraction coordinates
    reflectionCoords += combinedNormal.xz * 0.02;
    refractionCoords += combinedNormal.xz * 0.01;
    
    // Sample reflection and refraction textures
    float3 reflectionColor = ReflectionTexture.Sample(LinearSampler, reflectionCoords).rgb;
    float3 refractionColor = RefractionTexture.Sample(LinearSampler, refractionCoords).rgb;
    
    // Calculate Fresnel effect
    float3 fresnel = calculateFresnel(viewDir, combinedNormal, FresnelStrength);
    
    // Mix reflection and refraction based on Fresnel
    float3 waterSurfaceColor = lerp(refractionColor * RefractionStrength, reflectionColor * ReflectionStrength, fresnel);
    
    // Add water color tint
    waterSurfaceColor = lerp(waterSurfaceColor, WaterColor, 0.3);
    
    // Calculate specular highlights
    float3 lightDir = normalize(float3(0.5, 1.0, 0.3)); // Simplified directional light
    float3 halfDir = normalize(lightDir + viewDir);
    float specular = pow(max(0.0, dot(combinedNormal, halfDir)), SpecularPower);
    waterSurfaceColor += SpecularColor * specular;
    
    // Sample depth texture for foam calculation
    float depth = DepthTexture.Sample(LinearSampler, refractionCoords).r;
    float waterDepth = input.Position.z / input.Position.w;
    float depthDifference = abs(depth - waterDepth);
    
    // Calculate foam
    float foamFactor = 1.0 - saturate(depthDifference / FoamThreshold);
    float2 foamCoord = input.TexCoord * 8.0 + input.WaveOffset * 2.0;
    float foamNoise = FoamTexture.Sample(LinearSampler, foamCoord).r;
    foamFactor *= foamNoise;
    
    // Add foam to water color
    waterSurfaceColor = lerp(waterSurfaceColor, FoamColor, foamFactor);
    
    // Calculate final alpha
    float finalAlpha = WaterAlpha + foamFactor * 0.5;
    finalAlpha = saturate(finalAlpha);
    
    return float4(waterSurfaceColor, finalAlpha);
}
