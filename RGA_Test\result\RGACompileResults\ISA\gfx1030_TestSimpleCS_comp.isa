_amdgpu_cs_main:
	s_mov_b32 s0, s1                                           // ************: BE800301
	v_lshl_add_u32 v0, s2, 3, v0                               // ************: D7460000 ********
	s_or_saveexec_b64 s[2:3], -1                               // 00000000000C: BE8225C1
	v_cndmask_b32_e64 v1, 0, v0, s2                            // ************: D5010001 000A0080
	v_max_u32_dpp v1, v1, v1 row_xmask:1 row_mask:0xf bank_mask:0xf bound_ctrl:1// ************: 280202FA FF096101
	v_max_u32_dpp v1, v1, v1 row_xmask:2 row_mask:0xf bank_mask:0xf bound_ctrl:1// ************: 280202FA FF096201
	v_max_u32_dpp v1, v1, v1 row_xmask:4 row_mask:0xf bank_mask:0xf bound_ctrl:1// ************: 280202FA FF096401
	v_max_u32_dpp v1, v1, v1 row_xmask:8 row_mask:0xf bank_mask:0xf bound_ctrl:1// ************: 280202FA FF096801
	v_permlanex16_b32 v2, v1, 0, 0                             // ************: ******** ********
	v_max_u32_e32 v1, v1, v2                                   // ************: ********
	s_mov_b64 exec, s[2:3]                                     // ************: BEFE0402
	v_mbcnt_lo_u32_b32 v0, exec_lo, 0                          // ************: ******** 0001007E
	s_or_saveexec_b64 s[2:3], -1                               // ************: BE8225C1
	v_readlane_b32 s1, v1, 0                                   // ************: ******** ********
	v_readlane_b32 s4, v1, 32                                  // 00000000005C: ******** ********
	s_mov_b64 exec, s[2:3]                                     // ************: BEFE0402
	v_mbcnt_hi_u32_b32 v0, exec_hi, v0                         // ************: ******** 0002007F
	s_max_u32 s2, s1, s4                                       // ************: ********
	s_mov_b64 s[4:5], exec                                     // 000000000074: BE84047E
	v_cmpx_eq_u32_e32 0, v0                                    // 000000000078: 7DA40080
	s_cbranch_execz _L0                                        // 00000000007C: BF880008
	s_getpc_b64 s[4:5]                                         // 000000000080: BE841F00
	v_mov_b32_e32 v0, s2                                       // 000000000084: 7E000202
	s_mov_b32 s1, s5                                           // 000000000088: BE810305
	s_load_dwordx4 s[4:7], s[0:1], null                        // 00000000008C: ******** FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	buffer_atomic_umax v0, off, s[4:7], 0                      // 000000000098: E0E00000 80010000
_L0:
	s_endpgm                                                   // 0000000000A0: BF810000
