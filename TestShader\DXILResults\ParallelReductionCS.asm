;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 8cb953afd43e56afe1c719706efe22d9
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(64,1,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer ComputeParams
; {
;
;   struct ComputeParams
;   {
;
;       uint ParticleCount;                           ; Offset:    0
;       uint MaxParticles;                            ; Offset:    4
;       float DeltaTime;                              ; Offset:    8
;       float Time;                                   ; Offset:   12
;       float3 Gravity;                               ; Offset:   16
;       float Damping;                                ; Offset:   28
;       float3 EmitterPosition;                       ; Offset:   32
;       float EmissionRate;                           ; Offset:   44
;       float3 EmitterDirection;                      ; Offset:   48
;       float EmissionSpeed;                          ; Offset:   60
;       float2 LifetimeRange;                         ; Offset:   64
;       float2 SizeRange;                             ; Offset:   72
;       uint FrameCount;                              ; Offset:   80
;       float NoiseScale;                             ; Offset:   84
;       float NoiseStrength;                          ; Offset:   88
;       uint _padding;                                ; Offset:   92
;   
;   } ComputeParams;                                  ; Offset:    0 Size:    96
;
; }
;
; Resource bind info for ParticleBuffer
; {
;
;   struct struct.Particle
;   {
;
;       float3 Position;                              ; Offset:    0
;       float Life;                                   ; Offset:   12
;       float3 Velocity;                              ; Offset:   16
;       float Size;                                   ; Offset:   28
;       float4 Color;                                 ; Offset:   32
;       float3 Acceleration;                          ; Offset:   48
;       float Mass;                                   ; Offset:   60
;       uint Type;                                    ; Offset:   64
;       float3 _padding;                              ; Offset:   68
;   
;   } $Element;                                       ; Offset:    0 Size:    80
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; ComputeParams                     cbuffer      NA          NA     CB0            cb0     1
; ParticleBuffer                    texture  struct         r/o      T0             t0     1
; RawDataBuffer                         UAV    byte         r/w      U0             u0     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%"class.StructuredBuffer<Particle>" = type { %struct.Particle }
%struct.Particle = type { <3 x float>, float, <3 x float>, float, <4 x float>, <3 x float>, float, i32, <3 x float> }
%struct.RWByteAddressBuffer = type { i32 }
%ComputeParams = type { i32, i32, float, float, <3 x float>, float, <3 x float>, float, <3 x float>, float, <2 x float>, <2 x float>, i32, float, float, i32 }

@"\01?SharedDistances@@3PAMA" = external addrspace(3) global [64 x float], align 4

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %5 = call i32 @dx.op.threadIdInGroup.i32(i32 95, i32 0)  ; ThreadIdInGroup(component)
  %6 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %7 = extractvalue %dx.types.CBufRet.i32 %6, 0
  %8 = icmp ult i32 %4, %7
  br i1 %8, label %9, label %20

; <label>:9                                       ; preds = %0
  %10 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 0)  ; BufferLoad(srv,index,wot)
  %11 = extractvalue %dx.types.ResRet.f32 %10, 0
  %12 = extractvalue %dx.types.ResRet.f32 %10, 1
  %13 = extractvalue %dx.types.ResRet.f32 %10, 2
  %14 = fmul fast float %11, %11
  %15 = fmul fast float %12, %12
  %16 = fadd fast float %14, %15
  %17 = fmul fast float %13, %13
  %18 = fadd fast float %16, %17
  %19 = call float @dx.op.unary.f32(i32 24, float %18)  ; Sqrt(value)
  br label %20

; <label>:20                                      ; preds = %9, %0
  %21 = phi float [ %19, %9 ], [ 0.000000e+00, %0 ]
  %22 = getelementptr [64 x float], [64 x float] addrspace(3)* @"\01?SharedDistances@@3PAMA", i32 0, i32 %5
  store float %21, float addrspace(3)* %22, align 4
  call void @dx.op.barrier(i32 80, i32 9)  ; Barrier(barrierMode)
  br label %23

; <label>:23                                      ; preds = %32, %20
  %24 = phi i32 [ 32, %20 ], [ %33, %32 ]
  %25 = icmp ult i32 %5, %24
  br i1 %25, label %26, label %32

; <label>:26                                      ; preds = %23
  %27 = add i32 %24, %5
  %28 = getelementptr [64 x float], [64 x float] addrspace(3)* @"\01?SharedDistances@@3PAMA", i32 0, i32 %27
  %29 = load float, float addrspace(3)* %28, align 4, !tbaa !15
  %30 = load float, float addrspace(3)* %22, align 4, !tbaa !15
  %31 = call float @dx.op.binary.f32(i32 35, float %30, float %29)  ; FMax(a,b)
  store float %31, float addrspace(3)* %22, align 4, !tbaa !15
  br label %32

; <label>:32                                      ; preds = %26, %23
  call void @dx.op.barrier(i32 80, i32 9)  ; Barrier(barrierMode)
  %33 = lshr i32 %24, 1
  %34 = icmp eq i32 %33, 0
  br i1 %34, label %35, label %23

; <label>:35                                      ; preds = %32
  %36 = icmp eq i32 %5, 0
  br i1 %36, label %37, label %41

; <label>:37                                      ; preds = %35
  %38 = load float, float addrspace(3)* getelementptr inbounds ([64 x float], [64 x float] addrspace(3)* @"\01?SharedDistances@@3PAMA", i32 0, i32 0), align 4, !tbaa !15
  %39 = bitcast float %38 to i32
  %40 = call i32 @dx.op.atomicBinOp.i32(i32 78, %dx.types.Handle %1, i32 7, i32 0, i32 undef, i32 undef, i32 %39)  ; AtomicBinOp(handle,atomicOp,offset0,offset1,offset2,newValue)
  br label %41

; <label>:41                                      ; preds = %37, %35
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadIdInGroup.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: noduplicate nounwind
declare void @dx.op.barrier(i32, i32) #1

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind
declare i32 @dx.op.atomicBinOp.i32(i32, %dx.types.Handle, i32, i32, i32, i32, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #3

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #3

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #3

attributes #0 = { nounwind readnone }
attributes #1 = { noduplicate nounwind }
attributes #2 = { nounwind }
attributes #3 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!12}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{!5, !8, !10, null}
!5 = !{!6}
!6 = !{i32 0, %"class.StructuredBuffer<Particle>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i32 0, !7}
!7 = !{i32 1, i32 80}
!8 = !{!9}
!9 = !{i32 0, %struct.RWByteAddressBuffer* undef, !"", i32 0, i32 0, i32 1, i32 11, i1 false, i1 false, i1 false, null}
!10 = !{!11}
!11 = !{i32 0, %ComputeParams* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!12 = !{void ()* @main, !"main", null, !4, !13}
!13 = !{i32 0, i64 16, i32 4, !14}
!14 = !{i32 64, i32 1, i32 1}
!15 = !{!16, !16, i64 0}
!16 = !{!"float", !17, i64 0}
!17 = !{!"omnipotent char", !18, i64 0}
!18 = !{!"Simple C/C++ TBAA"}
