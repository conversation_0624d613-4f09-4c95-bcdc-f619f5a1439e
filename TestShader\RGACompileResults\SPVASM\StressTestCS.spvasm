; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 341
; Schema: 0
               OpCapability Shader
               OpCapability ImageQuery
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 64 1 1
               OpSource HLSL 600
               OpName %type_StressTestParams "type.StressTestParams"
               OpMemberName %type_StressTestParams 0 "WorkGroupSize"
               OpMemberName %type_StressTestParams 1 "TotalWorkItems"
               OpMemberName %type_StressTestParams 2 "ComplexityLevel"
               OpMemberName %type_StressTestParams 3 "TestType"
               OpMemberName %type_StressTestParams 4 "Time"
               OpMemberName %type_StressTestParams 5 "Padding"
               OpName %StressTestParams "StressTestParams"
               OpName %type_RWStructuredBuffer_v4float "type.RWStructuredBuffer.v4float"
               OpName %InputBuffer "InputBuffer"
               OpName %OutputBuffer "OutputBuffer"
               OpName %type_2d_image "type.2d.image"
               OpName %OutputTexture "OutputTexture"
               OpName %type_2d_image_0 "type.2d.image"
               OpName %InputTexture "InputTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %StressTestParams DescriptorSet 0
               OpDecorate %StressTestParams Binding 0
               OpDecorate %InputBuffer DescriptorSet 0
               OpDecorate %InputBuffer Binding 0
               OpDecorate %OutputBuffer DescriptorSet 0
               OpDecorate %OutputBuffer Binding 1
               OpDecorate %OutputTexture DescriptorSet 0
               OpDecorate %OutputTexture Binding 2
               OpDecorate %InputTexture DescriptorSet 0
               OpDecorate %InputTexture Binding 0
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_StressTestParams 0 Offset 0
               OpMemberDecorate %type_StressTestParams 1 Offset 4
               OpMemberDecorate %type_StressTestParams 2 Offset 8
               OpMemberDecorate %type_StressTestParams 3 Offset 12
               OpMemberDecorate %type_StressTestParams 4 Offset 16
               OpMemberDecorate %type_StressTestParams 5 Offset 20
               OpDecorate %type_StressTestParams Block
               OpDecorate %_runtimearr_v4float ArrayStride 16
               OpMemberDecorate %type_RWStructuredBuffer_v4float 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_v4float BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v4float = OpTypeVector %float 4
         %23 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
      %int_3 = OpConstant %int 3
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
      %int_2 = OpConstant %int 2
      %int_4 = OpConstant %int 4
%float_0_00999999978 = OpConstant %float 0.00999999978
     %uint_1 = OpConstant %uint 1
%float_0_100000001 = OpConstant %float 0.100000001
    %float_1 = OpConstant %float 1
     %uint_2 = OpConstant %uint 2
  %float_2_5 = OpConstant %float 2.5
     %uint_3 = OpConstant %uint 3
    %float_3 = OpConstant %float 3
%float_1_00999999 = OpConstant %float 1.00999999
%uint_1664525 = OpConstant %uint 1664525
%uint_1013904223 = OpConstant %uint 1013904223
%uint_1103515245 = OpConstant %uint 1103515245
 %uint_12345 = OpConstant %uint 12345
  %float_0_5 = OpConstant %float 0.5
    %float_2 = OpConstant %float 2
    %float_4 = OpConstant %float 4
    %float_8 = OpConstant %float 8
     %uint_5 = OpConstant %uint 5
    %uint_17 = OpConstant %uint 17
         %48 = OpConstantComposite %v4float %float_0_100000001 %float_0_100000001 %float_0_100000001 %float_0_100000001
   %float_10 = OpConstant %float 10
    %float_5 = OpConstant %float 5
    %v3float = OpTypeVector %float 3
%type_StressTestParams = OpTypeStruct %uint %uint %uint %uint %float %v3float
%_ptr_Uniform_type_StressTestParams = OpTypePointer Uniform %type_StressTestParams
%_runtimearr_v4float = OpTypeRuntimeArray %v4float
%type_RWStructuredBuffer_v4float = OpTypeStruct %_runtimearr_v4float
%_ptr_Uniform_type_RWStructuredBuffer_v4float = OpTypePointer Uniform %type_RWStructuredBuffer_v4float
%type_2d_image = OpTypeImage %float 2D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_2d_image_0 = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_0 = OpTypePointer UniformConstant %type_2d_image_0
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %60 = OpTypeFunction %void
     %v2uint = OpTypeVector %uint 2
    %v2float = OpTypeVector %float 2
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
       %bool = OpTypeBool
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image = OpTypeSampledImage %type_2d_image_0
%StressTestParams = OpVariable %_ptr_Uniform_type_StressTestParams Uniform
%InputBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_v4float Uniform
%OutputBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_v4float Uniform
%OutputTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%InputTexture = OpVariable %_ptr_UniformConstant_type_2d_image_0 UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%float_n0_100000001 = OpConstant %float -0.100000001
       %main = OpFunction %void None %60
         %68 = OpLabel
         %69 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %70 None
               OpSwitch %uint_0 %71
         %71 = OpLabel
         %72 = OpCompositeExtract %uint %69 0
         %73 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_1
         %74 = OpLoad %uint %73
         %75 = OpUGreaterThanEqual %bool %72 %74
               OpSelectionMerge %76 None
               OpBranchConditional %75 %77 %76
         %77 = OpLabel
               OpBranch %70
         %76 = OpLabel
         %78 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_3
         %79 = OpLoad %uint %78
         %80 = OpIEqual %bool %79 %uint_0
               OpSelectionMerge %81 None
               OpBranchConditional %80 %82 %83
         %82 = OpLabel
         %84 = OpAccessChain %_ptr_Uniform_v4float %InputBuffer %int_0 %72
         %85 = OpLoad %v4float %84
               OpBranch %86
         %86 = OpLabel
         %87 = OpPhi %v4float %85 %82 %88 %89
         %90 = OpPhi %v4float %23 %82 %91 %89
         %92 = OpPhi %uint %uint_0 %82 %93 %89
         %94 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_2
         %95 = OpLoad %uint %94
         %96 = OpULessThan %bool %92 %95
               OpLoopMerge %97 %89 None
               OpBranchConditional %96 %89 %97
         %89 = OpLabel
         %98 = OpAccessChain %_ptr_Uniform_float %StressTestParams %int_4
         %99 = OpLoad %float %98
        %100 = OpConvertUToF %float %92
        %101 = OpFMul %float %100 %float_0_00999999978
        %102 = OpFAdd %float %99 %101
        %103 = OpCompositeExtract %float %87 0
        %104 = OpFMul %float %103 %102
        %105 = OpExtInst %float %1 Sin %104
        %106 = OpCompositeExtract %float %87 1
        %107 = OpFMul %float %106 %102
        %108 = OpExtInst %float %1 Cos %107
        %109 = OpFMul %float %105 %108
        %110 = OpCompositeExtract %float %90 0
        %111 = OpFAdd %float %110 %109
        %112 = OpCompositeExtract %float %87 2
        %113 = OpFMul %float %112 %102
        %114 = OpExtInst %float %1 Tan %113
        %115 = OpCompositeExtract %float %87 3
        %116 = OpFMul %float %115 %102
        %117 = OpExtInst %float %1 Atan %116
        %118 = OpFMul %float %114 %117
        %119 = OpCompositeExtract %float %90 1
        %120 = OpFAdd %float %119 %118
        %121 = OpFMul %float %103 %float_0_100000001
        %122 = OpExtInst %float %1 Exp %121
        %123 = OpExtInst %float %1 FAbs %106
        %124 = OpFAdd %float %123 %float_1
        %125 = OpExtInst %float %1 Log %124
        %126 = OpFMul %float %122 %125
        %127 = OpCompositeExtract %float %90 2
        %128 = OpFAdd %float %127 %126
        %129 = OpExtInst %float %1 FAbs %112
        %130 = OpExtInst %float %1 Pow %129 %float_2_5
        %131 = OpExtInst %float %1 FAbs %115
        %132 = OpFAdd %float %131 %float_1
        %133 = OpExtInst %float %1 Sqrt %132
        %134 = OpFMul %float %130 %133
        %135 = OpCompositeExtract %float %90 3
        %136 = OpFAdd %float %135 %134
        %137 = OpCompositeConstruct %v4float %111 %120 %128 %136
        %138 = OpFAdd %float %103 %102
        %139 = OpExtInst %float %1 Sin %138
        %140 = OpFAdd %float %106 %102
        %141 = OpExtInst %float %1 Cos %140
        %142 = OpFMul %float %139 %141
        %143 = OpFMul %float %112 %float_n0_100000001
        %144 = OpExtInst %float %1 Exp %143
        %145 = OpFMul %float %142 %144
        %146 = OpFAdd %float %115 %102
        %147 = OpExtInst %float %1 Sin %146
        %148 = OpExtInst %float %1 FAbs %147
        %149 = OpExtInst %float %1 Pow %148 %float_3
        %150 = OpFMul %float %103 %106
        %151 = OpExtInst %float %1 FAbs %150
        %152 = OpFAdd %float %151 %float_1
        %153 = OpExtInst %float %1 Log %152
        %154 = OpFMul %float %149 %153
        %155 = OpFMul %float %145 %154
        %156 = OpFAdd %float %145 %154
        %157 = OpCompositeConstruct %v4float %145 %154 %155 %156
         %91 = OpFAdd %v4float %137 %157
        %158 = OpVectorTimesScalar %v4float %87 %float_1_00999999
        %159 = OpVectorTimesScalar %v4float %91 %float_0_00999999978
         %88 = OpFAdd %v4float %158 %159
         %93 = OpIAdd %uint %92 %uint_1
               OpBranch %86
         %97 = OpLabel
               OpBranch %81
         %83 = OpLabel
        %160 = OpIEqual %bool %79 %uint_1
               OpSelectionMerge %161 None
               OpBranchConditional %160 %162 %163
        %162 = OpLabel
               OpBranch %164
        %164 = OpLabel
        %165 = OpPhi %v4float %23 %162 %166 %167
        %168 = OpPhi %uint %uint_0 %162 %169 %167
        %170 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_2
        %171 = OpLoad %uint %170
        %172 = OpULessThan %bool %168 %171
               OpLoopMerge %173 %167 None
               OpBranchConditional %172 %167 %173
        %167 = OpLabel
        %174 = OpIMul %uint %72 %uint_1664525
        %175 = OpIAdd %uint %174 %uint_1013904223
        %176 = OpIAdd %uint %175 %168
        %177 = OpUMod %uint %176 %74
        %178 = OpAccessChain %_ptr_Uniform_v4float %InputBuffer %int_0 %177
        %179 = OpLoad %v4float %178
        %180 = OpConvertUToF %float %168
        %181 = OpAccessChain %_ptr_Uniform_float %StressTestParams %int_4
        %182 = OpLoad %float %181
        %183 = OpFAdd %float %180 %182
        %184 = OpExtInst %float %1 Sin %183
        %185 = OpVectorTimesScalar %v4float %179 %184
        %166 = OpFAdd %v4float %165 %185
        %186 = OpIMul %uint %177 %uint_1103515245
        %187 = OpIAdd %uint %186 %uint_12345
        %188 = OpUMod %uint %187 %74
        %189 = OpVectorTimesScalar %v4float %166 %float_0_100000001
        %190 = OpAccessChain %_ptr_Uniform_v4float %OutputBuffer %int_0 %188
               OpStore %190 %189
        %169 = OpIAdd %uint %168 %uint_1
               OpBranch %164
        %173 = OpLabel
               OpBranch %161
        %163 = OpLabel
        %191 = OpIEqual %bool %79 %uint_2
               OpSelectionMerge %192 None
               OpBranchConditional %191 %193 %194
        %193 = OpLabel
        %195 = OpLoad %type_2d_image_0 %InputTexture
        %196 = OpImageQuerySizeLod %v2uint %195 %int_0
        %197 = OpCompositeExtract %uint %196 0
        %198 = OpCompositeExtract %uint %196 1
               OpBranch %199
        %199 = OpLabel
        %200 = OpPhi %v4float %23 %193 %201 %202
        %203 = OpPhi %uint %uint_0 %193 %204 %202
        %205 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_2
        %206 = OpLoad %uint %205
        %207 = OpULessThan %bool %203 %206
               OpLoopMerge %208 %202 None
               OpBranchConditional %207 %202 %208
        %202 = OpLabel
        %209 = OpIAdd %uint %72 %203
        %210 = OpConvertUToF %float %209
        %211 = OpConvertUToF %float %74
        %212 = OpFDiv %float %210 %211
        %213 = OpAccessChain %_ptr_Uniform_float %StressTestParams %int_4
        %214 = OpLoad %float %213
        %215 = OpConvertUToF %float %203
        %216 = OpFAdd %float %214 %215
        %217 = OpExtInst %float %1 Sin %216
        %218 = OpFMul %float %217 %float_0_5
        %219 = OpFAdd %float %218 %float_0_5
        %220 = OpCompositeConstruct %v2float %212 %219
        %221 = OpLoad %type_2d_image_0 %InputTexture
        %222 = OpLoad %type_sampler %LinearSampler
        %223 = OpSampledImage %type_sampled_image %221 %222
        %224 = OpImageSampleExplicitLod %v4float %223 %220 Lod %float_0
        %225 = OpFAdd %v4float %200 %224
        %226 = OpLoad %type_2d_image_0 %InputTexture
        %227 = OpLoad %type_sampler %LinearSampler
        %228 = OpVectorTimesScalar %v2float %220 %float_2
        %229 = OpSampledImage %type_sampled_image %226 %227
        %230 = OpImageSampleExplicitLod %v4float %229 %228 Lod %float_1
        %231 = OpFAdd %v4float %225 %230
        %232 = OpLoad %type_2d_image_0 %InputTexture
        %233 = OpLoad %type_sampler %LinearSampler
        %234 = OpVectorTimesScalar %v2float %220 %float_4
        %235 = OpSampledImage %type_sampled_image %232 %233
        %236 = OpImageSampleExplicitLod %v4float %235 %234 Lod %float_2
        %237 = OpFAdd %v4float %231 %236
        %238 = OpLoad %type_2d_image_0 %InputTexture
        %239 = OpLoad %type_sampler %LinearSampler
        %240 = OpVectorTimesScalar %v2float %220 %float_8
        %241 = OpSampledImage %type_sampled_image %238 %239
        %242 = OpImageSampleExplicitLod %v4float %241 %240 Lod %float_3
        %243 = OpFAdd %v4float %237 %242
        %244 = OpConvertUToF %float %197
        %245 = OpFDiv %float %float_1 %244
        %246 = OpCompositeConstruct %v2float %245 %float_0
        %247 = OpConvertUToF %float %198
        %248 = OpFDiv %float %float_1 %247
        %249 = OpCompositeConstruct %v2float %float_0 %248
        %250 = OpLoad %type_2d_image_0 %InputTexture
        %251 = OpLoad %type_sampler %LinearSampler
        %252 = OpSampledImage %type_sampled_image %250 %251
        %253 = OpImageSampleExplicitLod %v4float %252 %220 Grad %246 %249
        %201 = OpFAdd %v4float %243 %253
        %204 = OpIAdd %uint %203 %uint_1
               OpBranch %199
        %208 = OpLabel
        %254 = OpUMod %uint %72 %197
        %255 = OpUDiv %uint %72 %197
        %256 = OpCompositeConstruct %v2uint %254 %255
        %257 = OpULessThan %bool %255 %198
               OpSelectionMerge %258 None
               OpBranchConditional %257 %259 %258
        %259 = OpLabel
        %260 = OpIMul %uint %206 %uint_5
        %261 = OpConvertUToF %float %260
        %262 = OpCompositeConstruct %v4float %261 %261 %261 %261
        %263 = OpFDiv %v4float %200 %262
        %264 = OpLoad %type_2d_image %OutputTexture
               OpImageWrite %264 %256 %263 None
               OpBranch %258
        %258 = OpLabel
               OpBranch %192
        %194 = OpLabel
        %265 = OpIEqual %bool %79 %uint_3
               OpSelectionMerge %266 None
               OpBranchConditional %265 %267 %266
        %267 = OpLabel
        %268 = OpAccessChain %_ptr_Uniform_v4float %InputBuffer %int_0 %72
        %269 = OpLoad %v4float %268
               OpBranch %270
        %270 = OpLabel
        %271 = OpPhi %v4float %269 %267 %272 %273
        %274 = OpPhi %v4float %23 %267 %275 %273
        %276 = OpPhi %uint %uint_0 %267 %277 %273
        %278 = OpAccessChain %_ptr_Uniform_uint %StressTestParams %int_2
        %279 = OpLoad %uint %278
        %280 = OpULessThan %bool %276 %279
               OpLoopMerge %281 %273 None
               OpBranchConditional %280 %273 %281
        %273 = OpLabel
        %282 = OpCompositeExtract %float %271 0
        %283 = OpAccessChain %_ptr_Uniform_float %StressTestParams %int_4
        %284 = OpLoad %float %283
        %285 = OpFAdd %float %282 %284
        %286 = OpExtInst %float %1 Sin %285
        %287 = OpCompositeExtract %float %271 1
        %288 = OpFAdd %float %287 %284
        %289 = OpExtInst %float %1 Cos %288
        %290 = OpFMul %float %286 %289
        %291 = OpCompositeExtract %float %271 2
        %292 = OpExtInst %float %1 FAbs %291
        %293 = OpExtInst %float %1 Pow %292 %float_2
        %294 = OpCompositeExtract %float %271 3
        %295 = OpFMul %float %294 %float_n0_100000001
        %296 = OpExtInst %float %1 Exp %295
        %297 = OpFMul %float %293 %296
        %298 = OpFAdd %float %290 %297
        %299 = OpIMul %uint %276 %uint_17
        %300 = OpIAdd %uint %72 %299
        %301 = OpUMod %uint %300 %74
        %302 = OpAccessChain %_ptr_Uniform_v4float %InputBuffer %int_0 %301
        %303 = OpLoad %v4float %302
        %304 = OpFMul %float %298 %float_0_5
        %305 = OpFAdd %float %304 %float_0_5
        %306 = OpConvertUToF %float %276
        %307 = OpConvertUToF %float %279
        %308 = OpFDiv %float %306 %307
        %309 = OpCompositeConstruct %v2float %305 %308
        %310 = OpLoad %type_2d_image_0 %InputTexture
        %311 = OpLoad %type_sampler %LinearSampler
        %312 = OpSampledImage %type_sampled_image %310 %311
        %313 = OpImageSampleExplicitLod %v4float %312 %309 Lod %float_0
        %314 = OpCompositeConstruct %v4float %298 %float_0 %float_0 %float_0
        %315 = OpVectorTimesScalar %v4float %303 %float_0_100000001
        %316 = OpFAdd %v4float %314 %315
        %317 = OpVectorTimesScalar %v4float %313 %float_0_100000001
        %318 = OpFAdd %v4float %316 %317
        %275 = OpFAdd %v4float %274 %318
        %272 = OpExtInst %v4float %1 FMix %271 %275 %48
        %277 = OpIAdd %uint %276 %uint_1
               OpBranch %270
        %281 = OpLabel
        %319 = OpExtInst %v4float %1 Normalize %274
        %320 = OpExtInst %float %1 Length %271
        %321 = OpVectorTimesScalar %v4float %319 %320
        %322 = OpCompositeExtract %float %321 0
        %323 = OpFMul %float %322 %float_10
        %324 = OpExtInst %float %1 Sin %323
        %325 = OpCompositeExtract %float %321 1
        %326 = OpFMul %float %325 %float_10
        %327 = OpExtInst %float %1 Cos %326
        %328 = OpCompositeExtract %float %321 2
        %329 = OpFMul %float %328 %float_5
        %330 = OpExtInst %float %1 Tan %329
        %331 = OpCompositeExtract %float %321 3
        %332 = OpFMul %float %331 %float_5
        %333 = OpExtInst %float %1 Atan %332
        %334 = OpCompositeConstruct %v4float %324 %327 %330 %333
        %335 = OpFAdd %v4float %321 %334
               OpBranch %266
        %266 = OpLabel
        %336 = OpPhi %v4float %23 %194 %335 %281
               OpBranch %192
        %192 = OpLabel
        %337 = OpPhi %v4float %200 %258 %336 %266
               OpBranch %161
        %161 = OpLabel
        %338 = OpPhi %v4float %165 %173 %337 %192
               OpBranch %81
         %81 = OpLabel
        %339 = OpPhi %v4float %90 %97 %338 %161
        %340 = OpAccessChain %_ptr_Uniform_v4float %OutputBuffer %int_0 %72
               OpStore %340 %339
               OpBranch %70
         %70 = OpLabel
               OpReturn
               OpFunctionEnd
