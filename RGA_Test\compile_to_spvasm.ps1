# Compile all shaders and save results to CompileResults folder
Write-Host "Compiling all shaders and saving results..." -ForegroundColor Green

$hlslOutputDir = "result\CompileResults"
$logFile = "result\CompileResults\compilation.log"

# Create output directory if it doesn't exist
if (-not (Test-Path $hlslOutputDir)) {
    New-Item -ItemType Directory -Path $hlslOutputDir -Force | Out-Null
}

# Initialize log file
"=== HLSL Compilation Log ===" | Out-File -FilePath $logFile -Encoding UTF8
"Start time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Get all shader files from current directory (TestShader) - test with simple files
$hlslFiles = Get-ChildItem -Path "result" -Filter "*.hlsl"

Write-Host "Found $($hlslFiles.Count) HLSL files" -ForegroundColor Cyan

# Initialize counters
$totalFiles = $hlslFiles.Count
$successCount = 0
$failureCount = 0

# Compile HLSL files to SPIR-V
Write-Host "`nCompiling HLSL files to SPIR-V..." -ForegroundColor Yellow
foreach ($file in $hlslFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor White

    # Log current file processing
    "Processing: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8

    # Determine shader profile and entry point based on filename
    $profile = ""
    $entryPoint = "main"

    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $profile = "vs_6_0"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $profile = "ps_6_0"
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $profile = "cs_6_0"
    } else {
        $profile = "vs_6_0"  # Default fallback
    }

    $outputFile = Join-Path $hlslOutputDir "$($file.BaseName).spvasm"
    $individualLogFile = Join-Path $hlslOutputDir "$($file.BaseName).log"

    try {
        # Compile to SPIR-V for Vulkan
        $result = & dxc -T $profile -E $entryPoint -spirv -Fc $outputFile $file.FullName

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - SPIR-V saved to $outputFile" -ForegroundColor Green
            "  Compilation: SUCCESS" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "COMPILATION SUCCESSFUL (SPIR-V)`n$result" | Out-File -FilePath $individualLogFile -Encoding UTF8
            $successCount++
        } else {
            # Try without entry point if failed
            Write-Host "  Retrying without entry point..." -ForegroundColor Yellow
            $result2 = & dxc -T $profile -spirv $file.FullName -Fo $outputFile

            if ($LASTEXITCODE -eq 0) {
                Write-Host "  [OK] SUCCESS (no entry point) - SPIR-V saved to $outputFile" -ForegroundColor Green
                "  Compilation: SUCCESS (no entry point)" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "COMPILATION SUCCESSFUL (SPIR-V, no entry point specified)`n$result2" | Out-File -FilePath $individualLogFile -Encoding UTF8
                $successCount++
            } else {
                Write-Host "  [FAIL] FAILED" -ForegroundColor Red
                "  Compilation: FAILED" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "COMPILATION FAILED`nFirst attempt with entry point '$entryPoint':`n$result`n`nSecond attempt without entry point:`n$result2" | Out-File -FilePath $individualLogFile -Encoding UTF8
                $failureCount++
            }
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "  Compilation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        "COMPILATION EXCEPTION: $($_.Exception.Message)" | Out-File -FilePath $individualLogFile -Encoding UTF8
        $failureCount++
    }

    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Final log entry with statistics
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Statistics ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Total files processed: $totalFiles" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Successful compilations: $successCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Failed compilations: $failureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Success rate: $(if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"End time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Complete ===" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Display statistics
Write-Host "`n=== Compilation Statistics ===" -ForegroundColor Yellow
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Successful compilations: $successCount" -ForegroundColor Green
Write-Host "Failed compilations: $failureCount" -ForegroundColor $(if ($failureCount -gt 0) { "Red" } else { "Green" })
$successRate = if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 }
Write-Host "Success rate: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host "`n[DONE] Compilation complete! Results saved in CompileResults\" -ForegroundColor Green
Write-Host "  - HLSL SPIR-V files and logs: result\CompileResults\" -ForegroundColor Cyan
Write-Host "  - Compilation log: result\CompileResults\compilation.log" -ForegroundColor Cyan

