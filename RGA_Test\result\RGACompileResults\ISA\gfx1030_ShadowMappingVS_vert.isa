_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s20, s9                                          // 000000000020: BE940309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v8, s3, 5, v1                                // 000000000040: D76F0008 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v8                            // 000000000048: 7D881001
	s_and_saveexec_b32 s34, vcc_lo                             // 00000000004C: BEA23C6A
	s_cbranch_execz _L1                                        // 000000000050: BF880093
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s21, s3                                          // 000000000060: BE950303
	s_load_dwordx16 s[4:19], s[10:11], null                    // 000000000064: F4100105 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[12:14], v5, s[4:7], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80010C05
	tbuffer_load_format_xyz v[15:17], v5, s[8:11], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80020F05
	tbuffer_load_format_xyzw v[1:4], v5, s[16:19], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 000000000080: EA6B2000 80040105
	tbuffer_load_format_xy v[6:7], v5, s[12:15], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000088: EA012000 80030605
	s_load_dwordx4 s[68:71], s[20:21], null                    // 000000000090: F408110A FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	s_clause 0xb                                               // 00000000009C: BFA1000B
	s_buffer_load_dwordx8 s[8:15], s[68:71], null              // 0000000000A0: F42C0222 FA000000
	s_buffer_load_dwordx8 s[24:31], s[68:71], 0x20             // 0000000000A8: F42C0622 FA000020
	s_buffer_load_dwordx8 s[0:7], s[68:71], 0x40               // 0000000000B0: F42C0022 FA000040
	s_buffer_load_dwordx8 s[16:23], s[68:71], 0x60             // 0000000000B8: F42C0422 FA000060
	s_buffer_load_dwordx8 s[36:43], s[68:71], 0x80             // 0000000000C0: F42C0922 FA000080
	s_buffer_load_dwordx8 s[44:51], s[68:71], 0xa0             // 0000000000C8: F42C0B22 FA0000A0
	s_buffer_load_dwordx8 s[52:59], s[68:71], 0xc0             // 0000000000D0: F42C0D22 FA0000C0
	s_buffer_load_dwordx8 s[60:67], s[68:71], 0xe0             // 0000000000D8: F42C0F22 FA0000E0
	s_buffer_load_dwordx2 s[72:73], s[68:71], 0x100            // 0000000000E0: F4241222 FA000100
	s_buffer_load_dword s35, s[68:71], 0x108                   // 0000000000E8: F42008E2 FA000108
	s_buffer_load_dwordx2 s[74:75], s[68:71], 0x110            // 0000000000F0: F42412A2 FA000110
	s_buffer_load_dword s68, s[68:71], 0x118                   // 0000000000F8: F4201122 FA000118
	s_waitcnt vmcnt(3) lgkmcnt(0)                              // 000000000100: BF8C0073
	v_fma_f32 v9, s8, v12, s11                                 // 000000000104: D54B0009 002E1808
	v_fma_f32 v10, s12, v12, s15                               // 00000000010C: D54B000A 003E180C
	s_waitcnt vmcnt(2)                                         // 000000000114: BF8C3F72
	v_mul_f32_e32 v21, s12, v15                                // 000000000118: 102A1E0C
	v_fma_f32 v11, s24, v12, s27                               // 00000000011C: D54B000B 006E1818
	v_mul_f32_e32 v20, s8, v15                                 // 000000000124: 10281E08
	v_fmac_f32_e32 v9, s9, v13                                 // 000000000128: 56121A09
	v_fmac_f32_e32 v10, s13, v13                               // 00000000012C: 56141A0D
	v_fmac_f32_e32 v21, s13, v16                               // 000000000130: 562A200D
	v_fma_f32 v5, s28, v12, s31                                // 000000000134: D54B0005 007E181C
	v_mul_f32_e32 v22, s24, v15                                // 00000000013C: 102C1E18
	v_fmac_f32_e32 v9, s10, v14                                // 000000000140: 56121C0A
	v_fmac_f32_e32 v11, s25, v13                               // 000000000144: 56161A19
	v_fmac_f32_e32 v20, s9, v16                                // 000000000148: 56282009
	v_fmac_f32_e32 v10, s14, v14                               // 00000000014C: 56141C0E
	v_fmac_f32_e32 v21, s14, v17                               // 000000000150: 562A220E
	v_mul_f32_e32 v19, s0, v9                                  // 000000000154: 10261200
	v_fmac_f32_e32 v5, s29, v13                                // 000000000158: 560A1A1D
	v_fmac_f32_e32 v22, s25, v16                               // 00000000015C: 562C2019
	v_fmac_f32_e32 v11, s26, v14                               // 000000000160: 56161C1A
	v_fmac_f32_e32 v20, s10, v17                               // 000000000164: 5628220A
	v_mul_f32_e32 v23, s4, v9                                  // 000000000168: 102E1204
	v_mul_f32_e32 v16, v21, v21                                // 00000000016C: 10202B15
	v_fmac_f32_e32 v19, s1, v10                                // 000000000170: 56261401
	v_fmac_f32_e32 v5, s30, v14                                // 000000000174: 560A1C1E
	v_fmac_f32_e32 v22, s26, v17                               // 000000000178: 562C221A
	v_mul_f32_e32 v24, s16, v9                                 // 00000000017C: 10301210
	v_fmac_f32_e32 v23, s5, v10                                // 000000000180: 562E1405
	v_fmac_f32_e32 v16, v20, v20                               // 000000000184: 56202914
	v_fmac_f32_e32 v19, s2, v11                                // 000000000188: 56261602
	v_mul_f32_e32 v25, s20, v9                                 // 00000000018C: 10321214
	v_fmac_f32_e32 v24, s17, v10                               // 000000000190: 56301411
	v_fmac_f32_e32 v23, s6, v11                                // 000000000194: 562E1606
	v_fmac_f32_e32 v16, v22, v22                               // 000000000198: 56202D16
	v_fmac_f32_e32 v19, s3, v5                                 // 00000000019C: 56260A03
	v_sub_f32_e32 v27, s73, v10                                // 0000000001A0: 08361449
	v_sub_f32_e32 v30, s75, v10                                // 0000000001A4: 083C144B
	v_fmac_f32_e32 v25, s21, v10                               // 0000000001A8: 56321415
	v_fmac_f32_e32 v24, s18, v11                               // 0000000001AC: 56301612
	v_fmac_f32_e32 v23, s7, v5                                 // 0000000001B0: 562E0A07
	v_rsq_f32_e32 v34, v16                                     // 0000000001B4: 7E445D10
	v_mul_f32_e32 v16, s36, v19                                // 0000000001B8: 10202624
	v_mul_f32_e32 v17, s40, v19                                // 0000000001BC: 10222628
	v_mul_f32_e32 v18, s44, v19                                // 0000000001C0: 1024262C
	v_mul_f32_e32 v19, s48, v19                                // 0000000001C4: 10262630
	v_sub_f32_e32 v26, s72, v9                                 // 0000000001C8: 08341248
	v_sub_f32_e32 v29, s74, v9                                 // 0000000001CC: 083A124A
	v_mul_f32_e32 v32, v27, v27                                // 0000000001D0: 1040371B
	v_mul_f32_e32 v33, v30, v30                                // 0000000001D4: 10423D1E
	v_fmac_f32_e32 v25, s22, v11                               // 0000000001D8: 56321616
	v_fmac_f32_e32 v24, s19, v5                                // 0000000001DC: 56300A13
	v_fmac_f32_e32 v19, s49, v23                               // 0000000001E0: 56262E31
	v_sub_f32_e32 v28, s35, v11                                // 0000000001E4: 08381623
	v_sub_f32_e32 v31, s68, v11                                // 0000000001E8: 083E1644
	v_fmac_f32_e32 v32, v26, v26                               // 0000000001EC: 5640351A
	v_fmac_f32_e32 v33, v29, v29                               // 0000000001F0: 56423B1D
	v_fmac_f32_e32 v25, s23, v5                                // 0000000001F4: 56320A17
	v_fmac_f32_e32 v19, s50, v24                               // 0000000001F8: 56263032
	v_mul_f32_e32 v12, s52, v9                                 // 0000000001FC: 10181234
	v_mul_f32_e32 v13, s56, v9                                 // 000000000200: 101A1238
	v_mul_f32_e32 v14, s60, v9                                 // 000000000204: 101C123C
	v_mul_f32_e32 v15, s64, v9                                 // 000000000208: 101E1240
	v_fmac_f32_e32 v32, v28, v28                               // 00000000020C: 5640391C
	v_fmac_f32_e32 v33, v31, v31                               // 000000000210: 56423F1F
	v_fmac_f32_e32 v18, s45, v23                               // 000000000214: 56242E2D
	v_fmac_f32_e32 v19, s51, v25                               // 000000000218: 56263233
	v_fmac_f32_e32 v12, s53, v10                               // 00000000021C: 56181435
	v_fmac_f32_e32 v13, s57, v10                               // 000000000220: 561A1439
	v_fmac_f32_e32 v14, s61, v10                               // 000000000224: 561C143D
	v_fmac_f32_e32 v15, s65, v10                               // 000000000228: 561E1441
	v_rsq_f32_e32 v32, v32                                     // 00000000022C: 7E405D20
	v_fmac_f32_e32 v16, s37, v23                               // 000000000230: 56202E25
	v_fmac_f32_e32 v17, s41, v23                               // 000000000234: 56222E29
	v_mul_legacy_f32_e32 v20, v20, v34                         // 000000000238: 0E284514
	v_mul_legacy_f32_e32 v21, v21, v34                         // 00000000023C: 0E2A4515
	v_fmac_f32_e32 v18, s46, v24                               // 000000000240: 5624302E
	v_mul_legacy_f32_e32 v22, v22, v34                         // 000000000244: 0E2C4516
	v_rsq_f32_e32 v33, v33                                     // 000000000248: 7E425D21
	v_rcp_f32_e32 v34, v19                                     // 00000000024C: 7E445513
	v_fmac_f32_e32 v12, s54, v11                               // 000000000250: 56181636
	v_fmac_f32_e32 v16, s38, v24                               // 000000000254: 56203026
	v_fmac_f32_e32 v17, s42, v24                               // 000000000258: 5622302A
	v_fmac_f32_e32 v18, s47, v25                               // 00000000025C: 5624322F
	v_fmac_f32_e32 v13, s58, v11                               // 000000000260: 561A163A
	v_fmac_f32_e32 v14, s62, v11                               // 000000000264: 561C163E
	v_fmac_f32_e32 v15, s66, v11                               // 000000000268: 561E1642
	v_fmac_f32_e32 v16, s39, v25                               // 00000000026C: 56203227
	v_fmac_f32_e32 v17, s43, v25                               // 000000000270: 5622322B
	v_fmac_f32_e32 v12, s55, v5                                // 000000000274: 56180A37
	v_fmac_f32_e32 v13, s59, v5                                // 000000000278: 561A0A3B
	v_fmac_f32_e32 v14, s63, v5                                // 00000000027C: 561C0A3F
	v_fmac_f32_e32 v15, s67, v5                                // 000000000280: 561E0A43
	v_mul_legacy_f32_e32 v23, v26, v32                         // 000000000284: 0E2E411A
	v_mul_legacy_f32_e32 v24, v27, v32                         // 000000000288: 0E30411B
	v_mul_legacy_f32_e32 v25, v28, v32                         // 00000000028C: 0E32411C
	v_mul_legacy_f32_e32 v26, v29, v33                         // 000000000290: 0E34431D
	v_mul_legacy_f32_e32 v27, v30, v33                         // 000000000294: 0E36431E
	v_mul_legacy_f32_e32 v28, v31, v33                         // 000000000298: 0E38431F
	v_mul_f32_e32 v29, v18, v34                                // 00000000029C: 103A4512
_L1:
	s_or_b32 exec_lo, exec_lo, s34                             // 0000000002A0: 887E227E
	s_mov_b32 s1, exec_lo                                      // 0000000002A4: BE81037E
	v_cmpx_gt_u32_e64 s33, v8                                  // 0000000002A8: D4D4007E 00021021
	s_cbranch_execz _L2                                        // 0000000002B0: BF880002
	exp prim v0, off, off, off done                            // 0000000002B4: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000002BC: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000002C0: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000002C4: BE803C6A
	s_cbranch_execz _L3                                        // 0000000002C8: BF880014
	exp pos0 v16, v17, v18, v19 done                           // 0000000002CC: F80008CF 13121110
	exp param5 v23, v24, v25, off                              // 0000000002D4: F8000257 00191817
	s_waitcnt vmcnt(1)                                         // 0000000002DC: BF8C3F71
	exp param3 v1, v2, v3, v4                                  // 0000000002E0: F800023F 04030201
	exp param1 v20, v21, v22, off                              // 0000000002E8: F8000217 00161514
	exp param6 v26, v27, v28, off                              // 0000000002F0: F8000267 001C1B1A
	exp param4 v12, v13, v14, v15                              // 0000000002F8: F800024F 0F0E0D0C
	s_waitcnt vmcnt(0)                                         // 000000000300: BF8C3F70
	exp param2 v6, v7, off, off                                // 000000000304: F8000223 00000706
	exp param7 v29, off, off, off                              // 00000000030C: F8000271 0000001D
	exp param0 v9, v10, v11, off                               // 000000000314: F8000207 000B0A09
_L3:
	s_endpgm                                                   // 00000000031C: BF810000
