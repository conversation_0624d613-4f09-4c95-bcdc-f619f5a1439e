; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 85
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID %gl_LocalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_StructuredBuffer_Particle "type.StructuredBuffer.Particle"
               OpName %Particle "Particle"
               OpMemberName %Particle 0 "Position"
               OpMemberName %Particle 1 "Life"
               OpMemberName %Particle 2 "Velocity"
               OpMemberName %Particle 3 "Size"
               OpMemberName %Particle 4 "Color"
               OpMemberName %Particle 5 "Acceleration"
               OpMemberName %Particle 6 "Mass"
               OpMemberName %Particle 7 "Type"
               OpMemberName %Particle 8 "_padding"
               OpName %ParticleBuffer "ParticleBuffer"
               OpName %type_RWByteAddressBuffer "type.RWByteAddressBuffer"
               OpName %RawDataBuffer "RawDataBuffer"
               OpName %SharedDistances "SharedDistances"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %gl_LocalInvocationID BuiltIn LocalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %ParticleBuffer DescriptorSet 0
               OpDecorate %ParticleBuffer Binding 0
               OpDecorate %RawDataBuffer DescriptorSet 0
               OpDecorate %RawDataBuffer Binding 0
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
               OpMemberDecorate %Particle 0 Offset 0
               OpMemberDecorate %Particle 1 Offset 12
               OpMemberDecorate %Particle 2 Offset 16
               OpMemberDecorate %Particle 3 Offset 28
               OpMemberDecorate %Particle 4 Offset 32
               OpMemberDecorate %Particle 5 Offset 48
               OpMemberDecorate %Particle 6 Offset 60
               OpMemberDecorate %Particle 7 Offset 64
               OpMemberDecorate %Particle 8 Offset 68
               OpDecorate %_runtimearr_Particle ArrayStride 80
               OpMemberDecorate %type_StructuredBuffer_Particle 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Particle 0 NonWritable
               OpDecorate %type_StructuredBuffer_Particle BufferBlock
               OpDecorate %_runtimearr_uint ArrayStride 4
               OpMemberDecorate %type_RWByteAddressBuffer 0 Offset 0
               OpDecorate %type_RWByteAddressBuffer BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
       %uint = OpTypeInt 32 0
    %uint_32 = OpConstant %uint 32
     %uint_0 = OpConstant %uint 0
     %uint_1 = OpConstant %uint 1
     %uint_2 = OpConstant %uint 2
    %v3float = OpTypeVector %float 3
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
    %v4float = OpTypeVector %float 4
   %Particle = OpTypeStruct %v3float %float %v3float %float %v4float %v3float %float %uint %v3float
%_runtimearr_Particle = OpTypeRuntimeArray %Particle
%type_StructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_StructuredBuffer_Particle = OpTypePointer Uniform %type_StructuredBuffer_Particle
%_runtimearr_uint = OpTypeRuntimeArray %uint
%type_RWByteAddressBuffer = OpTypeStruct %_runtimearr_uint
%_ptr_Uniform_type_RWByteAddressBuffer = OpTypePointer Uniform %type_RWByteAddressBuffer
    %uint_64 = OpConstant %uint 64
%_arr_float_uint_64 = OpTypeArray %float %uint_64
%_ptr_Workgroup__arr_float_uint_64 = OpTypePointer Workgroup %_arr_float_uint_64
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %36 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
       %bool = OpTypeBool
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Workgroup_float = OpTypePointer Workgroup %float
   %uint_264 = OpConstant %uint 264
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%ParticleBuffer = OpVariable %_ptr_Uniform_type_StructuredBuffer_Particle Uniform
%RawDataBuffer = OpVariable %_ptr_Uniform_type_RWByteAddressBuffer Uniform
%SharedDistances = OpVariable %_ptr_Workgroup__arr_float_uint_64 Workgroup
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%gl_LocalInvocationID = OpVariable %_ptr_Input_v3uint Input
       %main = OpFunction %void None %36
         %42 = OpLabel
         %43 = OpLoad %v3uint %gl_GlobalInvocationID
         %44 = OpLoad %v3uint %gl_LocalInvocationID
         %45 = OpCompositeExtract %uint %43 0
         %46 = OpAccessChain %_ptr_Uniform_uint %ComputeParams %int_0
         %47 = OpLoad %uint %46
         %48 = OpULessThan %bool %45 %47
               OpSelectionMerge %49 None
               OpBranchConditional %48 %50 %51
         %50 = OpLabel
         %52 = OpAccessChain %_ptr_Uniform_v3float %ParticleBuffer %int_0 %45 %int_0
         %53 = OpLoad %v3float %52
         %54 = OpExtInst %float %1 Length %53
         %55 = OpCompositeExtract %uint %44 0
         %56 = OpAccessChain %_ptr_Workgroup_float %SharedDistances %55
               OpStore %56 %54
               OpBranch %49
         %51 = OpLabel
         %57 = OpCompositeExtract %uint %44 0
         %58 = OpAccessChain %_ptr_Workgroup_float %SharedDistances %57
               OpStore %58 %float_0
               OpBranch %49
         %49 = OpLabel
               OpControlBarrier %uint_2 %uint_2 %uint_264
               OpBranch %59
         %59 = OpLabel
         %60 = OpPhi %uint %uint_32 %49 %61 %62
         %63 = OpUGreaterThan %bool %60 %uint_0
               OpLoopMerge %64 %62 None
               OpBranchConditional %63 %65 %64
         %65 = OpLabel
         %66 = OpCompositeExtract %uint %44 0
         %67 = OpULessThan %bool %66 %60
               OpSelectionMerge %68 None
               OpBranchConditional %67 %69 %68
         %69 = OpLabel
         %70 = OpAccessChain %_ptr_Workgroup_float %SharedDistances %66
         %71 = OpLoad %float %70
         %72 = OpIAdd %uint %66 %60
         %73 = OpAccessChain %_ptr_Workgroup_float %SharedDistances %72
         %74 = OpLoad %float %73
         %75 = OpExtInst %float %1 NMax %71 %74
               OpStore %70 %75
               OpBranch %68
         %68 = OpLabel
               OpControlBarrier %uint_2 %uint_2 %uint_264
               OpBranch %62
         %62 = OpLabel
         %61 = OpShiftRightLogical %uint %60 %uint_1
               OpBranch %59
         %64 = OpLabel
         %76 = OpCompositeExtract %uint %44 0
         %77 = OpIEqual %bool %76 %uint_0
               OpSelectionMerge %78 None
               OpBranchConditional %77 %79 %78
         %79 = OpLabel
         %80 = OpAccessChain %_ptr_Uniform_uint %RawDataBuffer %uint_0 %uint_0
         %81 = OpAccessChain %_ptr_Workgroup_float %SharedDistances %int_0
         %82 = OpLoad %float %81
         %83 = OpBitcast %uint %82
         %84 = OpAtomicUMax %uint %80 %uint_1 %uint_0 %83
               OpBranch %78
         %78 = OpLabel
               OpReturn
               OpFunctionEnd
