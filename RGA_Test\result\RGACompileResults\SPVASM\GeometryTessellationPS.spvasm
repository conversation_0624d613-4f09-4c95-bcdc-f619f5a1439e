; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 535
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %gl_FragCoord %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %in_var_TEXCOORD8 %in_var_TEXCOORD9 %in_var_TEXCOORD10 %out_var_SV_Target0 %out_var_SV_Target1 %out_var_SV_Target2 %out_var_SV_Target3
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "LightDirection"
               OpMemberName %type_PerFrame 6 "TessellationLevel"
               OpMemberName %type_PerFrame 7 "HeightmapSize"
               OpMemberName %type_PerFrame 8 "HeightScale"
               OpMemberName %type_PerFrame 9 "DetailScale"
               OpMemberName %type_PerFrame 10 "LODDistance"
               OpName %PerFrame "PerFrame"
               OpName %type_MaterialParams "type.MaterialParams"
               OpMemberName %type_MaterialParams 0 "GrassLayer"
               OpMemberName %type_MaterialParams 1 "RockLayer"
               OpMemberName %type_MaterialParams 2 "SnowLayer"
               OpMemberName %type_MaterialParams 3 "SandLayer"
               OpMemberName %type_MaterialParams 4 "FogColor"
               OpMemberName %type_MaterialParams 5 "FogDensity"
               OpMemberName %type_MaterialParams 6 "FogStart"
               OpMemberName %type_MaterialParams 7 "FogEnd"
               OpMemberName %type_MaterialParams 8 "WindDirection"
               OpMemberName %type_MaterialParams 9 "WindStrength"
               OpMemberName %type_MaterialParams 10 "_padding2"
               OpName %MaterialLayer "MaterialLayer"
               OpMemberName %MaterialLayer 0 "Albedo"
               OpMemberName %MaterialLayer 1 "Metallic"
               OpMemberName %MaterialLayer 2 "Roughness"
               OpMemberName %MaterialLayer 3 "NormalStrength"
               OpMemberName %MaterialLayer 4 "TilingScale"
               OpMemberName %MaterialLayer 5 "Offset"
               OpMemberName %MaterialLayer 6 "BlendSharpness"
               OpMemberName %MaterialLayer 7 "_padding"
               OpName %MaterialParams "MaterialParams"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %AlbedoTextures "AlbedoTextures"
               OpName %NormalTextures "NormalTextures"
               OpName %RoughnessTextures "RoughnessTextures"
               OpName %type_2d_image "type.2d.image"
               OpName %SplatmapTexture "SplatmapTexture"
               OpName %DetailNormalTexture "DetailNormalTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %SkyboxTexture "SkyboxTexture"
               OpName %ShadowMap "ShadowMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %TrilinearSampler "TrilinearSampler"
               OpName %ShadowSampler "ShadowSampler"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %in_var_TEXCOORD8 "in.var.TEXCOORD8"
               OpName %in_var_TEXCOORD9 "in.var.TEXCOORD9"
               OpName %in_var_TEXCOORD10 "in.var.TEXCOORD10"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %out_var_SV_Target1 "out.var.SV_Target1"
               OpName %out_var_SV_Target2 "out.var.SV_Target2"
               OpName %out_var_SV_Target3 "out.var.SV_Target3"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpName %type_sampled_image_1 "type.sampled.image"
               OpDecorate %gl_FragCoord BuiltIn FragCoord
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 7
               OpDecorate %in_var_TEXCOORD7 Location 8
               OpDecorate %in_var_TEXCOORD8 Location 9
               OpDecorate %in_var_TEXCOORD9 Location 10
               OpDecorate %in_var_TEXCOORD10 Location 11
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %out_var_SV_Target1 Location 1
               OpDecorate %out_var_SV_Target2 Location 2
               OpDecorate %out_var_SV_Target3 Location 3
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %MaterialParams DescriptorSet 0
               OpDecorate %MaterialParams Binding 1
               OpDecorate %AlbedoTextures DescriptorSet 0
               OpDecorate %AlbedoTextures Binding 0
               OpDecorate %NormalTextures DescriptorSet 0
               OpDecorate %NormalTextures Binding 1
               OpDecorate %RoughnessTextures DescriptorSet 0
               OpDecorate %RoughnessTextures Binding 2
               OpDecorate %SplatmapTexture DescriptorSet 0
               OpDecorate %SplatmapTexture Binding 5
               OpDecorate %DetailNormalTexture DescriptorSet 0
               OpDecorate %DetailNormalTexture Binding 6
               OpDecorate %SkyboxTexture DescriptorSet 0
               OpDecorate %SkyboxTexture Binding 7
               OpDecorate %ShadowMap DescriptorSet 0
               OpDecorate %ShadowMap Binding 8
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %TrilinearSampler DescriptorSet 0
               OpDecorate %TrilinearSampler Binding 1
               OpDecorate %ShadowSampler DescriptorSet 0
               OpDecorate %ShadowSampler Binding 2
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 220
               OpMemberDecorate %type_PerFrame 7 Offset 224
               OpMemberDecorate %type_PerFrame 8 Offset 232
               OpMemberDecorate %type_PerFrame 9 Offset 236
               OpMemberDecorate %type_PerFrame 10 Offset 240
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %MaterialLayer 0 Offset 0
               OpMemberDecorate %MaterialLayer 1 Offset 16
               OpMemberDecorate %MaterialLayer 2 Offset 20
               OpMemberDecorate %MaterialLayer 3 Offset 24
               OpMemberDecorate %MaterialLayer 4 Offset 28
               OpMemberDecorate %MaterialLayer 5 Offset 32
               OpMemberDecorate %MaterialLayer 6 Offset 40
               OpMemberDecorate %MaterialLayer 7 Offset 44
               OpMemberDecorate %type_MaterialParams 0 Offset 0
               OpMemberDecorate %type_MaterialParams 1 Offset 48
               OpMemberDecorate %type_MaterialParams 2 Offset 96
               OpMemberDecorate %type_MaterialParams 3 Offset 144
               OpMemberDecorate %type_MaterialParams 4 Offset 192
               OpMemberDecorate %type_MaterialParams 5 Offset 204
               OpMemberDecorate %type_MaterialParams 6 Offset 208
               OpMemberDecorate %type_MaterialParams 7 Offset 212
               OpMemberDecorate %type_MaterialParams 8 Offset 216
               OpMemberDecorate %type_MaterialParams 9 Offset 224
               OpMemberDecorate %type_MaterialParams 10 Offset 228
               OpDecorate %type_MaterialParams Block
        %int = OpTypeInt 32 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_5 = OpConstant %int 5
      %int_0 = OpConstant %int 0
      %int_8 = OpConstant %int 8
      %int_9 = OpConstant %int 9
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
         %54 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
    %float_0 = OpConstant %float 0
         %56 = OpConstantComposite %v4float %float_1 %float_0 %float_0 %float_0
         %57 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
    %v3float = OpTypeVector %float 3
         %59 = OpConstantComposite %v3float %float_0 %float_0 %float_0
      %int_6 = OpConstant %int 6
  %float_0_5 = OpConstant %float 0.5
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_150000006 = OpConstant %float 0.150000006
         %64 = OpConstantComposite %v3float %float_0_100000001 %float_0_100000001 %float_0_150000006
      %int_7 = OpConstant %int 7
         %66 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
%float_0_699999988 = OpConstant %float 0.699999988
%float_0_300000012 = OpConstant %float 0.300000012
   %float_10 = OpConstant %float 10
%float_0_00999999978 = OpConstant %float 0.00999999978
    %float_2 = OpConstant %float 2
         %72 = OpConstantComposite %v3float %float_1 %float_1 %float_1
         %73 = OpConstantComposite %v3float %float_0 %float_0 %float_1
   %float_n1 = OpConstant %float -1
         %75 = OpConstantComposite %v3float %float_n1 %float_n1 %float_1
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
    %v2float = OpTypeVector %float 2
     %int_n1 = OpConstant %int -1
    %float_7 = OpConstant %float 7
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %v3float %float %v2float %float %float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%MaterialLayer = OpTypeStruct %v4float %float %float %float %float %v2float %float %float
%type_MaterialParams = OpTypeStruct %MaterialLayer %MaterialLayer %MaterialLayer %MaterialLayer %v3float %float %float %float %v2float %float %float
%_ptr_Uniform_type_MaterialParams = OpTypePointer Uniform %type_MaterialParams
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %94 = OpTypeFunction %void
%mat3v3float = OpTypeMatrix %v3float 3
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_MaterialLayer = OpTypePointer Uniform %MaterialLayer
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image_array
%type_sampled_image_1 = OpTypeSampledImage %type_cube_image
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%MaterialParams = OpVariable %_ptr_Uniform_type_MaterialParams Uniform
%AlbedoTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NormalTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%RoughnessTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%SplatmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DetailNormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%SkyboxTexture = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
  %ShadowMap = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%TrilinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%ShadowSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_FragCoord = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD8 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD9 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD10 = OpVariable %_ptr_Input_float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target1 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target2 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target3 = OpVariable %_ptr_Output_v4float Output
 %float_0_75 = OpConstant %float 0.75
    %float_3 = OpConstant %float 3
%float_0_00048828125 = OpConstant %float 0.00048828125
        %103 = OpConstantComposite %v2float %float_0_00048828125 %float_0_00048828125
%float_0_111111112 = OpConstant %float 0.111111112
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
     %uint_4 = OpConstant %uint 4
     %uint_5 = OpConstant %uint 5
       %main = OpFunction %void None %94
        %110 = OpLabel
        %111 = OpLoad %v4float %gl_FragCoord
        %112 = OpLoad %v3float %in_var_TEXCOORD1
        %113 = OpLoad %v3float %in_var_TEXCOORD2
        %114 = OpLoad %v3float %in_var_TEXCOORD3
        %115 = OpLoad %v2float %in_var_TEXCOORD4
        %116 = OpLoad %v2float %in_var_TEXCOORD5
        %117 = OpLoad %float %in_var_TEXCOORD6
        %118 = OpLoad %float %in_var_TEXCOORD7
        %119 = OpLoad %v3float %in_var_TEXCOORD8
        %120 = OpLoad %v4float %in_var_TEXCOORD9
        %121 = OpLoad %float %in_var_TEXCOORD10
        %122 = OpExtInst %v3float %1 Normalize %112
        %123 = OpExtInst %v3float %1 Normalize %113
        %124 = OpExtInst %v3float %1 Normalize %114
        %125 = OpExtInst %v3float %1 Normalize %119
        %126 = OpLoad %type_2d_image %SplatmapTexture
        %127 = OpLoad %type_sampler %LinearSampler
        %128 = OpSampledImage %type_sampled_image %126 %127
        %129 = OpImageSampleImplicitLod %v4float %128 %115 None
        %130 = OpCompositeExtract %float %129 0
        %131 = OpFMul %float %118 %float_2
        %132 = OpFSub %float %float_1 %131
        %133 = OpExtInst %float %1 FClamp %132 %float_0 %float_1
        %134 = OpFMul %float %130 %133
        %135 = OpCompositeExtract %float %129 1
        %136 = OpFSub %float %118 %float_0_75
        %137 = OpExtInst %float %1 FClamp %136 %float_0 %float_1
        %138 = OpFMul %float %135 %137
        %139 = OpCompositeExtract %float %129 2
        %140 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_8
        %141 = OpLoad %float %140
        %142 = OpFMul %float %141 %float_0_699999988
        %143 = OpFMul %float %141 %float_0_300000012
        %144 = OpFDiv %float %142 %143
        %145 = OpFSub %float %117 %144
        %146 = OpExtInst %float %1 FClamp %145 %float_0 %float_1
        %147 = OpFMul %float %139 %146
        %148 = OpCompositeExtract %float %129 3
        %149 = OpFDiv %float %117 %143
        %150 = OpFSub %float %float_1 %149
        %151 = OpExtInst %float %1 FClamp %150 %float_0 %float_1
        %152 = OpFMul %float %148 %151
        %153 = OpCompositeConstruct %v4float %134 %138 %147 %152
        %154 = OpDot %float %153 %54
        %155 = OpFOrdGreaterThan %bool %154 %float_0
               OpSelectionMerge %156 None
               OpBranchConditional %155 %157 %158
        %158 = OpLabel
               OpBranch %156
        %157 = OpLabel
        %159 = OpCompositeConstruct %v4float %154 %154 %154 %154
        %160 = OpFDiv %v4float %153 %159
               OpBranch %156
        %156 = OpLabel
        %161 = OpPhi %v4float %160 %157 %56 %158
        %162 = OpCompositeExtract %float %161 0
        %163 = OpFOrdGreaterThan %bool %162 %float_0
               OpSelectionMerge %164 None
               OpBranchConditional %163 %165 %164
        %165 = OpLabel
        %166 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_0
        %167 = OpAccessChain %_ptr_Uniform_v4float %166 %uint_0
        %168 = OpLoad %v4float %167
        %169 = OpAccessChain %_ptr_Uniform_float %166 %uint_2
        %170 = OpLoad %float %169
        %171 = OpAccessChain %_ptr_Uniform_float %166 %uint_3
        %172 = OpLoad %float %171
        %173 = OpAccessChain %_ptr_Uniform_float %166 %uint_4
        %174 = OpLoad %float %173
        %175 = OpAccessChain %_ptr_Uniform_v2float %166 %uint_5
        %176 = OpLoad %v2float %175
        %177 = OpVectorTimesScalar %v2float %115 %174
        %178 = OpFAdd %v2float %177 %176
        %179 = OpAccessChain %_ptr_Uniform_v2float %MaterialParams %int_8
        %180 = OpLoad %v2float %179
        %181 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_9
        %182 = OpLoad %float %181
        %183 = OpVectorTimesScalar %v2float %180 %182
        %184 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_4
        %185 = OpLoad %float %184
        %186 = OpCompositeExtract %float %178 0
        %187 = OpFMul %float %186 %float_10
        %188 = OpFAdd %float %185 %187
        %189 = OpExtInst %float %1 Sin %188
        %190 = OpVectorTimesScalar %v2float %183 %189
        %191 = OpVectorTimesScalar %v2float %190 %float_0_00999999978
        %192 = OpFAdd %v2float %178 %191
        %193 = OpLoad %type_2d_image_array %AlbedoTextures
        %194 = OpLoad %type_sampler %TrilinearSampler
        %195 = OpCompositeExtract %float %192 0
        %196 = OpCompositeExtract %float %192 1
        %197 = OpCompositeConstruct %v3float %195 %196 %float_0
        %198 = OpSampledImage %type_sampled_image_0 %193 %194
        %199 = OpImageSampleImplicitLod %v4float %198 %197 None
        %200 = OpFMul %v4float %199 %168
        %201 = OpLoad %type_2d_image_array %NormalTextures
        %202 = OpLoad %type_sampler %TrilinearSampler
        %203 = OpCompositeExtract %float %178 1
        %204 = OpCompositeConstruct %v3float %186 %203 %float_0
        %205 = OpSampledImage %type_sampled_image_0 %201 %202
        %206 = OpImageSampleImplicitLod %v4float %205 %204 None
        %207 = OpVectorShuffle %v3float %206 %206 0 1 2
        %208 = OpVectorTimesScalar %v3float %207 %float_2
        %209 = OpFSub %v3float %208 %72
        %210 = OpVectorTimesScalar %v3float %209 %172
        %211 = OpLoad %type_2d_image_array %RoughnessTextures
        %212 = OpLoad %type_sampler %TrilinearSampler
        %213 = OpSampledImage %type_sampled_image_0 %211 %212
        %214 = OpImageSampleImplicitLod %v4float %213 %204 None
        %215 = OpCompositeExtract %float %214 0
        %216 = OpFMul %float %170 %215
        %217 = OpCompositeExtract %float %214 1
        %218 = OpVectorTimesScalar %v4float %200 %162
        %219 = OpVectorTimesScalar %v3float %210 %162
        %220 = OpFMul %float %216 %162
        %221 = OpFMul %float %217 %162
        %222 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_0 %int_1
        %223 = OpLoad %float %222
        %224 = OpFMul %float %223 %162
               OpBranch %164
        %164 = OpLabel
        %225 = OpPhi %float %float_0 %156 %224 %165
        %226 = OpPhi %float %float_0 %156 %221 %165
        %227 = OpPhi %float %float_0 %156 %220 %165
        %228 = OpPhi %v3float %59 %156 %219 %165
        %229 = OpPhi %v4float %57 %156 %218 %165
        %230 = OpCompositeExtract %float %161 1
        %231 = OpFOrdGreaterThan %bool %230 %float_0
               OpSelectionMerge %232 None
               OpBranchConditional %231 %233 %232
        %233 = OpLabel
        %234 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_1
        %235 = OpAccessChain %_ptr_Uniform_v4float %234 %uint_0
        %236 = OpLoad %v4float %235
        %237 = OpAccessChain %_ptr_Uniform_float %234 %uint_2
        %238 = OpLoad %float %237
        %239 = OpAccessChain %_ptr_Uniform_float %234 %uint_3
        %240 = OpLoad %float %239
        %241 = OpAccessChain %_ptr_Uniform_float %234 %uint_4
        %242 = OpLoad %float %241
        %243 = OpAccessChain %_ptr_Uniform_v2float %234 %uint_5
        %244 = OpLoad %v2float %243
        %245 = OpVectorTimesScalar %v2float %115 %242
        %246 = OpFAdd %v2float %245 %244
        %247 = OpLoad %type_2d_image_array %AlbedoTextures
        %248 = OpLoad %type_sampler %TrilinearSampler
        %249 = OpCompositeExtract %float %246 0
        %250 = OpCompositeExtract %float %246 1
        %251 = OpCompositeConstruct %v3float %249 %250 %float_1
        %252 = OpSampledImage %type_sampled_image_0 %247 %248
        %253 = OpImageSampleImplicitLod %v4float %252 %251 None
        %254 = OpFMul %v4float %253 %236
        %255 = OpLoad %type_2d_image_array %NormalTextures
        %256 = OpLoad %type_sampler %TrilinearSampler
        %257 = OpSampledImage %type_sampled_image_0 %255 %256
        %258 = OpImageSampleImplicitLod %v4float %257 %251 None
        %259 = OpVectorShuffle %v3float %258 %258 0 1 2
        %260 = OpVectorTimesScalar %v3float %259 %float_2
        %261 = OpFSub %v3float %260 %72
        %262 = OpVectorTimesScalar %v3float %261 %240
        %263 = OpLoad %type_2d_image_array %RoughnessTextures
        %264 = OpLoad %type_sampler %TrilinearSampler
        %265 = OpSampledImage %type_sampled_image_0 %263 %264
        %266 = OpImageSampleImplicitLod %v4float %265 %251 None
        %267 = OpCompositeExtract %float %266 0
        %268 = OpFMul %float %238 %267
        %269 = OpCompositeExtract %float %266 1
        %270 = OpVectorTimesScalar %v4float %254 %230
        %271 = OpFAdd %v4float %229 %270
        %272 = OpVectorTimesScalar %v3float %262 %230
        %273 = OpFAdd %v3float %228 %272
        %274 = OpFMul %float %268 %230
        %275 = OpFAdd %float %227 %274
        %276 = OpFMul %float %269 %230
        %277 = OpFAdd %float %226 %276
        %278 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_1 %int_1
        %279 = OpLoad %float %278
        %280 = OpFMul %float %279 %230
        %281 = OpFAdd %float %225 %280
               OpBranch %232
        %232 = OpLabel
        %282 = OpPhi %float %225 %164 %281 %233
        %283 = OpPhi %float %226 %164 %277 %233
        %284 = OpPhi %float %227 %164 %275 %233
        %285 = OpPhi %v3float %228 %164 %273 %233
        %286 = OpPhi %v4float %229 %164 %271 %233
        %287 = OpCompositeExtract %float %161 2
        %288 = OpFOrdGreaterThan %bool %287 %float_0
               OpSelectionMerge %289 None
               OpBranchConditional %288 %290 %289
        %290 = OpLabel
        %291 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_2
        %292 = OpAccessChain %_ptr_Uniform_v4float %291 %uint_0
        %293 = OpLoad %v4float %292
        %294 = OpAccessChain %_ptr_Uniform_float %291 %uint_2
        %295 = OpLoad %float %294
        %296 = OpAccessChain %_ptr_Uniform_float %291 %uint_3
        %297 = OpLoad %float %296
        %298 = OpAccessChain %_ptr_Uniform_float %291 %uint_4
        %299 = OpLoad %float %298
        %300 = OpAccessChain %_ptr_Uniform_v2float %291 %uint_5
        %301 = OpLoad %v2float %300
        %302 = OpVectorTimesScalar %v2float %115 %299
        %303 = OpFAdd %v2float %302 %301
        %304 = OpLoad %type_2d_image_array %AlbedoTextures
        %305 = OpLoad %type_sampler %TrilinearSampler
        %306 = OpCompositeExtract %float %303 0
        %307 = OpCompositeExtract %float %303 1
        %308 = OpCompositeConstruct %v3float %306 %307 %float_2
        %309 = OpSampledImage %type_sampled_image_0 %304 %305
        %310 = OpImageSampleImplicitLod %v4float %309 %308 None
        %311 = OpFMul %v4float %310 %293
        %312 = OpLoad %type_2d_image_array %NormalTextures
        %313 = OpLoad %type_sampler %TrilinearSampler
        %314 = OpSampledImage %type_sampled_image_0 %312 %313
        %315 = OpImageSampleImplicitLod %v4float %314 %308 None
        %316 = OpVectorShuffle %v3float %315 %315 0 1 2
        %317 = OpVectorTimesScalar %v3float %316 %float_2
        %318 = OpFSub %v3float %317 %72
        %319 = OpVectorTimesScalar %v3float %318 %297
        %320 = OpLoad %type_2d_image_array %RoughnessTextures
        %321 = OpLoad %type_sampler %TrilinearSampler
        %322 = OpSampledImage %type_sampled_image_0 %320 %321
        %323 = OpImageSampleImplicitLod %v4float %322 %308 None
        %324 = OpCompositeExtract %float %323 0
        %325 = OpFMul %float %295 %324
        %326 = OpCompositeExtract %float %323 1
        %327 = OpVectorTimesScalar %v4float %311 %287
        %328 = OpFAdd %v4float %286 %327
        %329 = OpVectorTimesScalar %v3float %319 %287
        %330 = OpFAdd %v3float %285 %329
        %331 = OpFMul %float %325 %287
        %332 = OpFAdd %float %284 %331
        %333 = OpFMul %float %326 %287
        %334 = OpFAdd %float %283 %333
        %335 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_2 %int_1
        %336 = OpLoad %float %335
        %337 = OpFMul %float %336 %287
        %338 = OpFAdd %float %282 %337
               OpBranch %289
        %289 = OpLabel
        %339 = OpPhi %float %282 %232 %338 %290
        %340 = OpPhi %float %283 %232 %334 %290
        %341 = OpPhi %float %284 %232 %332 %290
        %342 = OpPhi %v3float %285 %232 %330 %290
        %343 = OpPhi %v4float %286 %232 %328 %290
        %344 = OpCompositeExtract %float %161 3
        %345 = OpFOrdGreaterThan %bool %344 %float_0
               OpSelectionMerge %346 None
               OpBranchConditional %345 %347 %346
        %347 = OpLabel
        %348 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_3
        %349 = OpAccessChain %_ptr_Uniform_v4float %348 %uint_0
        %350 = OpLoad %v4float %349
        %351 = OpAccessChain %_ptr_Uniform_float %348 %uint_2
        %352 = OpLoad %float %351
        %353 = OpAccessChain %_ptr_Uniform_float %348 %uint_3
        %354 = OpLoad %float %353
        %355 = OpAccessChain %_ptr_Uniform_float %348 %uint_4
        %356 = OpLoad %float %355
        %357 = OpAccessChain %_ptr_Uniform_v2float %348 %uint_5
        %358 = OpLoad %v2float %357
        %359 = OpVectorTimesScalar %v2float %115 %356
        %360 = OpFAdd %v2float %359 %358
        %361 = OpLoad %type_2d_image_array %AlbedoTextures
        %362 = OpLoad %type_sampler %TrilinearSampler
        %363 = OpCompositeExtract %float %360 0
        %364 = OpCompositeExtract %float %360 1
        %365 = OpCompositeConstruct %v3float %363 %364 %float_3
        %366 = OpSampledImage %type_sampled_image_0 %361 %362
        %367 = OpImageSampleImplicitLod %v4float %366 %365 None
        %368 = OpFMul %v4float %367 %350
        %369 = OpLoad %type_2d_image_array %NormalTextures
        %370 = OpLoad %type_sampler %TrilinearSampler
        %371 = OpSampledImage %type_sampled_image_0 %369 %370
        %372 = OpImageSampleImplicitLod %v4float %371 %365 None
        %373 = OpVectorShuffle %v3float %372 %372 0 1 2
        %374 = OpVectorTimesScalar %v3float %373 %float_2
        %375 = OpFSub %v3float %374 %72
        %376 = OpVectorTimesScalar %v3float %375 %354
        %377 = OpLoad %type_2d_image_array %RoughnessTextures
        %378 = OpLoad %type_sampler %TrilinearSampler
        %379 = OpSampledImage %type_sampled_image_0 %377 %378
        %380 = OpImageSampleImplicitLod %v4float %379 %365 None
        %381 = OpCompositeExtract %float %380 0
        %382 = OpFMul %float %352 %381
        %383 = OpCompositeExtract %float %380 1
        %384 = OpVectorTimesScalar %v4float %368 %344
        %385 = OpFAdd %v4float %343 %384
        %386 = OpVectorTimesScalar %v3float %376 %344
        %387 = OpFAdd %v3float %342 %386
        %388 = OpFMul %float %382 %344
        %389 = OpFAdd %float %341 %388
        %390 = OpFMul %float %383 %344
        %391 = OpFAdd %float %340 %390
        %392 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_3 %int_1
        %393 = OpLoad %float %392
        %394 = OpFMul %float %393 %344
        %395 = OpFAdd %float %339 %394
               OpBranch %346
        %346 = OpLabel
        %396 = OpPhi %float %339 %289 %395 %347
        %397 = OpPhi %v4float %343 %289 %385 %347
        %398 = OpPhi %float %340 %289 %391 %347
        %399 = OpPhi %float %341 %289 %389 %347
        %400 = OpPhi %v3float %342 %289 %387 %347
        %401 = OpLoad %type_2d_image %DetailNormalTexture
        %402 = OpLoad %type_sampler %TrilinearSampler
        %403 = OpSampledImage %type_sampled_image %401 %402
        %404 = OpImageSampleImplicitLod %v4float %403 %116 None
        %405 = OpVectorShuffle %v3float %404 %404 0 1 2
        %406 = OpVectorTimesScalar %v3float %405 %float_2
        %407 = OpFSub %v3float %406 %72
        %408 = OpFAdd %v3float %400 %73
        %409 = OpFMul %v3float %407 %75
        %410 = OpCompositeExtract %float %408 2
        %411 = OpCompositeConstruct %v3float %410 %410 %410
        %412 = OpFDiv %v3float %408 %411
        %413 = OpDot %float %408 %409
        %414 = OpVectorTimesScalar %v3float %412 %413
        %415 = OpFSub %v3float %414 %409
        %416 = OpExtInst %v3float %1 FMix %400 %415 %66
        %417 = OpExtInst %v3float %1 Normalize %416
        %418 = OpCompositeConstruct %mat3v3float %123 %124 %122
        %419 = OpMatrixTimesVector %v3float %418 %417
        %420 = OpExtInst %v3float %1 Normalize %419
        %421 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_5
        %422 = OpLoad %v3float %421
        %423 = OpFNegate %v3float %422
        %424 = OpExtInst %v3float %1 Normalize %423
        %425 = OpDot %float %420 %424
        %426 = OpExtInst %float %1 NMax %425 %float_0
               OpSelectionMerge %427 None
               OpSwitch %uint_0 %428
        %428 = OpLabel
        %429 = OpVectorShuffle %v3float %120 %120 0 1 2
        %430 = OpCompositeExtract %float %120 3
        %431 = OpCompositeConstruct %v3float %430 %430 %430
        %432 = OpFDiv %v3float %429 %431
        %433 = OpVectorTimesScalar %v3float %432 %float_0_5
        %434 = OpFAdd %v3float %433 %66
        %435 = OpCompositeExtract %float %434 0
        %436 = OpFOrdLessThan %bool %435 %float_0
        %437 = OpLogicalNot %bool %436
               OpSelectionMerge %438 None
               OpBranchConditional %437 %439 %438
        %439 = OpLabel
        %440 = OpFOrdGreaterThan %bool %435 %float_1
               OpBranch %438
        %438 = OpLabel
        %441 = OpPhi %bool %true %428 %440 %439
        %442 = OpLogicalNot %bool %441
               OpSelectionMerge %443 None
               OpBranchConditional %442 %444 %443
        %444 = OpLabel
        %445 = OpCompositeExtract %float %434 1
        %446 = OpFOrdLessThan %bool %445 %float_0
               OpBranch %443
        %443 = OpLabel
        %447 = OpPhi %bool %true %438 %446 %444
        %448 = OpLogicalNot %bool %447
               OpSelectionMerge %449 None
               OpBranchConditional %448 %450 %449
        %450 = OpLabel
        %451 = OpCompositeExtract %float %434 1
        %452 = OpFOrdGreaterThan %bool %451 %float_1
               OpBranch %449
        %449 = OpLabel
        %453 = OpPhi %bool %true %443 %452 %450
               OpSelectionMerge %454 None
               OpBranchConditional %453 %455 %454
        %455 = OpLabel
               OpBranch %427
        %454 = OpLabel
               OpBranch %456
        %456 = OpLabel
        %457 = OpPhi %float %float_0 %454 %458 %459
        %460 = OpPhi %int %int_n1 %454 %461 %459
        %462 = OpSLessThanEqual %bool %460 %int_1
               OpLoopMerge %463 %459 None
               OpBranchConditional %462 %464 %463
        %464 = OpLabel
               OpBranch %465
        %465 = OpLabel
        %458 = OpPhi %float %457 %464 %466 %467
        %468 = OpPhi %int %int_n1 %464 %469 %467
        %470 = OpSLessThanEqual %bool %468 %int_1
               OpLoopMerge %471 %467 None
               OpBranchConditional %470 %467 %471
        %467 = OpLabel
        %472 = OpConvertSToF %float %460
        %473 = OpConvertSToF %float %468
        %474 = OpCompositeConstruct %v2float %472 %473
        %475 = OpFMul %v2float %474 %103
        %476 = OpLoad %type_2d_image %ShadowMap
        %477 = OpLoad %type_sampler %ShadowSampler
        %478 = OpVectorShuffle %v2float %434 %434 0 1
        %479 = OpFAdd %v2float %478 %475
        %480 = OpCompositeExtract %float %434 2
        %481 = OpSampledImage %type_sampled_image %476 %477
        %482 = OpImageSampleDrefExplicitLod %float %481 %479 %480 Lod %float_0
        %466 = OpFAdd %float %458 %482
        %469 = OpIAdd %int %468 %int_1
               OpBranch %465
        %471 = OpLabel
               OpBranch %459
        %459 = OpLabel
        %461 = OpIAdd %int %460 %int_1
               OpBranch %456
        %463 = OpLabel
        %483 = OpFMul %float %457 %float_0_111111112
               OpBranch %427
        %427 = OpLabel
        %484 = OpPhi %float %float_1 %455 %483 %463
        %485 = OpFNegate %v3float %125
        %486 = OpExtInst %v3float %1 Reflect %485 %420
        %487 = OpFMul %float %399 %float_7
        %488 = OpLoad %type_cube_image %SkyboxTexture
        %489 = OpLoad %type_sampler %TrilinearSampler
        %490 = OpSampledImage %type_sampled_image_1 %488 %489
        %491 = OpImageSampleExplicitLod %v4float %490 %486 Lod %487
        %492 = OpVectorShuffle %v3float %491 %491 0 1 2
        %493 = OpVectorTimesScalar %v3float %64 %398
        %494 = OpVectorShuffle %v3float %397 %397 0 1 2
        %495 = OpVectorTimesScalar %v3float %494 %426
        %496 = OpVectorTimesScalar %v3float %495 %484
        %497 = OpVectorTimesScalar %v3float %492 %float_1
        %498 = OpCompositeConstruct %v3float %399 %399 %399
        %499 = OpFSub %v3float %497 %498
        %500 = OpVectorTimesScalar %v3float %499 %396
        %501 = OpFAdd %v3float %493 %496
        %502 = OpFAdd %v3float %501 %500
        %503 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_6
        %504 = OpLoad %float %503
        %505 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_7
        %506 = OpLoad %float %505
        %507 = OpFDiv %float %504 %506
        %508 = OpFSub %float %121 %507
        %509 = OpFSub %float %508 %504
        %510 = OpExtInst %float %1 FClamp %509 %float_0 %float_1
        %511 = OpAccessChain %_ptr_Uniform_v3float %MaterialParams %int_4
        %512 = OpLoad %v3float %511
        %513 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_5
        %514 = OpLoad %float %513
        %515 = OpFMul %float %510 %514
        %516 = OpCompositeConstruct %v3float %515 %515 %515
        %517 = OpExtInst %v3float %1 FMix %502 %512 %516
        %518 = OpCompositeExtract %float %397 3
        %519 = OpCompositeExtract %float %517 0
        %520 = OpCompositeExtract %float %517 1
        %521 = OpCompositeExtract %float %517 2
        %522 = OpCompositeConstruct %v4float %519 %520 %521 %518
        %523 = OpVectorTimesScalar %v3float %420 %float_0_5
        %524 = OpFAdd %v3float %523 %66
        %525 = OpCompositeExtract %float %524 0
        %526 = OpCompositeExtract %float %524 1
        %527 = OpCompositeExtract %float %524 2
        %528 = OpCompositeConstruct %v4float %525 %526 %527 %399
        %529 = OpFDiv %float %117 %141
        %530 = OpCompositeConstruct %v4float %396 %399 %398 %529
        %531 = OpCompositeExtract %float %111 2
        %532 = OpCompositeExtract %float %111 3
        %533 = OpFDiv %float %531 %532
        %534 = OpCompositeConstruct %v4float %float_0 %float_0 %533 %float_1
               OpStore %out_var_SV_Target0 %522
               OpStore %out_var_SV_Target1 %528
               OpStore %out_var_SV_Target2 %530
               OpStore %out_var_SV_Target3 %534
               OpReturn
               OpFunctionEnd
