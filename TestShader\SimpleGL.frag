#version 320 es
#define WINDOW_SIZE 10

precision mediump  float;
precision mediump  sampler2D;

uniform bool toneMap;
uniform sampler2D texUnit;
uniform mat4 colorModulation;
uniform float gaussOffsets[WINDOW_SIZE];
uniform float gaussWeights[WINDOW_SIZE];

in vec2 texCoord;
out vec4 fragColor;

void main() {
    fragColor = vec4(0.0);

    // For each gaussian sample
    for (int i = 0; i < WINDOW_SIZE; i++) {
        // Create sample texture coord
        vec2 offsetTexCoord = texCoord + vec2(gaussOffsets[i], 0.0);

        // Load data and perform tone mapping
        vec4 data = texture(texUnit, offsetTexCoord);
        // if (toneMap) {
        //     data *= colorModulation;
        // }

        // Accumulate result
        fragColor += data * gaussWeights[i];
    }
    if (toneMap) { 
        fragColor *= colorModulation; 
    } 
}  
