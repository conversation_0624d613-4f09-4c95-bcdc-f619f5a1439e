; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 670
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_RayTracingParams "type.RayTracingParams"
               OpMemberName %type_RayTracingParams 0 "InverseViewMatrix"
               OpMemberName %type_RayTracingParams 1 "InverseProjectionMatrix"
               OpMemberName %type_RayTracingParams 2 "CameraPosition"
               OpMemberName %type_RayTracingParams 3 "Time"
               OpMemberName %type_RayTracingParams 4 "FrameCount"
               OpMemberName %type_RayTracingParams 5 "MaxBounces"
               OpMemberName %type_RayTracingParams 6 "SamplesPerPixel"
               OpMemberName %type_RayTracingParams 7 "RandomSeed"
               OpMemberName %type_RayTracingParams 8 "ScreenResolution"
               OpMemberName %type_RayTracingParams 9 "JitterOffset"
               OpMemberName %type_RayTracingParams 10 "NumSpheres"
               OpMemberName %type_RayTracingParams 11 "NumTriangles"
               OpMemberName %type_RayTracingParams 12 "NumMaterials"
               OpMemberName %type_RayTracingParams 13 "_padding"
               OpName %RayTracingParams "RayTracingParams"
               OpName %type_StructuredBuffer_Sphere "type.StructuredBuffer.Sphere"
               OpName %Sphere "Sphere"
               OpMemberName %Sphere 0 "Center"
               OpMemberName %Sphere 1 "Radius"
               OpMemberName %Sphere 2 "MaterialID"
               OpMemberName %Sphere 3 "_padding"
               OpName %Spheres "Spheres"
               OpName %type_StructuredBuffer_Triangle "type.StructuredBuffer.Triangle"
               OpName %Triangle "Triangle"
               OpMemberName %Triangle 0 "V0"
               OpMemberName %Triangle 1 "V1"
               OpMemberName %Triangle 2 "V2"
               OpMemberName %Triangle 3 "N0"
               OpMemberName %Triangle 4 "N1"
               OpMemberName %Triangle 5 "N2"
               OpMemberName %Triangle 6 "UV0"
               OpMemberName %Triangle 7 "UV1"
               OpMemberName %Triangle 8 "UV2"
               OpMemberName %Triangle 9 "MaterialID"
               OpMemberName %Triangle 10 "_padding"
               OpName %Triangles "Triangles"
               OpName %type_StructuredBuffer_Material "type.StructuredBuffer.Material"
               OpName %Material "Material"
               OpMemberName %Material 0 "Albedo"
               OpMemberName %Material 1 "Metallic"
               OpMemberName %Material 2 "Emission"
               OpMemberName %Material 3 "Roughness"
               OpMemberName %Material 4 "IOR"
               OpMemberName %Material 5 "Transparency"
               OpMemberName %Material 6 "_padding"
               OpName %Materials "Materials"
               OpName %type_2d_image "type.2d.image"
               OpName %OutputTexture "OutputTexture"
               OpName %AccumulationTexture "AccumulationTexture"
               OpName %type_2d_image_0 "type.2d.image"
               OpName %EnvironmentMap "EnvironmentMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %RayTracingParams DescriptorSet 0
               OpDecorate %RayTracingParams Binding 0
               OpDecorate %Spheres DescriptorSet 0
               OpDecorate %Spheres Binding 0
               OpDecorate %Triangles DescriptorSet 0
               OpDecorate %Triangles Binding 1
               OpDecorate %Materials DescriptorSet 0
               OpDecorate %Materials Binding 2
               OpDecorate %OutputTexture DescriptorSet 0
               OpDecorate %OutputTexture Binding 0
               OpDecorate %AccumulationTexture DescriptorSet 0
               OpDecorate %AccumulationTexture Binding 1
               OpDecorate %EnvironmentMap DescriptorSet 0
               OpDecorate %EnvironmentMap Binding 4
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_RayTracingParams 0 Offset 0
               OpMemberDecorate %type_RayTracingParams 0 MatrixStride 16
               OpMemberDecorate %type_RayTracingParams 0 RowMajor
               OpMemberDecorate %type_RayTracingParams 1 Offset 64
               OpMemberDecorate %type_RayTracingParams 1 MatrixStride 16
               OpMemberDecorate %type_RayTracingParams 1 RowMajor
               OpMemberDecorate %type_RayTracingParams 2 Offset 128
               OpMemberDecorate %type_RayTracingParams 3 Offset 140
               OpMemberDecorate %type_RayTracingParams 4 Offset 144
               OpMemberDecorate %type_RayTracingParams 5 Offset 148
               OpMemberDecorate %type_RayTracingParams 6 Offset 152
               OpMemberDecorate %type_RayTracingParams 7 Offset 156
               OpMemberDecorate %type_RayTracingParams 8 Offset 160
               OpMemberDecorate %type_RayTracingParams 9 Offset 168
               OpMemberDecorate %type_RayTracingParams 10 Offset 176
               OpMemberDecorate %type_RayTracingParams 11 Offset 180
               OpMemberDecorate %type_RayTracingParams 12 Offset 184
               OpMemberDecorate %type_RayTracingParams 13 Offset 188
               OpDecorate %type_RayTracingParams Block
               OpMemberDecorate %Sphere 0 Offset 0
               OpMemberDecorate %Sphere 1 Offset 12
               OpMemberDecorate %Sphere 2 Offset 16
               OpMemberDecorate %Sphere 3 Offset 20
               OpDecorate %_runtimearr_Sphere ArrayStride 32
               OpMemberDecorate %type_StructuredBuffer_Sphere 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Sphere 0 NonWritable
               OpDecorate %type_StructuredBuffer_Sphere BufferBlock
               OpMemberDecorate %Triangle 0 Offset 0
               OpMemberDecorate %Triangle 1 Offset 16
               OpMemberDecorate %Triangle 2 Offset 32
               OpMemberDecorate %Triangle 3 Offset 48
               OpMemberDecorate %Triangle 4 Offset 64
               OpMemberDecorate %Triangle 5 Offset 80
               OpMemberDecorate %Triangle 6 Offset 96
               OpMemberDecorate %Triangle 7 Offset 104
               OpMemberDecorate %Triangle 8 Offset 112
               OpMemberDecorate %Triangle 9 Offset 120
               OpMemberDecorate %Triangle 10 Offset 128
               OpDecorate %_runtimearr_Triangle ArrayStride 144
               OpMemberDecorate %type_StructuredBuffer_Triangle 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Triangle 0 NonWritable
               OpDecorate %type_StructuredBuffer_Triangle BufferBlock
               OpMemberDecorate %Material 0 Offset 0
               OpMemberDecorate %Material 1 Offset 12
               OpMemberDecorate %Material 2 Offset 16
               OpMemberDecorate %Material 3 Offset 28
               OpMemberDecorate %Material 4 Offset 32
               OpMemberDecorate %Material 5 Offset 36
               OpMemberDecorate %Material 6 Offset 40
               OpDecorate %_runtimearr_Material ArrayStride 48
               OpMemberDecorate %type_StructuredBuffer_Material 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_Material 0 NonWritable
               OpDecorate %type_StructuredBuffer_Material BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_8 = OpConstant %int 8
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
      %int_1 = OpConstant %int 1
      %int_7 = OpConstant %int 7
      %int_4 = OpConstant %int 4
       %uint = OpTypeInt 32 0
%uint_719393 = OpConstant %uint 719393
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %39 = OpConstantComposite %v3float %float_0 %float_0 %float_0
     %uint_0 = OpConstant %uint 0
      %int_6 = OpConstant %int 6
  %float_0_5 = OpConstant %float 0.5
    %v2float = OpTypeVector %float 2
         %44 = OpConstantComposite %v2float %float_0_5 %float_0_5
     %uint_1 = OpConstant %uint 1
    %float_1 = OpConstant %float 1
         %47 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%float_0_454545468 = OpConstant %float 0.454545468
         %49 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_2 = OpConstant %float 2
         %51 = OpConstantComposite %v2float %float_1 %float_1
      %int_2 = OpConstant %int 2
%float_0_00100000005 = OpConstant %float 0.00100000005
 %float_1000 = OpConstant %float 1000
      %int_5 = OpConstant %int 5
%float_0_100000001 = OpConstant %float 0.100000001
      %false = OpConstantFalse %bool
     %uint_3 = OpConstant %uint 3
     %int_10 = OpConstant %int 10
     %int_11 = OpConstant %int 11
%float_0_0399999991 = OpConstant %float 0.0399999991
         %62 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_5 = OpConstant %float 5
    %uint_61 = OpConstant %uint 61
    %uint_16 = OpConstant %uint 16
     %uint_9 = OpConstant %uint 9
     %uint_4 = OpConstant %uint 4
%uint_668265261 = OpConstant %uint 668265261
    %uint_15 = OpConstant %uint 15
    %float_4 = OpConstant %float 4
%float_9_99999975en06 = OpConstant %float 9.99999975e-06
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_RayTracingParams = OpTypeStruct %mat4v4float %mat4v4float %v3float %float %uint %uint %uint %uint %v2float %v2float %uint %uint %uint %uint
%_ptr_Uniform_type_RayTracingParams = OpTypePointer Uniform %type_RayTracingParams
     %Sphere = OpTypeStruct %v3float %float %uint %float
%_runtimearr_Sphere = OpTypeRuntimeArray %Sphere
%type_StructuredBuffer_Sphere = OpTypeStruct %_runtimearr_Sphere
%_ptr_Uniform_type_StructuredBuffer_Sphere = OpTypePointer Uniform %type_StructuredBuffer_Sphere
   %Triangle = OpTypeStruct %v3float %v3float %v3float %v3float %v3float %v3float %v2float %v2float %v2float %uint %v3float
%_runtimearr_Triangle = OpTypeRuntimeArray %Triangle
%type_StructuredBuffer_Triangle = OpTypeStruct %_runtimearr_Triangle
%_ptr_Uniform_type_StructuredBuffer_Triangle = OpTypePointer Uniform %type_StructuredBuffer_Triangle
   %Material = OpTypeStruct %v3float %float %v3float %float %float %float %v2float
%_runtimearr_Material = OpTypeRuntimeArray %Material
%type_StructuredBuffer_Material = OpTypeStruct %_runtimearr_Material
%_ptr_Uniform_type_StructuredBuffer_Material = OpTypePointer Uniform %type_StructuredBuffer_Material
%type_2d_image = OpTypeImage %float 2D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_2d_image_0 = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_0 = OpTypePointer UniformConstant %type_2d_image_0
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %84 = OpTypeFunction %void
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
     %v2uint = OpTypeVector %uint 2
     %uint_2 = OpConstant %uint 2
   %uint_264 = OpConstant %uint 264
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_Material = OpTypePointer Uniform %Material
%_ptr_Uniform_Sphere = OpTypePointer Uniform %Sphere
%_ptr_Uniform_Triangle = OpTypePointer Uniform %Triangle
%type_sampled_image = OpTypeSampledImage %type_2d_image_0
%RayTracingParams = OpVariable %_ptr_Uniform_type_RayTracingParams Uniform
    %Spheres = OpVariable %_ptr_Uniform_type_StructuredBuffer_Sphere Uniform
  %Triangles = OpVariable %_ptr_Uniform_type_StructuredBuffer_Triangle Uniform
  %Materials = OpVariable %_ptr_Uniform_type_StructuredBuffer_Material Uniform
%OutputTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%AccumulationTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EnvironmentMap = OpVariable %_ptr_UniformConstant_type_2d_image_0 UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
         %96 = OpUndef %uint
         %97 = OpUndef %v3float
         %98 = OpUndef %float
%float_n9_99999975en06 = OpConstant %float -9.99999975e-06
%float_2_32830644en10 = OpConstant %float 2.32830644e-10
%float_0_318309873 = OpConstant %float 0.318309873
        %102 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
   %float_n2 = OpConstant %float -2
     %v3bool = OpTypeVector %bool 3
     %uint_5 = OpConstant %uint 5
       %main = OpFunction %void None %84
        %106 = OpLabel
        %107 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %108 None
               OpSwitch %uint_0 %109
        %109 = OpLabel
        %110 = OpCompositeExtract %uint %107 0
        %111 = OpAccessChain %_ptr_Uniform_v2float %RayTracingParams %int_8
        %112 = OpAccessChain %_ptr_Uniform_float %RayTracingParams %int_8 %int_0
        %113 = OpLoad %float %112
        %114 = OpConvertFToU %uint %113
        %115 = OpUGreaterThanEqual %bool %110 %114
        %116 = OpLogicalNot %bool %115
               OpSelectionMerge %117 None
               OpBranchConditional %116 %118 %117
        %118 = OpLabel
        %119 = OpCompositeExtract %uint %107 1
        %120 = OpAccessChain %_ptr_Uniform_float %RayTracingParams %int_8 %int_1
        %121 = OpLoad %float %120
        %122 = OpConvertFToU %uint %121
        %123 = OpUGreaterThanEqual %bool %119 %122
               OpBranch %117
        %117 = OpLabel
        %124 = OpPhi %bool %true %109 %123 %118
               OpSelectionMerge %125 None
               OpBranchConditional %124 %126 %125
        %126 = OpLabel
               OpBranch %108
        %125 = OpLabel
        %127 = OpCompositeExtract %uint %107 1
        %128 = OpIMul %uint %127 %114
        %129 = OpIAdd %uint %128 %110
        %130 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_7
        %131 = OpLoad %uint %130
        %132 = OpIAdd %uint %131 %129
        %133 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_4
        %134 = OpLoad %uint %133
        %135 = OpIMul %uint %134 %uint_719393
        %136 = OpIAdd %uint %132 %135
               OpBranch %137
        %137 = OpLabel
        %138 = OpPhi %v3float %97 %125 %139 %140
        %141 = OpPhi %v3float %97 %125 %142 %140
        %143 = OpPhi %uint %96 %125 %144 %140
        %145 = OpPhi %uint %136 %125 %146 %140
        %147 = OpPhi %v3float %39 %125 %148 %140
        %149 = OpPhi %uint %uint_0 %125 %150 %140
        %151 = OpPhi %v3float %97 %125 %152 %140
        %153 = OpPhi %v3float %97 %125 %154 %140
        %155 = OpPhi %uint %96 %125 %156 %140
        %157 = OpPhi %float %98 %125 %158 %140
        %159 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_6
        %160 = OpLoad %uint %159
        %161 = OpULessThan %bool %149 %160
               OpLoopMerge %162 %140 None
               OpBranchConditional %161 %163 %162
        %163 = OpLabel
        %164 = OpBitwiseXor %uint %145 %uint_61
        %165 = OpShiftRightLogical %uint %145 %uint_16
        %166 = OpBitwiseXor %uint %164 %165
        %167 = OpIMul %uint %166 %uint_9
        %168 = OpShiftRightLogical %uint %167 %uint_4
        %169 = OpBitwiseXor %uint %167 %168
        %170 = OpIMul %uint %169 %uint_668265261
        %171 = OpShiftRightLogical %uint %170 %uint_15
        %172 = OpBitwiseXor %uint %170 %171
        %173 = OpConvertUToF %float %172
        %174 = OpFMul %float %173 %float_2_32830644en10
        %175 = OpBitwiseXor %uint %172 %uint_61
        %176 = OpShiftRightLogical %uint %172 %uint_16
        %177 = OpBitwiseXor %uint %175 %176
        %178 = OpIMul %uint %177 %uint_9
        %179 = OpShiftRightLogical %uint %178 %uint_4
        %180 = OpBitwiseXor %uint %178 %179
        %181 = OpIMul %uint %180 %uint_668265261
        %182 = OpShiftRightLogical %uint %181 %uint_15
        %183 = OpBitwiseXor %uint %181 %182
        %184 = OpConvertUToF %float %183
        %185 = OpFMul %float %184 %float_2_32830644en10
        %186 = OpCompositeConstruct %v2float %174 %185
        %187 = OpFSub %v2float %186 %44
        %188 = OpVectorShuffle %v2uint %107 %107 0 1
        %189 = OpConvertUToF %v2float %188
        %190 = OpLoad %v2float %111
        %191 = OpFDiv %v2float %187 %190
        %192 = OpVectorTimesScalar %v2float %191 %float_2
        %193 = OpFAdd %v2float %189 %192
        %194 = OpFSub %v2float %193 %51
        %195 = OpCompositeExtract %float %194 1
        %196 = OpFNegate %float %195
        %197 = OpCompositeExtract %float %194 0
        %198 = OpCompositeConstruct %v4float %197 %196 %float_1 %float_1
        %199 = OpAccessChain %_ptr_Uniform_mat4v4float %RayTracingParams %int_1
        %200 = OpLoad %mat4v4float %199
        %201 = OpMatrixTimesVector %v4float %200 %198
        %202 = OpCompositeExtract %float %201 3
        %203 = OpCompositeConstruct %v4float %202 %202 %202 %202
        %204 = OpFDiv %v4float %201 %203
        %205 = OpCompositeExtract %float %204 0
        %206 = OpCompositeExtract %float %204 1
        %207 = OpCompositeExtract %float %204 2
        %208 = OpCompositeConstruct %v4float %205 %206 %207 %float_1
        %209 = OpAccessChain %_ptr_Uniform_mat4v4float %RayTracingParams %int_0
        %210 = OpLoad %mat4v4float %209
        %211 = OpMatrixTimesVector %v4float %210 %208
        %212 = OpVectorShuffle %v3float %211 %211 0 1 2
        %213 = OpAccessChain %_ptr_Uniform_v3float %RayTracingParams %int_2
        %214 = OpLoad %v3float %213
        %215 = OpFSub %v3float %212 %214
        %216 = OpExtInst %v3float %1 Normalize %215
               OpBranch %217
        %217 = OpLabel
        %218 = OpPhi %v3float %138 %163 %219 %220
        %221 = OpPhi %uint %183 %163 %222 %220
        %223 = OpPhi %v3float %141 %163 %224 %220
        %225 = OpPhi %v3float %39 %163 %226 %220
        %227 = OpPhi %v3float %47 %163 %228 %220
        %229 = OpPhi %uint %143 %163 %230 %220
        %231 = OpPhi %v3float %214 %163 %232 %220
        %233 = OpPhi %v3float %216 %163 %234 %220
        %235 = OpPhi %uint %uint_0 %163 %236 %220
        %237 = OpPhi %v3float %151 %163 %238 %220
        %239 = OpPhi %v3float %153 %163 %240 %220
        %241 = OpPhi %uint %155 %163 %242 %220
        %243 = OpPhi %float %157 %163 %244 %220
        %245 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_5
        %246 = OpLoad %uint %245
        %247 = OpULessThan %bool %235 %246
               OpLoopMerge %248 %220 None
               OpBranchConditional %247 %249 %248
        %249 = OpLabel
               OpBranch %250
        %250 = OpLabel
        %251 = OpPhi %v3float %237 %249 %252 %253
        %254 = OpPhi %v3float %239 %249 %255 %253
        %256 = OpPhi %uint %241 %249 %257 %253
        %258 = OpPhi %float %float_1000 %249 %259 %253
        %260 = OpPhi %float %243 %249 %261 %253
        %262 = OpPhi %float %float_1000 %249 %263 %253
        %264 = OpPhi %v3float %218 %249 %265 %253
        %266 = OpPhi %v3float %223 %249 %267 %253
        %268 = OpPhi %uint %229 %249 %269 %253
        %270 = OpPhi %bool %false %249 %271 %253
        %272 = OpPhi %uint %uint_0 %249 %273 %253
        %274 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_10
        %275 = OpLoad %uint %274
        %276 = OpULessThan %bool %272 %275
               OpLoopMerge %277 %253 None
               OpBranchConditional %276 %278 %277
        %278 = OpLabel
        %279 = OpAccessChain %_ptr_Uniform_Sphere %Spheres %int_0 %272
        %280 = OpAccessChain %_ptr_Uniform_v3float %279 %uint_0
        %281 = OpLoad %v3float %280
        %282 = OpAccessChain %_ptr_Uniform_float %279 %uint_1
        %283 = OpLoad %float %282
        %284 = OpAccessChain %_ptr_Uniform_uint %279 %uint_2
        %285 = OpLoad %uint %284
               OpSelectionMerge %286 None
               OpSwitch %uint_0 %287
        %287 = OpLabel
        %288 = OpFSub %v3float %231 %281
        %289 = OpDot %float %233 %233
        %290 = OpDot %float %288 %233
        %291 = OpFMul %float %float_2 %290
        %292 = OpDot %float %288 %288
        %293 = OpFMul %float %283 %283
        %294 = OpFSub %float %292 %293
        %295 = OpFMul %float %291 %291
        %296 = OpFMul %float %float_4 %289
        %297 = OpFMul %float %296 %294
        %298 = OpFSub %float %295 %297
        %299 = OpFOrdLessThan %bool %298 %float_0
               OpSelectionMerge %300 None
               OpBranchConditional %299 %301 %300
        %301 = OpLabel
               OpBranch %286
        %300 = OpLabel
        %302 = OpExtInst %float %1 Sqrt %298
        %303 = OpFMul %float %290 %float_n2
        %304 = OpFMul %float %float_2 %289
        %305 = OpFDiv %float %302 %304
        %306 = OpFSub %float %303 %305
        %307 = OpFAdd %float %303 %305
        %308 = OpFOrdGreaterThan %bool %306 %float_0_00100000005
               OpSelectionMerge %309 None
               OpBranchConditional %308 %310 %309
        %310 = OpLabel
        %311 = OpFOrdLessThan %bool %306 %262
               OpBranch %309
        %309 = OpLabel
        %312 = OpPhi %bool %false %300 %311 %310
        %313 = OpSelect %float %312 %306 %307
        %314 = OpFOrdLessThan %bool %313 %float_0_00100000005
        %315 = OpLogicalNot %bool %314
               OpSelectionMerge %316 None
               OpBranchConditional %315 %317 %316
        %317 = OpLabel
        %318 = OpFOrdGreaterThan %bool %313 %262
               OpBranch %316
        %316 = OpLabel
        %319 = OpPhi %bool %true %309 %318 %317
               OpSelectionMerge %320 None
               OpBranchConditional %319 %321 %320
        %321 = OpLabel
               OpBranch %286
        %320 = OpLabel
        %322 = OpVectorTimesScalar %v3float %233 %313
        %323 = OpFAdd %v3float %231 %322
        %324 = OpFSub %v3float %323 %281
        %325 = OpExtInst %v3float %1 Normalize %324
               OpBranch %286
        %286 = OpLabel
        %326 = OpPhi %bool %false %301 %false %321 %true %320
        %252 = OpPhi %v3float %251 %301 %251 %321 %323 %320
        %255 = OpPhi %v3float %254 %301 %254 %321 %325 %320
        %257 = OpPhi %uint %256 %301 %256 %321 %285 %320
        %261 = OpPhi %float %260 %301 %260 %321 %313 %320
               OpSelectionMerge %327 None
               OpBranchConditional %326 %328 %327
        %328 = OpLabel
        %329 = OpFOrdLessThan %bool %261 %258
        %330 = OpSelect %float %329 %261 %258
        %331 = OpSelect %float %329 %261 %262
        %332 = OpCompositeConstruct %v3bool %329 %329 %329
        %333 = OpSelect %v3float %332 %252 %264
        %334 = OpSelect %v3float %332 %255 %266
        %335 = OpSelect %uint %329 %257 %268
        %336 = OpSelect %bool %329 %326 %270
               OpBranch %327
        %327 = OpLabel
        %259 = OpPhi %float %258 %286 %330 %328
        %263 = OpPhi %float %262 %286 %331 %328
        %265 = OpPhi %v3float %264 %286 %333 %328
        %267 = OpPhi %v3float %266 %286 %334 %328
        %269 = OpPhi %uint %268 %286 %335 %328
        %271 = OpPhi %bool %270 %286 %336 %328
               OpBranch %253
        %253 = OpLabel
        %273 = OpIAdd %uint %272 %uint_1
               OpBranch %250
        %277 = OpLabel
               OpBranch %337
        %337 = OpLabel
        %238 = OpPhi %v3float %251 %277 %338 %339
        %240 = OpPhi %v3float %254 %277 %340 %339
        %242 = OpPhi %uint %256 %277 %341 %339
        %342 = OpPhi %float %258 %277 %343 %339
        %244 = OpPhi %float %260 %277 %344 %339
        %345 = OpPhi %float %262 %277 %346 %339
        %219 = OpPhi %v3float %264 %277 %347 %339
        %224 = OpPhi %v3float %266 %277 %348 %339
        %230 = OpPhi %uint %268 %277 %349 %339
        %350 = OpPhi %bool %270 %277 %351 %339
        %352 = OpPhi %uint %uint_0 %277 %353 %339
        %354 = OpAccessChain %_ptr_Uniform_uint %RayTracingParams %int_11
        %355 = OpLoad %uint %354
        %356 = OpULessThan %bool %352 %355
               OpLoopMerge %357 %339 None
               OpBranchConditional %356 %358 %357
        %358 = OpLabel
        %359 = OpAccessChain %_ptr_Uniform_Triangle %Triangles %int_0 %352
        %360 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_0
        %361 = OpLoad %v3float %360
        %362 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_1
        %363 = OpLoad %v3float %362
        %364 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_2
        %365 = OpLoad %v3float %364
        %366 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_3
        %367 = OpLoad %v3float %366
        %368 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_4
        %369 = OpLoad %v3float %368
        %370 = OpAccessChain %_ptr_Uniform_v3float %359 %uint_5
        %371 = OpLoad %v3float %370
        %372 = OpAccessChain %_ptr_Uniform_uint %359 %uint_9
        %373 = OpLoad %uint %372
               OpSelectionMerge %374 None
               OpSwitch %uint_0 %375
        %375 = OpLabel
        %376 = OpFSub %v3float %363 %361
        %377 = OpFSub %v3float %365 %361
        %378 = OpExtInst %v3float %1 Cross %233 %377
        %379 = OpDot %float %376 %378
        %380 = OpFOrdGreaterThan %bool %379 %float_n9_99999975en06
               OpSelectionMerge %381 None
               OpBranchConditional %380 %382 %381
        %382 = OpLabel
        %383 = OpFOrdLessThan %bool %379 %float_9_99999975en06
               OpBranch %381
        %381 = OpLabel
        %384 = OpPhi %bool %false %375 %383 %382
               OpSelectionMerge %385 None
               OpBranchConditional %384 %386 %385
        %386 = OpLabel
               OpBranch %374
        %385 = OpLabel
        %387 = OpFDiv %float %float_1 %379
        %388 = OpFSub %v3float %231 %361
        %389 = OpDot %float %388 %378
        %390 = OpFMul %float %387 %389
        %391 = OpFOrdLessThan %bool %390 %float_0
        %392 = OpLogicalNot %bool %391
               OpSelectionMerge %393 None
               OpBranchConditional %392 %394 %393
        %394 = OpLabel
        %395 = OpFOrdGreaterThan %bool %390 %float_1
               OpBranch %393
        %393 = OpLabel
        %396 = OpPhi %bool %true %385 %395 %394
               OpSelectionMerge %397 None
               OpBranchConditional %396 %398 %397
        %398 = OpLabel
               OpBranch %374
        %397 = OpLabel
        %399 = OpExtInst %v3float %1 Cross %388 %376
        %400 = OpDot %float %233 %399
        %401 = OpFMul %float %387 %400
        %402 = OpFOrdLessThan %bool %401 %float_0
        %403 = OpLogicalNot %bool %402
               OpSelectionMerge %404 None
               OpBranchConditional %403 %405 %404
        %405 = OpLabel
        %406 = OpFAdd %float %390 %401
        %407 = OpFOrdGreaterThan %bool %406 %float_1
               OpBranch %404
        %404 = OpLabel
        %408 = OpPhi %bool %true %397 %407 %405
               OpSelectionMerge %409 None
               OpBranchConditional %408 %410 %409
        %410 = OpLabel
               OpBranch %374
        %409 = OpLabel
        %411 = OpDot %float %377 %399
        %412 = OpFMul %float %387 %411
        %413 = OpFOrdLessThan %bool %412 %float_0_00100000005
        %414 = OpLogicalNot %bool %413
               OpSelectionMerge %415 None
               OpBranchConditional %414 %416 %415
        %416 = OpLabel
        %417 = OpFOrdGreaterThan %bool %412 %345
               OpBranch %415
        %415 = OpLabel
        %418 = OpPhi %bool %true %409 %417 %416
               OpSelectionMerge %419 None
               OpBranchConditional %418 %420 %419
        %420 = OpLabel
               OpBranch %374
        %419 = OpLabel
        %421 = OpVectorTimesScalar %v3float %233 %412
        %422 = OpFAdd %v3float %231 %421
        %423 = OpFSub %float %float_1 %390
        %424 = OpFSub %float %423 %401
        %425 = OpVectorTimesScalar %v3float %367 %424
        %426 = OpVectorTimesScalar %v3float %369 %390
        %427 = OpFAdd %v3float %425 %426
        %428 = OpVectorTimesScalar %v3float %371 %401
        %429 = OpFAdd %v3float %427 %428
        %430 = OpExtInst %v3float %1 Normalize %429
               OpBranch %374
        %374 = OpLabel
        %431 = OpPhi %bool %false %386 %false %398 %false %410 %false %420 %true %419
        %338 = OpPhi %v3float %238 %386 %238 %398 %238 %410 %238 %420 %422 %419
        %340 = OpPhi %v3float %240 %386 %240 %398 %240 %410 %240 %420 %430 %419
        %341 = OpPhi %uint %242 %386 %242 %398 %242 %410 %242 %420 %373 %419
        %344 = OpPhi %float %244 %386 %244 %398 %244 %410 %244 %420 %412 %419
               OpSelectionMerge %432 None
               OpBranchConditional %431 %433 %432
        %433 = OpLabel
        %434 = OpFOrdLessThan %bool %344 %342
        %435 = OpSelect %float %434 %344 %342
        %436 = OpSelect %float %434 %344 %345
        %437 = OpCompositeConstruct %v3bool %434 %434 %434
        %438 = OpSelect %v3float %437 %338 %219
        %439 = OpSelect %v3float %437 %340 %224
        %440 = OpSelect %uint %434 %341 %230
        %441 = OpSelect %bool %434 %431 %350
               OpBranch %432
        %432 = OpLabel
        %343 = OpPhi %float %342 %374 %435 %433
        %346 = OpPhi %float %345 %374 %436 %433
        %347 = OpPhi %v3float %219 %374 %438 %433
        %348 = OpPhi %v3float %224 %374 %439 %433
        %349 = OpPhi %uint %230 %374 %440 %433
        %351 = OpPhi %bool %350 %374 %441 %433
               OpBranch %339
        %339 = OpLabel
        %353 = OpIAdd %uint %352 %uint_1
               OpBranch %337
        %357 = OpLabel
        %442 = OpLogicalNot %bool %350
               OpSelectionMerge %443 None
               OpBranchConditional %442 %444 %443
        %444 = OpLabel
        %445 = OpCompositeExtract %float %233 2
        %446 = OpCompositeExtract %float %233 0
        %447 = OpExtInst %float %1 Atan2 %445 %446
        %448 = OpCompositeExtract %float %233 1
        %449 = OpExtInst %float %1 Acos %448
        %450 = OpFAdd %float %447 %float_0_5
        %451 = OpFMul %float %449 %float_0_318309873
        %452 = OpCompositeConstruct %v2float %450 %451
        %453 = OpLoad %type_2d_image_0 %EnvironmentMap
        %454 = OpLoad %type_sampler %LinearSampler
        %455 = OpSampledImage %type_sampled_image %453 %454
        %456 = OpImageSampleExplicitLod %v4float %455 %452 Lod %float_0
        %457 = OpVectorShuffle %v3float %456 %456 0 1 2
        %458 = OpFMul %v3float %227 %457
        %459 = OpFAdd %v3float %225 %458
               OpBranch %248
        %443 = OpLabel
        %460 = OpAccessChain %_ptr_Uniform_Material %Materials %int_0 %230
        %461 = OpAccessChain %_ptr_Uniform_v3float %460 %uint_0
        %462 = OpLoad %v3float %461
        %463 = OpAccessChain %_ptr_Uniform_float %460 %uint_1
        %464 = OpLoad %float %463
        %465 = OpAccessChain %_ptr_Uniform_v3float %460 %uint_2
        %466 = OpLoad %v3float %465
        %467 = OpAccessChain %_ptr_Uniform_float %460 %uint_3
        %468 = OpLoad %float %467
        %469 = OpFMul %v3float %227 %466
        %226 = OpFAdd %v3float %225 %469
        %470 = OpFNegate %v3float %233
               OpSelectionMerge %471 None
               OpSwitch %uint_0 %472
        %472 = OpLabel
        %473 = OpBitwiseXor %uint %221 %uint_61
        %474 = OpShiftRightLogical %uint %221 %uint_16
        %475 = OpBitwiseXor %uint %473 %474
        %476 = OpIMul %uint %475 %uint_9
        %477 = OpShiftRightLogical %uint %476 %uint_4
        %478 = OpBitwiseXor %uint %476 %477
        %479 = OpIMul %uint %478 %uint_668265261
        %480 = OpShiftRightLogical %uint %479 %uint_15
        %481 = OpBitwiseXor %uint %479 %480
        %482 = OpConvertUToF %float %481
        %483 = OpFMul %float %482 %float_2_32830644en10
        %484 = OpFOrdLessThan %bool %483 %float_0_5
               OpSelectionMerge %485 None
               OpBranchConditional %484 %486 %487
        %487 = OpLabel
               OpBranch %488
        %488 = OpLabel
        %489 = OpPhi %uint %481 %487 %490 %488
        %491 = OpBitwiseXor %uint %489 %uint_61
        %492 = OpShiftRightLogical %uint %489 %uint_16
        %493 = OpBitwiseXor %uint %491 %492
        %494 = OpIMul %uint %493 %uint_9
        %495 = OpShiftRightLogical %uint %494 %uint_4
        %496 = OpBitwiseXor %uint %494 %495
        %497 = OpIMul %uint %496 %uint_668265261
        %498 = OpShiftRightLogical %uint %497 %uint_15
        %499 = OpBitwiseXor %uint %497 %498
        %500 = OpConvertUToF %float %499
        %501 = OpFMul %float %500 %float_2_32830644en10
        %502 = OpBitwiseXor %uint %499 %uint_61
        %503 = OpShiftRightLogical %uint %499 %uint_16
        %504 = OpBitwiseXor %uint %502 %503
        %505 = OpIMul %uint %504 %uint_9
        %506 = OpShiftRightLogical %uint %505 %uint_4
        %507 = OpBitwiseXor %uint %505 %506
        %508 = OpIMul %uint %507 %uint_668265261
        %509 = OpShiftRightLogical %uint %508 %uint_15
        %510 = OpBitwiseXor %uint %508 %509
        %511 = OpConvertUToF %float %510
        %512 = OpFMul %float %511 %float_2_32830644en10
        %513 = OpBitwiseXor %uint %510 %uint_61
        %514 = OpShiftRightLogical %uint %510 %uint_16
        %515 = OpBitwiseXor %uint %513 %514
        %516 = OpIMul %uint %515 %uint_9
        %517 = OpShiftRightLogical %uint %516 %uint_4
        %518 = OpBitwiseXor %uint %516 %517
        %519 = OpIMul %uint %518 %uint_668265261
        %520 = OpShiftRightLogical %uint %519 %uint_15
        %490 = OpBitwiseXor %uint %519 %520
        %521 = OpConvertUToF %float %490
        %522 = OpFMul %float %521 %float_2_32830644en10
        %523 = OpCompositeConstruct %v3float %501 %512 %522
        %524 = OpVectorTimesScalar %v3float %523 %float_2
        %525 = OpFSub %v3float %524 %47
        %526 = OpDot %float %525 %525
        %527 = OpFOrdGreaterThanEqual %bool %526 %float_1
               OpLoopMerge %528 %488 None
               OpBranchConditional %527 %488 %528
        %528 = OpLabel
        %529 = OpExtInst %v3float %1 Normalize %525
        %530 = OpDot %float %529 %224
        %531 = OpFOrdGreaterThan %bool %530 %float_0
               OpSelectionMerge %532 None
               OpBranchConditional %531 %533 %534
        %534 = OpLabel
        %535 = OpFNegate %v3float %529
               OpBranch %532
        %533 = OpLabel
               OpBranch %532
        %532 = OpLabel
        %536 = OpPhi %v3float %535 %534 %529 %533
        %537 = OpVectorTimesScalar %v3float %462 %float_1
        %538 = OpCompositeConstruct %v3float %464 %464 %464
        %539 = OpFSub %v3float %537 %538
        %540 = OpFMul %v3float %539 %102
               OpBranch %471
        %486 = OpLabel
        %541 = OpExtInst %v3float %1 Reflect %233 %224
               OpBranch %542
        %542 = OpLabel
        %543 = OpPhi %uint %481 %486 %544 %542
        %545 = OpBitwiseXor %uint %543 %uint_61
        %546 = OpShiftRightLogical %uint %543 %uint_16
        %547 = OpBitwiseXor %uint %545 %546
        %548 = OpIMul %uint %547 %uint_9
        %549 = OpShiftRightLogical %uint %548 %uint_4
        %550 = OpBitwiseXor %uint %548 %549
        %551 = OpIMul %uint %550 %uint_668265261
        %552 = OpShiftRightLogical %uint %551 %uint_15
        %553 = OpBitwiseXor %uint %551 %552
        %554 = OpConvertUToF %float %553
        %555 = OpFMul %float %554 %float_2_32830644en10
        %556 = OpBitwiseXor %uint %553 %uint_61
        %557 = OpShiftRightLogical %uint %553 %uint_16
        %558 = OpBitwiseXor %uint %556 %557
        %559 = OpIMul %uint %558 %uint_9
        %560 = OpShiftRightLogical %uint %559 %uint_4
        %561 = OpBitwiseXor %uint %559 %560
        %562 = OpIMul %uint %561 %uint_668265261
        %563 = OpShiftRightLogical %uint %562 %uint_15
        %564 = OpBitwiseXor %uint %562 %563
        %565 = OpConvertUToF %float %564
        %566 = OpFMul %float %565 %float_2_32830644en10
        %567 = OpBitwiseXor %uint %564 %uint_61
        %568 = OpShiftRightLogical %uint %564 %uint_16
        %569 = OpBitwiseXor %uint %567 %568
        %570 = OpIMul %uint %569 %uint_9
        %571 = OpShiftRightLogical %uint %570 %uint_4
        %572 = OpBitwiseXor %uint %570 %571
        %573 = OpIMul %uint %572 %uint_668265261
        %574 = OpShiftRightLogical %uint %573 %uint_15
        %544 = OpBitwiseXor %uint %573 %574
        %575 = OpConvertUToF %float %544
        %576 = OpFMul %float %575 %float_2_32830644en10
        %577 = OpCompositeConstruct %v3float %555 %566 %576
        %578 = OpVectorTimesScalar %v3float %577 %float_2
        %579 = OpFSub %v3float %578 %47
        %580 = OpDot %float %579 %579
        %581 = OpFOrdGreaterThanEqual %bool %580 %float_1
               OpLoopMerge %582 %542 None
               OpBranchConditional %581 %542 %582
        %582 = OpLabel
        %583 = OpExtInst %v3float %1 Normalize %579
        %584 = OpVectorTimesScalar %v3float %583 %468
        %585 = OpFAdd %v3float %541 %584
        %586 = OpExtInst %v3float %1 Normalize %585
        %587 = OpDot %float %586 %224
        %588 = OpFOrdLessThanEqual %bool %587 %float_0
               OpSelectionMerge %589 None
               OpBranchConditional %588 %590 %589
        %590 = OpLabel
        %591 = OpExtInst %v3float %1 Reflect %586 %224
               OpBranch %589
        %589 = OpLabel
        %592 = OpPhi %v3float %586 %582 %591 %590
        %593 = OpDot %float %224 %470
        %594 = OpExtInst %float %1 NMax %593 %float_0
        %595 = OpCompositeConstruct %v3float %464 %464 %464
        %596 = OpExtInst %v3float %1 FMix %62 %462 %595
        %597 = OpFSub %float %float_1 %594
        %598 = OpExtInst %float %1 Pow %597 %float_5
        %599 = OpVectorTimesScalar %v3float %596 %598
        %600 = OpFSub %v3float %47 %599
        %601 = OpFAdd %v3float %596 %600
               OpBranch %471
        %485 = OpLabel
               OpUnreachable
        %471 = OpLabel
        %602 = OpPhi %uint %490 %532 %544 %589
        %234 = OpPhi %v3float %536 %532 %592 %589
        %603 = OpPhi %float %float_0_5 %532 %float_2 %589
        %604 = OpPhi %v3float %540 %532 %601 %589
        %605 = OpFOrdLessThanEqual %bool %603 %float_0
               OpSelectionMerge %606 None
               OpBranchConditional %605 %607 %606
        %607 = OpLabel
               OpBranch %248
        %606 = OpLabel
        %608 = OpDot %float %224 %234
        %609 = OpExtInst %float %1 NMax %608 %float_0
        %610 = OpVectorTimesScalar %v3float %604 %609
        %611 = OpCompositeConstruct %v3float %603 %603 %603
        %612 = OpFDiv %v3float %610 %611
        %613 = OpFMul %v3float %227 %612
        %614 = OpCompositeExtract %float %613 0
        %615 = OpCompositeExtract %float %613 1
        %616 = OpExtInst %float %1 NMax %614 %615
        %617 = OpCompositeExtract %float %613 2
        %618 = OpExtInst %float %1 NMax %616 %617
        %619 = OpFOrdLessThan %bool %618 %float_0_100000001
               OpSelectionMerge %620 None
               OpBranchConditional %619 %621 %620
        %621 = OpLabel
        %622 = OpUGreaterThan %bool %235 %uint_3
               OpBranch %620
        %620 = OpLabel
        %623 = OpPhi %bool %false %606 %622 %621
               OpSelectionMerge %624 None
               OpBranchConditional %623 %625 %624
        %625 = OpLabel
        %626 = OpBitwiseXor %uint %602 %uint_61
        %627 = OpShiftRightLogical %uint %602 %uint_16
        %628 = OpBitwiseXor %uint %626 %627
        %629 = OpIMul %uint %628 %uint_9
        %630 = OpShiftRightLogical %uint %629 %uint_4
        %631 = OpBitwiseXor %uint %629 %630
        %632 = OpIMul %uint %631 %uint_668265261
        %633 = OpShiftRightLogical %uint %632 %uint_15
        %634 = OpBitwiseXor %uint %632 %633
        %635 = OpConvertUToF %float %634
        %636 = OpFMul %float %635 %float_2_32830644en10
        %637 = OpFOrdGreaterThan %bool %636 %618
               OpSelectionMerge %638 None
               OpBranchConditional %637 %639 %638
        %639 = OpLabel
               OpBranch %248
        %638 = OpLabel
        %640 = OpCompositeConstruct %v3float %618 %618 %618
        %641 = OpFDiv %v3float %613 %640
               OpBranch %624
        %624 = OpLabel
        %222 = OpPhi %uint %602 %620 %634 %638
        %228 = OpPhi %v3float %613 %620 %641 %638
        %642 = OpVectorTimesScalar %v3float %224 %float_0_00100000005
        %232 = OpFAdd %v3float %219 %642
               OpBranch %220
        %220 = OpLabel
        %236 = OpIAdd %uint %235 %uint_1
               OpBranch %217
        %248 = OpLabel
        %139 = OpPhi %v3float %218 %217 %219 %444 %219 %607 %219 %639
        %142 = OpPhi %v3float %223 %217 %224 %444 %224 %607 %224 %639
        %144 = OpPhi %uint %229 %217 %230 %444 %230 %607 %230 %639
        %146 = OpPhi %uint %221 %217 %221 %444 %602 %607 %634 %639
        %152 = OpPhi %v3float %237 %217 %238 %444 %238 %607 %238 %639
        %154 = OpPhi %v3float %239 %217 %240 %444 %240 %607 %240 %639
        %156 = OpPhi %uint %241 %217 %242 %444 %242 %607 %242 %639
        %158 = OpPhi %float %243 %217 %244 %444 %244 %607 %244 %639
        %643 = OpPhi %v3float %225 %217 %459 %444 %226 %607 %226 %639
        %148 = OpFAdd %v3float %147 %643
               OpBranch %140
        %140 = OpLabel
        %150 = OpIAdd %uint %149 %uint_1
               OpBranch %137
        %162 = OpLabel
        %644 = OpConvertUToF %float %160
        %645 = OpCompositeConstruct %v3float %644 %644 %644
        %646 = OpFDiv %v3float %147 %645
               OpControlBarrier %uint_2 %uint_2 %uint_264
        %647 = OpVectorShuffle %v2uint %107 %107 0 1
        %648 = OpLoad %type_2d_image %AccumulationTexture
        %649 = OpImageRead %v4float %648 %647 None
        %650 = OpVectorShuffle %v3float %649 %649 0 1 2
        %651 = OpCompositeExtract %float %649 3
        %652 = OpFDiv %float %float_1 %651
        %653 = OpFAdd %float %652 %float_1
        %654 = OpCompositeConstruct %v3float %653 %653 %653
        %655 = OpExtInst %v3float %1 FMix %650 %646 %654
        %656 = OpFAdd %float %651 %float_1
        %657 = OpFDiv %v3float %655 %655
        %658 = OpFAdd %v3float %657 %47
        %659 = OpExtInst %v3float %1 Pow %658 %49
        %660 = OpCompositeExtract %float %659 0
        %661 = OpCompositeExtract %float %659 1
        %662 = OpCompositeExtract %float %659 2
        %663 = OpCompositeConstruct %v4float %660 %661 %662 %float_1
        %664 = OpLoad %type_2d_image %OutputTexture
               OpImageWrite %664 %647 %663 None
        %665 = OpCompositeExtract %float %655 0
        %666 = OpCompositeExtract %float %655 1
        %667 = OpCompositeExtract %float %655 2
        %668 = OpCompositeConstruct %v4float %665 %666 %667 %656
        %669 = OpLoad %type_2d_image %AccumulationTexture
               OpImageWrite %669 %647 %668 None
               OpBranch %108
        %108 = OpLabel
               OpReturn
               OpFunctionEnd
