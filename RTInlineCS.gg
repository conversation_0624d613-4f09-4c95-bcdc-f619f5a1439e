{"$schema": "gigischema.json", "version": "1.0", "variables": [{"name": "CameraPos", "type": "Float3"}, {"name": "InvViewProjMtx", "type": "Float4x4"}, {"name": "<PERSON>", "type": "Float3"}, {"name": "Kd", "type": "Float3"}, {"name": "Ks", "type": "Float3"}, {"name": "Ns", "type": "Float"}, {"name": "<PERSON>", "type": "Float3"}, {"name": "Tf", "type": "Float3"}, {"name": "d", "type": "Float"}, {"name": "Tr", "type": "Float"}], "shaders": [{"name": "RTInlineCS", "fileName": "RTInlineCS.hlsl", "resources": [{"name": "Output", "access": "UAV", "type": "Texture"}, {"name": "Scene", "access": "RTScene", "type": "<PERSON><PERSON><PERSON>", "buffer": {"typeStruct": {"name": "VertexBuffer"}}}, {"name": "VertexBuffer", "access": "SRV", "type": "<PERSON><PERSON><PERSON>", "buffer": {"typeStruct": {"name": "VertexBuffer"}}}, {"name": "DiffuseMap", "access": "SRV", "type": "Texture", "texture": {"dimension": "Texture2DArray"}}, {"name": "AmbientMap", "access": "SRV", "type": "Texture", "texture": {"dimension": "Texture2DArray"}}, {"name": "NormalMap", "access": "SRV", "type": "Texture", "texture": {"dimension": "Texture2DArray"}}], "samplers": [{"name": "samLinear"}]}], "structs": [{"name": "VertexBuffer", "fields": [{"name": "Position ", "type": "Float3", "semantic": "Position"}, {"name": "Normal", "type": "Float3", "semantic": "Normal"}, {"name": "Color", "type": "Float3", "semantic": "Color"}, {"name": "Tangent", "type": "Float4", "semantic": "Tangent"}, {"name": "UV", "type": "Float2", "semantic": "UV"}, {"name": "MaterialID", "type": "Int", "semantic": "MaterialID"}, {"name": "ShapeID", "type": "Int", "semantic": "ShapeID"}]}], "nodes": [{"resourceTexture": {"name": "RenderTarget", "editorPos": [-41.0, -30.0], "format": {"format": "RGBA8_Unorm_sRGB"}, "size": {"multiply": [3840, 2160, 1]}}}, {"resourceBuffer": {"name": "Scene", "editorPos": [-37.0, 18.0], "visibility": "Imported"}}, {"actionComputeShader": {"name": "RayTrace", "editorPos": [139.0, -30.0], "linkProperties": [{}, {}, {}, {}, {}, {}, {}], "connections": [{"srcPin": "Output", "dstNode": "RenderTarget", "dstPin": "resource"}, {"srcPin": "Scene", "dstNode": "Scene", "dstPin": "resource"}, {"srcPin": "VertexBuffer", "dstNode": "Scene", "dstPin": "resource"}, {"srcPin": "DiffuseMap", "dstNode": "DiffuseMap", "dstPin": "resource"}, {"srcPin": "AmbientMap", "dstNode": "AmbientMap", "dstPin": "resource"}, {"srcPin": "NormalMap", "dstNode": "NormalMap", "dstPin": "resource"}], "shader": {"name": "RTInlineCS"}, "dispatchSize": {"node": {"name": "RenderTarget"}}}}, {"resourceTexture": {"name": "DiffuseMap", "editorPos": [-37.0, 66.0], "format": {"format": "RGBA8_Unorm_sRGB"}}}, {"resourceTexture": {"name": "AmbientMap", "editorPos": [-38.0, 114.0], "format": {"format": "RGBA8_Unorm_sRGB"}}}, {"resourceTexture": {"name": "NormalMap", "editorPos": [-21.0, 178.0], "format": {"format": "RGBA8_Unorm_sRGB"}}}], "settings": {"dx12": {"shaderModelCs": "cs_6_6"}}}