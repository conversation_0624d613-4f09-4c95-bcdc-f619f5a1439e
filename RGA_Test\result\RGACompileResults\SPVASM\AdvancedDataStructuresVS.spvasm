; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 262
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TANGENT %in_var_BITANGENT %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_COLOR0 %in_var_BLENDWEIGHT %in_var_BLENDINDICES %gl_InstanceIndex %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6 %out_var_TEXCOORD7 %out_var_TEXCOORD8 %out_var_TEXCOORD9 %out_var_TEXCOORD10 %out_var_TEXCOORD11
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "DeltaTime"
               OpMemberName %type_PerFrame 6 "FrameCount"
               OpMemberName %type_PerFrame 7 "ScreenResolution"
               OpName %PerFrame "PerFrame"
               OpName %type_PerObject "type.PerObject"
               OpMemberName %type_PerObject 0 "WorldMatrix"
               OpMemberName %type_PerObject 1 "NormalMatrix"
               OpMemberName %type_PerObject 2 "PrevWorldMatrix"
               OpMemberName %type_PerObject 3 "MaterialIndex"
               OpMemberName %type_PerObject 4 "BoundingBoxMin"
               OpMemberName %type_PerObject 5 "BoundingBoxMax"
               OpMemberName %type_PerObject 6 "LODLevel"
               OpName %PerObject "PerObject"
               OpName %type_StructuredBuffer_BoneTransform "type.StructuredBuffer.BoneTransform"
               OpName %BoneTransform "BoneTransform"
               OpMemberName %BoneTransform 0 "Transform"
               OpMemberName %BoneTransform 1 "InverseBindPose"
               OpName %BoneTransforms "BoneTransforms"
               OpName %type_StructuredBuffer_v4float "type.StructuredBuffer.v4float"
               OpName %InstanceTransforms "InstanceTransforms"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_BITANGENT "in.var.BITANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %in_var_BLENDWEIGHT "in.var.BLENDWEIGHT"
               OpName %in_var_BLENDINDICES "in.var.BLENDINDICES"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %out_var_TEXCOORD7 "out.var.TEXCOORD7"
               OpName %out_var_TEXCOORD8 "out.var.TEXCOORD8"
               OpName %out_var_TEXCOORD9 "out.var.TEXCOORD9"
               OpName %out_var_TEXCOORD10 "out.var.TEXCOORD10"
               OpName %out_var_TEXCOORD11 "out.var.TEXCOORD11"
               OpName %main "main"
               OpDecorate %gl_InstanceIndex BuiltIn InstanceIndex
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %out_var_TEXCOORD10 Flat
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TANGENT Location 2
               OpDecorate %in_var_BITANGENT Location 3
               OpDecorate %in_var_TEXCOORD0 Location 4
               OpDecorate %in_var_TEXCOORD1 Location 5
               OpDecorate %in_var_COLOR0 Location 6
               OpDecorate %in_var_BLENDWEIGHT Location 7
               OpDecorate %in_var_BLENDINDICES Location 8
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %out_var_TEXCOORD7 Location 7
               OpDecorate %out_var_TEXCOORD8 Location 8
               OpDecorate %out_var_TEXCOORD9 Location 9
               OpDecorate %out_var_TEXCOORD10 Location 10
               OpDecorate %out_var_TEXCOORD11 Location 11
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %PerObject DescriptorSet 0
               OpDecorate %PerObject Binding 1
               OpDecorate %BoneTransforms DescriptorSet 0
               OpDecorate %BoneTransforms Binding 1
               OpDecorate %InstanceTransforms DescriptorSet 0
               OpDecorate %InstanceTransforms Binding 3
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 212
               OpMemberDecorate %type_PerFrame 7 Offset 216
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_PerObject 0 Offset 0
               OpMemberDecorate %type_PerObject 0 MatrixStride 16
               OpMemberDecorate %type_PerObject 0 RowMajor
               OpMemberDecorate %type_PerObject 1 Offset 64
               OpMemberDecorate %type_PerObject 1 MatrixStride 16
               OpMemberDecorate %type_PerObject 1 RowMajor
               OpMemberDecorate %type_PerObject 2 Offset 128
               OpMemberDecorate %type_PerObject 2 MatrixStride 16
               OpMemberDecorate %type_PerObject 2 RowMajor
               OpMemberDecorate %type_PerObject 3 Offset 192
               OpMemberDecorate %type_PerObject 4 Offset 196
               OpMemberDecorate %type_PerObject 5 Offset 208
               OpMemberDecorate %type_PerObject 6 Offset 220
               OpDecorate %type_PerObject Block
               OpMemberDecorate %BoneTransform 0 Offset 0
               OpMemberDecorate %BoneTransform 0 MatrixStride 16
               OpMemberDecorate %BoneTransform 0 RowMajor
               OpMemberDecorate %BoneTransform 1 Offset 64
               OpMemberDecorate %BoneTransform 1 MatrixStride 16
               OpMemberDecorate %BoneTransform 1 RowMajor
               OpDecorate %_runtimearr_BoneTransform ArrayStride 128
               OpMemberDecorate %type_StructuredBuffer_BoneTransform 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_BoneTransform 0 NonWritable
               OpDecorate %type_StructuredBuffer_BoneTransform BufferBlock
               OpDecorate %_runtimearr_v4float ArrayStride 16
               OpMemberDecorate %type_StructuredBuffer_v4float 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_v4float 0 NonWritable
               OpDecorate %type_StructuredBuffer_v4float BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %uint = OpTypeInt 32 0
     %uint_4 = OpConstant %uint 4
     %uint_1 = OpConstant %uint 1
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
         %49 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
    %float_0 = OpConstant %float 0
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
         %53 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
%mat4v4float = OpTypeMatrix %v4float 4
         %55 = OpConstantComposite %mat4v4float %53 %53 %53 %53
    %v3float = OpTypeVector %float 3
         %57 = OpConstantComposite %v3float %float_0 %float_0 %float_0
   %float_50 = OpConstant %float 50
    %v2float = OpTypeVector %float 2
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %float %uint %v2float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_PerObject = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %uint %v3float %v3float %float
%_ptr_Uniform_type_PerObject = OpTypePointer Uniform %type_PerObject
%BoneTransform = OpTypeStruct %mat4v4float %mat4v4float
%_runtimearr_BoneTransform = OpTypeRuntimeArray %BoneTransform
%type_StructuredBuffer_BoneTransform = OpTypeStruct %_runtimearr_BoneTransform
%_ptr_Uniform_type_StructuredBuffer_BoneTransform = OpTypePointer Uniform %type_StructuredBuffer_BoneTransform
%_runtimearr_v4float = OpTypeRuntimeArray %v4float
%type_StructuredBuffer_v4float = OpTypeStruct %_runtimearr_v4float
%_ptr_Uniform_type_StructuredBuffer_v4float = OpTypePointer Uniform %type_StructuredBuffer_v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
     %v4uint = OpTypeVector %uint 4
%_ptr_Input_v4uint = OpTypePointer Input %v4uint
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_uint = OpTypePointer Output %uint
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %76 = OpTypeFunction %void
%_ptr_Function_v4float = OpTypePointer Function %v4float
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
       %bool = OpTypeBool
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Function_float = OpTypePointer Function %float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
  %PerObject = OpVariable %_ptr_Uniform_type_PerObject Uniform
%BoneTransforms = OpVariable %_ptr_Uniform_type_StructuredBuffer_BoneTransform Uniform
%InstanceTransforms = OpVariable %_ptr_Uniform_type_StructuredBuffer_v4float Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_BITANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%in_var_BLENDWEIGHT = OpVariable %_ptr_Input_v4float Input
%in_var_BLENDINDICES = OpVariable %_ptr_Input_v4uint Input
%gl_InstanceIndex = OpVariable %_ptr_Input_uint Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD7 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD8 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD9 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD10 = OpVariable %_ptr_Output_uint Output
%out_var_TEXCOORD11 = OpVariable %_ptr_Output_float Output
%float_0_00999999978 = OpConstant %float 0.00999999978
       %main = OpFunction %void None %76
         %86 = OpLabel
         %87 = OpVariable %_ptr_Function_v4float Function
         %88 = OpVariable %_ptr_Function_v4float Function
         %89 = OpVariable %_ptr_Function_v4float Function
         %90 = OpLoad %v3float %in_var_POSITION
         %91 = OpLoad %v3float %in_var_NORMAL
         %92 = OpLoad %v3float %in_var_TANGENT
         %93 = OpLoad %v3float %in_var_BITANGENT
         %94 = OpLoad %v2float %in_var_TEXCOORD0
         %95 = OpLoad %v2float %in_var_TEXCOORD1
         %96 = OpLoad %v4float %in_var_COLOR0
         %97 = OpLoad %v4float %in_var_BLENDWEIGHT
         %98 = OpLoad %uint %gl_InstanceIndex
         %99 = OpIMul %uint %98 %uint_4
        %100 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %99
        %101 = OpLoad %v4float %100
        %102 = OpIAdd %uint %99 %uint_1
        %103 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %102
        %104 = OpLoad %v4float %103
        %105 = OpIAdd %uint %99 %uint_2
        %106 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %105
        %107 = OpLoad %v4float %106
        %108 = OpIAdd %uint %99 %uint_3
        %109 = OpAccessChain %_ptr_Uniform_v4float %InstanceTransforms %int_0 %108
        %110 = OpLoad %v4float %109
        %111 = OpCompositeConstruct %mat4v4float %101 %104 %107 %110
        %112 = OpDot %float %97 %49
        %113 = OpFOrdGreaterThan %bool %112 %float_0
               OpSelectionMerge %114 None
               OpBranchConditional %113 %115 %114
        %115 = OpLabel
        %116 = OpCompositeConstruct %v4float %112 %112 %112 %112
        %117 = OpFDiv %v4float %97 %116
               OpStore %87 %117
               OpBranch %118
        %118 = OpLabel
        %119 = OpPhi %mat4v4float %55 %115 %120 %121
        %122 = OpPhi %int %int_0 %115 %123 %121
        %124 = OpSLessThan %bool %122 %int_4
               OpLoopMerge %125 %121 None
               OpBranchConditional %124 %126 %125
        %126 = OpLabel
        %127 = OpBitcast %uint %122
        %128 = OpAccessChain %_ptr_Function_float %87 %127
        %129 = OpLoad %float %128
        %130 = OpFOrdGreaterThan %bool %129 %float_0
               OpSelectionMerge %131 None
               OpBranchConditional %130 %132 %131
        %132 = OpLabel
        %133 = OpAccessChain %_ptr_Input_uint %in_var_BLENDINDICES %127
        %134 = OpLoad %uint %133
        %135 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %134 %int_0
        %136 = OpLoad %mat4v4float %135
        %137 = OpLoad %float %128
        %138 = OpMatrixTimesScalar %mat4v4float %136 %137
        %139 = OpCompositeExtract %v4float %119 0
        %140 = OpCompositeExtract %v4float %138 0
        %141 = OpFAdd %v4float %139 %140
        %142 = OpCompositeExtract %v4float %119 1
        %143 = OpCompositeExtract %v4float %138 1
        %144 = OpFAdd %v4float %142 %143
        %145 = OpCompositeExtract %v4float %119 2
        %146 = OpCompositeExtract %v4float %138 2
        %147 = OpFAdd %v4float %145 %146
        %148 = OpCompositeExtract %v4float %119 3
        %149 = OpCompositeExtract %v4float %138 3
        %150 = OpFAdd %v4float %148 %149
        %151 = OpCompositeConstruct %mat4v4float %141 %144 %147 %150
               OpBranch %131
        %131 = OpLabel
        %120 = OpPhi %mat4v4float %119 %126 %151 %132
               OpBranch %121
        %121 = OpLabel
        %123 = OpIAdd %int %122 %int_1
               OpBranch %118
        %125 = OpLabel
        %152 = OpCompositeExtract %float %90 0
        %153 = OpCompositeExtract %float %90 1
        %154 = OpCompositeExtract %float %90 2
        %155 = OpCompositeConstruct %v4float %152 %153 %154 %float_1
        %156 = OpMatrixTimesVector %v4float %119 %155
        %157 = OpVectorShuffle %v3float %156 %156 0 1 2
               OpStore %88 %117
               OpBranch %158
        %158 = OpLabel
        %159 = OpPhi %v3float %57 %125 %160 %161
        %162 = OpPhi %int %int_0 %125 %163 %161
        %164 = OpSLessThan %bool %162 %int_4
               OpLoopMerge %165 %161 None
               OpBranchConditional %164 %166 %165
        %166 = OpLabel
        %167 = OpBitcast %uint %162
        %168 = OpAccessChain %_ptr_Function_float %88 %167
        %169 = OpLoad %float %168
        %170 = OpFOrdGreaterThan %bool %169 %float_0
               OpSelectionMerge %171 None
               OpBranchConditional %170 %172 %171
        %172 = OpLabel
        %173 = OpAccessChain %_ptr_Input_uint %in_var_BLENDINDICES %167
        %174 = OpLoad %uint %173
        %175 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %174 %int_0
        %176 = OpLoad %mat4v4float %175
        %177 = OpCompositeExtract %v4float %176 0
        %178 = OpVectorShuffle %v3float %177 %177 0 1 2
        %179 = OpCompositeExtract %v4float %176 1
        %180 = OpVectorShuffle %v3float %179 %179 0 1 2
        %181 = OpCompositeExtract %v4float %176 2
        %182 = OpVectorShuffle %v3float %181 %181 0 1 2
        %183 = OpCompositeConstruct %mat3v3float %178 %180 %182
        %184 = OpMatrixTimesVector %v3float %183 %91
        %185 = OpLoad %float %168
        %186 = OpVectorTimesScalar %v3float %184 %185
        %187 = OpFAdd %v3float %159 %186
               OpBranch %171
        %171 = OpLabel
        %160 = OpPhi %v3float %159 %166 %187 %172
               OpBranch %161
        %161 = OpLabel
        %163 = OpIAdd %int %162 %int_1
               OpBranch %158
        %165 = OpLabel
               OpStore %89 %117
               OpBranch %188
        %188 = OpLabel
        %189 = OpPhi %v3float %57 %165 %190 %191
        %192 = OpPhi %int %int_0 %165 %193 %191
        %194 = OpSLessThan %bool %192 %int_4
               OpLoopMerge %195 %191 None
               OpBranchConditional %194 %196 %195
        %196 = OpLabel
        %197 = OpBitcast %uint %192
        %198 = OpAccessChain %_ptr_Function_float %89 %197
        %199 = OpLoad %float %198
        %200 = OpFOrdGreaterThan %bool %199 %float_0
               OpSelectionMerge %201 None
               OpBranchConditional %200 %202 %201
        %202 = OpLabel
        %203 = OpAccessChain %_ptr_Input_uint %in_var_BLENDINDICES %197
        %204 = OpLoad %uint %203
        %205 = OpAccessChain %_ptr_Uniform_mat4v4float %BoneTransforms %int_0 %204 %int_0
        %206 = OpLoad %mat4v4float %205
        %207 = OpCompositeExtract %v4float %206 0
        %208 = OpVectorShuffle %v3float %207 %207 0 1 2
        %209 = OpCompositeExtract %v4float %206 1
        %210 = OpVectorShuffle %v3float %209 %209 0 1 2
        %211 = OpCompositeExtract %v4float %206 2
        %212 = OpVectorShuffle %v3float %211 %211 0 1 2
        %213 = OpCompositeConstruct %mat3v3float %208 %210 %212
        %214 = OpMatrixTimesVector %v3float %213 %92
        %215 = OpLoad %float %198
        %216 = OpVectorTimesScalar %v3float %214 %215
        %217 = OpFAdd %v3float %189 %216
               OpBranch %201
        %201 = OpLabel
        %190 = OpPhi %v3float %189 %196 %217 %202
               OpBranch %191
        %191 = OpLabel
        %193 = OpIAdd %int %192 %int_1
               OpBranch %188
        %195 = OpLabel
               OpBranch %114
        %114 = OpLabel
        %218 = OpPhi %v3float %92 %86 %189 %195
        %219 = OpPhi %v3float %91 %86 %159 %195
        %220 = OpPhi %v3float %90 %86 %157 %195
        %221 = OpCompositeExtract %float %220 0
        %222 = OpCompositeExtract %float %220 1
        %223 = OpCompositeExtract %float %220 2
        %224 = OpCompositeConstruct %v4float %221 %222 %223 %float_1
        %225 = OpMatrixTimesVector %v4float %111 %224
        %226 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_0
        %227 = OpLoad %mat4v4float %226
        %228 = OpMatrixTimesVector %v4float %227 %225
        %229 = OpVectorShuffle %v3float %228 %228 0 1 2
        %230 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_2
        %231 = OpLoad %mat4v4float %230
        %232 = OpMatrixTimesVector %v4float %231 %225
        %233 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
        %234 = OpLoad %mat4v4float %233
        %235 = OpMatrixTimesVector %v4float %234 %232
        %236 = OpMatrixTimesVector %v4float %234 %228
        %237 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_1
        %238 = OpLoad %mat4v4float %237
        %239 = OpCompositeExtract %v4float %238 0
        %240 = OpVectorShuffle %v3float %239 %239 0 1 2
        %241 = OpCompositeExtract %v4float %238 1
        %242 = OpVectorShuffle %v3float %241 %241 0 1 2
        %243 = OpCompositeExtract %v4float %238 2
        %244 = OpVectorShuffle %v3float %243 %243 0 1 2
        %245 = OpCompositeConstruct %mat3v3float %240 %242 %244
        %246 = OpMatrixTimesVector %v3float %245 %219
        %247 = OpExtInst %v3float %1 Normalize %246
        %248 = OpMatrixTimesVector %v3float %245 %218
        %249 = OpExtInst %v3float %1 Normalize %248
        %250 = OpMatrixTimesVector %v3float %245 %93
        %251 = OpExtInst %v3float %1 Normalize %250
        %252 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
        %253 = OpLoad %v3float %252
        %254 = OpFSub %v3float %253 %229
        %255 = OpExtInst %v3float %1 Normalize %254
        %256 = OpAccessChain %_ptr_Uniform_uint %PerObject %int_3
        %257 = OpLoad %uint %256
        %258 = OpExtInst %float %1 Length %254
        %259 = OpFMul %float %258 %float_0_00999999978
        %260 = OpFSub %float %float_50 %259
        %261 = OpExtInst %float %1 FClamp %260 %float_0 %float_1
               OpStore %gl_Position %236
               OpStore %out_var_TEXCOORD0 %229
               OpStore %out_var_TEXCOORD1 %247
               OpStore %out_var_TEXCOORD2 %249
               OpStore %out_var_TEXCOORD3 %251
               OpStore %out_var_TEXCOORD4 %94
               OpStore %out_var_TEXCOORD5 %95
               OpStore %out_var_TEXCOORD6 %96
               OpStore %out_var_TEXCOORD7 %255
               OpStore %out_var_TEXCOORD8 %235
               OpStore %out_var_TEXCOORD9 %236
               OpStore %out_var_TEXCOORD10 %257
               OpStore %out_var_TEXCOORD11 %261
               OpReturn
               OpFunctionEnd
