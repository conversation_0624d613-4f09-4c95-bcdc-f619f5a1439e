﻿=== HLSL Optimization Log ===
Start time: 07/31/2025 18:32:44

Processing: AdvancedDataStructuresPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: AdvancedDataStructuresVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: BasicLightingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: BasicLightingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: DeferredGBufferPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: DeferredGBufferVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: DeferredLightingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: DeferredLightingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: GeometryTessellationPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: GeometryTessellationVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: ImageProcessingCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: InstancedRenderingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: InstancedRenderingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: MeshGenerationCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: MeshViewerPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: MeshViewerVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: ParallelReductionCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: ParticleSimulationCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: ParticleUpdateCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: PbrShadingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: PbrShadingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: PostProcessingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: PostProcessingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: RayTracingTestCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: ShadowMappingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: ShadowMappingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: SimplePS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: SimpleVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: StressTestCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: TestShaderPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: TestShaderVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: TestSimpleCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: VolumeTextureCS.hlsl
  Optimization: SUCCESS (Stage: compute)

Processing: VolumetricRenderingPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: VolumetricRenderingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: WaterSurfacePS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: WaterSurfaceVS.hlsl
  Optimization: SUCCESS (Stage: vertex)


=== Optimization Statistics ===
Total files processed: 37
Successful optimizations: 37
Failed optimizations: 0
Success rate: 100%
End time: 07/31/2025 18:32:57
=== Optimization Complete ===
