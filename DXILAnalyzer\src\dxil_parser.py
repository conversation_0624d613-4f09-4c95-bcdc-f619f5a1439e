"""
DXIL Parser - Parses DXIL assembly and binary files to extract instructions.
"""

import re
import os
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass


@dataclass
class DXILInstruction:
    """Represents a single DXIL instruction."""
    line_number: int
    raw_line: str
    instruction_type: str
    operands: List[str]
    result_variable: Optional[str] = None
    data_type: Optional[str] = None
    operation: Optional[str] = None
    comment: Optional[str] = None


@dataclass
class DXILFunction:
    """Represents a DXIL function with its instructions."""
    name: str
    instructions: List[DXILInstruction]
    start_line: int
    end_line: int


class DXILParser:
    """Parser for DXIL assembly files."""
    
    # Regex patterns for different instruction types
    COMPUTATIONAL_OPS = {
        'fadd': 'float_add',
        'fsub': 'float_sub', 
        'fmul': 'float_mul',
        'fdiv': 'float_div',
        'frem': 'float_rem',
        'add': 'int_add',
        'sub': 'int_sub',
        'mul': 'int_mul',
        'udiv': 'uint_div',
        'sdiv': 'int_div',
        'urem': 'uint_rem',
        'srem': 'int_rem',
        'and': 'bitwise_and',
        'or': 'bitwise_or',
        'xor': 'bitwise_xor',
        'shl': 'shift_left',
        'lshr': 'logical_shift_right',
        'ashr': 'arithmetic_shift_right'
    }
    
    # DXIL intrinsic operations
    DXIL_INTRINSICS = {
        'dx.op.dot2.f32': 'dot2_float',
        'dx.op.dot3.f32': 'dot3_float', 
        'dx.op.dot4.f32': 'dot4_float',
        'dx.op.unary.f32': 'unary_float',
        'dx.op.binary.f32': 'binary_float',
        'dx.op.tertiary.f32': 'tertiary_float',
        'dx.op.loadInput.f32': 'load_input_float',
        'dx.op.storeOutput.f32': 'store_output_float',
        'dx.op.sample.f32': 'sample_float',
        'dx.op.cbufferLoadLegacy.f32': 'cbuffer_load_float'
    }
    
    def __init__(self):
        self.functions: List[DXILFunction] = []
        self.current_file: Optional[str] = None
        
    def parse_file(self, file_path: str) -> List[DXILFunction]:
        """Parse a DXIL assembly file and return extracted functions."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
            
        if file_path.suffix not in ['.asm', '.ll']:
            raise ValueError(f"Unsupported file type: {file_path.suffix}")
            
        self.current_file = str(file_path)
        self.functions = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        return self._parse_lines(lines)
    
    def _parse_lines(self, lines: List[str]) -> List[DXILFunction]:
        """Parse lines and extract functions with their instructions."""
        current_function = None
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # Skip empty lines and comments
            if not line or line.startswith(';'):
                continue
                
            # Check for function definition
            func_match = re.match(r'define\s+\w+\s+@(\w+)\s*\(', line)
            if func_match:
                if current_function:
                    current_function.end_line = line_num - 1
                    self.functions.append(current_function)
                    
                current_function = DXILFunction(
                    name=func_match.group(1),
                    instructions=[],
                    start_line=line_num,
                    end_line=line_num
                )
                continue
                
            # Check for function end
            if line == '}' and current_function:
                current_function.end_line = line_num
                self.functions.append(current_function)
                current_function = None
                continue
                
            # Parse instructions within functions
            if current_function:
                instruction = self._parse_instruction(line_num, line)
                if instruction:
                    current_function.instructions.append(instruction)
                    
        return self.functions

    def _parse_instruction(self, line_num: int, line: str) -> Optional[DXILInstruction]:
        """Parse a single instruction line."""
        # Remove inline comments
        comment = None
        if ';' in line:
            line, comment = line.split(';', 1)
            line = line.strip()
            comment = comment.strip()

        if not line:
            return None

        # Parse assignment instructions (e.g., %1 = fadd float %2, %3)
        assignment_match = re.match(r'(%\d+)\s*=\s*(.+)', line)
        if assignment_match:
            result_var = assignment_match.group(1)
            instruction_part = assignment_match.group(2).strip()

            return self._parse_instruction_part(line_num, line, instruction_part, result_var, comment)

        # Parse call instructions without assignment
        if line.startswith('call'):
            return self._parse_instruction_part(line_num, line, line, None, comment)

        # Parse other instructions (ret, br, etc.)
        parts = line.split()
        if parts:
            return DXILInstruction(
                line_number=line_num,
                raw_line=line,
                instruction_type=parts[0],
                operands=parts[1:],
                comment=comment
            )

        return None

    def _parse_instruction_part(self, line_num: int, raw_line: str, instruction_part: str,
                               result_var: Optional[str], comment: Optional[str]) -> Optional[DXILInstruction]:
        """Parse the instruction part of a line."""
        parts = instruction_part.split()
        if not parts:
            return None

        instruction_type = parts[0]
        operands = parts[1:] if len(parts) > 1 else []

        # Determine data type and operation
        data_type = self._extract_data_type(instruction_part)
        operation = self._classify_operation(instruction_type, instruction_part)

        return DXILInstruction(
            line_number=line_num,
            raw_line=raw_line,
            instruction_type=instruction_type,
            operands=operands,
            result_variable=result_var,
            data_type=data_type,
            operation=operation,
            comment=comment
        )

    def _extract_data_type(self, instruction: str) -> Optional[str]:
        """Extract data type from instruction."""
        # Look for explicit type annotations
        type_patterns = [
            r'\bf32\b',  # float32
            r'\bf64\b',  # float64
            r'\bi32\b',  # int32
            r'\bi64\b',  # int64
            r'\bi16\b',  # int16
            r'\bi8\b',   # int8
            r'\bi1\b',   # bool
            r'float',    # generic float
            r'double',   # double precision
        ]

        for pattern in type_patterns:
            if re.search(pattern, instruction):
                match = re.search(pattern, instruction)
                return match.group(0)

        return None

    def _classify_operation(self, instruction_type: str, full_instruction: str) -> Optional[str]:
        """Classify the operation type."""
        # Check computational operations
        if instruction_type in self.COMPUTATIONAL_OPS:
            return self.COMPUTATIONAL_OPS[instruction_type]

        # Check DXIL intrinsics
        for intrinsic, op_type in self.DXIL_INTRINSICS.items():
            if intrinsic in full_instruction:
                return op_type

        # Check for call instructions to DXIL operations
        if instruction_type == 'call':
            # Extract function name from call
            call_match = re.search(r'@([\w\.]+)', full_instruction)
            if call_match:
                func_name = call_match.group(1)
                if func_name in self.DXIL_INTRINSICS:
                    return self.DXIL_INTRINSICS[func_name]

        return None

    def get_computational_instructions(self) -> List[DXILInstruction]:
        """Get all computational instructions from parsed functions."""
        computational = []

        for function in self.functions:
            for instruction in function.instructions:
                if instruction.operation and not instruction.operation.startswith('load') and not instruction.operation.startswith('store'):
                    computational.append(instruction)

        return computational

    def get_statistics(self) -> Dict[str, Any]:
        """Get parsing statistics."""
        total_instructions = sum(len(func.instructions) for func in self.functions)
        computational_instructions = len(self.get_computational_instructions())

        return {
            'file': self.current_file,
            'functions': len(self.functions),
            'total_instructions': total_instructions,
            'computational_instructions': computational_instructions,
            'function_names': [func.name for func in self.functions]
        }
