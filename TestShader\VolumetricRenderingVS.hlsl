// Volumetric Rendering Vertex Shader
// Tests volume rendering and ray marching setup

cbuffer PerFrame : register(b0)
{
    float4x4 WorldMatrix;
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 InverseViewProjectionMatrix;
    float3 CameraPosition;
    float3 VolumeMin;
    float3 VolumeMax;
    float Time;
};

struct VSInput
{
    float3 Position : POSITION;
    float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float2 TexCoord : TEXCOORD0;
    float3 WorldPos : TEXCOORD1;
    float3 RayDirection : TEXCOORD2;
    float3 CameraPos : TEXCOORD3;
};

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Transform to world space
    float4 worldPos = mul(float4(input.Position, 1.0), WorldMatrix);
    output.WorldPos = worldPos.xyz;
    
    // Transform to clip space
    output.Position = mul(mul(worldPos, ViewMatrix), ProjectionMatrix);
    
    // Pass through texture coordinates
    output.TexCoord = input.TexCoord;
    
    // Calculate ray direction from camera to world position
    output.RayDirection = normalize(worldPos.xyz - CameraPosition);
    output.CameraPos = CameraPosition;
    
    return output;
}
