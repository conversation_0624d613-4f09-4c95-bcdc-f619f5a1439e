// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VSInput
{
  float3 Position : POSITION;
  float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
  float3 WorldPos : TEXCOORD1;
  float3 RayDirection : TEXCOORD2;
  float3 CameraPos : TEXCOORD3;
};

cbuffer PerFrame : register(b0)
{
  float4x4 WorldMatrix;
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 InverseViewProjectionMatrix;
  float3 CameraPosition;
  float3 VolumeMin;
  float3 VolumeMax;
  float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4 worldPos = mul(float4(input.Position, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  output.Position = mul(mul(worldPos, ViewMatrix), ProjectionMatrix);
  output.TexCoord = input.TexCoord;
  output.RayDirection = normalize(worldPos.xyz - CameraPosition);
  output.CameraPos = CameraPosition;
  return output;
}

