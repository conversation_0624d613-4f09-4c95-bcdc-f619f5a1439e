;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float     z 
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                11      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5     zw        5     NONE   float     zw
; TEXCOORD                 6   xyzw        6     NONE   float   xyz 
; TEXCOORD                 7   xyz         7     NONE   float   xyz 
; TEXCOORD                 8   xyzw        8     NONE   float   xy w
; TEXCOORD                 9   xyzw        9     NONE   float   xy w
; TEXCOORD                10   x          10     NONE    uint   x   
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
; SV_Target                1   xyzw        1   TARGET   float   xyzw
; SV_Target                2   xyzw        2   TARGET   float   xyzw
; SV_Target                3   xyzw        3   TARGET   float   xyzw
;
; shader hash: 5344afb0c277abfc156bc6d85aed14cd
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 13
; SigOutputElements: 4
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 11
; SigOutputVectors[0]: 4
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
; TEXCOORD                 8                 linear       
; TEXCOORD                 9                 linear       
; TEXCOORD                10        nointerpolation       
; TEXCOORD                11                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
; SV_Target                1                              
; SV_Target                2                              
; SV_Target                3                              
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 ViewMatrix;             ; Offset:    0
;       column_major float4x4 ProjectionMatrix;       ; Offset:   64
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:  128
;       float3 CameraPosition;                        ; Offset:  192
;       float Time;                                   ; Offset:  204
;       float DeltaTime;                              ; Offset:  208
;       uint FrameCount;                              ; Offset:  212
;       float2 ScreenResolution;                      ; Offset:  216
;   
;   } PerFrame;                                       ; Offset:    0 Size:   224
;
; }
;
; cbuffer LightingParams
; {
;
;   struct LightingParams
;   {
;
;       uint NumLights;                               ; Offset:    0
;       float AmbientIntensity;                       ; Offset:    4
;       float3 AmbientColor;                          ; Offset:   16
;       float ShadowBias;                             ; Offset:   28
;       float ShadowNormalBias;                       ; Offset:   32
;       float2 ShadowMapSize;                         ; Offset:   36
;       float PCFRadius;                              ; Offset:   44
;   
;   } LightingParams;                                 ; Offset:    0 Size:    48
;
; }
;
; Resource bind info for Materials
; {
;
;   struct struct.MaterialData
;   {
;
;       float4 Albedo;                                ; Offset:    0
;       float Metallic;                               ; Offset:   16
;       float Roughness;                              ; Offset:   20
;       float AO;                                     ; Offset:   24
;       float Emission;                               ; Offset:   28
;       float4 TilingOffset;                          ; Offset:   32
;       uint TextureFlags;                            ; Offset:   48
;       float _padding;                               ; Offset:   52
;   
;   } $Element;                                       ; Offset:    0 Size:    56
;
; }
;
; Resource bind info for Lights
; {
;
;   struct struct.LightData
;   {
;
;       float3 Position;                              ; Offset:    0
;       float Range;                                  ; Offset:   12
;       float3 Direction;                             ; Offset:   16
;       float SpotAngle;                              ; Offset:   28
;       float3 Color;                                 ; Offset:   32
;       float Intensity;                              ; Offset:   44
;       uint Type;                                    ; Offset:   48
;       float3 _padding;                              ; Offset:   52
;   
;   } $Element;                                       ; Offset:    0 Size:    64
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
; LightingParams                    cbuffer      NA          NA     CB1            cb1     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; ShadowSampler                     sampler      NA          NA      S1             s2     1
; Materials                         texture  struct         r/o      T0             t0     1
; Lights                            texture  struct         r/o      T1             t1     1
; AlbedoTextures                    texture     f32     2darray      T2             t2     1
; NormalTextures                    texture     f32     2darray      T3             t3     1
; MetallicRoughnessTextures         texture     f32     2darray      T4             t4     1
; AOTextures                        texture     f32     2darray      T5             t5     1
; EmissionTextures                  texture     f32     2darray      T6             t6     1
; LightmapTexture                   texture     f32          2d      T7             t7     1
; ShadowMap                         texture     f32          2d      T8             t9     1
;
;
; ViewId state:
;
; Number of inputs: 41, outputs: 16
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23, 24, 28, 29, 30, 40 }
;   output 1 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23, 25, 28, 29, 30, 40 }
;   output 2 depends on inputs: { 4, 5, 6, 7, 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23, 26, 28, 29, 30, 40 }
;   output 3 depends on inputs: { 40 }
;   output 4 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 40 }
;   output 5 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 40 }
;   output 6 depends on inputs: { 8, 9, 10, 12, 13, 14, 16, 17, 18, 20, 21, 40 }
;   output 7 depends on inputs: { 20, 21, 40 }
;   output 8 depends on inputs: { 32, 35, 36, 39 }
;   output 9 depends on inputs: { 33, 35, 37, 39 }
;   output 10 depends on inputs: { 2 }
;   output 12 depends on inputs: { 20, 21, 40 }
;   output 13 depends on inputs: { 20, 21, 40 }
;   output 14 depends on inputs: { 20, 21, 40 }
;   output 15 depends on inputs: { 40 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.ResRet.i32 = type { i32, i32, i32, i32, i32 }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.StructuredBuffer<MaterialData>" = type { %struct.MaterialData }
%struct.MaterialData = type { <4 x float>, float, float, float, float, <4 x float>, i32, float }
%"class.StructuredBuffer<LightData>" = type { %struct.LightData }
%struct.LightData = type { <3 x float>, float, <3 x float>, float, <3 x float>, float, i32, <3 x float> }
%"class.Texture2DArray<vector<float, 4> >" = type { <4 x float>, %"class.Texture2DArray<vector<float, 4> >::mips_type" }
%"class.Texture2DArray<vector<float, 4> >::mips_type" = type { i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%hostlayout.PerFrame = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, float, i32, <2 x float> }
%LightingParams = type { i32, float, <3 x float>, float, float, <2 x float>, float }
%struct.SamplerState = type { i32 }
%struct.SamplerComparisonState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 8, i32 9, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 7, i32 7, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 6, i32 6, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 5, i32 5, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 4, i32 4, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %9 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %10 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 1, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %11 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %12 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %13 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 12, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call i32 @dx.op.loadInput.i32(i32 4, i32 11, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 9, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 9, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 9, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %27 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %28 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %29 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %30 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %31 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %32 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %33 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %34 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %35 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %36 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %37 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %38 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %39 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %40 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %41 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %42 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %43 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %44 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %45 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 0)  ; BufferLoad(srv,index,wot)
  %46 = extractvalue %dx.types.ResRet.f32 %45, 0
  %47 = extractvalue %dx.types.ResRet.f32 %45, 1
  %48 = extractvalue %dx.types.ResRet.f32 %45, 2
  %49 = extractvalue %dx.types.ResRet.f32 %45, 3
  %50 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 16)  ; BufferLoad(srv,index,wot)
  %51 = extractvalue %dx.types.ResRet.f32 %50, 0
  %52 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 20)  ; BufferLoad(srv,index,wot)
  %53 = extractvalue %dx.types.ResRet.f32 %52, 0
  %54 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 24)  ; BufferLoad(srv,index,wot)
  %55 = extractvalue %dx.types.ResRet.f32 %54, 0
  %56 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 28)  ; BufferLoad(srv,index,wot)
  %57 = extractvalue %dx.types.ResRet.f32 %56, 0
  %58 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %9, i32 %15, i32 32)  ; BufferLoad(srv,index,wot)
  %59 = extractvalue %dx.types.ResRet.f32 %58, 0
  %60 = extractvalue %dx.types.ResRet.f32 %58, 1
  %61 = extractvalue %dx.types.ResRet.f32 %58, 2
  %62 = extractvalue %dx.types.ResRet.f32 %58, 3
  %63 = call %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32 68, %dx.types.Handle %9, i32 %15, i32 48)  ; BufferLoad(srv,index,wot)
  %64 = extractvalue %dx.types.ResRet.i32 %63, 0
  %65 = fmul fast float %59, %30
  %66 = fmul fast float %60, %31
  %67 = fadd fast float %65, %61
  %68 = fadd fast float %66, %62
  %69 = and i32 %64, 1
  %70 = icmp eq i32 %69, 0
  br i1 %70, label %80, label %71

; <label>:71                                      ; preds = %0
  %72 = uitofp i32 %15 to float
  %73 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %11, float %67, float %68, float %72, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %74 = extractvalue %dx.types.ResRet.f32 %73, 0
  %75 = extractvalue %dx.types.ResRet.f32 %73, 1
  %76 = extractvalue %dx.types.ResRet.f32 %73, 2
  %77 = fmul fast float %74, %46
  %78 = fmul fast float %75, %47
  %79 = fmul fast float %76, %48
  br label %80

; <label>:80                                      ; preds = %71, %0
  %81 = phi float [ %77, %71 ], [ %46, %0 ]
  %82 = phi float [ %78, %71 ], [ %47, %0 ]
  %83 = phi float [ %79, %71 ], [ %48, %0 ]
  %84 = fmul fast float %81, %25
  %85 = fmul fast float %82, %26
  %86 = fmul fast float %83, %27
  %87 = and i32 %64, 2
  %88 = icmp eq i32 %87, 0
  br i1 %88, label %115, label %89

; <label>:89                                      ; preds = %80
  %90 = uitofp i32 %15 to float
  %91 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %11, float %67, float %68, float %90, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %92 = extractvalue %dx.types.ResRet.f32 %91, 0
  %93 = extractvalue %dx.types.ResRet.f32 %91, 1
  %94 = extractvalue %dx.types.ResRet.f32 %91, 2
  %95 = fmul fast float %92, 2.000000e+00
  %96 = fmul fast float %93, 2.000000e+00
  %97 = fmul fast float %94, 2.000000e+00
  %98 = fadd fast float %95, -1.000000e+00
  %99 = fadd fast float %96, -1.000000e+00
  %100 = fadd fast float %97, -1.000000e+00
  %101 = fmul fast float %98, %35
  %102 = call float @dx.op.tertiary.f32(i32 46, float %99, float %32, float %101)  ; FMad(a,b,c)
  %103 = call float @dx.op.tertiary.f32(i32 46, float %100, float %38, float %102)  ; FMad(a,b,c)
  %104 = fmul fast float %98, %36
  %105 = call float @dx.op.tertiary.f32(i32 46, float %99, float %33, float %104)  ; FMad(a,b,c)
  %106 = call float @dx.op.tertiary.f32(i32 46, float %100, float %39, float %105)  ; FMad(a,b,c)
  %107 = fmul fast float %98, %37
  %108 = call float @dx.op.tertiary.f32(i32 46, float %99, float %34, float %107)  ; FMad(a,b,c)
  %109 = call float @dx.op.tertiary.f32(i32 46, float %100, float %40, float %108)  ; FMad(a,b,c)
  %110 = call float @dx.op.dot3.f32(i32 55, float %103, float %106, float %109, float %103, float %106, float %109)  ; Dot3(ax,ay,az,bx,by,bz)
  %111 = call float @dx.op.unary.f32(i32 25, float %110)  ; Rsqrt(value)
  %112 = fmul fast float %111, %103
  %113 = fmul fast float %111, %106
  %114 = fmul fast float %111, %109
  br label %115

; <label>:115                                     ; preds = %89, %80
  %116 = phi float [ %112, %89 ], [ %38, %80 ]
  %117 = phi float [ %113, %89 ], [ %39, %80 ]
  %118 = phi float [ %114, %89 ], [ %40, %80 ]
  %119 = and i32 %64, 4
  %120 = icmp eq i32 %119, 0
  br i1 %120, label %128, label %121

; <label>:121                                     ; preds = %115
  %122 = uitofp i32 %15 to float
  %123 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %11, float %67, float %68, float %122, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %124 = extractvalue %dx.types.ResRet.f32 %123, 0
  %125 = extractvalue %dx.types.ResRet.f32 %123, 1
  %126 = fmul fast float %124, %51
  %127 = fmul fast float %125, %53
  br label %128

; <label>:128                                     ; preds = %121, %115
  %129 = phi float [ %126, %121 ], [ %51, %115 ]
  %130 = phi float [ %127, %121 ], [ %53, %115 ]
  %131 = and i32 %64, 8
  %132 = icmp eq i32 %131, 0
  br i1 %132, label %138, label %133

; <label>:133                                     ; preds = %128
  %134 = uitofp i32 %15 to float
  %135 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %11, float %67, float %68, float %134, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %136 = extractvalue %dx.types.ResRet.f32 %135, 0
  %137 = fmul fast float %136, %55
  br label %138

; <label>:138                                     ; preds = %133, %128
  %139 = phi float [ %137, %133 ], [ %55, %128 ]
  %140 = and i32 %64, 16
  %141 = icmp eq i32 %140, 0
  br i1 %141, label %151, label %142

; <label>:142                                     ; preds = %138
  %143 = uitofp i32 %15 to float
  %144 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %11, float %67, float %68, float %143, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %145 = extractvalue %dx.types.ResRet.f32 %144, 0
  %146 = extractvalue %dx.types.ResRet.f32 %144, 1
  %147 = extractvalue %dx.types.ResRet.f32 %144, 2
  %148 = fmul fast float %145, %57
  %149 = fmul fast float %146, %57
  %150 = fmul fast float %147, %57
  br label %151

; <label>:151                                     ; preds = %142, %138
  %152 = phi float [ %148, %142 ], [ 0.000000e+00, %138 ]
  %153 = phi float [ %149, %142 ], [ 0.000000e+00, %138 ]
  %154 = phi float [ %150, %142 ], [ 0.000000e+00, %138 ]
  %155 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %11, float %28, float %29, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %156 = extractvalue %dx.types.ResRet.f32 %155, 0
  %157 = extractvalue %dx.types.ResRet.f32 %155, 1
  %158 = extractvalue %dx.types.ResRet.f32 %155, 2
  %159 = call float @dx.op.dot3.f32(i32 55, float %22, float %23, float %24, float %22, float %23, float %24)  ; Dot3(ax,ay,az,bx,by,bz)
  %160 = call float @dx.op.unary.f32(i32 25, float %159)  ; Rsqrt(value)
  %161 = fmul fast float %160, %22
  %162 = fmul fast float %160, %23
  %163 = fmul fast float %160, %24
  %164 = fadd fast float %84, 0xBFA47AE140000000
  %165 = fadd fast float %85, 0xBFA47AE140000000
  %166 = fadd fast float %86, 0xBFA47AE140000000
  %167 = fmul fast float %129, %164
  %168 = fmul fast float %129, %165
  %169 = fmul fast float %129, %166
  %170 = fadd fast float %167, 0x3FA47AE140000000
  %171 = fadd fast float %168, 0x3FA47AE140000000
  %172 = fadd fast float %169, 0x3FA47AE140000000
  %173 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %12, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %174 = extractvalue %dx.types.CBufRet.i32 %173, 0
  %175 = icmp eq i32 %174, 0
  br i1 %175, label %465, label %176

; <label>:176                                     ; preds = %151
  br label %177

; <label>:177                                     ; preds = %378, %176
  %178 = phi float [ %457, %378 ], [ 0.000000e+00, %176 ]
  %179 = phi float [ %458, %378 ], [ 0.000000e+00, %176 ]
  %180 = phi float [ %459, %378 ], [ 0.000000e+00, %176 ]
  %181 = phi i32 [ %460, %378 ], [ 0, %176 ]
  %182 = phi float [ %275, %378 ], [ undef, %176 ]
  %183 = phi float [ %276, %378 ], [ undef, %176 ]
  %184 = phi float [ %277, %378 ], [ undef, %176 ]
  %185 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %8, i32 %181, i32 0)  ; BufferLoad(srv,index,wot)
  %186 = extractvalue %dx.types.ResRet.f32 %185, 0
  %187 = extractvalue %dx.types.ResRet.f32 %185, 1
  %188 = extractvalue %dx.types.ResRet.f32 %185, 2
  %189 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %8, i32 %181, i32 16)  ; BufferLoad(srv,index,wot)
  %190 = extractvalue %dx.types.ResRet.f32 %189, 0
  %191 = extractvalue %dx.types.ResRet.f32 %189, 1
  %192 = extractvalue %dx.types.ResRet.f32 %189, 2
  %193 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %8, i32 %181, i32 28)  ; BufferLoad(srv,index,wot)
  %194 = extractvalue %dx.types.ResRet.f32 %193, 0
  %195 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %8, i32 %181, i32 32)  ; BufferLoad(srv,index,wot)
  %196 = extractvalue %dx.types.ResRet.f32 %195, 0
  %197 = extractvalue %dx.types.ResRet.f32 %195, 1
  %198 = extractvalue %dx.types.ResRet.f32 %195, 2
  %199 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %8, i32 %181, i32 44)  ; BufferLoad(srv,index,wot)
  %200 = extractvalue %dx.types.ResRet.f32 %199, 0
  %201 = call %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32 68, %dx.types.Handle %8, i32 %181, i32 48)  ; BufferLoad(srv,index,wot)
  %202 = extractvalue %dx.types.ResRet.i32 %201, 0
  %203 = icmp eq i32 %202, 0
  br i1 %203, label %204, label %213

; <label>:204                                     ; preds = %177
  %205 = fsub fast float -0.000000e+00, %190
  %206 = fsub fast float -0.000000e+00, %191
  %207 = fsub fast float -0.000000e+00, %192
  %208 = call float @dx.op.dot3.f32(i32 55, float %205, float %206, float %207, float %205, float %206, float %207)  ; Dot3(ax,ay,az,bx,by,bz)
  %209 = call float @dx.op.unary.f32(i32 25, float %208)  ; Rsqrt(value)
  %210 = fmul fast float %209, %205
  %211 = fmul fast float %209, %206
  %212 = fmul fast float %209, %207
  br label %274

; <label>:213                                     ; preds = %177
  %214 = icmp eq i32 %202, 1
  br i1 %214, label %215, label %235

; <label>:215                                     ; preds = %213
  %216 = fsub fast float %186, %41
  %217 = fsub fast float %187, %42
  %218 = fsub fast float %188, %43
  %219 = call float @dx.op.dot3.f32(i32 55, float %216, float %217, float %218, float %216, float %217, float %218)  ; Dot3(ax,ay,az,bx,by,bz)
  %220 = call float @dx.op.unary.f32(i32 25, float %219)  ; Rsqrt(value)
  %221 = fmul fast float %220, %216
  %222 = fmul fast float %220, %217
  %223 = fmul fast float %220, %218
  %224 = fmul fast float %216, %216
  %225 = fmul fast float %217, %217
  %226 = fadd fast float %224, %225
  %227 = fmul fast float %218, %218
  %228 = fadd fast float %226, %227
  %229 = call float @dx.op.unary.f32(i32 24, float %228)  ; Sqrt(value)
  %230 = fmul fast float %229, 0x3FA0624DE0000000
  %231 = fadd fast float %230, 0x3FB70A3D80000000
  %232 = fmul fast float %231, %229
  %233 = fadd fast float %232, 1.000000e+00
  %234 = fdiv fast float 1.000000e+00, %233
  br label %274

; <label>:235                                     ; preds = %213
  %236 = icmp eq i32 %202, 2
  br i1 %236, label %237, label %274

; <label>:237                                     ; preds = %235
  %238 = fsub fast float %186, %41
  %239 = fsub fast float %187, %42
  %240 = fsub fast float %188, %43
  %241 = call float @dx.op.dot3.f32(i32 55, float %238, float %239, float %240, float %238, float %239, float %240)  ; Dot3(ax,ay,az,bx,by,bz)
  %242 = call float @dx.op.unary.f32(i32 25, float %241)  ; Rsqrt(value)
  %243 = fmul fast float %242, %238
  %244 = fmul fast float %242, %239
  %245 = fmul fast float %242, %240
  %246 = fmul fast float %238, %238
  %247 = fmul fast float %239, %239
  %248 = fadd fast float %246, %247
  %249 = fmul fast float %240, %240
  %250 = fadd fast float %248, %249
  %251 = call float @dx.op.unary.f32(i32 24, float %250)  ; Sqrt(value)
  %252 = fsub fast float -0.000000e+00, %190
  %253 = fsub fast float -0.000000e+00, %191
  %254 = fsub fast float -0.000000e+00, %192
  %255 = call float @dx.op.dot3.f32(i32 55, float %252, float %253, float %254, float %252, float %253, float %254)  ; Dot3(ax,ay,az,bx,by,bz)
  %256 = call float @dx.op.unary.f32(i32 25, float %255)  ; Rsqrt(value)
  %257 = fmul fast float %256, %252
  %258 = fmul fast float %256, %253
  %259 = fmul fast float %256, %254
  %260 = call float @dx.op.dot3.f32(i32 55, float %243, float %244, float %245, float %257, float %258, float %259)  ; Dot3(ax,ay,az,bx,by,bz)
  %261 = call float @dx.op.unary.f32(i32 12, float %194)  ; Cos(value)
  %262 = fmul fast float %194, 0x3FF3333340000000
  %263 = call float @dx.op.unary.f32(i32 12, float %262)  ; Cos(value)
  %264 = fsub fast float %261, %263
  %265 = fsub fast float %260, %263
  %266 = fdiv fast float %265, %264
  %267 = call float @dx.op.binary.f32(i32 35, float %266, float 0.000000e+00)  ; FMax(a,b)
  %268 = call float @dx.op.binary.f32(i32 36, float %267, float 1.000000e+00)  ; FMin(a,b)
  %269 = fmul fast float %251, 0x3FA0624DE0000000
  %270 = fadd fast float %269, 0x3FB70A3D80000000
  %271 = fmul fast float %270, %251
  %272 = fadd fast float %271, 1.000000e+00
  %273 = fdiv fast float %268, %272
  br label %274

; <label>:274                                     ; preds = %237, %235, %215, %204
  %275 = phi float [ %210, %204 ], [ %221, %215 ], [ %243, %237 ], [ %182, %235 ]
  %276 = phi float [ %211, %204 ], [ %222, %215 ], [ %244, %237 ], [ %183, %235 ]
  %277 = phi float [ %212, %204 ], [ %223, %215 ], [ %245, %237 ], [ %184, %235 ]
  %278 = phi float [ 1.000000e+00, %204 ], [ %234, %215 ], [ %273, %237 ], [ 1.000000e+00, %235 ]
  %279 = fadd fast float %275, %161
  %280 = fadd fast float %276, %162
  %281 = fadd fast float %277, %163
  %282 = call float @dx.op.dot3.f32(i32 55, float %279, float %280, float %281, float %279, float %280, float %281)  ; Dot3(ax,ay,az,bx,by,bz)
  %283 = call float @dx.op.unary.f32(i32 25, float %282)  ; Rsqrt(value)
  %284 = fmul fast float %283, %279
  %285 = fmul fast float %283, %280
  %286 = fmul fast float %283, %281
  %287 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %13, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %288 = extractvalue %dx.types.CBufRet.f32 %287, 0
  %289 = extractvalue %dx.types.CBufRet.f32 %287, 1
  %290 = extractvalue %dx.types.CBufRet.f32 %287, 2
  %291 = extractvalue %dx.types.CBufRet.f32 %287, 3
  %292 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %13, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %293 = extractvalue %dx.types.CBufRet.f32 %292, 0
  %294 = extractvalue %dx.types.CBufRet.f32 %292, 1
  %295 = extractvalue %dx.types.CBufRet.f32 %292, 2
  %296 = extractvalue %dx.types.CBufRet.f32 %292, 3
  %297 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %13, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %298 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %13, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %299 = extractvalue %dx.types.CBufRet.f32 %298, 0
  %300 = extractvalue %dx.types.CBufRet.f32 %298, 1
  %301 = extractvalue %dx.types.CBufRet.f32 %298, 2
  %302 = extractvalue %dx.types.CBufRet.f32 %298, 3
  %303 = fmul fast float %288, %41
  %304 = call float @dx.op.tertiary.f32(i32 46, float %42, float %289, float %303)  ; FMad(a,b,c)
  %305 = call float @dx.op.tertiary.f32(i32 46, float %43, float %290, float %304)  ; FMad(a,b,c)
  %306 = fadd fast float %305, %291
  %307 = fmul fast float %293, %41
  %308 = call float @dx.op.tertiary.f32(i32 46, float %42, float %294, float %307)  ; FMad(a,b,c)
  %309 = call float @dx.op.tertiary.f32(i32 46, float %43, float %295, float %308)  ; FMad(a,b,c)
  %310 = fadd fast float %309, %296
  %311 = fmul fast float %299, %41
  %312 = call float @dx.op.tertiary.f32(i32 46, float %42, float %300, float %311)  ; FMad(a,b,c)
  %313 = call float @dx.op.tertiary.f32(i32 46, float %43, float %301, float %312)  ; FMad(a,b,c)
  %314 = fadd fast float %313, %302
  %315 = fdiv fast float %306, %314
  %316 = fdiv fast float %310, %314
  %317 = fmul fast float %315, 5.000000e-01
  %318 = fmul fast float %316, 5.000000e-01
  %319 = fadd fast float %317, 5.000000e-01
  %320 = fadd fast float %318, 5.000000e-01
  %321 = fcmp fast olt float %319, 0.000000e+00
  %322 = fcmp fast ogt float %319, 1.000000e+00
  %323 = or i1 %321, %322
  %324 = fcmp fast olt float %320, 0.000000e+00
  %325 = or i1 %323, %324
  %326 = fcmp fast ogt float %320, 1.000000e+00
  %327 = or i1 %326, %325
  %328 = call float @dx.op.dot3.f32(i32 55, float %116, float %117, float %118, float %275, float %276, float %277)  ; Dot3(ax,ay,az,bx,by,bz)
  br i1 %327, label %378, label %329

; <label>:329                                     ; preds = %274
  %330 = extractvalue %dx.types.CBufRet.f32 %297, 3
  %331 = extractvalue %dx.types.CBufRet.f32 %297, 2
  %332 = extractvalue %dx.types.CBufRet.f32 %297, 1
  %333 = extractvalue %dx.types.CBufRet.f32 %297, 0
  %334 = fmul fast float %333, %41
  %335 = call float @dx.op.tertiary.f32(i32 46, float %42, float %332, float %334)  ; FMad(a,b,c)
  %336 = call float @dx.op.tertiary.f32(i32 46, float %43, float %331, float %335)  ; FMad(a,b,c)
  %337 = fadd fast float %336, %330
  %338 = fdiv fast float %337, %314
  %339 = fmul fast float %338, 5.000000e-01
  %340 = fadd fast float %339, 5.000000e-01
  %341 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %342 = extractvalue %dx.types.CBufRet.f32 %341, 3
  %343 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %344 = extractvalue %dx.types.CBufRet.f32 %343, 0
  %345 = fsub fast float 1.000000e+00, %328
  %346 = fmul fast float %344, %345
  %347 = call float @dx.op.binary.f32(i32 35, float %346, float %342)  ; FMax(a,b)
  %348 = fsub fast float %340, %347
  %349 = extractvalue %dx.types.CBufRet.f32 %343, 1
  %350 = extractvalue %dx.types.CBufRet.f32 %343, 2
  %351 = fdiv fast float 1.000000e+00, %349
  %352 = fdiv fast float 1.000000e+00, %350
  br label %353

; <label>:353                                     ; preds = %353, %329
  %354 = phi float [ 0.000000e+00, %329 ], [ %373, %353 ]
  %355 = phi i32 [ -1, %329 ], [ %374, %353 ]
  %356 = sitofp i32 %355 to float
  %357 = fmul fast float %356, %351
  %358 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %359 = extractvalue %dx.types.CBufRet.f32 %358, 3
  %360 = fmul fast float %357, %359
  %361 = fmul fast float %359, %352
  %362 = fadd fast float %360, %319
  %363 = fsub fast float %320, %361
  %364 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %10, float %362, float %363, float undef, float undef, i32 0, i32 0, i32 undef, float %348)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %365 = extractvalue %dx.types.ResRet.f32 %364, 0
  %366 = fadd fast float %365, %354
  %367 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %10, float %362, float %320, float undef, float undef, i32 0, i32 0, i32 undef, float %348)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %368 = extractvalue %dx.types.ResRet.f32 %367, 0
  %369 = fadd fast float %366, %368
  %370 = fadd fast float %361, %320
  %371 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %10, float %362, float %370, float undef, float undef, i32 0, i32 0, i32 undef, float %348)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %372 = extractvalue %dx.types.ResRet.f32 %371, 0
  %373 = fadd fast float %369, %372
  %374 = add nsw i32 %355, 1
  %375 = icmp eq i32 %374, 2
  br i1 %375, label %376, label %353

; <label>:376                                     ; preds = %353
  %377 = fmul fast float %373, 0x3FBC71C720000000
  br label %378

; <label>:378                                     ; preds = %376, %274
  %379 = phi float [ %377, %376 ], [ 1.000000e+00, %274 ]
  %380 = fmul fast float %130, %130
  %381 = fmul fast float %380, %380
  %382 = call float @dx.op.dot3.f32(i32 55, float %116, float %117, float %118, float %284, float %285, float %286)  ; Dot3(ax,ay,az,bx,by,bz)
  %383 = call float @dx.op.binary.f32(i32 35, float %382, float 0.000000e+00)  ; FMax(a,b)
  %384 = fadd fast float %381, -1.000000e+00
  %385 = fmul fast float %383, %383
  %386 = fmul fast float %385, %384
  %387 = fadd fast float %386, 1.000000e+00
  %388 = fmul fast float %387, %387
  %389 = fmul fast float %388, 0x400921FB60000000
  %390 = fdiv fast float %381, %389
  %391 = call float @dx.op.dot3.f32(i32 55, float %116, float %117, float %118, float %161, float %162, float %163)  ; Dot3(ax,ay,az,bx,by,bz)
  %392 = call float @dx.op.binary.f32(i32 35, float %391, float 0.000000e+00)  ; FMax(a,b)
  %393 = call float @dx.op.binary.f32(i32 35, float %328, float 0.000000e+00)  ; FMax(a,b)
  %394 = fadd fast float %130, 1.000000e+00
  %395 = fmul fast float %394, %394
  %396 = fmul fast float %395, 1.250000e-01
  %397 = fsub fast float 1.000000e+00, %396
  %398 = fmul fast float %392, %397
  %399 = fadd fast float %398, %396
  %400 = fdiv fast float %392, %399
  %401 = fmul fast float %393, %397
  %402 = fadd fast float %401, %396
  %403 = fdiv fast float %393, %402
  %404 = call float @dx.op.dot3.f32(i32 55, float %284, float %285, float %286, float %161, float %162, float %163)  ; Dot3(ax,ay,az,bx,by,bz)
  %405 = call float @dx.op.binary.f32(i32 35, float %404, float 0.000000e+00)  ; FMax(a,b)
  %406 = fsub fast float 0x3FEEB851E0000000, %167
  %407 = fsub fast float 0x3FEEB851E0000000, %168
  %408 = fsub fast float 0x3FEEB851E0000000, %169
  %409 = fsub fast float 1.000000e+00, %405
  %410 = call float @dx.op.binary.f32(i32 35, float %409, float 0.000000e+00)  ; FMax(a,b)
  %411 = call float @dx.op.binary.f32(i32 36, float %410, float 1.000000e+00)  ; FMin(a,b)
  %412 = call float @dx.op.unary.f32(i32 23, float %411)  ; Log(value)
  %413 = fmul fast float %412, 5.000000e+00
  %414 = call float @dx.op.unary.f32(i32 21, float %413)  ; Exp(value)
  %415 = fmul fast float %414, %406
  %416 = fmul fast float %414, %407
  %417 = fmul fast float %414, %408
  %418 = fadd fast float %170, %415
  %419 = fadd fast float %171, %416
  %420 = fadd fast float %172, %417
  %421 = fsub fast float 1.000000e+00, %418
  %422 = fsub fast float 1.000000e+00, %419
  %423 = fsub fast float 1.000000e+00, %420
  %424 = fsub fast float 1.000000e+00, %129
  %425 = fmul fast float %400, %390
  %426 = fmul fast float %425, %403
  %427 = fmul fast float %418, %426
  %428 = fmul fast float %419, %426
  %429 = fmul fast float %420, %426
  %430 = fmul fast float %392, 4.000000e+00
  %431 = fmul fast float %430, %393
  %432 = fadd fast float %431, 0x3F1A36E2E0000000
  %433 = fdiv fast float %427, %432
  %434 = fdiv fast float %428, %432
  %435 = fdiv fast float %429, %432
  %436 = fmul fast float %424, 0x3FD45F3060000000
  %437 = fmul fast float %436, %84
  %438 = fmul fast float %437, %421
  %439 = fmul fast float %436, %85
  %440 = fmul fast float %439, %422
  %441 = fmul fast float %436, %86
  %442 = fmul fast float %441, %423
  %443 = fadd fast float %438, %433
  %444 = fadd fast float %440, %434
  %445 = fadd fast float %442, %435
  %446 = fmul fast float %278, %200
  %447 = fmul fast float %379, %446
  %448 = fmul fast float %447, %196
  %449 = fmul fast float %448, %393
  %450 = fmul fast float %449, %443
  %451 = fmul fast float %447, %197
  %452 = fmul fast float %451, %393
  %453 = fmul fast float %452, %444
  %454 = fmul fast float %447, %198
  %455 = fmul fast float %454, %393
  %456 = fmul fast float %455, %445
  %457 = fadd fast float %450, %178
  %458 = fadd fast float %453, %179
  %459 = fadd fast float %456, %180
  %460 = add i32 %181, 1
  %461 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %12, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %462 = extractvalue %dx.types.CBufRet.i32 %461, 0
  %463 = icmp ult i32 %460, %462
  br i1 %463, label %177, label %464

; <label>:464                                     ; preds = %378
  br label %465

; <label>:465                                     ; preds = %464, %151
  %466 = phi float [ 0.000000e+00, %151 ], [ %457, %464 ]
  %467 = phi float [ 0.000000e+00, %151 ], [ %458, %464 ]
  %468 = phi float [ 0.000000e+00, %151 ], [ %459, %464 ]
  %469 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %470 = extractvalue %dx.types.CBufRet.f32 %469, 0
  %471 = extractvalue %dx.types.CBufRet.f32 %469, 1
  %472 = extractvalue %dx.types.CBufRet.f32 %469, 2
  %473 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %474 = extractvalue %dx.types.CBufRet.f32 %473, 1
  %475 = fmul fast float %474, %139
  %476 = fmul fast float %475, %470
  %477 = fmul fast float %475, %471
  %478 = fmul fast float %475, %472
  %479 = fadd fast float %476, %156
  %480 = fmul fast float %479, %84
  %481 = fadd fast float %466, %152
  %482 = fadd fast float %481, %480
  %483 = fadd fast float %477, %157
  %484 = fmul fast float %483, %85
  %485 = fadd fast float %467, %153
  %486 = fadd fast float %485, %484
  %487 = fadd fast float %478, %158
  %488 = fmul fast float %487, %86
  %489 = fadd fast float %468, %154
  %490 = fadd fast float %489, %488
  %491 = fmul fast float %482, %14
  %492 = fmul fast float %486, %14
  %493 = fmul fast float %490, %14
  %494 = fdiv fast float %16, %18
  %495 = fdiv fast float %17, %18
  %496 = fdiv fast float %19, %21
  %497 = fdiv fast float %20, %21
  %498 = fsub fast float %494, %496
  %499 = fmul fast float %498, 5.000000e-01
  %500 = fsub fast float %495, %497
  %501 = fmul fast float %500, 5.000000e-01
  %502 = fmul fast float %116, 5.000000e-01
  %503 = fmul fast float %117, 5.000000e-01
  %504 = fmul fast float %118, 5.000000e-01
  %505 = fadd fast float %502, 5.000000e-01
  %506 = fadd fast float %503, 5.000000e-01
  %507 = fadd fast float %504, 5.000000e-01
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %491)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %492)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %493)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %49)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %505)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %506)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %507)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 3, float %130)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %499)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %501)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %44)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 3, float 1.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %129)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %130)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %139)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 3, float %57)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.loadInput.i32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32, %dx.types.Handle, i32, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!24}
!dx.entryPoints = !{!25}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !18, !21}
!5 = !{!6, !8, !10, !12, !13, !14, !15, !16, !17}
!6 = !{i32 0, %"class.StructuredBuffer<MaterialData>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i32 0, !7}
!7 = !{i32 1, i32 56}
!8 = !{i32 1, %"class.StructuredBuffer<LightData>"* undef, !"", i32 0, i32 1, i32 1, i32 12, i32 0, !9}
!9 = !{i32 1, i32 64}
!10 = !{i32 2, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 7, i32 0, !11}
!11 = !{i32 0, i32 9}
!12 = !{i32 3, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 7, i32 0, !11}
!13 = !{i32 4, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 4, i32 1, i32 7, i32 0, !11}
!14 = !{i32 5, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 5, i32 1, i32 7, i32 0, !11}
!15 = !{i32 6, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 6, i32 1, i32 7, i32 0, !11}
!16 = !{i32 7, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 7, i32 1, i32 2, i32 0, !11}
!17 = !{i32 8, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 9, i32 1, i32 2, i32 0, !11}
!18 = !{!19, !20}
!19 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 224, null}
!20 = !{i32 1, %LightingParams* undef, !"", i32 0, i32 1, i32 1, i32 48, null}
!21 = !{!22, !23}
!22 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!23 = !{i32 1, %struct.SamplerComparisonState* undef, !"", i32 0, i32 2, i32 1, i32 1, null}
!24 = !{[43 x i32] [i32 41, i32 16, i32 0, i32 0, i32 1024, i32 0, i32 7, i32 7, i32 7, i32 7, i32 119, i32 119, i32 119, i32 0, i32 119, i32 119, i32 119, i32 0, i32 119, i32 119, i32 119, i32 0, i32 28919, i32 28919, i32 7, i32 7, i32 1, i32 2, i32 4, i32 0, i32 7, i32 7, i32 7, i32 0, i32 256, i32 512, i32 0, i32 768, i32 256, i32 512, i32 0, i32 768, i32 61695]}
!25 = !{void ()* @main, !"main", !26, !4, !64}
!26 = !{!27, !58, null}
!27 = !{!28, !31, !33, !35, !37, !39, !42, !44, !46, !48, !51, !53, !56}
!28 = !{i32 0, !"SV_Position", i8 9, i8 3, !29, i8 4, i32 1, i8 4, i32 0, i8 0, !30}
!29 = !{i32 0}
!30 = !{i32 3, i32 4}
!31 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !29, i8 2, i32 1, i8 3, i32 1, i8 0, !32}
!32 = !{i32 3, i32 7}
!33 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !34, i8 2, i32 1, i8 3, i32 2, i8 0, !32}
!34 = !{i32 1}
!35 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !36, i8 2, i32 1, i8 3, i32 3, i8 0, !32}
!36 = !{i32 2}
!37 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !38, i8 2, i32 1, i8 3, i32 4, i8 0, !32}
!38 = !{i32 3}
!39 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !40, i8 2, i32 1, i8 2, i32 5, i8 0, !41}
!40 = !{i32 4}
!41 = !{i32 3, i32 3}
!42 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !43, i8 2, i32 1, i8 2, i32 5, i8 2, !41}
!43 = !{i32 5}
!44 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !45, i8 2, i32 1, i8 4, i32 6, i8 0, !32}
!45 = !{i32 6}
!46 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !47, i8 2, i32 1, i8 3, i32 7, i8 0, !32}
!47 = !{i32 7}
!48 = !{i32 9, !"TEXCOORD", i8 9, i8 0, !49, i8 2, i32 1, i8 4, i32 8, i8 0, !50}
!49 = !{i32 8}
!50 = !{i32 3, i32 11}
!51 = !{i32 10, !"TEXCOORD", i8 9, i8 0, !52, i8 2, i32 1, i8 4, i32 9, i8 0, !50}
!52 = !{i32 9}
!53 = !{i32 11, !"TEXCOORD", i8 5, i8 0, !54, i8 1, i32 1, i8 1, i32 10, i8 0, !55}
!54 = !{i32 10}
!55 = !{i32 3, i32 1}
!56 = !{i32 12, !"TEXCOORD", i8 9, i8 0, !57, i8 2, i32 1, i8 1, i32 1, i8 3, !55}
!57 = !{i32 11}
!58 = !{!59, !61, !62, !63}
!59 = !{i32 0, !"SV_Target", i8 9, i8 16, !29, i8 0, i32 1, i8 4, i32 0, i8 0, !60}
!60 = !{i32 3, i32 15}
!61 = !{i32 1, !"SV_Target", i8 9, i8 16, !34, i8 0, i32 1, i8 4, i32 1, i8 0, !60}
!62 = !{i32 2, !"SV_Target", i8 9, i8 16, !36, i8 0, i32 1, i8 4, i32 2, i8 0, !60}
!63 = !{i32 3, !"SV_Target", i8 9, i8 16, !38, i8 0, i32 1, i8 4, i32 3, i8 0, !60}
!64 = !{i32 0, i64 16}
