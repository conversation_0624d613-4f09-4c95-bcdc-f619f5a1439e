;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TANGENT                  0   xyz         2     NONE   float   xyz 
; BITANGENT                0   xyz         3     NONE   float   xyz 
; TEXCOORD                 0   xy          4     NONE   float   xy  
; TEXCOORD                 1   xy          5     NONE   float   xy  
; COLOR                    0   xyzw        6     NONE   float   xyzw
; BLENDWEIGHT              0   xyzw        7     NONE   float   xyzw
; BLENDINDICES             0   xyzw        8     NONE    uint   xyzw
; SV_InstanceID            0   x           9   INSTID    uint   x   
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                11      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5     zw        5     NONE   float     zw
; TEXCOORD                 6   xyzw        6     NONE   float   xyzw
; TEXCOORD                 7   xyz         7     NONE   float   xyz 
; TEXCOORD                 8   xyzw        8     NONE   float   xyzw
; TEXCOORD                 9   xyzw        9     NONE   float   xyzw
; TEXCOORD                10   x          10     NONE    uint   x   
;
; shader hash: 110fcd14b4490a97afd13d298ccd5b4c
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 10
; SigOutputElements: 13
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 10
; SigOutputVectors[0]: 11
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TANGENT                  0                              
; BITANGENT                0                              
; TEXCOORD                 0                              
; TEXCOORD                 1                              
; COLOR                    0                              
; BLENDWEIGHT              0                              
; BLENDINDICES             0                              
; SV_InstanceID            0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
; TEXCOORD                 8                 linear       
; TEXCOORD                 9                 linear       
; TEXCOORD                10        nointerpolation       
; TEXCOORD                11                 linear       
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 ViewMatrix;             ; Offset:    0
;       column_major float4x4 ProjectionMatrix;       ; Offset:   64
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:  128
;       float3 CameraPosition;                        ; Offset:  192
;       float Time;                                   ; Offset:  204
;       float DeltaTime;                              ; Offset:  208
;       uint FrameCount;                              ; Offset:  212
;       float2 ScreenResolution;                      ; Offset:  216
;   
;   } PerFrame;                                       ; Offset:    0 Size:   224
;
; }
;
; cbuffer PerObject
; {
;
;   struct hostlayout.PerObject
;   {
;
;       column_major float4x4 WorldMatrix;            ; Offset:    0
;       column_major float4x4 NormalMatrix;           ; Offset:   64
;       column_major float4x4 PrevWorldMatrix;        ; Offset:  128
;       uint MaterialIndex;                           ; Offset:  192
;       float3 BoundingBoxMin;                        ; Offset:  196
;       float3 BoundingBoxMax;                        ; Offset:  208
;       float LODLevel;                               ; Offset:  220
;   
;   } PerObject;                                      ; Offset:    0 Size:   224
;
; }
;
; Resource bind info for BoneTransforms
; {
;
;   struct hostlayout.struct.BoneTransform
;   {
;
;       column_major float4x4 Transform;              ; Offset:    0
;       column_major float4x4 InverseBindPose;        ; Offset:   64
;   
;   } $Element;                                       ; Offset:    0 Size:   128
;
; }
;
; Resource bind info for InstanceTransforms
; {
;
;   float4 $Element;                                  ; Offset:    0 Size:    16
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
; PerObject                         cbuffer      NA          NA     CB1            cb1     1
; BoneTransforms                    texture  struct         r/o      T0             t1     1
; InstanceTransforms                texture  struct         r/o      T1             t3     1
;
;
; ViewId state:
;
; Number of inputs: 37, outputs: 41
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 1 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 2 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 3 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 4 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 5 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 6 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 7 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 8 depends on inputs: { 4, 5, 6, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 9 depends on inputs: { 4, 5, 6, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 10 depends on inputs: { 4, 5, 6, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 12 depends on inputs: { 8, 9, 10, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 13 depends on inputs: { 8, 9, 10, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 14 depends on inputs: { 8, 9, 10, 28, 29, 30, 31, 32, 33, 34, 35 }
;   output 16 depends on inputs: { 12, 13, 14 }
;   output 17 depends on inputs: { 12, 13, 14 }
;   output 18 depends on inputs: { 12, 13, 14 }
;   output 20 depends on inputs: { 16 }
;   output 21 depends on inputs: { 17 }
;   output 22 depends on inputs: { 20 }
;   output 23 depends on inputs: { 21 }
;   output 24 depends on inputs: { 24 }
;   output 25 depends on inputs: { 25 }
;   output 26 depends on inputs: { 26 }
;   output 27 depends on inputs: { 27 }
;   output 28 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 29 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 30 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 32 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 33 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 34 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 35 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 36 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 37 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 38 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;   output 39 depends on inputs: { 0, 1, 2, 28, 29, 30, 31, 32, 33, 34, 35, 36 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%"hostlayout.class.StructuredBuffer<BoneTransform>" = type { %hostlayout.struct.BoneTransform }
%hostlayout.struct.BoneTransform = type { [4 x <4 x float>], [4 x <4 x float>] }
%"class.StructuredBuffer<vector<float, 4> >" = type { <4 x float> }
%hostlayout.PerFrame = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, float, i32, <2 x float> }
%hostlayout.PerObject = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], i32, <3 x float>, <3 x float>, float }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call i32 @dx.op.loadInput.i32(i32 4, i32 9, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call i32 @dx.op.loadInput.i32(i32 4, i32 8, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call i32 @dx.op.loadInput.i32(i32 4, i32 8, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call i32 @dx.op.loadInput.i32(i32 4, i32 8, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call i32 @dx.op.loadInput.i32(i32 4, i32 8, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %27 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %28 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %29 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %30 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %31 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %32 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %33 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %34 = shl i32 %5, 2
  %35 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %34, i32 0)  ; BufferLoad(srv,index,wot)
  %36 = extractvalue %dx.types.ResRet.f32 %35, 0
  %37 = extractvalue %dx.types.ResRet.f32 %35, 1
  %38 = extractvalue %dx.types.ResRet.f32 %35, 2
  %39 = extractvalue %dx.types.ResRet.f32 %35, 3
  %40 = or i32 %34, 1
  %41 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %40, i32 0)  ; BufferLoad(srv,index,wot)
  %42 = extractvalue %dx.types.ResRet.f32 %41, 0
  %43 = extractvalue %dx.types.ResRet.f32 %41, 1
  %44 = extractvalue %dx.types.ResRet.f32 %41, 2
  %45 = extractvalue %dx.types.ResRet.f32 %41, 3
  %46 = or i32 %34, 2
  %47 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %46, i32 0)  ; BufferLoad(srv,index,wot)
  %48 = extractvalue %dx.types.ResRet.f32 %47, 0
  %49 = extractvalue %dx.types.ResRet.f32 %47, 1
  %50 = extractvalue %dx.types.ResRet.f32 %47, 2
  %51 = extractvalue %dx.types.ResRet.f32 %47, 3
  %52 = or i32 %34, 3
  %53 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %52, i32 0)  ; BufferLoad(srv,index,wot)
  %54 = extractvalue %dx.types.ResRet.f32 %53, 0
  %55 = extractvalue %dx.types.ResRet.f32 %53, 1
  %56 = extractvalue %dx.types.ResRet.f32 %53, 2
  %57 = extractvalue %dx.types.ResRet.f32 %53, 3
  %58 = call float @dx.op.dot4.f32(i32 56, float %10, float %11, float %12, float %13, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00)  ; Dot4(ax,ay,az,aw,bx,by,bz,bw)
  %59 = fcmp fast ogt float %58, 0.000000e+00
  br i1 %59, label %60, label %527

; <label>:60                                      ; preds = %0
  %61 = fdiv fast float %10, %58
  %62 = fdiv fast float %11, %58
  %63 = fdiv fast float %12, %58
  %64 = fdiv fast float %13, %58
  %65 = fcmp fast ogt float %61, 0.000000e+00
  br i1 %65, label %66, label %94

; <label>:66                                      ; preds = %60
  %67 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 0)  ; BufferLoad(srv,index,wot)
  %68 = extractvalue %dx.types.ResRet.f32 %67, 0
  %69 = extractvalue %dx.types.ResRet.f32 %67, 1
  %70 = extractvalue %dx.types.ResRet.f32 %67, 2
  %71 = extractvalue %dx.types.ResRet.f32 %67, 3
  %72 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 16)  ; BufferLoad(srv,index,wot)
  %73 = extractvalue %dx.types.ResRet.f32 %72, 0
  %74 = extractvalue %dx.types.ResRet.f32 %72, 1
  %75 = extractvalue %dx.types.ResRet.f32 %72, 2
  %76 = extractvalue %dx.types.ResRet.f32 %72, 3
  %77 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 32)  ; BufferLoad(srv,index,wot)
  %78 = extractvalue %dx.types.ResRet.f32 %77, 0
  %79 = extractvalue %dx.types.ResRet.f32 %77, 1
  %80 = extractvalue %dx.types.ResRet.f32 %77, 2
  %81 = extractvalue %dx.types.ResRet.f32 %77, 3
  %82 = fmul fast float %68, %61
  %83 = fmul fast float %73, %61
  %84 = fmul fast float %78, %61
  %85 = fmul fast float %69, %61
  %86 = fmul fast float %74, %61
  %87 = fmul fast float %79, %61
  %88 = fmul fast float %70, %61
  %89 = fmul fast float %75, %61
  %90 = fmul fast float %80, %61
  %91 = fmul fast float %71, %61
  %92 = fmul fast float %76, %61
  %93 = fmul fast float %81, %61
  br label %94

; <label>:94                                      ; preds = %66, %60
  %95 = phi float [ %82, %66 ], [ 0.000000e+00, %60 ]
  %96 = phi float [ %85, %66 ], [ 0.000000e+00, %60 ]
  %97 = phi float [ %88, %66 ], [ 0.000000e+00, %60 ]
  %98 = phi float [ %91, %66 ], [ 0.000000e+00, %60 ]
  %99 = phi float [ %83, %66 ], [ 0.000000e+00, %60 ]
  %100 = phi float [ %86, %66 ], [ 0.000000e+00, %60 ]
  %101 = phi float [ %89, %66 ], [ 0.000000e+00, %60 ]
  %102 = phi float [ %92, %66 ], [ 0.000000e+00, %60 ]
  %103 = phi float [ %84, %66 ], [ 0.000000e+00, %60 ]
  %104 = phi float [ %87, %66 ], [ 0.000000e+00, %60 ]
  %105 = phi float [ %90, %66 ], [ 0.000000e+00, %60 ]
  %106 = phi float [ %93, %66 ], [ 0.000000e+00, %60 ]
  %107 = fcmp fast ogt float %62, 0.000000e+00
  br i1 %107, label %108, label %148

; <label>:108                                     ; preds = %94
  %109 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 0)  ; BufferLoad(srv,index,wot)
  %110 = extractvalue %dx.types.ResRet.f32 %109, 0
  %111 = extractvalue %dx.types.ResRet.f32 %109, 1
  %112 = extractvalue %dx.types.ResRet.f32 %109, 2
  %113 = extractvalue %dx.types.ResRet.f32 %109, 3
  %114 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 16)  ; BufferLoad(srv,index,wot)
  %115 = extractvalue %dx.types.ResRet.f32 %114, 0
  %116 = extractvalue %dx.types.ResRet.f32 %114, 1
  %117 = extractvalue %dx.types.ResRet.f32 %114, 2
  %118 = extractvalue %dx.types.ResRet.f32 %114, 3
  %119 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 32)  ; BufferLoad(srv,index,wot)
  %120 = extractvalue %dx.types.ResRet.f32 %119, 0
  %121 = extractvalue %dx.types.ResRet.f32 %119, 1
  %122 = extractvalue %dx.types.ResRet.f32 %119, 2
  %123 = extractvalue %dx.types.ResRet.f32 %119, 3
  %124 = fmul fast float %110, %62
  %125 = fmul fast float %115, %62
  %126 = fmul fast float %120, %62
  %127 = fmul fast float %111, %62
  %128 = fmul fast float %116, %62
  %129 = fmul fast float %121, %62
  %130 = fmul fast float %112, %62
  %131 = fmul fast float %117, %62
  %132 = fmul fast float %122, %62
  %133 = fmul fast float %113, %62
  %134 = fmul fast float %118, %62
  %135 = fmul fast float %123, %62
  %136 = fadd fast float %124, %95
  %137 = fadd fast float %125, %99
  %138 = fadd fast float %126, %103
  %139 = fadd fast float %127, %96
  %140 = fadd fast float %128, %100
  %141 = fadd fast float %129, %104
  %142 = fadd fast float %130, %97
  %143 = fadd fast float %131, %101
  %144 = fadd fast float %132, %105
  %145 = fadd fast float %133, %98
  %146 = fadd fast float %134, %102
  %147 = fadd fast float %135, %106
  br label %148

; <label>:148                                     ; preds = %108, %94
  %149 = phi float [ %136, %108 ], [ %95, %94 ]
  %150 = phi float [ %139, %108 ], [ %96, %94 ]
  %151 = phi float [ %142, %108 ], [ %97, %94 ]
  %152 = phi float [ %145, %108 ], [ %98, %94 ]
  %153 = phi float [ %137, %108 ], [ %99, %94 ]
  %154 = phi float [ %140, %108 ], [ %100, %94 ]
  %155 = phi float [ %143, %108 ], [ %101, %94 ]
  %156 = phi float [ %146, %108 ], [ %102, %94 ]
  %157 = phi float [ %138, %108 ], [ %103, %94 ]
  %158 = phi float [ %141, %108 ], [ %104, %94 ]
  %159 = phi float [ %144, %108 ], [ %105, %94 ]
  %160 = phi float [ %147, %108 ], [ %106, %94 ]
  %161 = fcmp fast ogt float %63, 0.000000e+00
  br i1 %161, label %162, label %202

; <label>:162                                     ; preds = %148
  %163 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 0)  ; BufferLoad(srv,index,wot)
  %164 = extractvalue %dx.types.ResRet.f32 %163, 0
  %165 = extractvalue %dx.types.ResRet.f32 %163, 1
  %166 = extractvalue %dx.types.ResRet.f32 %163, 2
  %167 = extractvalue %dx.types.ResRet.f32 %163, 3
  %168 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 16)  ; BufferLoad(srv,index,wot)
  %169 = extractvalue %dx.types.ResRet.f32 %168, 0
  %170 = extractvalue %dx.types.ResRet.f32 %168, 1
  %171 = extractvalue %dx.types.ResRet.f32 %168, 2
  %172 = extractvalue %dx.types.ResRet.f32 %168, 3
  %173 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 32)  ; BufferLoad(srv,index,wot)
  %174 = extractvalue %dx.types.ResRet.f32 %173, 0
  %175 = extractvalue %dx.types.ResRet.f32 %173, 1
  %176 = extractvalue %dx.types.ResRet.f32 %173, 2
  %177 = extractvalue %dx.types.ResRet.f32 %173, 3
  %178 = fmul fast float %164, %63
  %179 = fmul fast float %169, %63
  %180 = fmul fast float %174, %63
  %181 = fmul fast float %165, %63
  %182 = fmul fast float %170, %63
  %183 = fmul fast float %175, %63
  %184 = fmul fast float %166, %63
  %185 = fmul fast float %171, %63
  %186 = fmul fast float %176, %63
  %187 = fmul fast float %167, %63
  %188 = fmul fast float %172, %63
  %189 = fmul fast float %177, %63
  %190 = fadd fast float %178, %149
  %191 = fadd fast float %179, %153
  %192 = fadd fast float %180, %157
  %193 = fadd fast float %181, %150
  %194 = fadd fast float %182, %154
  %195 = fadd fast float %183, %158
  %196 = fadd fast float %184, %151
  %197 = fadd fast float %185, %155
  %198 = fadd fast float %186, %159
  %199 = fadd fast float %187, %152
  %200 = fadd fast float %188, %156
  %201 = fadd fast float %189, %160
  br label %202

; <label>:202                                     ; preds = %162, %148
  %203 = phi float [ %190, %162 ], [ %149, %148 ]
  %204 = phi float [ %193, %162 ], [ %150, %148 ]
  %205 = phi float [ %196, %162 ], [ %151, %148 ]
  %206 = phi float [ %199, %162 ], [ %152, %148 ]
  %207 = phi float [ %191, %162 ], [ %153, %148 ]
  %208 = phi float [ %194, %162 ], [ %154, %148 ]
  %209 = phi float [ %197, %162 ], [ %155, %148 ]
  %210 = phi float [ %200, %162 ], [ %156, %148 ]
  %211 = phi float [ %192, %162 ], [ %157, %148 ]
  %212 = phi float [ %195, %162 ], [ %158, %148 ]
  %213 = phi float [ %198, %162 ], [ %159, %148 ]
  %214 = phi float [ %201, %162 ], [ %160, %148 ]
  %215 = fcmp fast ogt float %64, 0.000000e+00
  br i1 %215, label %216, label %256

; <label>:216                                     ; preds = %202
  %217 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 0)  ; BufferLoad(srv,index,wot)
  %218 = extractvalue %dx.types.ResRet.f32 %217, 0
  %219 = extractvalue %dx.types.ResRet.f32 %217, 1
  %220 = extractvalue %dx.types.ResRet.f32 %217, 2
  %221 = extractvalue %dx.types.ResRet.f32 %217, 3
  %222 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 16)  ; BufferLoad(srv,index,wot)
  %223 = extractvalue %dx.types.ResRet.f32 %222, 0
  %224 = extractvalue %dx.types.ResRet.f32 %222, 1
  %225 = extractvalue %dx.types.ResRet.f32 %222, 2
  %226 = extractvalue %dx.types.ResRet.f32 %222, 3
  %227 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 32)  ; BufferLoad(srv,index,wot)
  %228 = extractvalue %dx.types.ResRet.f32 %227, 0
  %229 = extractvalue %dx.types.ResRet.f32 %227, 1
  %230 = extractvalue %dx.types.ResRet.f32 %227, 2
  %231 = extractvalue %dx.types.ResRet.f32 %227, 3
  %232 = fmul fast float %218, %64
  %233 = fmul fast float %223, %64
  %234 = fmul fast float %228, %64
  %235 = fmul fast float %219, %64
  %236 = fmul fast float %224, %64
  %237 = fmul fast float %229, %64
  %238 = fmul fast float %220, %64
  %239 = fmul fast float %225, %64
  %240 = fmul fast float %230, %64
  %241 = fmul fast float %221, %64
  %242 = fmul fast float %226, %64
  %243 = fmul fast float %231, %64
  %244 = fadd fast float %232, %203
  %245 = fadd fast float %233, %207
  %246 = fadd fast float %234, %211
  %247 = fadd fast float %235, %204
  %248 = fadd fast float %236, %208
  %249 = fadd fast float %237, %212
  %250 = fadd fast float %238, %205
  %251 = fadd fast float %239, %209
  %252 = fadd fast float %240, %213
  %253 = fadd fast float %241, %206
  %254 = fadd fast float %242, %210
  %255 = fadd fast float %243, %214
  br label %256

; <label>:256                                     ; preds = %216, %202
  %257 = phi float [ %244, %216 ], [ %203, %202 ]
  %258 = phi float [ %247, %216 ], [ %204, %202 ]
  %259 = phi float [ %250, %216 ], [ %205, %202 ]
  %260 = phi float [ %253, %216 ], [ %206, %202 ]
  %261 = phi float [ %245, %216 ], [ %207, %202 ]
  %262 = phi float [ %248, %216 ], [ %208, %202 ]
  %263 = phi float [ %251, %216 ], [ %209, %202 ]
  %264 = phi float [ %254, %216 ], [ %210, %202 ]
  %265 = phi float [ %246, %216 ], [ %211, %202 ]
  %266 = phi float [ %249, %216 ], [ %212, %202 ]
  %267 = phi float [ %252, %216 ], [ %213, %202 ]
  %268 = phi float [ %255, %216 ], [ %214, %202 ]
  %269 = fmul fast float %257, %31
  %270 = call float @dx.op.tertiary.f32(i32 46, float %32, float %258, float %269)  ; FMad(a,b,c)
  %271 = call float @dx.op.tertiary.f32(i32 46, float %33, float %259, float %270)  ; FMad(a,b,c)
  %272 = fadd fast float %271, %260
  %273 = fmul fast float %261, %31
  %274 = call float @dx.op.tertiary.f32(i32 46, float %32, float %262, float %273)  ; FMad(a,b,c)
  %275 = call float @dx.op.tertiary.f32(i32 46, float %33, float %263, float %274)  ; FMad(a,b,c)
  %276 = fadd fast float %275, %264
  %277 = fmul fast float %265, %31
  %278 = call float @dx.op.tertiary.f32(i32 46, float %32, float %266, float %277)  ; FMad(a,b,c)
  %279 = call float @dx.op.tertiary.f32(i32 46, float %33, float %267, float %278)  ; FMad(a,b,c)
  %280 = fadd fast float %279, %268
  br i1 %65, label %281, label %306

; <label>:281                                     ; preds = %256
  %282 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 0)  ; BufferLoad(srv,index,wot)
  %283 = extractvalue %dx.types.ResRet.f32 %282, 0
  %284 = extractvalue %dx.types.ResRet.f32 %282, 1
  %285 = extractvalue %dx.types.ResRet.f32 %282, 2
  %286 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 16)  ; BufferLoad(srv,index,wot)
  %287 = extractvalue %dx.types.ResRet.f32 %286, 0
  %288 = extractvalue %dx.types.ResRet.f32 %286, 1
  %289 = extractvalue %dx.types.ResRet.f32 %286, 2
  %290 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 32)  ; BufferLoad(srv,index,wot)
  %291 = extractvalue %dx.types.ResRet.f32 %290, 0
  %292 = extractvalue %dx.types.ResRet.f32 %290, 1
  %293 = extractvalue %dx.types.ResRet.f32 %290, 2
  %294 = fmul fast float %283, %28
  %295 = call float @dx.op.tertiary.f32(i32 46, float %29, float %284, float %294)  ; FMad(a,b,c)
  %296 = call float @dx.op.tertiary.f32(i32 46, float %30, float %285, float %295)  ; FMad(a,b,c)
  %297 = fmul fast float %287, %28
  %298 = call float @dx.op.tertiary.f32(i32 46, float %29, float %288, float %297)  ; FMad(a,b,c)
  %299 = call float @dx.op.tertiary.f32(i32 46, float %30, float %289, float %298)  ; FMad(a,b,c)
  %300 = fmul fast float %291, %28
  %301 = call float @dx.op.tertiary.f32(i32 46, float %29, float %292, float %300)  ; FMad(a,b,c)
  %302 = call float @dx.op.tertiary.f32(i32 46, float %30, float %293, float %301)  ; FMad(a,b,c)
  %303 = fmul fast float %296, %61
  %304 = fmul fast float %299, %61
  %305 = fmul fast float %302, %61
  br label %306

; <label>:306                                     ; preds = %281, %256
  %307 = phi float [ %303, %281 ], [ 0.000000e+00, %256 ]
  %308 = phi float [ %304, %281 ], [ 0.000000e+00, %256 ]
  %309 = phi float [ %305, %281 ], [ 0.000000e+00, %256 ]
  br i1 %107, label %310, label %338

; <label>:310                                     ; preds = %306
  %311 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 0)  ; BufferLoad(srv,index,wot)
  %312 = extractvalue %dx.types.ResRet.f32 %311, 0
  %313 = extractvalue %dx.types.ResRet.f32 %311, 1
  %314 = extractvalue %dx.types.ResRet.f32 %311, 2
  %315 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 16)  ; BufferLoad(srv,index,wot)
  %316 = extractvalue %dx.types.ResRet.f32 %315, 0
  %317 = extractvalue %dx.types.ResRet.f32 %315, 1
  %318 = extractvalue %dx.types.ResRet.f32 %315, 2
  %319 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 32)  ; BufferLoad(srv,index,wot)
  %320 = extractvalue %dx.types.ResRet.f32 %319, 0
  %321 = extractvalue %dx.types.ResRet.f32 %319, 1
  %322 = extractvalue %dx.types.ResRet.f32 %319, 2
  %323 = fmul fast float %312, %28
  %324 = call float @dx.op.tertiary.f32(i32 46, float %29, float %313, float %323)  ; FMad(a,b,c)
  %325 = call float @dx.op.tertiary.f32(i32 46, float %30, float %314, float %324)  ; FMad(a,b,c)
  %326 = fmul fast float %316, %28
  %327 = call float @dx.op.tertiary.f32(i32 46, float %29, float %317, float %326)  ; FMad(a,b,c)
  %328 = call float @dx.op.tertiary.f32(i32 46, float %30, float %318, float %327)  ; FMad(a,b,c)
  %329 = fmul fast float %320, %28
  %330 = call float @dx.op.tertiary.f32(i32 46, float %29, float %321, float %329)  ; FMad(a,b,c)
  %331 = call float @dx.op.tertiary.f32(i32 46, float %30, float %322, float %330)  ; FMad(a,b,c)
  %332 = fmul fast float %325, %62
  %333 = fmul fast float %328, %62
  %334 = fmul fast float %331, %62
  %335 = fadd fast float %332, %307
  %336 = fadd fast float %333, %308
  %337 = fadd fast float %334, %309
  br label %338

; <label>:338                                     ; preds = %310, %306
  %339 = phi float [ %335, %310 ], [ %307, %306 ]
  %340 = phi float [ %336, %310 ], [ %308, %306 ]
  %341 = phi float [ %337, %310 ], [ %309, %306 ]
  br i1 %161, label %342, label %370

; <label>:342                                     ; preds = %338
  %343 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 0)  ; BufferLoad(srv,index,wot)
  %344 = extractvalue %dx.types.ResRet.f32 %343, 0
  %345 = extractvalue %dx.types.ResRet.f32 %343, 1
  %346 = extractvalue %dx.types.ResRet.f32 %343, 2
  %347 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 16)  ; BufferLoad(srv,index,wot)
  %348 = extractvalue %dx.types.ResRet.f32 %347, 0
  %349 = extractvalue %dx.types.ResRet.f32 %347, 1
  %350 = extractvalue %dx.types.ResRet.f32 %347, 2
  %351 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 32)  ; BufferLoad(srv,index,wot)
  %352 = extractvalue %dx.types.ResRet.f32 %351, 0
  %353 = extractvalue %dx.types.ResRet.f32 %351, 1
  %354 = extractvalue %dx.types.ResRet.f32 %351, 2
  %355 = fmul fast float %344, %28
  %356 = call float @dx.op.tertiary.f32(i32 46, float %29, float %345, float %355)  ; FMad(a,b,c)
  %357 = call float @dx.op.tertiary.f32(i32 46, float %30, float %346, float %356)  ; FMad(a,b,c)
  %358 = fmul fast float %348, %28
  %359 = call float @dx.op.tertiary.f32(i32 46, float %29, float %349, float %358)  ; FMad(a,b,c)
  %360 = call float @dx.op.tertiary.f32(i32 46, float %30, float %350, float %359)  ; FMad(a,b,c)
  %361 = fmul fast float %352, %28
  %362 = call float @dx.op.tertiary.f32(i32 46, float %29, float %353, float %361)  ; FMad(a,b,c)
  %363 = call float @dx.op.tertiary.f32(i32 46, float %30, float %354, float %362)  ; FMad(a,b,c)
  %364 = fmul fast float %357, %63
  %365 = fmul fast float %360, %63
  %366 = fmul fast float %363, %63
  %367 = fadd fast float %364, %339
  %368 = fadd fast float %365, %340
  %369 = fadd fast float %366, %341
  br label %370

; <label>:370                                     ; preds = %342, %338
  %371 = phi float [ %367, %342 ], [ %339, %338 ]
  %372 = phi float [ %368, %342 ], [ %340, %338 ]
  %373 = phi float [ %369, %342 ], [ %341, %338 ]
  br i1 %215, label %374, label %402

; <label>:374                                     ; preds = %370
  %375 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 0)  ; BufferLoad(srv,index,wot)
  %376 = extractvalue %dx.types.ResRet.f32 %375, 0
  %377 = extractvalue %dx.types.ResRet.f32 %375, 1
  %378 = extractvalue %dx.types.ResRet.f32 %375, 2
  %379 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 16)  ; BufferLoad(srv,index,wot)
  %380 = extractvalue %dx.types.ResRet.f32 %379, 0
  %381 = extractvalue %dx.types.ResRet.f32 %379, 1
  %382 = extractvalue %dx.types.ResRet.f32 %379, 2
  %383 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 32)  ; BufferLoad(srv,index,wot)
  %384 = extractvalue %dx.types.ResRet.f32 %383, 0
  %385 = extractvalue %dx.types.ResRet.f32 %383, 1
  %386 = extractvalue %dx.types.ResRet.f32 %383, 2
  %387 = fmul fast float %376, %28
  %388 = call float @dx.op.tertiary.f32(i32 46, float %29, float %377, float %387)  ; FMad(a,b,c)
  %389 = call float @dx.op.tertiary.f32(i32 46, float %30, float %378, float %388)  ; FMad(a,b,c)
  %390 = fmul fast float %380, %28
  %391 = call float @dx.op.tertiary.f32(i32 46, float %29, float %381, float %390)  ; FMad(a,b,c)
  %392 = call float @dx.op.tertiary.f32(i32 46, float %30, float %382, float %391)  ; FMad(a,b,c)
  %393 = fmul fast float %384, %28
  %394 = call float @dx.op.tertiary.f32(i32 46, float %29, float %385, float %393)  ; FMad(a,b,c)
  %395 = call float @dx.op.tertiary.f32(i32 46, float %30, float %386, float %394)  ; FMad(a,b,c)
  %396 = fmul fast float %389, %64
  %397 = fmul fast float %392, %64
  %398 = fmul fast float %395, %64
  %399 = fadd fast float %396, %371
  %400 = fadd fast float %397, %372
  %401 = fadd fast float %398, %373
  br label %402

; <label>:402                                     ; preds = %374, %370
  %403 = phi float [ %399, %374 ], [ %371, %370 ]
  %404 = phi float [ %400, %374 ], [ %372, %370 ]
  %405 = phi float [ %401, %374 ], [ %373, %370 ]
  br i1 %65, label %406, label %431

; <label>:406                                     ; preds = %402
  %407 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 0)  ; BufferLoad(srv,index,wot)
  %408 = extractvalue %dx.types.ResRet.f32 %407, 0
  %409 = extractvalue %dx.types.ResRet.f32 %407, 1
  %410 = extractvalue %dx.types.ResRet.f32 %407, 2
  %411 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 16)  ; BufferLoad(srv,index,wot)
  %412 = extractvalue %dx.types.ResRet.f32 %411, 0
  %413 = extractvalue %dx.types.ResRet.f32 %411, 1
  %414 = extractvalue %dx.types.ResRet.f32 %411, 2
  %415 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %6, i32 32)  ; BufferLoad(srv,index,wot)
  %416 = extractvalue %dx.types.ResRet.f32 %415, 0
  %417 = extractvalue %dx.types.ResRet.f32 %415, 1
  %418 = extractvalue %dx.types.ResRet.f32 %415, 2
  %419 = fmul fast float %408, %25
  %420 = call float @dx.op.tertiary.f32(i32 46, float %26, float %409, float %419)  ; FMad(a,b,c)
  %421 = call float @dx.op.tertiary.f32(i32 46, float %27, float %410, float %420)  ; FMad(a,b,c)
  %422 = fmul fast float %412, %25
  %423 = call float @dx.op.tertiary.f32(i32 46, float %26, float %413, float %422)  ; FMad(a,b,c)
  %424 = call float @dx.op.tertiary.f32(i32 46, float %27, float %414, float %423)  ; FMad(a,b,c)
  %425 = fmul fast float %416, %25
  %426 = call float @dx.op.tertiary.f32(i32 46, float %26, float %417, float %425)  ; FMad(a,b,c)
  %427 = call float @dx.op.tertiary.f32(i32 46, float %27, float %418, float %426)  ; FMad(a,b,c)
  %428 = fmul fast float %421, %61
  %429 = fmul fast float %424, %61
  %430 = fmul fast float %427, %61
  br label %431

; <label>:431                                     ; preds = %406, %402
  %432 = phi float [ %428, %406 ], [ 0.000000e+00, %402 ]
  %433 = phi float [ %429, %406 ], [ 0.000000e+00, %402 ]
  %434 = phi float [ %430, %406 ], [ 0.000000e+00, %402 ]
  br i1 %107, label %435, label %463

; <label>:435                                     ; preds = %431
  %436 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 0)  ; BufferLoad(srv,index,wot)
  %437 = extractvalue %dx.types.ResRet.f32 %436, 0
  %438 = extractvalue %dx.types.ResRet.f32 %436, 1
  %439 = extractvalue %dx.types.ResRet.f32 %436, 2
  %440 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 16)  ; BufferLoad(srv,index,wot)
  %441 = extractvalue %dx.types.ResRet.f32 %440, 0
  %442 = extractvalue %dx.types.ResRet.f32 %440, 1
  %443 = extractvalue %dx.types.ResRet.f32 %440, 2
  %444 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %7, i32 32)  ; BufferLoad(srv,index,wot)
  %445 = extractvalue %dx.types.ResRet.f32 %444, 0
  %446 = extractvalue %dx.types.ResRet.f32 %444, 1
  %447 = extractvalue %dx.types.ResRet.f32 %444, 2
  %448 = fmul fast float %437, %25
  %449 = call float @dx.op.tertiary.f32(i32 46, float %26, float %438, float %448)  ; FMad(a,b,c)
  %450 = call float @dx.op.tertiary.f32(i32 46, float %27, float %439, float %449)  ; FMad(a,b,c)
  %451 = fmul fast float %441, %25
  %452 = call float @dx.op.tertiary.f32(i32 46, float %26, float %442, float %451)  ; FMad(a,b,c)
  %453 = call float @dx.op.tertiary.f32(i32 46, float %27, float %443, float %452)  ; FMad(a,b,c)
  %454 = fmul fast float %445, %25
  %455 = call float @dx.op.tertiary.f32(i32 46, float %26, float %446, float %454)  ; FMad(a,b,c)
  %456 = call float @dx.op.tertiary.f32(i32 46, float %27, float %447, float %455)  ; FMad(a,b,c)
  %457 = fmul fast float %450, %62
  %458 = fmul fast float %453, %62
  %459 = fmul fast float %456, %62
  %460 = fadd fast float %457, %432
  %461 = fadd fast float %458, %433
  %462 = fadd fast float %459, %434
  br label %463

; <label>:463                                     ; preds = %435, %431
  %464 = phi float [ %460, %435 ], [ %432, %431 ]
  %465 = phi float [ %461, %435 ], [ %433, %431 ]
  %466 = phi float [ %462, %435 ], [ %434, %431 ]
  br i1 %161, label %467, label %495

; <label>:467                                     ; preds = %463
  %468 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 0)  ; BufferLoad(srv,index,wot)
  %469 = extractvalue %dx.types.ResRet.f32 %468, 0
  %470 = extractvalue %dx.types.ResRet.f32 %468, 1
  %471 = extractvalue %dx.types.ResRet.f32 %468, 2
  %472 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 16)  ; BufferLoad(srv,index,wot)
  %473 = extractvalue %dx.types.ResRet.f32 %472, 0
  %474 = extractvalue %dx.types.ResRet.f32 %472, 1
  %475 = extractvalue %dx.types.ResRet.f32 %472, 2
  %476 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %8, i32 32)  ; BufferLoad(srv,index,wot)
  %477 = extractvalue %dx.types.ResRet.f32 %476, 0
  %478 = extractvalue %dx.types.ResRet.f32 %476, 1
  %479 = extractvalue %dx.types.ResRet.f32 %476, 2
  %480 = fmul fast float %469, %25
  %481 = call float @dx.op.tertiary.f32(i32 46, float %26, float %470, float %480)  ; FMad(a,b,c)
  %482 = call float @dx.op.tertiary.f32(i32 46, float %27, float %471, float %481)  ; FMad(a,b,c)
  %483 = fmul fast float %473, %25
  %484 = call float @dx.op.tertiary.f32(i32 46, float %26, float %474, float %483)  ; FMad(a,b,c)
  %485 = call float @dx.op.tertiary.f32(i32 46, float %27, float %475, float %484)  ; FMad(a,b,c)
  %486 = fmul fast float %477, %25
  %487 = call float @dx.op.tertiary.f32(i32 46, float %26, float %478, float %486)  ; FMad(a,b,c)
  %488 = call float @dx.op.tertiary.f32(i32 46, float %27, float %479, float %487)  ; FMad(a,b,c)
  %489 = fmul fast float %482, %63
  %490 = fmul fast float %485, %63
  %491 = fmul fast float %488, %63
  %492 = fadd fast float %489, %464
  %493 = fadd fast float %490, %465
  %494 = fadd fast float %491, %466
  br label %495

; <label>:495                                     ; preds = %467, %463
  %496 = phi float [ %492, %467 ], [ %464, %463 ]
  %497 = phi float [ %493, %467 ], [ %465, %463 ]
  %498 = phi float [ %494, %467 ], [ %466, %463 ]
  br i1 %215, label %499, label %527

; <label>:499                                     ; preds = %495
  %500 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 0)  ; BufferLoad(srv,index,wot)
  %501 = extractvalue %dx.types.ResRet.f32 %500, 0
  %502 = extractvalue %dx.types.ResRet.f32 %500, 1
  %503 = extractvalue %dx.types.ResRet.f32 %500, 2
  %504 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 16)  ; BufferLoad(srv,index,wot)
  %505 = extractvalue %dx.types.ResRet.f32 %504, 0
  %506 = extractvalue %dx.types.ResRet.f32 %504, 1
  %507 = extractvalue %dx.types.ResRet.f32 %504, 2
  %508 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %9, i32 32)  ; BufferLoad(srv,index,wot)
  %509 = extractvalue %dx.types.ResRet.f32 %508, 0
  %510 = extractvalue %dx.types.ResRet.f32 %508, 1
  %511 = extractvalue %dx.types.ResRet.f32 %508, 2
  %512 = fmul fast float %501, %25
  %513 = call float @dx.op.tertiary.f32(i32 46, float %26, float %502, float %512)  ; FMad(a,b,c)
  %514 = call float @dx.op.tertiary.f32(i32 46, float %27, float %503, float %513)  ; FMad(a,b,c)
  %515 = fmul fast float %505, %25
  %516 = call float @dx.op.tertiary.f32(i32 46, float %26, float %506, float %515)  ; FMad(a,b,c)
  %517 = call float @dx.op.tertiary.f32(i32 46, float %27, float %507, float %516)  ; FMad(a,b,c)
  %518 = fmul fast float %509, %25
  %519 = call float @dx.op.tertiary.f32(i32 46, float %26, float %510, float %518)  ; FMad(a,b,c)
  %520 = call float @dx.op.tertiary.f32(i32 46, float %27, float %511, float %519)  ; FMad(a,b,c)
  %521 = fmul fast float %514, %64
  %522 = fmul fast float %517, %64
  %523 = fmul fast float %520, %64
  %524 = fadd fast float %521, %496
  %525 = fadd fast float %522, %497
  %526 = fadd fast float %523, %498
  br label %527

; <label>:527                                     ; preds = %499, %495, %0
  %528 = phi float [ %31, %0 ], [ %272, %495 ], [ %272, %499 ]
  %529 = phi float [ %32, %0 ], [ %276, %495 ], [ %276, %499 ]
  %530 = phi float [ %33, %0 ], [ %280, %495 ], [ %280, %499 ]
  %531 = phi float [ %28, %0 ], [ %403, %495 ], [ %403, %499 ]
  %532 = phi float [ %29, %0 ], [ %404, %495 ], [ %404, %499 ]
  %533 = phi float [ %30, %0 ], [ %405, %495 ], [ %405, %499 ]
  %534 = phi float [ %25, %0 ], [ %496, %495 ], [ %524, %499 ]
  %535 = phi float [ %26, %0 ], [ %497, %495 ], [ %525, %499 ]
  %536 = phi float [ %27, %0 ], [ %498, %495 ], [ %526, %499 ]
  %537 = fmul fast float %528, %36
  %538 = call float @dx.op.tertiary.f32(i32 46, float %529, float %42, float %537)  ; FMad(a,b,c)
  %539 = call float @dx.op.tertiary.f32(i32 46, float %530, float %48, float %538)  ; FMad(a,b,c)
  %540 = fadd fast float %539, %54
  %541 = fmul fast float %528, %37
  %542 = call float @dx.op.tertiary.f32(i32 46, float %529, float %43, float %541)  ; FMad(a,b,c)
  %543 = call float @dx.op.tertiary.f32(i32 46, float %530, float %49, float %542)  ; FMad(a,b,c)
  %544 = fadd fast float %543, %55
  %545 = fmul fast float %528, %38
  %546 = call float @dx.op.tertiary.f32(i32 46, float %529, float %44, float %545)  ; FMad(a,b,c)
  %547 = call float @dx.op.tertiary.f32(i32 46, float %530, float %50, float %546)  ; FMad(a,b,c)
  %548 = fadd fast float %547, %56
  %549 = fmul fast float %528, %39
  %550 = call float @dx.op.tertiary.f32(i32 46, float %529, float %45, float %549)  ; FMad(a,b,c)
  %551 = call float @dx.op.tertiary.f32(i32 46, float %530, float %51, float %550)  ; FMad(a,b,c)
  %552 = fadd fast float %551, %57
  %553 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %554 = extractvalue %dx.types.CBufRet.f32 %553, 0
  %555 = extractvalue %dx.types.CBufRet.f32 %553, 1
  %556 = extractvalue %dx.types.CBufRet.f32 %553, 2
  %557 = extractvalue %dx.types.CBufRet.f32 %553, 3
  %558 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %559 = extractvalue %dx.types.CBufRet.f32 %558, 0
  %560 = extractvalue %dx.types.CBufRet.f32 %558, 1
  %561 = extractvalue %dx.types.CBufRet.f32 %558, 2
  %562 = extractvalue %dx.types.CBufRet.f32 %558, 3
  %563 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %564 = extractvalue %dx.types.CBufRet.f32 %563, 0
  %565 = extractvalue %dx.types.CBufRet.f32 %563, 1
  %566 = extractvalue %dx.types.CBufRet.f32 %563, 2
  %567 = extractvalue %dx.types.CBufRet.f32 %563, 3
  %568 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %569 = extractvalue %dx.types.CBufRet.f32 %568, 0
  %570 = extractvalue %dx.types.CBufRet.f32 %568, 1
  %571 = extractvalue %dx.types.CBufRet.f32 %568, 2
  %572 = extractvalue %dx.types.CBufRet.f32 %568, 3
  %573 = fmul fast float %554, %540
  %574 = call float @dx.op.tertiary.f32(i32 46, float %544, float %555, float %573)  ; FMad(a,b,c)
  %575 = call float @dx.op.tertiary.f32(i32 46, float %548, float %556, float %574)  ; FMad(a,b,c)
  %576 = call float @dx.op.tertiary.f32(i32 46, float %552, float %557, float %575)  ; FMad(a,b,c)
  %577 = fmul fast float %559, %540
  %578 = call float @dx.op.tertiary.f32(i32 46, float %544, float %560, float %577)  ; FMad(a,b,c)
  %579 = call float @dx.op.tertiary.f32(i32 46, float %548, float %561, float %578)  ; FMad(a,b,c)
  %580 = call float @dx.op.tertiary.f32(i32 46, float %552, float %562, float %579)  ; FMad(a,b,c)
  %581 = fmul fast float %564, %540
  %582 = call float @dx.op.tertiary.f32(i32 46, float %544, float %565, float %581)  ; FMad(a,b,c)
  %583 = call float @dx.op.tertiary.f32(i32 46, float %548, float %566, float %582)  ; FMad(a,b,c)
  %584 = call float @dx.op.tertiary.f32(i32 46, float %552, float %567, float %583)  ; FMad(a,b,c)
  %585 = fmul fast float %569, %540
  %586 = call float @dx.op.tertiary.f32(i32 46, float %544, float %570, float %585)  ; FMad(a,b,c)
  %587 = call float @dx.op.tertiary.f32(i32 46, float %548, float %571, float %586)  ; FMad(a,b,c)
  %588 = call float @dx.op.tertiary.f32(i32 46, float %552, float %572, float %587)  ; FMad(a,b,c)
  %589 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %590 = extractvalue %dx.types.CBufRet.f32 %589, 0
  %591 = extractvalue %dx.types.CBufRet.f32 %589, 1
  %592 = extractvalue %dx.types.CBufRet.f32 %589, 2
  %593 = extractvalue %dx.types.CBufRet.f32 %589, 3
  %594 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %595 = extractvalue %dx.types.CBufRet.f32 %594, 0
  %596 = extractvalue %dx.types.CBufRet.f32 %594, 1
  %597 = extractvalue %dx.types.CBufRet.f32 %594, 2
  %598 = extractvalue %dx.types.CBufRet.f32 %594, 3
  %599 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %600 = extractvalue %dx.types.CBufRet.f32 %599, 0
  %601 = extractvalue %dx.types.CBufRet.f32 %599, 1
  %602 = extractvalue %dx.types.CBufRet.f32 %599, 2
  %603 = extractvalue %dx.types.CBufRet.f32 %599, 3
  %604 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %605 = extractvalue %dx.types.CBufRet.f32 %604, 0
  %606 = extractvalue %dx.types.CBufRet.f32 %604, 1
  %607 = extractvalue %dx.types.CBufRet.f32 %604, 2
  %608 = extractvalue %dx.types.CBufRet.f32 %604, 3
  %609 = fmul fast float %590, %540
  %610 = call float @dx.op.tertiary.f32(i32 46, float %544, float %591, float %609)  ; FMad(a,b,c)
  %611 = call float @dx.op.tertiary.f32(i32 46, float %548, float %592, float %610)  ; FMad(a,b,c)
  %612 = call float @dx.op.tertiary.f32(i32 46, float %552, float %593, float %611)  ; FMad(a,b,c)
  %613 = fmul fast float %595, %540
  %614 = call float @dx.op.tertiary.f32(i32 46, float %544, float %596, float %613)  ; FMad(a,b,c)
  %615 = call float @dx.op.tertiary.f32(i32 46, float %548, float %597, float %614)  ; FMad(a,b,c)
  %616 = call float @dx.op.tertiary.f32(i32 46, float %552, float %598, float %615)  ; FMad(a,b,c)
  %617 = fmul fast float %600, %540
  %618 = call float @dx.op.tertiary.f32(i32 46, float %544, float %601, float %617)  ; FMad(a,b,c)
  %619 = call float @dx.op.tertiary.f32(i32 46, float %548, float %602, float %618)  ; FMad(a,b,c)
  %620 = call float @dx.op.tertiary.f32(i32 46, float %552, float %603, float %619)  ; FMad(a,b,c)
  %621 = fmul fast float %605, %540
  %622 = call float @dx.op.tertiary.f32(i32 46, float %544, float %606, float %621)  ; FMad(a,b,c)
  %623 = call float @dx.op.tertiary.f32(i32 46, float %548, float %607, float %622)  ; FMad(a,b,c)
  %624 = call float @dx.op.tertiary.f32(i32 46, float %552, float %608, float %623)  ; FMad(a,b,c)
  %625 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %626 = extractvalue %dx.types.CBufRet.f32 %625, 0
  %627 = extractvalue %dx.types.CBufRet.f32 %625, 1
  %628 = extractvalue %dx.types.CBufRet.f32 %625, 2
  %629 = extractvalue %dx.types.CBufRet.f32 %625, 3
  %630 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %631 = extractvalue %dx.types.CBufRet.f32 %630, 0
  %632 = extractvalue %dx.types.CBufRet.f32 %630, 1
  %633 = extractvalue %dx.types.CBufRet.f32 %630, 2
  %634 = extractvalue %dx.types.CBufRet.f32 %630, 3
  %635 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %636 = extractvalue %dx.types.CBufRet.f32 %635, 0
  %637 = extractvalue %dx.types.CBufRet.f32 %635, 1
  %638 = extractvalue %dx.types.CBufRet.f32 %635, 2
  %639 = extractvalue %dx.types.CBufRet.f32 %635, 3
  %640 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %641 = extractvalue %dx.types.CBufRet.f32 %640, 0
  %642 = extractvalue %dx.types.CBufRet.f32 %640, 1
  %643 = extractvalue %dx.types.CBufRet.f32 %640, 2
  %644 = extractvalue %dx.types.CBufRet.f32 %640, 3
  %645 = fmul fast float %626, %612
  %646 = call float @dx.op.tertiary.f32(i32 46, float %616, float %627, float %645)  ; FMad(a,b,c)
  %647 = call float @dx.op.tertiary.f32(i32 46, float %620, float %628, float %646)  ; FMad(a,b,c)
  %648 = call float @dx.op.tertiary.f32(i32 46, float %624, float %629, float %647)  ; FMad(a,b,c)
  %649 = fmul fast float %631, %612
  %650 = call float @dx.op.tertiary.f32(i32 46, float %616, float %632, float %649)  ; FMad(a,b,c)
  %651 = call float @dx.op.tertiary.f32(i32 46, float %620, float %633, float %650)  ; FMad(a,b,c)
  %652 = call float @dx.op.tertiary.f32(i32 46, float %624, float %634, float %651)  ; FMad(a,b,c)
  %653 = fmul fast float %636, %612
  %654 = call float @dx.op.tertiary.f32(i32 46, float %616, float %637, float %653)  ; FMad(a,b,c)
  %655 = call float @dx.op.tertiary.f32(i32 46, float %620, float %638, float %654)  ; FMad(a,b,c)
  %656 = call float @dx.op.tertiary.f32(i32 46, float %624, float %639, float %655)  ; FMad(a,b,c)
  %657 = fmul fast float %641, %612
  %658 = call float @dx.op.tertiary.f32(i32 46, float %616, float %642, float %657)  ; FMad(a,b,c)
  %659 = call float @dx.op.tertiary.f32(i32 46, float %620, float %643, float %658)  ; FMad(a,b,c)
  %660 = call float @dx.op.tertiary.f32(i32 46, float %624, float %644, float %659)  ; FMad(a,b,c)
  %661 = fmul fast float %626, %576
  %662 = call float @dx.op.tertiary.f32(i32 46, float %580, float %627, float %661)  ; FMad(a,b,c)
  %663 = call float @dx.op.tertiary.f32(i32 46, float %584, float %628, float %662)  ; FMad(a,b,c)
  %664 = call float @dx.op.tertiary.f32(i32 46, float %588, float %629, float %663)  ; FMad(a,b,c)
  %665 = fmul fast float %631, %576
  %666 = call float @dx.op.tertiary.f32(i32 46, float %580, float %632, float %665)  ; FMad(a,b,c)
  %667 = call float @dx.op.tertiary.f32(i32 46, float %584, float %633, float %666)  ; FMad(a,b,c)
  %668 = call float @dx.op.tertiary.f32(i32 46, float %588, float %634, float %667)  ; FMad(a,b,c)
  %669 = fmul fast float %636, %576
  %670 = call float @dx.op.tertiary.f32(i32 46, float %580, float %637, float %669)  ; FMad(a,b,c)
  %671 = call float @dx.op.tertiary.f32(i32 46, float %584, float %638, float %670)  ; FMad(a,b,c)
  %672 = call float @dx.op.tertiary.f32(i32 46, float %588, float %639, float %671)  ; FMad(a,b,c)
  %673 = fmul fast float %641, %576
  %674 = call float @dx.op.tertiary.f32(i32 46, float %580, float %642, float %673)  ; FMad(a,b,c)
  %675 = call float @dx.op.tertiary.f32(i32 46, float %584, float %643, float %674)  ; FMad(a,b,c)
  %676 = call float @dx.op.tertiary.f32(i32 46, float %588, float %644, float %675)  ; FMad(a,b,c)
  %677 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %678 = extractvalue %dx.types.CBufRet.f32 %677, 0
  %679 = extractvalue %dx.types.CBufRet.f32 %677, 1
  %680 = extractvalue %dx.types.CBufRet.f32 %677, 2
  %681 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %682 = extractvalue %dx.types.CBufRet.f32 %681, 0
  %683 = extractvalue %dx.types.CBufRet.f32 %681, 1
  %684 = extractvalue %dx.types.CBufRet.f32 %681, 2
  %685 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %686 = extractvalue %dx.types.CBufRet.f32 %685, 0
  %687 = extractvalue %dx.types.CBufRet.f32 %685, 1
  %688 = extractvalue %dx.types.CBufRet.f32 %685, 2
  %689 = fmul fast float %678, %531
  %690 = call float @dx.op.tertiary.f32(i32 46, float %532, float %679, float %689)  ; FMad(a,b,c)
  %691 = call float @dx.op.tertiary.f32(i32 46, float %533, float %680, float %690)  ; FMad(a,b,c)
  %692 = fmul fast float %682, %531
  %693 = call float @dx.op.tertiary.f32(i32 46, float %532, float %683, float %692)  ; FMad(a,b,c)
  %694 = call float @dx.op.tertiary.f32(i32 46, float %533, float %684, float %693)  ; FMad(a,b,c)
  %695 = fmul fast float %686, %531
  %696 = call float @dx.op.tertiary.f32(i32 46, float %532, float %687, float %695)  ; FMad(a,b,c)
  %697 = call float @dx.op.tertiary.f32(i32 46, float %533, float %688, float %696)  ; FMad(a,b,c)
  %698 = call float @dx.op.dot3.f32(i32 55, float %691, float %694, float %697, float %691, float %694, float %697)  ; Dot3(ax,ay,az,bx,by,bz)
  %699 = call float @dx.op.unary.f32(i32 25, float %698)  ; Rsqrt(value)
  %700 = fmul fast float %699, %691
  %701 = fmul fast float %699, %694
  %702 = fmul fast float %699, %697
  %703 = fmul fast float %678, %534
  %704 = call float @dx.op.tertiary.f32(i32 46, float %535, float %679, float %703)  ; FMad(a,b,c)
  %705 = call float @dx.op.tertiary.f32(i32 46, float %536, float %680, float %704)  ; FMad(a,b,c)
  %706 = fmul fast float %682, %534
  %707 = call float @dx.op.tertiary.f32(i32 46, float %535, float %683, float %706)  ; FMad(a,b,c)
  %708 = call float @dx.op.tertiary.f32(i32 46, float %536, float %684, float %707)  ; FMad(a,b,c)
  %709 = fmul fast float %686, %534
  %710 = call float @dx.op.tertiary.f32(i32 46, float %535, float %687, float %709)  ; FMad(a,b,c)
  %711 = call float @dx.op.tertiary.f32(i32 46, float %536, float %688, float %710)  ; FMad(a,b,c)
  %712 = call float @dx.op.dot3.f32(i32 55, float %705, float %708, float %711, float %705, float %708, float %711)  ; Dot3(ax,ay,az,bx,by,bz)
  %713 = call float @dx.op.unary.f32(i32 25, float %712)  ; Rsqrt(value)
  %714 = fmul fast float %713, %705
  %715 = fmul fast float %713, %708
  %716 = fmul fast float %713, %711
  %717 = fmul fast float %678, %22
  %718 = call float @dx.op.tertiary.f32(i32 46, float %23, float %679, float %717)  ; FMad(a,b,c)
  %719 = call float @dx.op.tertiary.f32(i32 46, float %24, float %680, float %718)  ; FMad(a,b,c)
  %720 = fmul fast float %682, %22
  %721 = call float @dx.op.tertiary.f32(i32 46, float %23, float %683, float %720)  ; FMad(a,b,c)
  %722 = call float @dx.op.tertiary.f32(i32 46, float %24, float %684, float %721)  ; FMad(a,b,c)
  %723 = fmul fast float %686, %22
  %724 = call float @dx.op.tertiary.f32(i32 46, float %23, float %687, float %723)  ; FMad(a,b,c)
  %725 = call float @dx.op.tertiary.f32(i32 46, float %24, float %688, float %724)  ; FMad(a,b,c)
  %726 = call float @dx.op.dot3.f32(i32 55, float %719, float %722, float %725, float %719, float %722, float %725)  ; Dot3(ax,ay,az,bx,by,bz)
  %727 = call float @dx.op.unary.f32(i32 25, float %726)  ; Rsqrt(value)
  %728 = fmul fast float %727, %719
  %729 = fmul fast float %727, %722
  %730 = fmul fast float %727, %725
  %731 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %732 = extractvalue %dx.types.CBufRet.f32 %731, 0
  %733 = extractvalue %dx.types.CBufRet.f32 %731, 1
  %734 = extractvalue %dx.types.CBufRet.f32 %731, 2
  %735 = fsub fast float %732, %576
  %736 = fsub fast float %733, %580
  %737 = fsub fast float %734, %584
  %738 = call float @dx.op.dot3.f32(i32 55, float %735, float %736, float %737, float %735, float %736, float %737)  ; Dot3(ax,ay,az,bx,by,bz)
  %739 = call float @dx.op.unary.f32(i32 25, float %738)  ; Rsqrt(value)
  %740 = fmul fast float %735, %739
  %741 = fmul fast float %736, %739
  %742 = fmul fast float %737, %739
  %743 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %3, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %744 = extractvalue %dx.types.CBufRet.i32 %743, 0
  %745 = fmul fast float %735, %735
  %746 = fmul fast float %736, %736
  %747 = fadd fast float %745, %746
  %748 = fmul fast float %737, %737
  %749 = fadd fast float %747, %748
  %750 = call float @dx.op.unary.f32(i32 24, float %749)  ; Sqrt(value)
  %751 = fsub fast float 1.000000e+02, %750
  %752 = fmul fast float %751, 0x3F947AE140000000
  %753 = call float @dx.op.unary.f32(i32 7, float %752)  ; Saturate(value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %664)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %668)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %672)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %676)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %576)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %580)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %584)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %700)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %701)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %702)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %714)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %715)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %716)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %728)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %729)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 2, float %730)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %20)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %21)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %18)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 1, float %19)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %14)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 1, float %15)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 2, float %16)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 3, float %17)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 0, float %740)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 1, float %741)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 8, i32 0, i8 2, float %742)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 9, i32 0, i8 0, float %648)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 9, i32 0, i8 1, float %652)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 9, i32 0, i8 2, float %656)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 9, i32 0, i8 3, float %660)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 10, i32 0, i8 0, float %664)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 10, i32 0, i8 1, float %668)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 10, i32 0, i8 2, float %672)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 10, i32 0, i8 3, float %676)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.i32(i32 5, i32 11, i32 0, i8 0, i32 %744)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 12, i32 0, i8 0, float %753)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.loadInput.i32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind
declare void @dx.op.storeOutput.i32(i32, i32, i32, i8, i32) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot4.f32(i32, float, float, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!13}
!dx.entryPoints = !{!14}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{!5, null, !10, null}
!5 = !{!6, !8}
!6 = !{i32 0, %"hostlayout.class.StructuredBuffer<BoneTransform>"* undef, !"", i32 0, i32 1, i32 1, i32 12, i32 0, !7}
!7 = !{i32 1, i32 128}
!8 = !{i32 1, %"class.StructuredBuffer<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 12, i32 0, !9}
!9 = !{i32 1, i32 16}
!10 = !{!11, !12}
!11 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 224, null}
!12 = !{i32 1, %hostlayout.PerObject* undef, !"", i32 0, i32 1, i32 1, i32 224, null}
!13 = !{[76 x i32] [i32 37, i32 41, i32 1879048447, i32 255, i32 1879048447, i32 255, i32 1879048447, i32 255, i32 0, i32 0, i32 1792, i32 0, i32 1792, i32 0, i32 1792, i32 0, i32 0, i32 0, i32 28672, i32 0, i32 28672, i32 0, i32 28672, i32 0, i32 0, i32 0, i32 458752, i32 0, i32 458752, i32 0, i32 458752, i32 0, i32 0, i32 0, i32 1048576, i32 0, i32 2097152, i32 0, i32 0, i32 0, i32 0, i32 0, i32 4194304, i32 0, i32 8388608, i32 0, i32 0, i32 0, i32 0, i32 0, i32 16777216, i32 0, i32 33554432, i32 0, i32 67108864, i32 0, i32 134217728, i32 0, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879078911, i32 255, i32 1879048447, i32 255]}
!14 = !{void ()* @main, !"main", !15, !4, !57}
!15 = !{!16, !33, null}
!16 = !{!17, !20, !21, !22, !23, !25, !27, !29, !30, !31}
!17 = !{i32 0, !"POSITION", i8 9, i8 0, !18, i8 0, i32 1, i8 3, i32 0, i8 0, !19}
!18 = !{i32 0}
!19 = !{i32 3, i32 7}
!20 = !{i32 1, !"NORMAL", i8 9, i8 0, !18, i8 0, i32 1, i8 3, i32 1, i8 0, !19}
!21 = !{i32 2, !"TANGENT", i8 9, i8 0, !18, i8 0, i32 1, i8 3, i32 2, i8 0, !19}
!22 = !{i32 3, !"BITANGENT", i8 9, i8 0, !18, i8 0, i32 1, i8 3, i32 3, i8 0, !19}
!23 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !18, i8 0, i32 1, i8 2, i32 4, i8 0, !24}
!24 = !{i32 3, i32 3}
!25 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !26, i8 0, i32 1, i8 2, i32 5, i8 0, !24}
!26 = !{i32 1}
!27 = !{i32 6, !"COLOR", i8 9, i8 0, !18, i8 0, i32 1, i8 4, i32 6, i8 0, !28}
!28 = !{i32 3, i32 15}
!29 = !{i32 7, !"BLENDWEIGHT", i8 9, i8 0, !18, i8 0, i32 1, i8 4, i32 7, i8 0, !28}
!30 = !{i32 8, !"BLENDINDICES", i8 5, i8 0, !18, i8 0, i32 1, i8 4, i32 8, i8 0, !28}
!31 = !{i32 9, !"SV_InstanceID", i8 5, i8 2, !18, i8 0, i32 1, i8 1, i32 9, i8 0, !32}
!32 = !{i32 3, i32 1}
!33 = !{!34, !35, !36, !37, !39, !41, !43, !45, !47, !49, !51, !53, !55}
!34 = !{i32 0, !"SV_Position", i8 9, i8 3, !18, i8 4, i32 1, i8 4, i32 0, i8 0, !28}
!35 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !18, i8 2, i32 1, i8 3, i32 1, i8 0, !19}
!36 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 2, i8 0, !19}
!37 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !38, i8 2, i32 1, i8 3, i32 3, i8 0, !19}
!38 = !{i32 2}
!39 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !40, i8 2, i32 1, i8 3, i32 4, i8 0, !19}
!40 = !{i32 3}
!41 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !42, i8 2, i32 1, i8 2, i32 5, i8 0, !24}
!42 = !{i32 4}
!43 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !44, i8 2, i32 1, i8 2, i32 5, i8 2, !24}
!44 = !{i32 5}
!45 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !46, i8 2, i32 1, i8 4, i32 6, i8 0, !28}
!46 = !{i32 6}
!47 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !48, i8 2, i32 1, i8 3, i32 7, i8 0, !19}
!48 = !{i32 7}
!49 = !{i32 9, !"TEXCOORD", i8 9, i8 0, !50, i8 2, i32 1, i8 4, i32 8, i8 0, !28}
!50 = !{i32 8}
!51 = !{i32 10, !"TEXCOORD", i8 9, i8 0, !52, i8 2, i32 1, i8 4, i32 9, i8 0, !28}
!52 = !{i32 9}
!53 = !{i32 11, !"TEXCOORD", i8 5, i8 0, !54, i8 1, i32 1, i8 1, i32 10, i8 0, !32}
!54 = !{i32 10}
!55 = !{i32 12, !"TEXCOORD", i8 9, i8 0, !56, i8 2, i32 1, i8 1, i32 1, i8 3, !32}
!56 = !{i32 11}
!57 = !{i32 0, i64 16}
