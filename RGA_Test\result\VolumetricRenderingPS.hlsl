// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
  float3 WorldPos : TEXCOORD1;
  float3 RayDirection : TEXCOORD2;
  float3 CameraPos : TEXCOORD3;
};

cbuffer VolumeParams : register(b0)
{
  float3 VolumeMin;
  float StepSize;
  float3 VolumeMax;
  int MaxSteps;
  float3 LightDirection;
  float Density;
  float3 LightColor;
  float Absorption;
  float3 ScatteringColor;
  float Scattering;
  float Time;
  float NoiseScale;
  float NoiseStrength;
}

Texture3D VolumeTexture : register(t0);
Texture3D NoiseTexture : register(t1);
SamplerState LinearSampler : register(s0);
bool rayBoxIntersection(float3 rayOrigin, float3 rayDir, float3 boxMin, float3 boxMax, out float tNear, out float tFar)
{
  float3 invDir = 1.0f / rayDir;
  float3 t1 = (boxMin - rayOrigin * invDir);
  float3 t2 = (boxMax - rayOrigin * invDir);
  float3 tMin = min(t1, t2);
  float3 tMax = max(t1, t2);
  tNear = max(max(tMin.x, tMin.y), tMin.z);
  tFar = min(min(tMax.x, tMax.y), tMax.z);
  return tFar > tNear && tFar > 0.0f;
}

float noise3D(float3 p)
{
  return NoiseTexture.SampleLevel(LinearSampler, p, 0).r;
}

float fbm(float3 p, int octaves)
{
  float value = 0.0f;
  float amplitude = 0.5f;
  float frequency = 1.0f;
  for (int i = 0; i < octaves; (i++))
  {
    value += (amplitude * noise3D((p * frequency)));
    amplitude *= 0.5f;
    frequency *= 2.0f;
  }
  return value;
}

float sampleVolume(float3 worldPos)
{
  float3 volumeCoord = worldPos - VolumeMin / VolumeMax - VolumeMin;
  float baseDensity = VolumeTexture.SampleLevel(LinearSampler, volumeCoord, 0).r;
  float3 noiseCoord = (worldPos * NoiseScale) + float3((Time * 0.1f), (Time * 0.05f), (Time * 0.08f));
  float noiseDensity = (fbm(noiseCoord, 4) * NoiseStrength);
  float finalDensity = (baseDensity + noiseDensity * Density);
  return max(0.0f, finalDensity);
}

float phaseFunction(float3 lightDir, float3 viewDir)
{
  float cosTheta = dot(lightDir, viewDir);
  float g = 0.3f;
  float g2 = (g * g);
  return 1.0f - g2 / ((4.0f * 3.14159265f) * pow(1.0f + g2 - ((2.0f * g) * cosTheta), 1.5f));
}

float4 main(PSInput input) : SV_TARGET
{
  float3 rayOrigin = input.CameraPos;
  float3 rayDir = normalize(input.RayDirection);
  float tNear;
  float tFar;
  if ((!rayBoxIntersection(rayOrigin, rayDir, VolumeMin, VolumeMax, tNear, tFar)))
  {
    discard;
  }
  tNear = max(0.0f, tNear);
  float3 color = float3(0, 0, 0);
  float transmittance = 1.0f;
  float t = tNear;
  for (int step = 0; step < MaxSteps && t < tFar; (step++))
  {
    float3 currentPos = rayOrigin + (rayDir * t);
    float density = sampleVolume(currentPos);
    if (density > 0.001f)
    {
      float3 lightDir = normalize((-LightDirection));
      float phase = phaseFunction(lightDir, (-rayDir));
      float3 scatteredLight = ((((LightColor * ScatteringColor) * density) * Scattering) * phase);
      float extinction = (density * Absorption + Scattering);
      float stepTransmittance = exp(((-extinction) * StepSize));
      color += ((scatteredLight * transmittance) * 1.0f - stepTransmittance) / extinction;
      transmittance *= stepTransmittance;
      if (transmittance < 0.01f)
        break;
    }
    t += StepSize;
  }
  float alpha = 1.0f - transmittance;
  return float4(color, alpha);
}

