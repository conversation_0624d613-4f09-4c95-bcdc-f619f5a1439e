// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float4 Color : TEXCOORD3;
  float3 ViewDir : TEXCOORD4;
  float InstanceType : TEXCOORD5;
  float Health : TEXCOORD6;
};

cbuffer Material : register(b0)
{
  float4 BaseColor;
  float Metallic;
  float Roughness;
  float3 LightDirection;
  float3 LightColor;
  float3 AmbientColor;
  float Time;
}

Texture2DArray DiffuseTextureArray : register(t0);
Texture2D NoiseTexture : register(t2);
SamplerState LinearSampler : register(s0);
float noise(float2 p)
{
  return NoiseTexture.Sample(LinearSampler, (p * 0.1f)).r;
}

float4 getMaterialProperties(float instanceType, float2 texCoord)
{
  float4 material;
  if (instanceType < 1.0f)
  {
    material = float4(0.1f, 0.8f, 0.0f, 1.0f);
  }
  else if (instanceType < 2.0f)
  {
    material = float4(0.0f, 0.9f, 0.0f, 1.0f);
  }
  else
  {
    material = float4(0.8f, 0.2f, 0.0f, 1.0f);
  }
  float noiseValue = noise(texCoord + instanceType);
  material.y += (noiseValue - 0.5f * 0.2f);
  return material;
}

float3 subsurfaceScattering(float3 normal, float3 lightDir, float3 viewDir, float3 color)
{
  float3 backLightDir = (-lightDir);
  float backScatter = max(0.0f, dot(backLightDir, normal));
  float viewScatter = max(0.0f, dot(viewDir, backLightDir));
  float scatterStrength = (pow(viewScatter, 4.0f) * backScatter);
  return ((color * scatterStrength) * 0.5f);
}

float3 applyWindEffect(float3 color, float3 worldPos, float instanceType)
{
  if (instanceType < 1.0f)
  {
    float windNoise = noise((worldPos.xz * 0.1f) + (Time * 0.1f));
    float windEffect = (sin((Time * 2.0f) + (worldPos.x * 0.1f)) * windNoise);
    color.g += (windEffect * 0.1f);
    color.b -= (windEffect * 0.05f);
  }
  return color;
}

float4 main(PSInput input) : SV_TARGET
{
  float3 normal = normalize(input.Normal);
  float3 lightDir = normalize((-LightDirection));
  float3 viewDir = normalize(input.ViewDir);
  int textureIndex = (int)input.InstanceType;
  float4 albedo = DiffuseTextureArray.Sample(LinearSampler, float3(input.TexCoord, textureIndex));
  albedo *= input.Color;
  float4 materialProps = getMaterialProperties(input.InstanceType, input.TexCoord);
  float metallic = materialProps.x;
  float roughness = materialProps.y;
  float NdotL = max(0.0f, dot(normal, lightDir));
  float3 diffuse = ((albedo.rgb * LightColor) * NdotL);
  float3 halfDir = normalize(lightDir + viewDir);
  float NdotH = max(0.0f, dot(normal, halfDir));
  float specularPower = lerp(32.0f, 256.0f, 1.0f - roughness);
  float3 specular = ((LightColor * pow(NdotH, specularPower)) * metallic);
  float3 ambient = (AmbientColor * albedo.rgb);
  float3 finalColor = ambient + diffuse + specular;
  if (input.InstanceType < 1.0f)
  {
    float3 sss = subsurfaceScattering(normal, lightDir, viewDir, albedo.rgb);
    finalColor += sss;
    finalColor = applyWindEffect(finalColor, input.WorldPos, input.InstanceType);
  }
  if (input.Health < 1.0f)
  {
    float healthFactor = input.Health;
    finalColor = lerp((finalColor * 0.5f), finalColor, healthFactor);
    if (input.Health < 0.3f)
    {
      finalColor.r += (0.3f - input.Health * 2.0f);
    }
  }
  float distance = length(input.WorldPos - float3(0, 0, 0));
  float fogFactor = exp(((-distance) * 0.01f));
  finalColor = lerp(float3(0.7f, 0.8f, 0.9f), finalColor, fogFactor);
  return float4(finalColor, albedo.a);
}

