{"$schema": "gigischema.json", "version": "1.0", "variables": [{"name": "CameraPos", "type": "Float3"}, {"name": "InvViewProjMtx", "type": "Float4x4"}, {"name": "AmbientColor", "type": "Float3", "dflt": "0.0,0.0,0.0"}, {"name": "SpecularColor", "type": "Float3", "dflt": "1.0,1.0,1.0"}, {"name": "<PERSON>pec<PERSON><PERSON><PERSON><PERSON>", "type": "Float", "dflt": "32.0"}], "shaders": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fileName": "MeshViewer.hlsl", "resources": [{"name": "Output", "access": "UAV", "type": "Texture"}, {"name": "Scene", "access": "RTScene", "type": "<PERSON><PERSON><PERSON>", "buffer": {"typeStruct": {"name": "VertexBuffer"}}}, {"name": "VertexBuffer", "access": "SRV", "type": "<PERSON><PERSON><PERSON>", "buffer": {"typeStruct": {"name": "VertexBuffer"}}}], "samplers": [{"name": "samLinear"}]}], "structs": [{"name": "VertexBuffer", "fields": [{"name": "Position ", "type": "Float3", "semantic": "Position"}, {"name": "Normal", "type": "Float3", "semantic": "Normal"}, {"name": "Color", "type": "Float3", "semantic": "Color"}, {"name": "Tangent", "type": "Float4", "semantic": "Tangent"}, {"name": "UV", "type": "Float2", "semantic": "UV"}, {"name": "MaterialID", "type": "Int", "semantic": "MaterialID"}, {"name": "ShapeID", "type": "Int", "semantic": "ShapeID"}]}], "nodes": [{"resourceTexture": {"name": "RenderTarget", "editorPos": [-41.0, -30.0], "format": {"format": "RGBA8_Unorm_sRGB"}, "size": {"multiply": [1920, 1080, 1]}}}, {"resourceBuffer": {"name": "Scene", "editorPos": [-37.0, 18.0], "visibility": "Imported"}}, {"actionComputeShader": {"name": "<PERSON><PERSON><PERSON>iew", "editorPos": [139.0, -30.0], "linkProperties": [{}, {}, {}, {}], "connections": [{"srcPin": "Output", "dstNode": "RenderTarget", "dstPin": "resource"}, {"srcPin": "Scene", "dstNode": "Scene", "dstPin": "resource"}, {"srcPin": "VertexBuffer", "dstNode": "Scene", "dstPin": "resource"}], "shader": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "dispatchSize": {"node": {"name": "RenderTarget"}}}}], "settings": {"dx12": {"shaderModelCs": "cs_6_6"}}}