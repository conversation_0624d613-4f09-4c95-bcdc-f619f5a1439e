struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float4 Color : TEXCOORD3;
  float4 LightSpacePos : TEXCOORD4;
  float3 LightDir : TEXCOORD5;
  float3 ViewDir : TEXCOORD6;
  float Depth : TEXCOORD7;
};

cbuffer PerFrame : register(b0)
{
  uniform float4x4 WorldMatrix;
  uniform float4x4 ViewMatrix;
  uniform float4x4 ProjectionMatrix;
  uniform float4x4 LightViewProjectionMatrix;
  uniform float3 LightPosition;
  uniform float3 CameraPosition;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4 worldPos = mul(float4(input.Position, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  float4 viewPos = mul(worldPos, ViewMatrix);
  output.Position = mul(viewPos, ProjectionMatrix);
  output.Normal = normalize(mul(input.Normal, (float3x3)WorldMatrix));
  output.TexCoord = input.TexCoord;
  output.Color = input.Color;
  output.LightSpacePos = mul(worldPos, LightViewProjectionMatrix);
  output.LightDir = normalize(LightPosition - output.WorldPos);
  output.ViewDir = normalize(CameraPosition - output.WorldPos);
  output.Depth = output.Position.z / output.Position.w;
  return output;
}

