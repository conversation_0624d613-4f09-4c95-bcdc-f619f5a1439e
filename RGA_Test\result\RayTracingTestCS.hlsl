// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct Ray
{
  float3 Origin;
  float3 Direction;
  float MinT;
  float MaxT;
};

struct HitInfo
{
  bool Hit;
  float T;
  float3 Position;
  float3 Normal;
  float2 UV;
  uint MaterialID;
  float3 Tangent;
  float3 Bitangent;
};

struct Sphere
{
  float3 Center;
  float Radius;
  uint MaterialID;
  float _padding;
};

struct Triangle
{
  float3 V0;
  float3 V1;
  float3 V2;
  float3 N0;
  float3 N1;
  float3 N2;
  float2 UV0;
  float2 UV1;
  float2 UV2;
  uint MaterialID;
  float3 _padding;
};

struct Material
{
  float3 Albedo;
  float Metallic;
  float3 Emission;
  float Roughness;
  float IOR;
  float Transparency;
  float2 _padding;
};

struct BVHNode
{
  float3 AABBMin;
  uint LeftChild;
  float3 AABBMax;
  uint RightChild;
  uint PrimitiveStart;
  uint PrimitiveCount;
  uint2 _padding;
};

cbuffer RayTracingParams : register(b0)
{
  float4x4 InverseViewMatrix;
  float4x4 InverseProjectionMatrix;
  float3 CameraPosition;
  float Time;
  uint FrameCount;
  uint MaxBounces;
  uint SamplesPerPixel;
  uint RandomSeed;
  float2 ScreenResolution;
  float2 JitterOffset;
  uint NumSpheres;
  uint NumTriangles;
  uint NumMaterials;
  uint _padding;
}

StructuredBuffer<Sphere> Spheres : register(t0);
StructuredBuffer<Triangle> Triangles : register(t1);
StructuredBuffer<Material> Materials : register(t2);
RWTexture2D<float4> OutputTexture : register(u0);
RWTexture2D<float4> AccumulationTexture : register(u1);
Texture2D<float4> EnvironmentMap : register(t4);
SamplerState LinearSampler : register(s0);
groupshared float3 SharedRadiance[64];
groupshared uint SharedSampleCount[64];
uint WangHash(uint seed)
{
  seed = seed ^ 61 ^ seed >> 16;
  seed *= 9;
  seed = seed ^ seed >> 4;
  seed *= 668265261;
  seed = seed ^ seed >> 15;
  return seed;
}

float RandomFloat(inout uint seed)
{
  seed = WangHash(seed);
  return float(seed) / 4294967296.0f;
}

float2 RandomFloat2(inout uint seed)
{
  return float2(RandomFloat(seed), RandomFloat(seed));
}

float3 RandomFloat3(inout uint seed)
{
  return float3(RandomFloat(seed), RandomFloat(seed), RandomFloat(seed));
}

float3 RandomUnitVector(inout uint seed)
{
  float3 v;
  do
  {
    v = (RandomFloat3(seed) * 2.0f) - 1.0f;
  }
  while (dot(v, v) >= 1.0f);
  return normalize(v);
}

float3 RandomHemisphereVector(float3 normal, inout uint seed)
{
  float3 v = RandomUnitVector(seed);
  return ((dot(v, normal) > 0.0f) ? v : (-v));
}

bool IntersectSphere(Ray ray, Sphere sphere, out HitInfo hit)
{
  hit.Hit = false;
  float3 oc = ray.Origin - sphere.Center;
  float a = dot(ray.Direction, ray.Direction);
  float b = (2.0f * dot(oc, ray.Direction));
  float c = dot(oc, oc) - (sphere.Radius * sphere.Radius);
  float discriminant = (b * b) - ((4.0f * a) * c);
  if (discriminant < 0.0f)
    return false;
  float sqrt_discriminant = sqrt(discriminant);
  float t1 = (-b) - sqrt_discriminant / (2.0f * a);
  float t2 = (-b) + sqrt_discriminant / (2.0f * a);
  float t = ((t1 > ray.MinT && t1 < ray.MaxT) ? t1 : t2);
  if (t < ray.MinT || t > ray.MaxT)
    return false;
  hit.Hit = true;
  hit.T = t;
  hit.Position = ray.Origin + (t * ray.Direction);
  hit.Normal = normalize(hit.Position - sphere.Center);
  hit.MaterialID = sphere.MaterialID;
  float phi = atan2(hit.Normal.z, hit.Normal.x);
  float theta = acos(hit.Normal.y);
  hit.UV = float2(phi + 3.14159265359f / (2.0f * 3.14159265359f), theta / 3.14159265359f);
  return true;
}

bool IntersectTriangle(Ray ray, Triangle tri, out HitInfo hit)
{
  hit.Hit = false;
  float3 edge1 = tri.V1 - tri.V0;
  float3 edge2 = tri.V2 - tri.V0;
  float3 h = cross(ray.Direction, edge2);
  float a = dot(edge1, h);
  if (a > -1e-05f && a < 1e-05f)
    return false;
  float f = 1.0f / a;
  float3 s = ray.Origin - tri.V0;
  float u = (f * dot(s, h));
  if (u < 0.0f || u > 1.0f)
    return false;
  float3 q = cross(s, edge1);
  float v = (f * dot(ray.Direction, q));
  if (v < 0.0f || u + v > 1.0f)
    return false;
  float t = (f * dot(edge2, q));
  if (t < ray.MinT || t > ray.MaxT)
    return false;
  hit.Hit = true;
  hit.T = t;
  hit.Position = ray.Origin + (t * ray.Direction);
  float w = 1.0f - u - v;
  hit.Normal = normalize((w * tri.N0) + (u * tri.N1) + (v * tri.N2));
  hit.UV = (w * tri.UV0) + (u * tri.UV1) + (v * tri.UV2);
  hit.MaterialID = tri.MaterialID;
  return true;
}

bool IntersectScene(Ray ray, out HitInfo closestHit)
{
  closestHit.Hit = false;
  closestHit.T = ray.MaxT;
  HitInfo hit;
  for (uint i = 0; i < NumSpheres; (++i))
  {
    if (IntersectSphere(ray, Spheres[i], hit))
    {
      if (hit.T < closestHit.T)
      {
        closestHit = hit;
        ray.MaxT = hit.T;
      }
    }
  }
  for (uint j = 0; j < NumTriangles; (++j))
  {
    if (IntersectTriangle(ray, Triangles[j], hit))
    {
      if (hit.T < closestHit.T)
      {
        closestHit = hit;
        ray.MaxT = hit.T;
      }
    }
  }
  return closestHit.Hit;
}

float3 SampleEnvironment(float3 direction)
{
  float phi = atan2(direction.z, direction.x);
  float theta = acos(direction.y);
  float2 uv = float2(phi + 3.14159265359f / (2.0f * 3.14159265359f), theta / 3.14159265359f);
  return EnvironmentMap.SampleLevel(LinearSampler, uv, 0).rgb;
}

float3 SampleBRDF(float3 normal, float3 viewDir, Material material, inout uint seed, out float3 sampleDir, out float pdf)
{
  float3 albedo = material.Albedo;
  float metallic = material.Metallic;
  float roughness = material.Roughness;
  float specularChance = 0.5f;
  if (RandomFloat(seed) < specularChance)
  {
    float3 reflectDir = reflect((-viewDir), normal);
    float3 perturbation = (RandomUnitVector(seed) * roughness);
    sampleDir = normalize(reflectDir + perturbation);
    if (dot(sampleDir, normal) <= 0.0f)
      sampleDir = reflect(sampleDir, normal);
    pdf = 1.0f / specularChance;
    float cosTheta = max(dot(normal, viewDir), 0.0f);
    float3 F0 = lerp(float3(0.04f, 0.04f, 0.04f), albedo, metallic);
    float3 fresnel = F0 + (1.0f - F0 * pow(1.0f - cosTheta, 5.0f));
    return fresnel;
  }
  else
  {
    sampleDir = RandomHemisphereVector(normal, seed);
    pdf = 1.0f / 1.0f - specularChance;
    return (albedo * 1.0f - metallic) / 3.14159265359f;
  }
}

float3 TraceRay(Ray ray, inout uint seed)
{
  float3 radiance = float3(0, 0, 0);
  float3 throughput = float3(1, 1, 1);
  for (uint bounce = 0; bounce < MaxBounces; (++bounce))
  {
    HitInfo hit;
    if ((!IntersectScene(ray, hit)))
    {
      radiance += (throughput * SampleEnvironment(ray.Direction));
      break;
    }
    Material material = Materials[hit.MaterialID];
    radiance += (throughput * material.Emission);
    float3 sampleDir;
    float pdf;
    float3 brdf = SampleBRDF(hit.Normal, (-ray.Direction), material, seed, sampleDir, pdf);
    if (pdf <= 0.0f)
      break;
    float cosTheta = max(dot(hit.Normal, sampleDir), 0.0f);
    throughput *= (brdf * cosTheta) / pdf;
    float maxComponent = max(max(throughput.r, throughput.g), throughput.b);
    if (maxComponent < 0.1f && bounce > 3)
    {
      float roulette = RandomFloat(seed);
      if (roulette > maxComponent)
        break;
      throughput /= maxComponent;
    }
    ray.Origin = hit.Position + (hit.Normal * 0.001f);
    ray.Direction = sampleDir;
    ray.MinT = 0.001f;
    ray.MaxT = 1000.0f;
  }
  return radiance;
}

Ray GenerateCameraRay(float2 screenCoord, float2 jitter)
{
  float2 ndc = (screenCoord + jitter / ScreenResolution * 2.0f) - 1.0f;
  ndc.y = (-ndc.y);
  float4 clipPos = float4(ndc, 1.0f, 1.0f);
  float4 viewPos = mul(clipPos, InverseProjectionMatrix);
  viewPos /= viewPos.w;
  float3 worldPos = mul(float4(viewPos.xyz, 1.0f), InverseViewMatrix).xyz;
  float3 rayDir = normalize(worldPos - CameraPosition);
  Ray ray;
  ray.Origin = CameraPosition;
  ray.Direction = rayDir;
  ray.MinT = 0.001f;
  ray.MaxT = 1000.0f;
  return ray;
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID, uint3 localId : SV_GroupThreadID)
{
  if (id.x >= (uint)ScreenResolution.x || id.y >= (uint)ScreenResolution.y)
    return ;
  uint pixelIndex = (id.y * (uint)ScreenResolution.x) + id.x;
  uint seed = RandomSeed + pixelIndex + (FrameCount * 719393);
  float3 pixelRadiance = float3(0, 0, 0);
  for (uint sample = 0; sample < SamplesPerPixel; (++sample))
  {
    float2 jitter = RandomFloat2(seed) - 0.5f;
    Ray ray = GenerateCameraRay(float2(id.xy), jitter);
    float3 sampleRadiance = TraceRay(ray, seed);
    pixelRadiance += sampleRadiance;
  }
  pixelRadiance /= float(SamplesPerPixel);
  uint localIndex = (localId.y * 8) + localId.x;
  SharedRadiance[localIndex] = pixelRadiance;
  SharedSampleCount[localIndex] = SamplesPerPixel;
  GroupMemoryBarrierWithGroupSync();
  float4 previousColor = AccumulationTexture[id.xy];
  float3 accumulatedColor = previousColor.rgb;
  float sampleCount = previousColor.a;
  float blendFactor = 1.0f / sampleCount + 1.0f;
  accumulatedColor = lerp(accumulatedColor, pixelRadiance, blendFactor);
  sampleCount += 1.0f;
  float3 toneMapped = accumulatedColor / accumulatedColor + 1.0f;
  float3 gammaCorrected = pow(toneMapped, 0.454545455f);
  OutputTexture[id.xy] = float4(gammaCorrected, 1.0f);
  AccumulationTexture[id.xy] = float4(accumulatedColor, sampleCount);
}

