# Convert SPIR-V files to GLSL
Write-Host "Converting SPIR-V files to GLSL..." -ForegroundColor Green

$isaInputDir = "result\RGACompileResults\ISA"
$glslOutputDir = "result\RGACompileResults\GLSL"
$logFile = "result\RGACompileResults\spv_to_glsl.log"

# Create output directory if it doesn't exist
if (-not (Test-Path $glslOutputDir)) {
    New-Item -ItemType Directory -Path $glslOutputDir -Force | Out-Null
}

# Initialize log file
"=== SPIR-V to GLSL Conversion Log ===" | Out-File -FilePath $logFile -Encoding UTF8
"Start time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Get all SPIR-V files from ISA directory
$spvFiles = Get-ChildItem -Path $isaInputDir -Filter "*.spv" -ErrorAction SilentlyContinue

if ($spvFiles.Count -eq 0) {
    Write-Host "No SPIR-V files found in ISA directory!" -ForegroundColor Red
    Write-Host "Please run rga_compile_bin.ps1 first to generate SPIR-V files." -ForegroundColor Yellow
    exit 1
}

Write-Host "Found $($spvFiles.Count) .spv files" -ForegroundColor Cyan
Write-Host "Will generate GLSL files using spirv-cross..." -ForegroundColor Cyan

# Function to determine shader stage from filename
function Get-ShaderStage($filename) {
    if ($filename -like "*VS*" -or $filename -like "*Vert*" -or $filename -like "*_vert*") {
        return "vertex"
    } elseif ($filename -like "*PS*" -or $filename -like "*Pixel*" -or $filename -like "*_frag*") {
        return "fragment"
    } elseif ($filename -like "*CS*" -or $filename -like "*Compute*" -or $filename -like "*_comp*") {
        return "compute"
    } elseif ($filename -like "*GS*" -or $filename -like "*Geometry*" -or $filename -like "*_geom*") {
        return "geometry"
    } elseif ($filename -like "*HS*" -or $filename -like "*Hull*" -or $filename -like "*_tesc*") {
        return "tessellation_control"
    } elseif ($filename -like "*DS*" -or $filename -like "*Domain*" -or $filename -like "*_tese*") {
        return "tessellation_evaluation"
    } else {
        return "unknown"
    }
}

# Function to get appropriate GLSL version and extension
function Get-GLSLVersion($shaderStage) {
    switch ($shaderStage) {
        "vertex" { return @("450", "vert") }
        "fragment" { return @("450", "frag") }
        "compute" { return @("450", "comp") }
        "geometry" { return @("450", "geom") }
        "tessellation_control" { return @("450", "tesc") }
        "tessellation_evaluation" { return @("450", "tese") }
        default { return @("450", "glsl") }
    }
}

Write-Host "`nConverting SPIR-V files to GLSL..." -ForegroundColor Yellow

# Initialize counters
$successCount = 0
$failureCount = 0

# Process .spv files
foreach ($file in $spvFiles) {
    Write-Host "`nProcessing: $($file.Name)" -ForegroundColor White
    
    # Log current file processing
    "Processing: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8
    
    # Determine shader stage and GLSL version
    $shaderStage = Get-ShaderStage $file.BaseName
    $glslInfo = Get-GLSLVersion $shaderStage
    $glslVersion = $glslInfo[0]
    $glslExtension = $glslInfo[1]
    
    # Define output file
    $glslFile = Join-Path $glslOutputDir "$($file.BaseName).glsl"
    
    Write-Host "  [1/1] Converting SPIR-V to GLSL ($shaderStage)..." -ForegroundColor Cyan
    
    try {
        # Use spirv-cross to convert SPIR-V to GLSL
        $spirvCrossArgs = @(
            $file.FullName
            "--output", $glslFile
            "--version", $glslVersion
        )
        
        # Add stage-specific options
        if ($shaderStage -eq "vertex") {
            $spirvCrossArgs += "--flip-vert-y"
        }
        
        $spirvCrossResult = & spirv-cross $spirvCrossArgs 2>&1
        
        if ($LASTEXITCODE -eq 0 -and (Test-Path $glslFile)) {
            Write-Host "    [OK] GLSL generated: $($file.BaseName).glsl" -ForegroundColor Green
            "  [1/1] GLSL generation: SUCCESS - $($file.BaseName).glsl (Stage: $shaderStage)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $successCount++

            # Rename file with proper extension if needed
            if ($glslExtension -ne "glsl") {
                $properGlslFile = Join-Path $glslOutputDir "$($file.BaseName).$glslExtension"
                if (Test-Path $glslFile) {
                    Move-Item $glslFile $properGlslFile -Force
                    Write-Host "    [INFO] Renamed to: $($file.BaseName).$glslExtension" -ForegroundColor Cyan
                    "    Renamed to: $($file.BaseName).$glslExtension" | Out-File -FilePath $logFile -Append -Encoding UTF8
                }
            }
        } else {
            Write-Host "    [ERROR] GLSL generation failed - Exit code: $LASTEXITCODE" -ForegroundColor Red
            "  [1/1] GLSL generation: FAILED (Exit code: $LASTEXITCODE)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "    spirv-cross Output: $spirvCrossResult" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $failureCount++
        }
    } catch {
        Write-Host "    [ERROR] spirv-cross exception: $($_.Exception.Message)" -ForegroundColor Red
        "  [1/1] GLSL generation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        $failureCount++
    }
    
    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}



# Final log entry with statistics
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Conversion Statistics ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Total files processed: $($spvFiles.Count)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Successful conversions: $successCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Failed conversions: $failureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Success rate: $(if ($spvFiles.Count -gt 0) { [math]::Round(($successCount / $spvFiles.Count) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"End time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Conversion Complete ===" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Display statistics
Write-Host "`n=== Conversion Statistics ===" -ForegroundColor Yellow
Write-Host "Total files processed: $($spvFiles.Count)" -ForegroundColor White
Write-Host "Successful conversions: $successCount" -ForegroundColor Green
Write-Host "Failed conversions: $failureCount" -ForegroundColor $(if ($failureCount -gt 0) { "Red" } else { "Green" })
$successRate = if ($spvFiles.Count -gt 0) { [math]::Round(($successCount / $spvFiles.Count) * 100, 2) } else { 0 }
Write-Host "Success rate: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host "`n[DONE] SPIR-V to GLSL conversion complete!" -ForegroundColor Green
Write-Host "  - GLSL files generated: result\RGACompileResults\GLSL\" -ForegroundColor Cyan
Write-Host "  - Conversion log: result\RGACompileResults\spv_to_glsl.log" -ForegroundColor Cyan
Write-Host "`nGenerated GLSL files:" -ForegroundColor Yellow

# List generated GLSL files
$generatedFiles = @()
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.glsl" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.vert" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.frag" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.comp" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.geom" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.tesc" -ErrorAction SilentlyContinue
$generatedFiles += Get-ChildItem -Path $glslOutputDir -Filter "*.tese" -ErrorAction SilentlyContinue
if ($generatedFiles.Count -gt 0) {
    foreach ($glslFile in $generatedFiles) {
        $stage = Get-ShaderStage $glslFile.BaseName
        Write-Host "  - $($glslFile.Name) ($stage)" -ForegroundColor White
    }
} else {
    Write-Host "  No GLSL files were generated. Check the log for errors." -ForegroundColor Red
}

Write-Host "`nNote: Generated GLSL files are compatible with OpenGL/Vulkan and can be used for cross-platform development." -ForegroundColor Green
