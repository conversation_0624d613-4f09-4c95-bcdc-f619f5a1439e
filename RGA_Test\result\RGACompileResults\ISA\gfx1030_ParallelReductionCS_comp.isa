_amdgpu_cs_main:
	s_getpc_b64 s[4:5]                                         // 000000000000: BE841F00
	s_mov_b32 s0, s1                                           // 000000000004: BE800301
	s_mov_b32 s1, s5                                           // 000000000008: BE810305
	v_lshl_add_u32 v1, s2, 3, v0                               // 00000000000C: D7460001 04010602
	s_load_dwordx4 s[4:7], s[0:1], null                        // 000000000014: F4080100 FA000000
	v_mov_b32_e32 v2, 0                                        // 00000000001C: 7E040280
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s0, s[4:7], null                       // 000000000024: F4200002 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cmp_gt_u32_e32 vcc_lo, s0, v1                            // 000000000030: 7D880200
	s_and_saveexec_b64 s[0:1], vcc                             // 000000000034: BE80246A
	s_cbranch_execz _L0                                        // 000000000038: BF88000A
	v_mul_lo_u32 v1, 0x50, v1                                  // 00000000003C: D5690001 000202FF 00000050
	buffer_load_dwordx3 v[1:3], v1, s[4:7], 0 offen            // 000000000048: E03C1000 80010101
	s_waitcnt vmcnt(0)                                         // 000000000050: BF8C3F70
	v_mul_f32_e32 v2, v2, v2                                   // 000000000054: 10040502
	v_fmac_f32_e32 v2, v1, v1                                  // 000000000058: 56040301
	v_fmac_f32_e32 v2, v3, v3                                  // 00000000005C: 56040703
	v_sqrt_f32_e32 v2, v2                                      // 000000000060: 7E046702
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 000000000064: 88FE007E
	v_lshlrev_b32_e32 v1, 2, v0                                // 000000000068: 34020082
	s_mov_b64 s[0:1], exec                                     // 00000000006C: BE80047E
	ds_write_b32 v1, v2                                        // 000000000070: D8340000 00000201
	s_waitcnt lgkmcnt(0)                                       // 000000000078: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 00000000007C: BF8CC07F
	v_cmpx_gt_u32_e32 32, v0                                   // 000000000080: 7DA800A0
	s_cbranch_execz _L1                                        // 000000000084: BF880006
	ds_read2_b32 v[2:3], v1 offset1:32                         // 000000000088: D8DC2000 02000001
	s_waitcnt lgkmcnt(0)                                       // 000000000090: BF8CC07F
	v_max_f32_e32 v2, v2, v3                                   // 000000000094: 20040702
	ds_write_b32 v1, v2                                        // 000000000098: D8340000 00000201
_L1:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000A0: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 0000000000A4: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 0000000000A8: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 0000000000AC: BF8CC07F
	v_cmpx_gt_u32_e32 16, v0                                   // 0000000000B0: 7DA80090
	s_cbranch_execz _L2                                        // 0000000000B4: BF880006
	ds_read2_b32 v[2:3], v1 offset1:16                         // 0000000000B8: D8DC1000 02000001
	s_waitcnt lgkmcnt(0)                                       // 0000000000C0: BF8CC07F
	v_max_f32_e32 v2, v2, v3                                   // 0000000000C4: 20040702
	ds_write_b32 v1, v2                                        // 0000000000C8: D8340000 00000201
_L2:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000D0: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 0000000000D4: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 0000000000D8: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 0000000000DC: BF8CC07F
	v_cmpx_gt_u32_e32 8, v0                                    // 0000000000E0: 7DA80088
	s_cbranch_execz _L3                                        // 0000000000E4: BF880006
	ds_read2_b32 v[2:3], v1 offset1:8                          // 0000000000E8: D8DC0800 02000001
	s_waitcnt lgkmcnt(0)                                       // 0000000000F0: BF8CC07F
	v_max_f32_e32 v2, v2, v3                                   // 0000000000F4: 20040702
	ds_write_b32 v1, v2                                        // 0000000000F8: D8340000 00000201
_L3:
	s_or_b64 exec, exec, s[0:1]                                // 000000000100: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 000000000104: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 000000000108: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 00000000010C: BF8CC07F
	v_cmpx_gt_u32_e32 4, v0                                    // 000000000110: 7DA80084
	s_cbranch_execz _L4                                        // 000000000114: BF880006
	ds_read2_b32 v[2:3], v1 offset1:4                          // 000000000118: D8DC0400 02000001
	s_waitcnt lgkmcnt(0)                                       // 000000000120: BF8CC07F
	v_max_f32_e32 v2, v2, v3                                   // 000000000124: 20040702
	ds_write_b32 v1, v2                                        // 000000000128: D8340000 00000201
_L4:
	s_or_b64 exec, exec, s[0:1]                                // 000000000130: 88FE007E
	s_mov_b64 s[0:1], exec                                     // 000000000134: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 000000000138: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 00000000013C: BF8CC07F
	v_cmpx_gt_u32_e32 2, v0                                    // 000000000140: 7DA80082
	s_cbranch_execz _L5                                        // 000000000144: BF880006
	ds_read2_b32 v[2:3], v1 offset1:2                          // 000000000148: D8DC0200 02000001
	s_waitcnt lgkmcnt(0)                                       // 000000000150: BF8CC07F
	v_max_f32_e32 v2, v2, v3                                   // 000000000154: 20040702
	ds_write_b32 v1, v2                                        // 000000000158: D8340000 00000201
_L5:
	s_or_b64 exec, exec, s[0:1]                                // 000000000160: 88FE007E
	v_cmp_eq_u32_e32 vcc_lo, 0, v0                             // 000000000164: 7D840080
	s_waitcnt lgkmcnt(0)                                       // 000000000168: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 00000000016C: BF8CC07F
	s_and_saveexec_b64 s[0:1], vcc                             // 000000000170: BE80246A
	s_cbranch_execz _L6                                        // 000000000174: BF880009
	v_mov_b32_e32 v0, 0                                        // 000000000178: 7E000280
	ds_read_b32 v2, v1                                         // 00000000017C: D8D80000 02000001
	ds_read_b32 v0, v0 offset:4                                // 000000000184: D8D80004 00000000
	s_waitcnt lgkmcnt(0)                                       // 00000000018C: BF8CC07F
	v_max_f32_e32 v0, v2, v0                                   // 000000000190: 20000102
	ds_write_b32 v1, v0                                        // 000000000194: D8340000 00000001
_L6:
	s_or_b64 exec, exec, s[0:1]                                // 00000000019C: 88FE007E
	s_waitcnt lgkmcnt(0)                                       // 0000000001A0: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 0000000001A4: BF8CC07F
	s_and_saveexec_b64 s[0:1], vcc                             // 0000000001A8: BE80246A
	s_cbranch_execz _L7                                        // 0000000001AC: BF88000D
	v_mbcnt_lo_u32_b32 v0, exec_lo, 0                          // 0000000001B0: D7650000 0001007E
	v_mbcnt_hi_u32_b32 v0, exec_hi, v0                         // 0000000001B8: D7660000 0002007F
	v_cmp_eq_u32_e32 vcc_lo, 0, v0                             // 0000000001C0: 7D840080
	s_and_b64 exec, exec, vcc                                  // 0000000001C4: 87FE6A7E
	s_cbranch_execz _L7                                        // 0000000001C8: BF880006
	v_mov_b32_e32 v0, 0                                        // 0000000001CC: 7E000280
	ds_read_b32 v0, v0                                         // 0000000001D0: D8D80000 00000000
	s_waitcnt lgkmcnt(0)                                       // 0000000001D8: BF8CC07F
	buffer_atomic_umax v0, off, s[4:7], 0                      // 0000000001DC: E0E00000 80010000
_L7:
	s_endpgm                                                   // 0000000001E4: BF810000
