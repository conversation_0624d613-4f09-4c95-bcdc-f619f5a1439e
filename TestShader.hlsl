// Unnamed technique, shader MeshViewerVS


struct ViewModes
{
    static const int InputPos = 0;
    static const int OutputPos = 1;
    static const int VertexID = 2;
    static const int InstanceID = 3;
    static const int Color = 4;
    static const int Normal = 5;
    static const int Tangent = 6;
    static const int UV = 7;
    static const int MaterialID = 8;
    static const int ShapeID = 9;
    static const int ViewerColor = 10;
    static const int PlasticShaded = 11;
};

struct Struct__MeshViewerVSCB
{
    uint RemapRanges;
    int ViewMode;
    half2 _padding0;
    half4x4 ViewProjMtx;
    half4 ViewerColor;
};

SamplerState samLinear : register(s0);
ConstantBuffer<Struct__MeshViewerVSCB> _MeshViewerVSCB : register(b0);

struct VSInput
{
	half3 Position   : POSITION;
	uint   VertexID   : SV_VertexID;
	uint   InstanceId : SV_InstanceID;
	half3 Color      : COLOR;
	half3 Normal     : NORMAL;
	half4 Tangent    : TANGENT;
	half2 UV         : TEXCOORD0;
	int MaterialID    : TEXCOORD1;
	int ShapeID       : TEXCOORD2;
};

struct VSOutput // AKA PSInput
{
	half4 Position   : SV_POSITION;
	half4 Color      : TEXCOORD0;
	half3 Normal     : NORMAL;
	half3 WorldPos   : POSITION;
};

VSOutput vsmain(VSInput input)
{
	VSOutput ret = (VSOutput)0;
	half4 outPos = mul(half4(input.Position, 1.0f), _MeshViewerVSCB.ViewProjMtx);

	switch (_MeshViewerVSCB.ViewMode)
	{
		case ViewModes::InputPos:
			ret.Color = half4(input.Position, 1);
			break;
		case ViewModes::OutputPos:
		{
			ret.Color = half4(outPos.xyz / outPos.w, 1.0f);
			break;
		}

		case ViewModes::VertexID:
			 ret.Color = half4(input.VertexID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::InstanceID:
			ret.Color = half4(input.InstanceId, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::Color:
			ret.Color = half4(input.Color, 1.0f);
			break;
		case ViewModes::Normal:
		{
			half3 normalizedNormal = normalize(input.Normal);
			half3 displayNormal = _MeshViewerVSCB.RemapRanges ? (normalizedNormal + 1.0f) / 2.0f : normalizedNormal;
			ret.Color = half4(displayNormal, 1.0f);
			break;
		}
		case ViewModes::Tangent:
		{
			half3 normalizedTangent = normalize(input.Tangent.xyz);
			half3 displayTangent = _MeshViewerVSCB.RemapRanges ? (normalizedTangent + 1.0f) / 2.0f : normalizedTangent;
			ret.Color = half4(displayTangent, 1.0f);
			break;
		}
		case ViewModes::UV:
			ret.Color = half4(input.UV, 0.0f, 1.0f);
			break;
		case ViewModes::MaterialID:
			ret.Color = half4(input.MaterialID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::ShapeID:
			ret.Color = half4(input.ShapeID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::ViewerColor:
			ret.Color = _MeshViewerVSCB.ViewerColor;
			break;
		default:
			ret.Color = _MeshViewerVSCB.ViewerColor;
			break;
	}
    ret.Position = outPos;
	ret.Normal = input.Normal;
	ret.WorldPos = input.Position;
	return ret;
}
/*
Shader Samplers:
	samLinear filter: MinMagMipLinear addressmode: Wrap
*/
