// Advanced Data Structures Vertex Shader
// Tests complex HLSL data structures and array operations

// Structured buffer for vertex data
struct VertexData
{
    float3 Position;
    float3 Normal;
    float2 TexCoord;
    float4 Color;
    uint MaterialID;
    float3 Tangent;
    float3 Bitangent;
    float4 BoneWeights;
    uint4 BoneIndices;
};

// Material data structure
struct MaterialData
{
    float4 Albedo;
    float Metallic;
    float Roughness;
    float AO;
    float Emission;
    float4 TilingOffset; // xy = tiling, zw = offset
    uint TextureFlags; // Bitfield for texture presence
    float _padding;
};

// Bone transformation matrix
struct BoneTransform
{
    float4x4 Transform;
    float4x4 InverseBindPose;
};

// Light data structure
struct LightData
{
    float3 Position;
    float Range;
    float3 Direction;
    float SpotAngle;
    float3 Color;
    float Intensity;
    uint Type; // 0=Directional, 1=Point, 2=Spot
    float3 _padding;
};

// Constant buffers
cbuffer PerFrame : register(b0)
{
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 ViewProjectionMatrix;
    float3 CameraPosition;
    float Time;
    float DeltaTime;
    uint FrameCount;
    float2 ScreenResolution;
};

cbuffer PerObject : register(b1)
{
    float4x4 WorldMatrix;
    float4x4 NormalMatrix;
    float4x4 PrevWorldMatrix; // For motion vectors
    uint MaterialIndex;
    float3 BoundingBoxMin;
    float3 BoundingBoxMax;
    float LODLevel;
};

// Structured buffers
StructuredBuffer<MaterialData> Materials : register(t0);
StructuredBuffer<BoneTransform> BoneTransforms : register(t1);
StructuredBuffer<LightData> Lights : register(t2);
StructuredBuffer<float4> InstanceTransforms : register(t3); // For instanced rendering

// Input/Output structures
struct VSInput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float3 Tangent : TANGENT;
    float3 Bitangent : BITANGENT;
    float2 TexCoord : TEXCOORD0;
    float2 TexCoord2 : TEXCOORD1; // Lightmap UV
    float4 Color : COLOR0;
    float4 BoneWeights : BLENDWEIGHT;
    uint4 BoneIndices : BLENDINDICES;
    uint InstanceID : SV_InstanceID;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float3 Tangent : TEXCOORD2;
    float3 Bitangent : TEXCOORD3;
    float2 TexCoord : TEXCOORD4;
    float2 LightmapUV : TEXCOORD5;
    float4 Color : TEXCOORD6;
    float3 ViewDir : TEXCOORD7;
    float4 PrevClipPos : TEXCOORD8; // For motion vectors
    float4 CurrClipPos : TEXCOORD9;
    nointerpolation uint MaterialID : TEXCOORD10;
    float LODFade : TEXCOORD11;
};

// Utility functions
float4x4 GetBoneTransform(uint4 boneIndices, float4 boneWeights)
{
    float4x4 boneTransform = 0;
    
    [unroll]
    for (int i = 0; i < 4; i++)
    {
        if (boneWeights[i] > 0.0)
        {
            boneTransform += BoneTransforms[boneIndices[i]].Transform * boneWeights[i];
        }
    }
    
    return boneTransform;
}

float3 ApplyBoneTransformToVector(float3 inputVector, uint4 boneIndices, float4 boneWeights)
{
    float3 result = 0;

    [unroll]
    for (int i = 0; i < 4; i++)
    {
        if (boneWeights[i] > 0.0)
        {
            result += mul(inputVector, (float3x3)BoneTransforms[boneIndices[i]].Transform) * boneWeights[i];
        }
    }

    return result;
}

float CalculateLODFade(float3 worldPos)
{
    float distance = length(CameraPosition - worldPos);
    float fadeStart = 50.0;
    float fadeEnd = 100.0;
    return saturate((fadeEnd - distance) / (fadeEnd - fadeStart));
}

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Get instance transform for instanced rendering
    float4x4 instanceTransform = float4x4(
        InstanceTransforms[input.InstanceID * 4 + 0],
        InstanceTransforms[input.InstanceID * 4 + 1],
        InstanceTransforms[input.InstanceID * 4 + 2],
        InstanceTransforms[input.InstanceID * 4 + 3]
    );
    
    // Apply bone transformations for skeletal animation
    float3 skinnedPosition = input.Position;
    float3 skinnedNormal = input.Normal;
    float3 skinnedTangent = input.Tangent;
    
    // Check if vertex has bone weights
    if (dot(input.BoneWeights, 1.0) > 0.0)
    {
        // Normalize bone weights
        float4 normalizedWeights = input.BoneWeights / dot(input.BoneWeights, 1.0);
        
        // Apply bone transformations
        float4x4 boneTransform = GetBoneTransform(input.BoneIndices, normalizedWeights);
        skinnedPosition = mul(float4(input.Position, 1.0), boneTransform).xyz;
        skinnedNormal = ApplyBoneTransformToVector(input.Normal, input.BoneIndices, normalizedWeights);
        skinnedTangent = ApplyBoneTransformToVector(input.Tangent, input.BoneIndices, normalizedWeights);
    }
    
    // Apply instance transform
    float4 instancedPosition = mul(float4(skinnedPosition, 1.0), instanceTransform);
    
    // Transform to world space
    float4 worldPos = mul(instancedPosition, WorldMatrix);
    output.WorldPos = worldPos.xyz;
    
    // Calculate previous frame position for motion vectors
    float4 prevWorldPos = mul(instancedPosition, PrevWorldMatrix);
    output.PrevClipPos = mul(prevWorldPos, ViewProjectionMatrix);
    
    // Transform to clip space
    output.Position = mul(worldPos, ViewProjectionMatrix);
    output.CurrClipPos = output.Position;
    
    // Transform vectors to world space
    output.Normal = normalize(mul(skinnedNormal, (float3x3)NormalMatrix));
    output.Tangent = normalize(mul(skinnedTangent, (float3x3)NormalMatrix));
    output.Bitangent = normalize(mul(input.Bitangent, (float3x3)NormalMatrix));
    
    // Pass through texture coordinates and color
    output.TexCoord = input.TexCoord;
    output.LightmapUV = input.TexCoord2;
    output.Color = input.Color;
    
    // Calculate view direction
    output.ViewDir = normalize(CameraPosition - output.WorldPos);
    
    // Pass material ID
    output.MaterialID = MaterialIndex;
    
    // Calculate LOD fade factor
    output.LODFade = CalculateLODFade(output.WorldPos);
    
    return output;
}
