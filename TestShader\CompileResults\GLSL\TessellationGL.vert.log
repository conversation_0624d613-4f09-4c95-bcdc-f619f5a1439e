﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Vertex  Main shader ===========  Position variant ----------------  Work registers: 32 (100% used at 100% occupancy) Uniform registers: 32 (25% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    0.54    1.00    0.00       LS Shortest path cycles:        0.54    1.00    0.00       LS Longest path cycles:         0.54    1.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Varying variant ---------------  Work registers: 22 (68% used at 100% occupancy) Uniform registers: 30 (23% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    0.33    9.00    0.00       LS Shortest path cycles:        0.33    9.00    0.00       LS Longest path cycles:         0.33    9.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: false  Recommended attribute streams =============================  Position attributes  - aPosition (location=0)  Non-position attributes  - aNormal (location=1)  - aTexCoord (location=2) 
