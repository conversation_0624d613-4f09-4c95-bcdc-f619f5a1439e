; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 20
; Schema: 0
               OpCapability Shader
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_RWByteAddressBuffer "type.RWByteAddressBuffer"
               OpName %TestBuffer "TestBuffer"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %TestBuffer DescriptorSet 0
               OpDecorate %TestBuffer Binding 0
               OpDecorate %_runtimearr_uint ArrayStride 4
               OpMemberDecorate %type_RWByteAddressBuffer 0 Offset 0
               OpDecorate %type_RWByteAddressBuffer BufferBlock
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
%_runtimearr_uint = OpTypeRuntimeArray %uint
%type_RWByteAddressBuffer = OpTypeStruct %_runtimearr_uint
%_ptr_Uniform_type_RWByteAddressBuffer = OpTypePointer Uniform %type_RWByteAddressBuffer
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %12 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
     %uint_1 = OpConstant %uint 1
 %TestBuffer = OpVariable %_ptr_Uniform_type_RWByteAddressBuffer Uniform
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
       %main = OpFunction %void None %12
         %15 = OpLabel
         %16 = OpLoad %v3uint %gl_GlobalInvocationID
         %17 = OpCompositeExtract %uint %16 0
         %18 = OpAccessChain %_ptr_Uniform_uint %TestBuffer %uint_0 %uint_0
         %19 = OpAtomicUMax %uint %18 %uint_1 %uint_0 %17
               OpReturn
               OpFunctionEnd
