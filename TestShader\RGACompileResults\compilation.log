﻿=== RGA Compilation Log ===
Start time: 07/31/2025 15:04:06

Processing: AdvancedDataStructuresPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_AdvancedDataStructuresPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_AdvancedDataStructuresPS.bin

Processing: AdvancedDataStructuresVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_AdvancedDataStructuresVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_AdvancedDataStructuresVS.bin

Processing: BasicLightingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_BasicLightingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_BasicLightingPS.bin

Processing: BasicLightingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_BasicLightingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_BasicLightingVS.bin

Processing: DeferredGBufferPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_DeferredGBufferPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_DeferredGBufferPS.bin

Processing: DeferredGBufferVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_DeferredGBufferVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_DeferredGBufferVS.bin

Processing: DeferredLightingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_DeferredLightingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_DeferredLightingPS.bin

Processing: DeferredLightingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_DeferredLightingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_DeferredLightingVS.bin

Processing: GeometryTessellationPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_GeometryTessellationPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_GeometryTessellationPS.bin

Processing: GeometryTessellationVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_GeometryTessellationVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_GeometryTessellationVS.bin

Processing: ImageProcessingCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ImageProcessingCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ImageProcessingCS.bin

Processing: InstancedRenderingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_InstancedRenderingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_InstancedRenderingPS.bin

Processing: InstancedRenderingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_InstancedRenderingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_InstancedRenderingVS.bin

Processing: MeshGenerationCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_MeshGenerationCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_MeshGenerationCS.bin

Processing: MeshViewerPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_MeshViewerPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_MeshViewerPS.bin

Processing: MeshViewerVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_MeshViewerVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_MeshViewerVS.bin

Processing: ParallelReductionCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ParallelReductionCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ParallelReductionCS.bin

Processing: ParticleSimulationCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ParticleSimulationCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ParticleSimulationCS.bin

Processing: ParticleUpdateCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ParticleUpdateCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ParticleUpdateCS.bin

Processing: PbrShadingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_PbrShadingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_PbrShadingPS.bin

Processing: PbrShadingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_PbrShadingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_PbrShadingVS.bin

Processing: PostProcessingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_PostProcessingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_PostProcessingPS.bin

Processing: PostProcessingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_PostProcessingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_PostProcessingVS.bin

Processing: RayTracingTestCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_RayTracingTestCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_RayTracingTestCS.bin

Processing: ShadowMappingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ShadowMappingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ShadowMappingPS.bin

Processing: ShadowMappingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_ShadowMappingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_ShadowMappingVS.bin

Processing: SimplePS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_SimplePS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_SimplePS.bin

Processing: SimpleVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_SimpleVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_SimpleVS.bin

Processing: StressTestCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_StressTestCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_StressTestCS.bin

Processing: TestShaderPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_TestShaderPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_TestShaderPS.bin

Processing: TestShaderVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_TestShaderVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_TestShaderVS.bin

Processing: VolumeTextureCS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_VolumeTextureCS_comp.isa
  [4/4] Binary generation: SUCCESS - gfx1030_VolumeTextureCS.bin

Processing: VolumetricRenderingPS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_VolumetricRenderingPS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_VolumetricRenderingPS.bin

Processing: VolumetricRenderingVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_VolumetricRenderingVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_VolumetricRenderingVS.bin

Processing: WaterSurfacePS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_WaterSurfacePS_frag.isa
  [4/4] Binary generation: SUCCESS - gfx1030_WaterSurfacePS.bin

Processing: WaterSurfaceVS.hlsl
  [1/4] SPIR-V generation: SUCCESS
  [2/4] SPVASM generation: SUCCESS
  [3/4] ISA generation: SUCCESS - gfx1030_WaterSurfaceVS_vert.isa
  [4/4] Binary generation: SUCCESS - gfx1030_WaterSurfaceVS.bin


=== Compilation Statistics ===
Total files processed: 36
Successful compilations: 36
Failed compilations: 0
Success rate: 100%
End time: 07/31/2025 15:08:56
=== Compilation Complete ===
