_amdgpu_cs_main:
	s_mov_b32 s34, s1                                          // 000000000000: BEA20301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v4, s2, 3, v0                               // 000000000008: D7460004 04010602
	s_mov_b32 s35, s1                                          // 000000000010: BEA30301
	s_mov_b64 s[0:1], exec                                     // 000000000014: BE80047E
	s_load_dwordx4 s[24:27], s[34:35], null                    // 000000000018: F4080611 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s33, s[24:27], 0xa0                    // 000000000024: F420084C FA0000A0
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cvt_u32_f32_e32 v0, s33                                  // 000000000030: 7E000E21
	v_cmpx_lt_u32_e64 v4, v0                                   // 000000000034: D4D1007E 00020104
	s_cbranch_execz _L0                                        // 00000000003C: BF88040D
	s_buffer_load_dword s42, s[24:27], 0xa4                    // 000000000040: F4200A8C FA0000A4
	v_lshl_add_u32 v5, s3, 3, v1                               // 000000000048: D7460005 04050603
	s_waitcnt lgkmcnt(0)                                       // 000000000050: BF8CC07F
	v_cvt_u32_f32_e32 v1, s42                                  // 000000000054: 7E020E2A
	v_cmp_lt_u32_e32 vcc_lo, v5, v1                            // 000000000058: 7D820305
	s_and_b64 exec, exec, vcc                                  // 00000000005C: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000060: BF880404
	s_buffer_load_dword s43, s[24:27], 0x98                    // 000000000064: F4200ACC FA000098
	v_mov_b32_e32 v1, 0                                        // 00000000006C: 7E020280
	v_mov_b32_e32 v7, 0                                        // 000000000070: 7E0E0280
	v_mov_b32_e32 v8, 0                                        // 000000000074: 7E100280
	v_mov_b32_e32 v6, 0                                        // 000000000078: 7E0C0280
	s_mov_b32 s44, 0                                           // 00000000007C: BEAC0380
	s_waitcnt lgkmcnt(0)                                       // 000000000080: BF8CC07F
	s_cmp_eq_u32 s43, 0                                        // 000000000084: BF06802B
	s_cbranch_scc1 _L1                                         // 000000000088: BF8503C7
	s_clause 0x1                                               // 00000000008C: BFA10001
	s_buffer_load_dwordx2 s[40:41], s[24:27], 0x90             // 000000000090: F4240A0C FA000090
	s_buffer_load_dword s0, s[24:27], 0x9c                     // 000000000098: F420000C FA00009C
	s_clause 0x1                                               // 0000000000A0: BFA10001
	s_load_dwordx4 s[28:31], s[34:35], 0x30                    // 0000000000A4: F4080711 FA000030
	s_load_dwordx4 s[36:39], s[34:35], 0x60                    // 0000000000AC: F4080911 FA000060
	v_mad_u64_u32 v[0:1], null, v0, v5, v[4:5]                 // 0000000000B4: D5767D00 04120B00
	v_mov_b32_e32 v6, 0                                        // 0000000000BC: 7E0C0280
	v_mov_b32_e32 v8, 0                                        // 0000000000C0: 7E100280
	v_mov_b32_e32 v7, 0                                        // 0000000000C4: 7E0E0280
	s_mov_b32 s45, 0xbcc19a5f                                  // 0000000000C8: BEAD03FF BCC19A5F
	s_waitcnt lgkmcnt(0)                                       // 0000000000D0: BF8CC07F
	s_mul_i32 s1, s40, 0xafa21                                 // 0000000000D4: 9301FF28 000AFA21
	s_mov_b32 s40, 0xbc46c6a5                                  // 0000000000DC: BEA803FF BC46C6A5
	v_add3_u32 v34, s1, s0, v0                                 // 0000000000E4: D76D0022 04000001
	s_branch _L2                                               // 0000000000EC: BF820007
_L28:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000F0: 88FE007E
_L29:
	v_add_f32_e32 v6, v3, v6                                   // 0000000000F4: 060C0D03
	v_add_f32_e32 v8, v35, v8                                  // 0000000000F8: 06101123
	v_add_f32_e32 v7, v36, v7                                  // 0000000000FC: 060E0F24
	s_add_i32 s44, s44, 1                                      // 000000000100: 812C812C
	s_cmp_lt_u32 s44, s43                                      // 000000000104: BF0A2B2C
	s_cbranch_scc0 _L3                                         // 000000000108: BF8403A6
_L2:
	v_lshrrev_b32_e32 v0, 16, v34                              // 00000000010C: 2C004490
	s_cmp_eq_u32 s41, 0                                        // 000000000110: BF068029
	v_xor3_b32 v0, v34, v0, 61                                 // 000000000114: D5780000 02F60122
	v_lshl_add_u32 v0, v0, 3, v0                               // 00000000011C: D7460000 04010700
	v_lshrrev_b32_e32 v1, 4, v0                                // 000000000124: 2C020084
	v_xor_b32_e32 v0, v1, v0                                   // 000000000128: 3A000101
	v_mul_lo_u32 v0, 0x27d4eb2d, v0                            // 00000000012C: D5690000 000200FF 27D4EB2D
	v_lshrrev_b32_e32 v1, 15, v0                               // 000000000138: 2C02008F
	v_xor_b32_e32 v0, v1, v0                                   // 00000000013C: 3A000101
	v_lshrrev_b32_e32 v1, 16, v0                               // 000000000140: 2C020090
	v_xor3_b32 v1, v0, v1, 61                                  // 000000000144: D5780001 02F60300
	v_lshl_add_u32 v1, v1, 3, v1                               // 00000000014C: D7460001 04050701
	v_lshrrev_b32_e32 v2, 4, v1                                // 000000000154: 2C040284
	v_xor_b32_e32 v1, v2, v1                                   // 000000000158: 3A020302
	v_mul_lo_u32 v1, 0x27d4eb2d, v1                            // 00000000015C: D5690001 000202FF 27D4EB2D
	v_lshrrev_b32_e32 v2, 15, v1                               // 000000000168: 2C04028F
	v_xor_b32_e32 v34, v2, v1                                  // 00000000016C: 3A440302
	s_cbranch_scc1 _L4                                         // 000000000170: BF850388
	s_buffer_load_dwordx8 s[0:7], s[24:27], 0x60               // 000000000174: F42C000C FA000060
	v_cvt_f32_u32_e32 v2, v5                                   // 00000000017C: 7E040D05
	v_cvt_f32_u32_e32 v3, v34                                  // 000000000180: 7E060D22
	v_rcp_f32_e32 v24, s42                                     // 000000000184: 7E30542A
	v_cvt_f32_u32_e32 v1, v4                                   // 000000000188: 7E020D04
	v_cvt_f32_u32_e32 v0, v0                                   // 00000000018C: 7E000D00
	v_rcp_f32_e32 v25, s33                                     // 000000000190: 7E325421
	s_buffer_load_dwordx8 s[8:15], s[24:27], 0x40              // 000000000194: F42C020C FA000040
	v_add_f32_e32 v2, -1.0, v2                                 // 00000000019C: 060404F3
	v_fma_f32 v3, 0x30000000, v3, -1.0                         // 0000000001A0: D54B0003 03CE06FF 30000000
	v_add_f32_e32 v1, -1.0, v1                                 // 0000000001AC: 060202F3
	v_fma_f32 v0, 0x30000000, v0, -1.0                         // 0000000001B0: D54B0000 03CE00FF 30000000
	s_buffer_load_dwordx8 s[16:23], s[24:27], null             // 0000000001BC: F42C040C FA000000
	v_mov_b32_e32 v40, 1.0                                     // 0000000001C4: 7E5002F2
	v_fmac_f32_e32 v2, v3, v24                                 // 0000000001C8: 56043103
	v_mov_b32_e32 v41, 1.0                                     // 0000000001CC: 7E5202F2
	v_fmac_f32_e32 v1, v0, v25                                 // 0000000001D0: 56023300
	v_mov_b32_e32 v42, 1.0                                     // 0000000001D4: 7E5402F2
	v_mov_b32_e32 v35, 0                                       // 0000000001D8: 7E460280
	v_mov_b32_e32 v36, 0                                       // 0000000001DC: 7E480280
	s_waitcnt lgkmcnt(0)                                       // 0000000001E0: BF8CC07F
	v_mul_f32_e32 v0, s5, v2                                   // 0000000001E4: 10000405
	v_fma_f32 v0, v1, s4, -v0                                  // 0000000001E8: D54B0000 84000901
	v_mul_f32_e32 v3, s9, v2                                   // 0000000001F0: 10060409
	v_mul_f32_e32 v24, s13, v2                                 // 0000000001F4: 1030040D
	v_mul_f32_e32 v2, s1, v2                                   // 0000000001F8: 10040401
	v_add_f32_e32 v0, s6, v0                                   // 0000000001FC: 06000006
	v_fma_f32 v3, v1, s8, -v3                                  // 000000000200: D54B0003 840C1101
	v_fma_f32 v24, v1, s12, -v24                               // 000000000208: D54B0018 84601901
	v_fma_f32 v1, v1, s0, -v2                                  // 000000000210: D54B0001 84080101
	v_add_f32_e32 v0, s7, v0                                   // 000000000218: 06000007
	s_buffer_load_dwordx4 s[4:7], s[24:27], 0x20               // 00000000021C: F428010C FA000020
	v_add_f32_e32 v3, s10, v3                                  // 000000000224: 0606060A
	v_add_f32_e32 v2, s14, v24                                 // 000000000228: 0604300E
	v_add_f32_e32 v1, s2, v1                                   // 00000000022C: 06020202
	v_rcp_f32_e32 v0, v0                                       // 000000000230: 7E005500
	s_clause 0x1                                               // 000000000234: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[24:27], 0x80               // 000000000238: F424000C FA000080
	s_buffer_load_dword s2, s[24:27], 0x88                     // 000000000240: F420008C FA000088
	v_add_f32_e32 v3, s11, v3                                  // 000000000248: 0606060B
	v_add_f32_e32 v2, s15, v2                                  // 00000000024C: 0604040F
	v_add_f32_e32 v1, s3, v1                                   // 000000000250: 06020203
	v_mul_f32_e32 v3, v0, v3                                   // 000000000254: 10060700
	v_mul_f32_e32 v2, v0, v2                                   // 000000000258: 10040500
	v_mul_f32_e32 v0, v0, v1                                   // 00000000025C: 10000300
	v_fma_f32 v24, v3, s20, s23                                // 000000000260: D54B0018 005C2903
	v_fma_f32 v1, v3, s16, s19                                 // 000000000268: D54B0001 004C2103
	s_waitcnt lgkmcnt(0)                                       // 000000000270: BF8CC07F
	v_fma_f32 v3, v3, s4, s7                                   // 000000000274: D54B0003 001C0903
	s_mov_b32 s20, 0                                           // 00000000027C: BE940380
	v_fmac_f32_e32 v24, s21, v2                                // 000000000280: 56300415
	v_fmac_f32_e32 v1, s17, v2                                 // 000000000284: 56020411
	v_fmac_f32_e32 v3, s5, v2                                  // 000000000288: 56060405
	v_mov_b32_e32 v2, s2                                       // 00000000028C: 7E040202
	s_mov_b64 s[4:5], 0                                        // 000000000290: BE840480
	v_fmac_f32_e32 v24, s22, v0                                // 000000000294: 56300016
	v_fmac_f32_e32 v1, s18, v0                                 // 000000000298: 56020012
	v_fmac_f32_e32 v3, s6, v0                                  // 00000000029C: 56060006
	v_subrev_f32_e32 v24, s1, v24                              // 0000000002A0: 0A303001
	v_subrev_f32_e32 v25, s0, v1                               // 0000000002A4: 0A320200
	v_subrev_f32_e32 v26, s2, v3                               // 0000000002A8: 0A340602
	v_mov_b32_e32 v3, 0                                        // 0000000002AC: 7E060280
	v_mul_f32_e32 v0, v24, v24                                 // 0000000002B0: 10003118
	v_fmac_f32_e32 v0, v25, v25                                // 0000000002B4: 56003319
	v_fmac_f32_e32 v0, v26, v26                                // 0000000002B8: 5600351A
	v_rsq_f32_e32 v1, v0                                       // 0000000002BC: 7E025D00
	v_cmp_neq_f32_e32 vcc_lo, 0, v0                            // 0000000002C0: 7C1A0080
	v_mov_b32_e32 v0, s0                                       // 0000000002C4: 7E000200
	v_cndmask_b32_e32 v27, 0, v1, vcc_lo                       // 0000000002C8: 02360280
	v_mov_b32_e32 v1, s1                                       // 0000000002CC: 7E020201
	v_mul_f32_e32 v37, v27, v26                                // 0000000002D0: 104A351B
	v_mul_f32_e32 v38, v27, v24                                // 0000000002D4: 104C311B
	v_mul_f32_e32 v39, v27, v25                                // 0000000002D8: 104E331B
	s_branch _L5                                               // 0000000002DC: BF82000E
_L27:
	s_or_b64 exec, exec, s[12:13]                              // 0000000002E0: 88FE0C7E
	s_andn2_b64 s[6:7], s[6:7], exec                           // 0000000002E4: 8A867E06
	s_and_b64 s[10:11], s[10:11], exec                         // 0000000002E8: 878A7E0A
	s_orn2_b64 s[8:9], s[8:9], exec                            // 0000000002EC: 8B887E08
	s_or_b64 s[6:7], s[6:7], s[10:11]                          // 0000000002F0: 88860A06
_L21:
	s_or_b64 exec, exec, s[0:1]                                // 0000000002F4: 88FE007E
	s_and_b64 s[0:1], exec, s[8:9]                             // 0000000002F8: 8780087E
	s_mov_b32 s20, s16                                         // 0000000002FC: BE940310
	s_or_b64 s[4:5], s[0:1], s[4:5]                            // 000000000300: 88840400
	s_andn2_b64 s[0:1], s[2:3], exec                           // 000000000304: 8A807E02
	s_and_b64 s[2:3], s[6:7], exec                             // 000000000308: 87827E06
	s_or_b64 s[2:3], s[0:1], s[2:3]                            // 00000000030C: 88820200
	s_andn2_b64 exec, exec, s[4:5]                             // 000000000310: 8AFE047E
	s_cbranch_execz _L6                                        // 000000000314: BF8802AC
_L5:
	s_buffer_load_dword s16, s[24:27], 0xb0                    // 000000000318: F420040C FA0000B0
	v_mov_b32_e32 v24, v34                                     // 000000000320: 7E300322
	v_mov_b32_e32 v27, v36                                     // 000000000324: 7E360324
	v_mov_b32_e32 v26, v35                                     // 000000000328: 7E340323
	v_mov_b32_e32 v25, v3                                      // 00000000032C: 7E320303
	v_mov_b32_e32 v30, v42                                     // 000000000330: 7E3C032A
	v_mov_b32_e32 v29, v41                                     // 000000000334: 7E3A0329
	v_mov_b32_e32 v28, v40                                     // 000000000338: 7E380328
	v_mov_b32_e32 v31, v37                                     // 00000000033C: 7E3E0325
	v_mov_b32_e32 v33, v38                                     // 000000000340: 7E420326
	v_mov_b32_e32 v32, v39                                     // 000000000344: 7E400327
	v_mov_b32_e32 v3, 0x447a0000                               // 000000000348: 7E0602FF 447A0000
	s_waitcnt lgkmcnt(0)                                       // 000000000350: BF8CC07F
	s_cmp_eq_u32 s16, 0                                        // 000000000354: BF068010
	s_cbranch_scc1 _L7                                         // 000000000358: BF850075
	v_mul_f32_e32 v3, v33, v33                                 // 00000000035C: 10064321
	v_mov_b32_e32 v34, 0x447a0000                              // 000000000360: 7E4402FF 447A0000
	s_mov_b64 s[10:11], 0                                      // 000000000368: BE8A0480
	s_mov_b32 s17, 0                                           // 00000000036C: BE910380
	s_mov_b32 s18, 0                                           // 000000000370: BE920380
	v_fmac_f32_e32 v3, v32, v32                                // 000000000374: 56064120
	v_fmac_f32_e32 v3, v31, v31                                // 000000000378: 56063F1F
	v_rcp_f32_e32 v35, v3                                      // 00000000037C: 7E465503
	v_mul_f32_e32 v36, 4.0, v3                                 // 000000000380: 104806F6
	v_mov_b32_e32 v3, 0x447a0000                               // 000000000384: 7E0602FF 447A0000
_L12:
	s_add_i32 s0, s17, 4                                       // 00000000038C: 81008411
	s_mov_b64 s[12:13], exec                                   // 000000000390: BE8C047E
	s_clause 0x1                                               // 000000000394: BFA10001
	s_buffer_load_dword s22, s[24:27], s0                      // 000000000398: F420058C 00000000
	s_buffer_load_dword s21, s[24:27], s17                     // 0000000003A0: F420054C 22000000
	s_add_i32 s0, s17, 8                                       // 0000000003A8: 81008811
	s_buffer_load_dword s19, s[24:27], s0                      // 0000000003AC: F42004CC 00000000
	s_add_i32 s0, s17, 12                                      // 0000000003B4: 81008C11
	s_buffer_load_dword s0, s[24:27], s0                       // 0000000003B8: F420000C 00000000
	s_waitcnt lgkmcnt(0)                                       // 0000000003C0: BF8CC07F
	v_subrev_f32_e32 v37, s22, v1                              // 0000000003C4: 0A4A0216
	v_subrev_f32_e32 v38, s21, v0                              // 0000000003C8: 0A4C0015
	v_subrev_f32_e32 v40, s19, v2                              // 0000000003CC: 0A500413
	v_mul_f32_e32 v39, v37, v37                                // 0000000003D0: 104E4B25
	v_mul_f32_e32 v37, v37, v33                                // 0000000003D4: 104A4325
	v_fmac_f32_e32 v39, v38, v38                               // 0000000003D8: 564E4D26
	v_fmac_f32_e32 v37, v38, v32                               // 0000000003DC: 564A4126
	v_fmac_f32_e32 v39, v40, v40                               // 0000000003E0: 564E5128
	v_fmac_f32_e32 v37, v40, v31                               // 0000000003E4: 564A3F28
	v_fma_f32 v38, s0, s0, -v39                                // 0000000003E8: D54B0026 849C0000
	v_add_f32_e32 v39, v37, v37                                // 0000000003F0: 064E4B25
	s_mov_b64 s[0:1], 0                                        // 0000000003F4: BE800480
	v_mul_f32_e32 v38, v36, v38                                // 0000000003F8: 104C4D24
	v_fmac_f32_e32 v38, v39, v39                               // 0000000003FC: 564C4F27
	v_cmpx_ngt_f32_e32 0, v38                                  // 000000000400: 7C364C80
	s_cbranch_execz _L8                                        // 000000000404: BF88002F
	v_sqrt_f32_e64 v38, v38 div:2                              // 000000000408: D5B30026 18000126
	v_mul_f32_e32 v37, -2.0, v37                               // 000000000410: 104A4AF5
	v_fma_f32 v39, -v38, v35, v37                              // 000000000414: D54B0027 24964726
	v_fmac_f32_e32 v37, v38, v35                               // 00000000041C: 564A4726
	v_cmp_lt_f32_e32 vcc_lo, 0x3a83126f, v39                   // 000000000420: 7C024EFF 3A83126F
	v_cmp_lt_f32_e64 s0, v39, v3                               // 000000000428: D4010000 00020727
	s_and_b64 vcc, vcc, s[0:1]                                 // 000000000430: 87EA006A
	v_cndmask_b32_e32 v37, v37, v39, vcc_lo                    // 000000000434: 024A4F25
	v_cmp_ngt_f32_e32 vcc_lo, 0x3a83126f, v37                  // 000000000438: 7C164AFF 3A83126F
	v_cmp_ngt_f32_e64 s0, v37, v3                              // 000000000440: D40B0000 00020725
	s_and_b64 s[46:47], vcc, s[0:1]                            // 000000000448: 87AE006A
	s_mov_b64 s[0:1], 0                                        // 00000000044C: BE800480
	s_and_saveexec_b64 s[14:15], s[46:47]                      // 000000000450: BE8E242E
	s_cbranch_execz _L9                                        // 000000000454: BF880019
	v_fma_f32 v17, v37, v33, v1                                // 000000000458: D54B0011 04064325
	v_fma_f32 v16, v37, v32, v0                                // 000000000460: D54B0010 04024125
	v_fma_f32 v18, v37, v31, v2                                // 000000000468: D54B0012 040A3F25
	s_mov_b64 s[0:1], exec                                     // 000000000470: BE80047E
	v_subrev_f32_e32 v21, s22, v17                             // 000000000474: 0A2A2216
	v_subrev_f32_e32 v20, s21, v16                             // 000000000478: 0A282015
	v_subrev_f32_e32 v22, s19, v18                             // 00000000047C: 0A2C2413
	s_add_i32 s19, s17, 16                                     // 000000000480: 81139011
	s_buffer_load_dword s19, s[24:27], s19                     // 000000000484: F42004CC 26000000
	v_mul_f32_e32 v19, v21, v21                                // 00000000048C: 10262B15
	v_fmac_f32_e32 v19, v20, v20                               // 000000000490: 56262914
	v_fmac_f32_e32 v19, v22, v22                               // 000000000494: 56262D16
	v_rsq_f32_e32 v23, v19                                     // 000000000498: 7E2E5D13
	v_cmp_neq_f32_e32 vcc_lo, 0, v19                           // 00000000049C: 7C1A2680
	s_waitcnt lgkmcnt(0)                                       // 0000000004A0: BF8CC07F
	v_mov_b32_e32 v19, s19                                     // 0000000004A4: 7E260213
	v_cndmask_b32_e32 v23, 0, v23, vcc_lo                      // 0000000004A8: 022E2E80
	v_mul_f32_e32 v20, v23, v20                                // 0000000004AC: 10282917
	v_mul_f32_e32 v21, v23, v21                                // 0000000004B0: 102A2B17
	v_mul_f32_e32 v22, v23, v22                                // 0000000004B4: 102C2D17
	v_mov_b32_e32 v23, v37                                     // 0000000004B8: 7E2E0325
_L9:
	s_or_b64 exec, exec, s[14:15]                              // 0000000004BC: 88FE0E7E
	s_and_b64 s[0:1], s[0:1], exec                             // 0000000004C0: 87807E00
_L8:
	s_or_b64 exec, exec, s[12:13]                              // 0000000004C4: 88FE0C7E
	s_andn2_b64 s[8:9], s[8:9], exec                           // 0000000004C8: 8A887E08
	s_and_b64 s[12:13], s[10:11], exec                         // 0000000004CC: 878C7E0A
	s_or_b64 s[8:9], s[8:9], s[12:13]                          // 0000000004D0: 88880C08
	s_and_saveexec_b64 s[12:13], s[0:1]                        // 0000000004D4: BE8C2400
	s_cbranch_execz _L10                                       // 0000000004D8: BF88000E
	v_cmp_lt_f32_e32 vcc_lo, v23, v34                          // 0000000004DC: 7C024517
	s_andn2_b64 s[8:9], s[8:9], exec                           // 0000000004E0: 8A887E08
	s_or_b64 s[0:1], vcc, s[10:11]                             // 0000000004E4: 88800A6A
	v_cndmask_b32_e32 v34, v34, v23, vcc_lo                    // 0000000004E8: 02442F22
	v_cndmask_b32_e32 v3, v3, v23, vcc_lo                      // 0000000004EC: 02062F03
	v_cndmask_b32_e32 v13, v13, v16, vcc_lo                    // 0000000004F0: 021A210D
	v_cndmask_b32_e32 v14, v14, v17, vcc_lo                    // 0000000004F4: 021C230E
	v_cndmask_b32_e32 v15, v15, v18, vcc_lo                    // 0000000004F8: 021E250F
	v_cndmask_b32_e32 v10, v10, v20, vcc_lo                    // 0000000004FC: 0214290A
	v_cndmask_b32_e32 v11, v11, v21, vcc_lo                    // 000000000500: 02162B0B
	v_cndmask_b32_e32 v12, v12, v22, vcc_lo                    // 000000000504: 02182D0C
	v_cndmask_b32_e32 v9, v9, v19, vcc_lo                      // 000000000508: 02122709
	s_and_b64 s[0:1], s[0:1], exec                             // 00000000050C: 87807E00
	s_or_b64 s[8:9], s[8:9], s[0:1]                            // 000000000510: 88880008
_L10:
	s_or_b64 exec, exec, s[12:13]                              // 000000000514: 88FE0C7E
	s_add_i32 s18, s18, 1                                      // 000000000518: 81128112
	s_add_i32 s17, s17, 32                                     // 00000000051C: 8111A011
	s_cmp_ge_u32 s18, s16                                      // 000000000520: BF091012
	s_cbranch_scc1 _L11                                        // 000000000524: BF850005
	s_mov_b64 s[10:11], s[8:9]                                 // 000000000528: BE8A0408
	s_branch _L12                                              // 00000000052C: BF82FF97
_L7:
	v_mov_b32_e32 v34, 0x447a0000                              // 000000000530: 7E4402FF 447A0000
	s_mov_b64 s[8:9], 0                                        // 000000000538: BE880480
_L11:
	s_buffer_load_dword s21, s[24:27], 0xb4                    // 00000000053C: F420054C FA0000B4
	s_waitcnt lgkmcnt(0)                                       // 000000000544: BF8CC07F
	s_cmp_eq_u32 s21, 0                                        // 000000000548: BF068015
	s_cbranch_scc1 _L13                                        // 00000000054C: BF8500D5
	s_mov_b32 s22, 0                                           // 000000000550: BE960380
	s_mov_b32 s23, 0                                           // 000000000554: BE970380
_L20:
	s_add_i32 s1, s22, 40                                      // 000000000558: 8101A816
	s_add_i32 s0, s22, 4                                       // 00000000055C: 81008416
	s_add_i32 s12, s22, 8                                      // 000000000560: 810C8816
	s_clause 0x3                                               // 000000000564: BFA10003
	s_buffer_load_dword s1, s[28:31], s1                       // 000000000568: F420004E 02000000
	s_buffer_load_dword s15, s[28:31], s22                     // 000000000570: F42003CE 2C000000
	s_buffer_load_dword s16, s[28:31], s0                      // 000000000578: F420040E 00000000
	s_buffer_load_dword s14, s[28:31], s12                     // 000000000580: F420038E 18000000
	s_add_i32 s0, s22, 32                                      // 000000000588: 8100A016
	s_add_i32 s12, s22, 36                                     // 00000000058C: 810CA416
	s_clause 0x1                                               // 000000000590: BFA10001
	s_buffer_load_dword s0, s[28:31], s0                       // 000000000594: F420000E 00000000
	s_buffer_load_dword s12, s[28:31], s12                     // 00000000059C: F420030E 18000000
	s_add_i32 s13, s22, 20                                     // 0000000005A4: 810D9416
	s_add_i32 s17, s22, 16                                     // 0000000005A8: 81119016
	s_clause 0x1                                               // 0000000005AC: BFA10001
	s_buffer_load_dword s13, s[28:31], s13                     // 0000000005B0: F420034E 1A000000
	s_buffer_load_dword s17, s[28:31], s17                     // 0000000005B8: F420044E 22000000
	s_add_i32 s18, s22, 24                                     // 0000000005C0: 81129816
	s_buffer_load_dword s18, s[28:31], s18                     // 0000000005C4: F420048E 24000000
	s_waitcnt lgkmcnt(0)                                       // 0000000005CC: BF8CC07F
	v_sub_f32_e64 v36, s1, s14                                 // 0000000005D0: D5040024 00001C01
	v_sub_f32_e64 v37, s0, s15                                 // 0000000005D8: D5040025 00001E00
	v_sub_f32_e64 v38, s12, s16                                // 0000000005E0: D5040026 0000200C
	s_mov_b64 s[0:1], 0                                        // 0000000005E8: BE800480
	v_mul_f32_e32 v35, v36, v32                                // 0000000005EC: 10464124
	v_sub_f32_e64 v39, s13, s16                                // 0000000005F0: D5040027 0000200D
	v_sub_f32_e64 v40, s17, s15                                // 0000000005F8: D5040028 00001E11
	v_mul_f32_e32 v42, v38, v31                                // 000000000600: 10543F26
	v_mul_f32_e32 v43, v37, v33                                // 000000000604: 10564325
	v_fma_f32 v41, v37, v31, -v35                              // 000000000608: D54B0029 848E3F25
	s_mov_b64 s[12:13], exec                                   // 000000000610: BE8C047E
	v_fma_f32 v35, v36, v33, -v42                              // 000000000614: D54B0023 84AA4324
	v_sub_f32_e64 v42, s18, s14                                // 00000000061C: D504002A 00001C12
	v_mul_f32_e32 v46, v41, v39                                // 000000000624: 105C4F29
	v_fma_f32 v45, v38, v32, -v43                              // 000000000628: D54B002D 84AE4126
	v_fmac_f32_e32 v46, v35, v40                               // 000000000630: 565C5123
	v_fmac_f32_e32 v46, v45, v42                               // 000000000634: 565C552D
	v_cmpx_ngt_f32_e64 0x3727c5ac, |v46|                       // 000000000638: D41B027E 00025CFF 3727C5AC
	s_cbranch_execz _L14                                       // 000000000644: BF88007C
	v_subrev_f32_e32 v44, s16, v1                              // 000000000648: 0A580210
	v_subrev_f32_e32 v43, s15, v0                              // 00000000064C: 0A56000F
	v_mul_f32_e32 v47, v41, v44                                // 000000000650: 105E5929
	v_rcp_f32_e32 v41, v46                                     // 000000000654: 7E52552E
	v_subrev_f32_e32 v46, s14, v2                              // 000000000658: 0A5C040E
	v_fmac_f32_e32 v47, v35, v43                               // 00000000065C: 565E5723
	v_fmac_f32_e32 v47, v45, v46                               // 000000000660: 565E5D2D
	v_mul_f32_e32 v35, v41, v47                                // 000000000664: 10465F29
	v_cmp_ngt_f32_e32 vcc_lo, 0, v35                           // 000000000668: 7C164680
	v_cmp_nlt_f32_e64 s0, 1.0, v35                             // 00000000066C: D40E0000 000246F2
	s_and_b64 s[16:17], vcc, s[0:1]                            // 000000000674: 8790006A
	s_mov_b64 s[0:1], 0                                        // 000000000678: BE800480
	s_and_saveexec_b64 s[14:15], s[16:17]                      // 00000000067C: BE8E2410
	s_cbranch_execz _L15                                       // 000000000680: BF88006B
	v_mul_f32_e32 v45, v42, v43                                // 000000000684: 105A572A
	v_mul_f32_e32 v47, v39, v46                                // 000000000688: 105E5D27
	v_fma_f32 v45, v40, v46, -v45                              // 00000000068C: D54B002D 84B65D28
	v_mul_f32_e32 v40, v40, v44                                // 000000000694: 10505928
	v_fma_f32 v42, v42, v44, -v47                              // 000000000698: D54B002A 84BE592A
	v_mul_f32_e32 v44, v45, v33                                // 0000000006A0: 1058432D
	v_fma_f32 v43, v39, v43, -v40                              // 0000000006A4: D54B002B 84A25727
	v_fmac_f32_e32 v44, v42, v32                               // 0000000006AC: 5658412A
	v_fmac_f32_e32 v44, v43, v31                               // 0000000006B0: 56583F2B
	v_mul_f32_e32 v39, v41, v44                                // 0000000006B4: 104E5929
	v_fma_f32 v40, v41, v44, v35                               // 0000000006B8: D54B0028 048E5929
	v_cmp_ngt_f32_e32 vcc_lo, 0, v39                           // 0000000006C0: 7C164E80
	v_cmp_nlt_f32_e64 s0, 1.0, v40                             // 0000000006C4: D40E0000 000250F2
	s_and_b64 s[18:19], vcc, s[0:1]                            // 0000000006CC: 8792006A
	s_mov_b64 s[0:1], 0                                        // 0000000006D0: BE800480
	s_and_saveexec_b64 s[16:17], s[18:19]                      // 0000000006D4: BE902412
	s_cbranch_execz _L16                                       // 0000000006D8: BF880053
	v_mul_f32_e32 v38, v45, v38                                // 0000000006DC: 104C4D2D
	v_fmac_f32_e32 v38, v42, v37                               // 0000000006E0: 564C4B2A
	v_fmac_f32_e32 v38, v43, v36                               // 0000000006E4: 564C492B
	v_mul_f32_e32 v36, v41, v38                                // 0000000006E8: 10484D29
	v_cmp_ngt_f32_e32 vcc_lo, 0x3a83126f, v36                  // 0000000006EC: 7C1648FF 3A83126F
	v_cmp_ngt_f32_e64 s0, v36, v3                              // 0000000006F4: D40B0000 00020724
	s_and_b64 s[46:47], vcc, s[0:1]                            // 0000000006FC: 87AE006A
	s_mov_b64 s[0:1], 0                                        // 000000000700: BE800480
	s_and_saveexec_b64 s[18:19], s[46:47]                      // 000000000704: BE92242E
	s_cbranch_execz _L17                                       // 000000000708: BF880045
	s_add_i32 s46, s22, 0x44                                   // 00000000070C: 812EFF16 00000044
	s_add_i32 s47, s22, 64                                     // 000000000714: 812FC016
	s_clause 0x1                                               // 000000000718: BFA10001
	s_buffer_load_dword s46, s[28:31], s46                     // 00000000071C: F4200B8E 5C000000
	s_buffer_load_dword s47, s[28:31], s47                     // 000000000724: F4200BCE 5E000000
	s_add_i32 s48, s22, 0x54                                   // 00000000072C: 8130FF16 00000054
	s_add_i32 s50, s22, 0x48                                   // 000000000734: 8132FF16 00000048
	s_buffer_load_dword s48, s[28:31], s48                     // 00000000073C: F4200C0E 60000000
	s_add_i32 s49, s22, 52                                     // 000000000744: 8131B416
	s_add_i32 s51, s22, 0x50                                   // 000000000748: 8133FF16 00000050
	s_clause 0x2                                               // 000000000750: BFA10002
	s_buffer_load_dword s50, s[28:31], s50                     // 000000000754: F4200C8E 64000000
	s_buffer_load_dword s51, s[28:31], s51                     // 00000000075C: F4200CCE 66000000
	s_buffer_load_dword s49, s[28:31], s49                     // 000000000764: F4200C4E 62000000
	s_add_i32 s52, s22, 0x58                                   // 00000000076C: 8134FF16 00000058
	s_add_i32 s53, s22, 48                                     // 000000000774: 8135B016
	s_clause 0x1                                               // 000000000778: BFA10001
	s_buffer_load_dword s52, s[28:31], s52                     // 00000000077C: F4200D0E 68000000
	s_buffer_load_dword s53, s[28:31], s53                     // 000000000784: F4200D4E 6A000000
	s_add_i32 s54, s22, 56                                     // 00000000078C: 8136B816
	v_sub_f32_e32 v16, 1.0, v40                                // 000000000790: 082050F2
	s_buffer_load_dword s54, s[28:31], s54                     // 000000000794: F4200D8E 6C000000
	v_fma_f32 v18, v36, v31, v2                                // 00000000079C: D54B0012 040A3F24
	s_mov_b64 s[0:1], exec                                     // 0000000007A4: BE80047E
	s_waitcnt lgkmcnt(0)                                       // 0000000007A8: BF8CC07F
	v_mul_f32_e32 v21, s46, v35                                // 0000000007AC: 102A462E
	s_add_i32 s46, s22, 0x78                                   // 0000000007B0: 812EFF16 00000078
	v_mul_f32_e32 v20, s47, v35                                // 0000000007B8: 1028462F
	s_buffer_load_dword s46, s[28:31], s46                     // 0000000007BC: F4200B8E 5C000000
	v_mul_f32_e32 v22, s50, v35                                // 0000000007C4: 102C4632
	v_fmac_f32_e32 v21, s48, v39                               // 0000000007C8: 562A4E30
	v_fmac_f32_e32 v20, s51, v39                               // 0000000007CC: 56284E33
	v_fmac_f32_e32 v22, s52, v39                               // 0000000007D0: 562C4E34
	v_fmac_f32_e32 v21, s49, v16                               // 0000000007D4: 562A2031
	v_fmac_f32_e32 v20, s53, v16                               // 0000000007D8: 56282035
	v_fmac_f32_e32 v22, s54, v16                               // 0000000007DC: 562C2036
	v_mul_f32_e32 v17, v21, v21                                // 0000000007E0: 10222B15
	v_fmac_f32_e32 v17, v20, v20                               // 0000000007E4: 56222914
	s_waitcnt lgkmcnt(0)                                       // 0000000007E8: BF8CC07F
	v_mov_b32_e32 v19, s46                                     // 0000000007EC: 7E26022E
	v_fmac_f32_e32 v17, v22, v22                               // 0000000007F0: 56222D16
	v_rsq_f32_e32 v16, v17                                     // 0000000007F4: 7E205D11
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 0000000007F8: 7C1A2280
	v_fma_f32 v17, v36, v33, v1                                // 0000000007FC: D54B0011 04064324
	v_cndmask_b32_e32 v23, 0, v16, vcc_lo                      // 000000000804: 022E2080
	v_fma_f32 v16, v36, v32, v0                                // 000000000808: D54B0010 04024124
	v_mul_f32_e32 v20, v23, v20                                // 000000000810: 10282917
	v_mul_f32_e32 v21, v23, v21                                // 000000000814: 102A2B17
	v_mul_f32_e32 v22, v23, v22                                // 000000000818: 102C2D17
	v_mov_b32_e32 v23, v36                                     // 00000000081C: 7E2E0324
_L17:
	s_or_b64 exec, exec, s[18:19]                              // 000000000820: 88FE127E
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000824: 87807E00
_L16:
	s_or_b64 exec, exec, s[16:17]                              // 000000000828: 88FE107E
	s_and_b64 s[0:1], s[0:1], exec                             // 00000000082C: 87807E00
_L15:
	s_or_b64 exec, exec, s[14:15]                              // 000000000830: 88FE0E7E
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000834: 87807E00
_L14:
	s_or_b64 exec, exec, s[12:13]                              // 000000000838: 88FE0C7E
	s_andn2_b64 s[10:11], s[10:11], exec                       // 00000000083C: 8A8A7E0A
	s_and_b64 s[12:13], s[8:9], exec                           // 000000000840: 878C7E08
	s_or_b64 s[10:11], s[10:11], s[12:13]                      // 000000000844: 888A0C0A
	s_and_saveexec_b64 s[12:13], s[0:1]                        // 000000000848: BE8C2400
	s_cbranch_execz _L18                                       // 00000000084C: BF88000E
	v_cmp_lt_f32_e32 vcc_lo, v23, v34                          // 000000000850: 7C024517
	s_or_b64 s[0:1], s[8:9], vcc                               // 000000000854: 88806A08
	v_cndmask_b32_e32 v34, v34, v23, vcc_lo                    // 000000000858: 02442F22
	v_cndmask_b32_e32 v3, v3, v23, vcc_lo                      // 00000000085C: 02062F03
	v_cndmask_b32_e32 v13, v13, v16, vcc_lo                    // 000000000860: 021A210D
	v_cndmask_b32_e32 v14, v14, v17, vcc_lo                    // 000000000864: 021C230E
	v_cndmask_b32_e32 v15, v15, v18, vcc_lo                    // 000000000868: 021E250F
	v_cndmask_b32_e32 v10, v10, v20, vcc_lo                    // 00000000086C: 0214290A
	v_cndmask_b32_e32 v11, v11, v21, vcc_lo                    // 000000000870: 02162B0B
	v_cndmask_b32_e32 v12, v12, v22, vcc_lo                    // 000000000874: 02182D0C
	v_cndmask_b32_e32 v9, v9, v19, vcc_lo                      // 000000000878: 02122709
	s_andn2_b64 s[8:9], s[10:11], exec                         // 00000000087C: 8A887E0A
	s_and_b64 s[0:1], s[0:1], exec                             // 000000000880: 87807E00
	s_or_b64 s[10:11], s[8:9], s[0:1]                          // 000000000884: 888A0008
_L18:
	s_or_b64 exec, exec, s[12:13]                              // 000000000888: 88FE0C7E
	s_add_i32 s23, s23, 1                                      // 00000000088C: 81178117
	s_addk_i32 s22, 0x90                                       // 000000000890: B7960090
	s_cmp_lt_u32 s23, s21                                      // 000000000894: BF0A1517
	s_cbranch_scc0 _L19                                        // 000000000898: BF840003
	s_mov_b64 s[8:9], s[10:11]                                 // 00000000089C: BE88040A
	s_branch _L20                                              // 0000000008A0: BF82FF2D
_L13:
	s_mov_b64 s[10:11], s[8:9]                                 // 0000000008A4: BE8A0408
_L19:
	v_readfirstlane_b32 s16, v0                                // 0000000008A8: 7E200500
	s_mov_b64 s[8:9], -1                                       // 0000000008AC: BE8804C1
	s_or_b64 s[6:7], s[6:7], exec                              // 0000000008B0: 88867E06
	s_and_saveexec_b64 s[0:1], s[10:11]                        // 0000000008B4: BE80240A
	s_cbranch_execz _L21                                       // 0000000008B8: BF88FE8E
	v_mul_lo_u32 v35, v9, 48                                   // 0000000008BC: D5690023 00016109
	v_lshrrev_b32_e32 v34, 16, v24                             // 0000000008C4: 2C443090
	v_xor3_b32 v34, v24, v34, 61                               // 0000000008C8: D5780022 02F64518
	buffer_load_dwordx4 v[0:3], v35, s[36:39], 0 offen         // 0000000008D0: E0381000 80090023
	v_lshl_add_u32 v34, v34, 3, v34                            // 0000000008D8: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 0000000008E0: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 0000000008E4: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 0000000008E8: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 0000000008F4: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 0000000008F8: 3A444524
	v_cvt_f32_u32_e32 v36, v34                                 // 0000000008FC: 7E480D22
	v_mul_f32_e32 v36, 0x2f800000, v36                         // 000000000900: 104848FF 2F800000
	v_cmp_ngt_f32_e32 vcc_lo, 0.5, v36                         // 000000000908: 7C1648F0
	s_and_saveexec_b64 s[8:9], vcc                             // 00000000090C: BE88246A
	s_xor_b64 s[8:9], exec, s[8:9]                             // 000000000910: 8988087E
	s_cbranch_execz _L22                                       // 000000000914: BF880053
	s_mov_b64 s[10:11], 0                                      // 000000000918: BE8A0480
_L23:
	v_lshrrev_b32_e32 v36, 16, v34                             // 00000000091C: 2C484490
	v_xor3_b32 v34, v34, v36, 61                               // 000000000920: D5780022 02F64922
	v_lshl_add_u32 v34, v34, 3, v34                            // 000000000928: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 000000000930: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 000000000934: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 000000000938: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 000000000944: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 000000000948: 3A444524
	v_lshrrev_b32_e32 v36, 16, v34                             // 00000000094C: 2C484490
	v_cvt_f32_u32_e32 v39, v34                                 // 000000000950: 7E4E0D22
	v_xor3_b32 v36, v34, v36, 61                               // 000000000954: D5780024 02F64922
	v_lshl_add_u32 v36, v36, 3, v36                            // 00000000095C: D7460024 04910724
	v_lshrrev_b32_e32 v37, 4, v36                              // 000000000964: 2C4A4884
	v_xor_b32_e32 v36, v37, v36                                // 000000000968: 3A484925
	v_mul_lo_u32 v36, 0x27d4eb2d, v36                          // 00000000096C: D5690024 000248FF 27D4EB2D
	v_lshrrev_b32_e32 v37, 15, v36                             // 000000000978: 2C4A488F
	v_xor_b32_e32 v36, v37, v36                                // 00000000097C: 3A484925
	v_lshrrev_b32_e32 v37, 16, v36                             // 000000000980: 2C4A4890
	v_xor3_b32 v37, v36, v37, 61                               // 000000000984: D5780025 02F64B24
	v_cvt_f32_u32_e32 v36, v36                                 // 00000000098C: 7E480D24
	v_lshl_add_u32 v37, v37, 3, v37                            // 000000000990: D7460025 04950725
	v_fma_f32 v36, 0x30000000, v36, -1.0                       // 000000000998: D54B0024 03CE48FF 30000000
	v_lshrrev_b32_e32 v38, 4, v37                              // 0000000009A4: 2C4C4A84
	v_xor_b32_e32 v37, v38, v37                                // 0000000009A8: 3A4A4B26
	v_mul_lo_u32 v37, 0x27d4eb2d, v37                          // 0000000009AC: D5690025 00024AFF 27D4EB2D
	v_lshrrev_b32_e32 v38, 15, v37                             // 0000000009B8: 2C4C4A8F
	v_xor_b32_e32 v34, v38, v37                                // 0000000009BC: 3A444B26
	v_fma_f32 v37, 0x30000000, v39, -1.0                       // 0000000009C0: D54B0025 03CE4EFF 30000000
	v_mul_f32_e32 v39, v36, v36                                // 0000000009CC: 104E4924
	v_cvt_f32_u32_e32 v38, v34                                 // 0000000009D0: 7E4C0D22
	v_fmac_f32_e32 v39, v37, v37                               // 0000000009D4: 564E4B25
	v_fma_f32 v38, 0x30000000, v38, -1.0                       // 0000000009D8: D54B0026 03CE4CFF 30000000
	v_fmac_f32_e32 v39, v38, v38                               // 0000000009E4: 564E4D26
	v_cmp_nle_f32_e32 vcc_lo, 1.0, v39                         // 0000000009E8: 7C184EF2
	s_or_b64 s[10:11], vcc, s[10:11]                           // 0000000009EC: 888A0A6A
	s_andn2_b64 exec, exec, s[10:11]                           // 0000000009F0: 8AFE0A7E
	s_cbranch_execnz _L23                                      // 0000000009F4: BF89FFC9
	s_or_b64 exec, exec, s[10:11]                              // 0000000009F8: 88FE0A7E
	v_rsq_f32_e32 v40, v39                                     // 0000000009FC: 7E505D27
	v_cmp_neq_f32_e32 vcc_lo, 0, v39                           // 000000000A00: 7C1A4E80
	s_waitcnt vmcnt(0)                                         // 000000000A04: BF8C3F70
	v_sub_f32_e32 v0, v0, v3                                   // 000000000A08: 08000700
	v_sub_f32_e32 v1, v1, v3                                   // 000000000A0C: 08020701
	v_sub_f32_e32 v2, v2, v3                                   // 000000000A10: 08040702
	v_cndmask_b32_e32 v39, 0, v40, vcc_lo                      // 000000000A14: 024E5080
	v_mul_f32_e32 v40, 0x3ea2f983, v1                          // 000000000A18: 105002FF 3EA2F983
	v_mul_f32_e32 v41, v39, v36                                // 000000000A20: 10524927
	v_mul_f32_e32 v37, v39, v37                                // 000000000A24: 104A4B27
	v_mul_f32_e32 v42, v39, v38                                // 000000000A28: 10544D27
	v_mul_f32_e32 v36, v41, v11                                // 000000000A2C: 10481729
	v_fmac_f32_e32 v36, v37, v10                               // 000000000A30: 56481525
	v_fmac_f32_e32 v36, v42, v12                               // 000000000A34: 5648192A
	v_cmp_lt_f32_e32 vcc_lo, 0, v36                            // 000000000A38: 7C024880
	v_mul_f32_e32 v36, 0x3ea2f983, v0                          // 000000000A3C: 104800FF 3EA2F983
	v_cndmask_b32_e64 v39, -v37, v37, vcc_lo                   // 000000000A44: D5010027 21AA4B25
	v_cndmask_b32_e64 v38, -v41, v41, vcc_lo                   // 000000000A4C: D5010026 21AA5329
	v_cndmask_b32_e64 v37, -v42, v42, vcc_lo                   // 000000000A54: D5010025 21AA552A
	v_mul_f32_e32 v41, 0x3ea2f983, v2                          // 000000000A5C: 105204FF 3EA2F983
_L22:
	s_or_saveexec_b64 s[8:9], s[8:9]                           // 000000000A64: BE882508
	v_mov_b32_e32 v42, 0.5                                     // 000000000A68: 7E5402F0
	s_xor_b64 exec, exec, s[8:9]                               // 000000000A6C: 89FE087E
	s_cbranch_execz _L24                                       // 000000000A70: BF880089
	s_mov_b64 s[10:11], 0                                      // 000000000A74: BE8A0480
_L25:
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000A78: 2C484490
	v_xor3_b32 v34, v34, v36, 61                               // 000000000A7C: D5780022 02F64922
	v_lshl_add_u32 v34, v34, 3, v34                            // 000000000A84: D7460022 04890722
	v_lshrrev_b32_e32 v36, 4, v34                              // 000000000A8C: 2C484484
	v_xor_b32_e32 v34, v36, v34                                // 000000000A90: 3A444524
	v_mul_lo_u32 v34, 0x27d4eb2d, v34                          // 000000000A94: D5690022 000244FF 27D4EB2D
	v_lshrrev_b32_e32 v36, 15, v34                             // 000000000AA0: 2C48448F
	v_xor_b32_e32 v34, v36, v34                                // 000000000AA4: 3A444524
	v_lshrrev_b32_e32 v36, 16, v34                             // 000000000AA8: 2C484490
	v_cvt_f32_u32_e32 v40, v34                                 // 000000000AAC: 7E500D22
	v_xor3_b32 v36, v34, v36, 61                               // 000000000AB0: D5780024 02F64922
	v_lshl_add_u32 v36, v36, 3, v36                            // 000000000AB8: D7460024 04910724
	v_lshrrev_b32_e32 v37, 4, v36                              // 000000000AC0: 2C4A4884
	v_xor_b32_e32 v36, v37, v36                                // 000000000AC4: 3A484925
	v_mul_lo_u32 v36, 0x27d4eb2d, v36                          // 000000000AC8: D5690024 000248FF 27D4EB2D
	v_lshrrev_b32_e32 v37, 15, v36                             // 000000000AD4: 2C4A488F
	v_xor_b32_e32 v36, v37, v36                                // 000000000AD8: 3A484925
	v_lshrrev_b32_e32 v37, 16, v36                             // 000000000ADC: 2C4A4890
	v_xor3_b32 v37, v36, v37, 61                               // 000000000AE0: D5780025 02F64B24
	v_cvt_f32_u32_e32 v36, v36                                 // 000000000AE8: 7E480D24
	v_lshl_add_u32 v37, v37, 3, v37                            // 000000000AEC: D7460025 04950725
	v_lshrrev_b32_e32 v38, 4, v37                              // 000000000AF4: 2C4C4A84
	v_xor_b32_e32 v37, v38, v37                                // 000000000AF8: 3A4A4B26
	v_fma_f32 v38, 0x30000000, v36, -1.0                       // 000000000AFC: D54B0026 03CE48FF 30000000
	v_mul_lo_u32 v37, 0x27d4eb2d, v37                          // 000000000B08: D5690025 00024AFF 27D4EB2D
	v_lshrrev_b32_e32 v39, 15, v37                             // 000000000B14: 2C4E4A8F
	v_xor_b32_e32 v34, v39, v37                                // 000000000B18: 3A444B27
	v_fma_f32 v37, 0x30000000, v40, -1.0                       // 000000000B1C: D54B0025 03CE50FF 30000000
	v_mul_f32_e32 v39, v38, v38                                // 000000000B28: 104E4D26
	v_cvt_f32_u32_e32 v36, v34                                 // 000000000B2C: 7E480D22
	v_fmac_f32_e32 v39, v37, v37                               // 000000000B30: 564E4B25
	v_fma_f32 v36, 0x30000000, v36, -1.0                       // 000000000B34: D54B0024 03CE48FF 30000000
	v_fmac_f32_e32 v39, v36, v36                               // 000000000B40: 564E4924
	v_cmp_nle_f32_e32 vcc_lo, 1.0, v39                         // 000000000B44: 7C184EF2
	s_or_b64 s[10:11], vcc, s[10:11]                           // 000000000B48: 888A0A6A
	s_andn2_b64 exec, exec, s[10:11]                           // 000000000B4C: 8AFE0A7E
	s_cbranch_execnz _L25                                      // 000000000B50: BF89FFC9
	s_or_b64 exec, exec, s[10:11]                              // 000000000B54: 88FE0A7E
	buffer_load_dword v40, v35, s[36:39], 0 offen offset:28    // 000000000B58: E030101C 80092823
	v_mul_f32_e32 v41, v33, v11                                // 000000000B60: 10521721
	v_rsq_f32_e32 v42, v39                                     // 000000000B64: 7E545D27
	v_cmp_neq_f32_e32 vcc_lo, 0, v39                           // 000000000B68: 7C1A4E80
	s_waitcnt vmcnt(1)                                         // 000000000B6C: BF8C3F71
	v_add_f32_e32 v0, 0xbd23d70a, v0                           // 000000000B70: 060000FF BD23D70A
	v_add_f32_e32 v1, 0xbd23d70a, v1                           // 000000000B78: 060202FF BD23D70A
	v_fmac_f32_e32 v41, v32, v10                               // 000000000B80: 56521520
	v_add_f32_e32 v2, 0xbd23d70a, v2                           // 000000000B84: 060404FF BD23D70A
	v_fma_f32 v45, -v0, v3, 0xbd23d70a                         // 000000000B8C: D54B002D 23FE0700 BD23D70A
	v_fma_f32 v46, -v1, v3, 0xbd23d70a                         // 000000000B98: D54B002E 23FE0701 BD23D70A
	v_fma_f32 v41, v31, v12, v41 mul:2                         // 000000000BA4: D54B0029 0CA6191F
	v_cndmask_b32_e32 v39, 0, v42, vcc_lo                      // 000000000BAC: 024E5480
	v_fma_f32 v42, -v11, v41, v33                              // 000000000BB0: D54B002A 2486530B
	v_mul_f32_e32 v38, v39, v38                                // 000000000BB8: 104C4D27
	v_fma_f32 v43, -v10, v41, v32                              // 000000000BBC: D54B002B 2482530A
	v_mul_f32_e32 v37, v39, v37                                // 000000000BC4: 104A4B27
	v_mul_f32_e32 v36, v39, v36                                // 000000000BC8: 10484927
	s_waitcnt vmcnt(0)                                         // 000000000BCC: BF8C3F70
	v_fmac_f32_e32 v42, v38, v40                               // 000000000BD0: 56545126
	v_fma_f32 v38, -v12, v41, v31                              // 000000000BD4: D54B0026 247E530C
	v_fmac_f32_e32 v43, v37, v40                               // 000000000BDC: 56565125
	v_fmaak_f32 v41, v2, v3, 0x3f851eb8                        // 000000000BE0: 5A520702 3F851EB8
	v_mul_f32_e32 v37, v42, v42                                // 000000000BE8: 104A552A
	v_fmac_f32_e32 v38, v36, v40                               // 000000000BEC: 564C5124
	v_mul_f32_e32 v36, v31, v12                                // 000000000BF0: 1048191F
	v_fmaak_f32 v40, v1, v3, 0x3f851eb8                        // 000000000BF4: 5A500701 3F851EB8
	v_fmac_f32_e32 v37, v43, v43                               // 000000000BFC: 564A572B
	v_fmac_f32_e32 v36, v32, v10                               // 000000000C00: 56481520
	v_fmac_f32_e32 v37, v38, v38                               // 000000000C04: 564A4D26
	v_fma_f32 v36, v11, -v33, -v36                             // 000000000C08: D54B0024 C492430B
	v_rsq_f32_e32 v39, v37                                     // 000000000C10: 7E4E5D25
	v_cmp_neq_f32_e32 vcc_lo, 0, v37                           // 000000000C14: 7C1A4A80
	v_max_f32_e32 v36, 0, v36                                  // 000000000C18: 20484880
	v_cndmask_b32_e32 v37, 0, v39, vcc_lo                      // 000000000C1C: 024A4E80
	v_sub_f32_e32 v39, 1.0, v36                                // 000000000C20: 084E48F2
	v_mul_f32_e32 v42, v37, v42                                // 000000000C24: 10545525
	v_mul_f32_e32 v43, v37, v43                                // 000000000C28: 10565725
	v_mul_f32_e32 v36, v39, v39                                // 000000000C2C: 10484F27
	v_mul_f32_e32 v37, v37, v38                                // 000000000C30: 104A4D25
	v_mul_f32_e32 v44, v11, v42                                // 000000000C34: 1058550B
	v_mul_f32_e32 v38, v36, v36                                // 000000000C38: 104C4924
	v_fmaak_f32 v36, v0, v3, 0x3f851eb8                        // 000000000C3C: 5A480700 3F851EB8
	v_fmac_f32_e32 v44, v10, v43                               // 000000000C44: 5658570A
	v_mul_f32_e32 v0, v39, v38                                 // 000000000C48: 10004D27
	v_fma_f32 v39, -v2, v3, 0xbd23d70a                         // 000000000C4C: D54B0027 23FE0702 BD23D70A
	v_fmac_f32_e32 v44, v12, v37                               // 000000000C58: 56584B0C
	v_fmac_f32_e32 v36, v0, v45                                // 000000000C5C: 56485B00
	v_fmac_f32_e32 v40, v0, v46                                // 000000000C60: 56505D00
	v_fmac_f32_e32 v41, v0, v39                                // 000000000C64: 56524F00
	v_add_f32_e32 v38, v44, v44                                // 000000000C68: 064C592C
	v_cmp_nge_f32_e32 vcc_lo, 0, v44                           // 000000000C6C: 7C125880
	v_fma_f32 v1, -v10, v38, v43                               // 000000000C70: D54B0001 24AE4D0A
	v_fma_f32 v2, -v11, v38, v42                               // 000000000C78: D54B0002 24AA4D0B
	v_fma_f32 v3, -v12, v38, v37                               // 000000000C80: D54B0003 24964D0C
	v_cndmask_b32_e32 v39, v1, v43, vcc_lo                     // 000000000C88: 024E5701
	v_cndmask_b32_e32 v38, v2, v42, vcc_lo                     // 000000000C8C: 024C5502
	v_cndmask_b32_e32 v37, v3, v37, vcc_lo                     // 000000000C90: 024A4B03
	v_mov_b32_e32 v42, 2.0                                     // 000000000C94: 7E5402F4
_L24:
	s_or_b64 exec, exec, s[8:9]                                // 000000000C98: 88FE087E
	buffer_load_dwordx3 v[43:45], v35, s[36:39], 0 offen offset:16// 000000000C9C: E03C1010 80092B23
	s_waitcnt vmcnt(1)                                         // 000000000CA4: BF8C3F71
	v_mul_f32_e32 v0, v38, v11                                 // 000000000CA8: 10001726
	v_rcp_f32_e32 v1, v42                                      // 000000000CAC: 7E02552A
	v_mul_f32_e32 v2, v36, v28                                 // 000000000CB0: 10043924
	v_mul_f32_e32 v3, v41, v30                                 // 000000000CB4: 10063D29
	s_cmp_gt_u32 s20, 3                                        // 000000000CB8: BF088314
	v_fmac_f32_e32 v0, v39, v10                                // 000000000CBC: 56001527
	s_cselect_b64 s[8:9], -1, 0                                // 000000000CC0: 858880C1
	s_mov_b64 s[14:15], -1                                     // 000000000CC4: BE8E04C1
	s_mov_b64 s[10:11], -1                                     // 000000000CC8: BE8A04C1
	v_fmac_f32_e32 v0, v37, v12                                // 000000000CCC: 56001925
	v_max_f32_e32 v0, 0, v0                                    // 000000000CD0: 20000080
	v_mul_f32_e32 v0, v0, v1                                   // 000000000CD4: 10000300
	v_mul_f32_e32 v1, v40, v29                                 // 000000000CD8: 10023B28
	v_mul_f32_e32 v40, v0, v2                                  // 000000000CDC: 10500500
	v_mul_f32_e32 v41, v0, v1                                  // 000000000CE0: 10520300
	v_mul_f32_e32 v42, v0, v3                                  // 000000000CE4: 10540700
	v_max3_f32 v0, v40, v41, v42                               // 000000000CE8: D5540000 04AA5328
	v_cmp_gt_f32_e32 vcc_lo, 0x3dcccccd, v0                    // 000000000CF0: 7C0800FF 3DCCCCCD
	s_and_b64 s[16:17], vcc, s[8:9]                            // 000000000CF8: 8790086A
	s_mov_b64 s[8:9], -1                                       // 000000000CFC: BE8804C1
	s_waitcnt vmcnt(0)                                         // 000000000D00: BF8C3F70
	v_fma_f32 v3, v28, v43, v25                                // 000000000D04: D54B0003 0466571C
	v_fma_f32 v35, v29, v44, v26                               // 000000000D0C: D54B0023 046A591D
	v_fma_f32 v36, v30, v45, v27                               // 000000000D14: D54B0024 046E5B1E
	s_and_saveexec_b64 s[12:13], s[16:17]                      // 000000000D1C: BE8C2410
	s_cbranch_execz _L26                                       // 000000000D20: BF88001A
	v_lshrrev_b32_e32 v1, 16, v34                              // 000000000D24: 2C024490
	s_mov_b64 s[14:15], 0                                      // 000000000D28: BE8E0480
	s_mov_b64 s[10:11], exec                                   // 000000000D2C: BE8A047E
	v_xor3_b32 v1, v34, v1, 61                                 // 000000000D30: D5780001 02F60322
	v_lshl_add_u32 v1, v1, 3, v1                               // 000000000D38: D7460001 04050701
	v_lshrrev_b32_e32 v2, 4, v1                                // 000000000D40: 2C040284
	v_xor_b32_e32 v1, v2, v1                                   // 000000000D44: 3A020302
	v_mul_lo_u32 v1, 0x27d4eb2d, v1                            // 000000000D48: D5690001 000202FF 27D4EB2D
	v_lshrrev_b32_e32 v2, 15, v1                               // 000000000D54: 2C04028F
	v_xor_b32_e32 v34, v2, v1                                  // 000000000D58: 3A440302
	v_cvt_f32_u32_e32 v1, v34                                  // 000000000D5C: 7E020D22
	v_mul_f32_e32 v1, 0x2f800000, v1                           // 000000000D60: 100202FF 2F800000
	v_cmpx_ngt_f32_e32 v1, v0                                  // 000000000D68: 7C360101
	v_rcp_f32_e32 v0, v0                                       // 000000000D6C: 7E005500
	s_mov_b64 s[14:15], exec                                   // 000000000D70: BE8E047E
	v_mul_f32_e32 v40, v0, v40                                 // 000000000D74: 10505100
	v_mul_f32_e32 v41, v0, v41                                 // 000000000D78: 10525300
	v_mul_f32_e32 v42, v0, v42                                 // 000000000D7C: 10545500
	s_or_b64 exec, exec, s[10:11]                              // 000000000D80: 88FE0A7E
	s_xor_b64 s[10:11], exec, -1                               // 000000000D84: 898AC17E
	s_orn2_b64 s[14:15], s[14:15], exec                        // 000000000D88: 8B8E7E0E
_L26:
	s_or_b64 exec, exec, s[12:13]                              // 000000000D8C: 88FE0C7E
	s_and_saveexec_b64 s[12:13], s[14:15]                      // 000000000D90: BE8C240E
	s_cbranch_execz _L27                                       // 000000000D94: BF88FD52
	s_add_i32 s16, s20, 1                                      // 000000000D98: 81108114
	v_fmamk_f32 v0, v10, 0x3a83126f, v13                       // 000000000D9C: 58001B0A 3A83126F
	s_cmp_ge_u32 s16, s41                                      // 000000000DA4: BF092910
	v_fmamk_f32 v1, v11, 0x3a83126f, v14                       // 000000000DA8: 58021D0B 3A83126F
	v_fmamk_f32 v2, v12, 0x3a83126f, v15                       // 000000000DB0: 58041F0C 3A83126F
	s_cselect_b64 s[8:9], -1, 0                                // 000000000DB8: 858880C1
	s_andn2_b64 s[10:11], s[10:11], exec                       // 000000000DBC: 8A8A7E0A
	s_orn2_b64 s[8:9], s[8:9], exec                            // 000000000DC0: 8B887E08
	s_branch _L27                                              // 000000000DC4: BF82FD46
_L6:
	s_or_b64 exec, exec, s[4:5]                                // 000000000DC8: 88FE047E
	s_and_saveexec_b64 s[0:1], s[2:3]                          // 000000000DCC: BE802402
	s_xor_b64 s[0:1], exec, s[0:1]                             // 000000000DD0: 8980007E
	s_cbranch_execz _L28                                       // 000000000DD4: BF88FCC6
	v_cmp_lt_f32_e64 s2, 0x6f800000, |v32|                     // 000000000DD8: D4010202 000240FF 6F800000
	v_cmp_eq_f32_e32 vcc_lo, v32, v31                          // 000000000DE4: 7C043F20
	s_clause 0x1                                               // 000000000DE8: BFA10001
	s_load_dwordx8 s[4:11], s[34:35], 0x70                     // 000000000DEC: F40C0111 FA000070
	s_load_dwordx4 s[12:15], s[34:35], 0x20                    // 000000000DF4: F4080311 FA000020
	v_cndmask_b32_e64 v0, 1.0, 0x2f800000, s2                  // 000000000DFC: D5010000 0009FEF2 2F800000
	v_cndmask_b32_e64 v2, -1.0, 1.0, vcc_lo                    // 000000000E08: D5010002 01A9E4F3
	v_cmp_eq_f32_e64 vcc_lo, |v32|, |v31|                      // 000000000E10: D402036A 00023F20
	v_mul_f32_e32 v1, v32, v0                                  // 000000000E18: 10020120
	v_rcp_f32_e32 v1, v1                                       // 000000000E1C: 7E025501
	v_mul_f32_e32 v1, v31, v1                                  // 000000000E20: 1002031F
	v_mul_f32_e32 v0, v0, v1                                   // 000000000E24: 10000300
	v_cndmask_b32_e32 v0, v0, v2, vcc_lo                       // 000000000E28: 02000500
	v_max_f32_e64 v1, |v0|, 1.0                                // 000000000E2C: D5100101 0001E500
	v_min_f32_e64 v2, |v0|, 1.0                                // 000000000E34: D50F0102 0001E500
	v_cmp_nlt_f32_e32 vcc_lo, 0, v0                            // 000000000E3C: 7C1C0080
	v_cmp_gt_f32_e64 s2, |v0|, 1.0                             // 000000000E40: D4040102 0001E500
	v_rcp_f32_e32 v1, v1                                       // 000000000E48: 7E025501
	v_mul_f32_e32 v1, v2, v1                                   // 000000000E4C: 10020302
	v_mul_f32_e32 v2, v1, v1                                   // 000000000E50: 10040301
	v_mul_f32_e32 v3, v2, v1                                   // 000000000E54: 10060302
	v_mul_f32_e32 v1, 0x3f7ffea5, v1                           // 000000000E58: 100202FF 3F7FFEA5
	v_fmaak_f32 v34, s40, v2, 0x3d5be101                       // 000000000E60: 5A440428 3D5BE101
	v_mul_f32_e32 v35, v3, v2                                  // 000000000E68: 10460503
	v_fmac_f32_e32 v1, 0xbeaa5476, v3                          // 000000000E6C: 560206FF BEAA5476
	v_fmaak_f32 v3, v34, v2, 0xbdf0555d                        // 000000000E74: 5A060522 BDF0555D
	v_mul_f32_e32 v2, v35, v2                                  // 000000000E7C: 10040523
	v_fmac_f32_e32 v1, 0x3e468bc1, v35                         // 000000000E80: 560246FF 3E468BC1
	v_fmac_f32_e32 v1, v2, v3                                  // 000000000E88: 56020702
	v_cndmask_b32_e32 v2, 1.0, v0, vcc_lo                      // 000000000E8C: 020400F2
	v_cndmask_b32_e64 v3, 0, 1.0, s2                           // 000000000E90: D5010003 0009E480
	v_fmaak_f32 v34, -2.0, v1, 0x3fc90fdb                      // 000000000E98: 5A4402F5 3FC90FDB
	v_cmp_le_f32_e32 vcc_lo, 0, v2                             // 000000000EA0: 7C060480
	v_fmac_f32_e32 v1, v34, v3                                 // 000000000EA4: 56020722
	v_cndmask_b32_e32 v2, -1.0, v2, vcc_lo                     // 000000000EA8: 020404F3
	v_cmp_nlt_f32_e32 vcc_lo, 0, v31                           // 000000000EAC: 7C1C3E80
	v_fma_f32 v34, |v33|, s45, 0x3da68d87                      // 000000000EB0: D54B0122 03FC5B21 3DA68D87
	v_mul_f32_e32 v1, v1, v2                                   // 000000000EBC: 10020501
	v_cndmask_b32_e32 v3, 1.0, v31, vcc_lo                     // 000000000EC0: 02063EF2
	v_sub_f32_e64 v2, 1.0, |v33|                               // 000000000EC4: D5040202 000242F2
	v_fma_f32 v34, v34, |v33|, 0xbe5bc094                      // 000000000ECC: D54B0222 03FE4322 BE5BC094
	v_cmp_le_f32_e32 vcc_lo, 0, v3                             // 000000000ED8: 7C060680
	v_sqrt_f32_e32 v2, v2                                      // 000000000EDC: 7E046702
	v_cndmask_b32_e32 v3, -1.0, v3, vcc_lo                     // 000000000EE0: 020606F3
	v_cmp_o_f32_e32 vcc_lo, v0, v0                             // 000000000EE4: 7C0E0100
	v_cndmask_b32_e32 v0, 0x7fc00000, v1, vcc_lo               // 000000000EE8: 020002FF 7FC00000
	v_cmp_nlt_f32_e32 vcc_lo, 0, v33                           // 000000000EF0: 7C1C4280
	v_fmamk_f32 v35, v3, 0x40490fdb, v0                        // 000000000EF4: 58460103 40490FDB
	v_cndmask_b32_e32 v1, 1.0, v33, vcc_lo                     // 000000000EFC: 020242F2
	v_cmp_gt_f32_e32 vcc_lo, 0, v32                            // 000000000F00: 7C084080
	v_mul_f32_e32 v3, 0x3fc90fdb, v3                           // 000000000F04: 100606FF 3FC90FDB
	v_fma_f32 v33, v34, |v33|, 0x3fc90fdb                      // 000000000F0C: D54B0221 03FE4322 3FC90FDB
	v_mov_b32_e32 v34, v24                                     // 000000000F18: 7E440318
	v_mul_f32_e32 v36, 0x3ea2f983, v1                          // 000000000F1C: 104802FF 3EA2F983
	v_cndmask_b32_e32 v0, v0, v35, vcc_lo                      // 000000000F24: 02004700
	v_cmp_lt_f32_e32 vcc_lo, 0, v32                            // 000000000F28: 7C024080
	v_fmaak_f32 v2, v33, v2, 0xbfc90fdb                        // 000000000F2C: 5A040521 BFC90FDB
	v_cndmask_b32_e64 v35, 0x40490fdb, 0, vcc_lo               // 000000000F34: D5010023 01A900FF 40490FDB
	v_cmp_neq_f32_e32 vcc_lo, 0, v32                           // 000000000F40: 7C1A4080
	v_cndmask_b32_e32 v0, v3, v0, vcc_lo                       // 000000000F44: 02000103
	v_cmp_le_f32_e32 vcc_lo, 0, v1                             // 000000000F48: 7C060280
	v_cndmask_b32_e32 v1, 0xbea2f983, v36, vcc_lo              // 000000000F4C: 020248FF BEA2F983
	v_cmp_neq_f32_e32 vcc_lo, 0, v31                           // 000000000F54: 7C1A3E80
	v_fma_f32 v1, v1, v2, 0.5                                  // 000000000F58: D54B0001 03C20501
	v_cndmask_b32_e32 v0, v35, v0, vcc_lo                      // 000000000F60: 02000123
	v_add_f32_e32 v0, 0.5, v0                                  // 000000000F64: 060000F0
	s_waitcnt lgkmcnt(0)                                       // 000000000F68: BF8CC07F
	image_sample_lz v[0:2], v[0:1], s[4:11], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000F6C: F09C0708 00610000
	s_waitcnt vmcnt(0)                                         // 000000000F74: BF8C3F70
	v_fmac_f32_e32 v27, v2, v30                                // 000000000F78: 56363D02
	v_fmac_f32_e32 v26, v1, v29                                // 000000000F7C: 56343B01
	v_fmac_f32_e32 v25, v0, v28                                // 000000000F80: 56323900
	v_mov_b32_e32 v36, v27                                     // 000000000F84: 7E48031B
	v_mov_b32_e32 v35, v26                                     // 000000000F88: 7E46031A
	v_mov_b32_e32 v3, v25                                      // 000000000F8C: 7E060319
	s_branch _L28                                              // 000000000F90: BF82FC57
_L4:
	v_mov_b32_e32 v36, 0                                       // 000000000F94: 7E480280
	v_mov_b32_e32 v35, 0                                       // 000000000F98: 7E460280
	v_mov_b32_e32 v3, 0                                        // 000000000F9C: 7E060280
	s_branch _L29                                              // 000000000FA0: BF82FC54
_L3:
	v_cvt_f32_u32_e32 v1, s43                                  // 000000000FA4: 7E020C2B
_L1:
	s_waitcnt lgkmcnt(0)                                       // 000000000FA8: BF8CC07F
	s_load_dwordx8 s[0:7], s[34:35], 0x30                      // 000000000FAC: F40C0011 FA000030
	v_rcp_f32_e32 v0, v1                                       // 000000000FB4: 7E005501
	s_load_dwordx8 s[8:15], s[34:35], null                     // 000000000FB8: F40C0211 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000FC0: BF8CC07F
	image_load v[9:12], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000FC4: F0001F08 00000904
	s_waitcnt vmcnt(0)                                         // 000000000FCC: BF8C3F70
	v_rcp_f32_e32 v1, v12                                      // 000000000FD0: 7E02550C
	v_fma_f32 v2, v0, v6, -v9                                  // 000000000FD4: D54B0002 84260D00
	v_fma_f32 v6, v0, v7, -v11                                 // 000000000FDC: D54B0006 842E0F00
	v_add_f32_e32 v3, 1.0, v1                                  // 000000000FE4: 060602F2
	v_fma_f32 v1, v0, v8, -v10                                 // 000000000FE8: D54B0001 842A1100
	v_fma_f32 v0, v3, v2, v9                                   // 000000000FF0: D54B0000 04260503
	v_fma_f32 v1, v3, v1, v10                                  // 000000000FF8: D54B0001 042A0303
	v_fma_f32 v2, v3, v6, v11                                  // 000000001000: D54B0002 042E0D03
	v_rcp_f32_e32 v3, v0                                       // 000000001008: 7E065500
	v_rcp_f32_e32 v6, v1                                       // 00000000100C: 7E0C5501
	v_rcp_f32_e32 v7, v2                                       // 000000001010: 7E0E5502
	v_fma_f32 v3, v0, v3, 1.0                                  // 000000001014: D54B0003 03CA0700
	v_fma_f32 v6, v1, v6, 1.0                                  // 00000000101C: D54B0006 03CA0D01
	v_fma_f32 v7, v2, v7, 1.0                                  // 000000001024: D54B0007 03CA0F02
	v_log_f32_e32 v3, v3                                       // 00000000102C: 7E064F03
	v_log_f32_e32 v6, v6                                       // 000000001030: 7E0C4F06
	v_log_f32_e32 v7, v7                                       // 000000001034: 7E0E4F07
	v_mul_legacy_f32_e32 v3, 0x3ee8ba2f, v3                    // 000000001038: 0E0606FF 3EE8BA2F
	v_mul_legacy_f32_e32 v8, 0x3ee8ba2f, v6                    // 000000001040: 0E100CFF 3EE8BA2F
	v_mul_legacy_f32_e32 v9, 0x3ee8ba2f, v7                    // 000000001048: 0E120EFF 3EE8BA2F
	v_exp_f32_e32 v6, v3                                       // 000000001050: 7E0C4B03
	v_exp_f32_e32 v7, v8                                       // 000000001054: 7E0E4B08
	v_exp_f32_e32 v8, v9                                       // 000000001058: 7E104B09
	v_mov_b32_e32 v9, 1.0                                      // 00000000105C: 7E1202F2
	v_add_f32_e32 v3, 1.0, v12                                 // 000000001060: 060618F2
	image_store v[6:9], v[4:5], s[8:15] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000001064: F0201F08 00020604
	image_store v[0:3], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 00000000106C: F0201F08 00000004
_L0:
	s_endpgm                                                   // 000000001074: BF810000
