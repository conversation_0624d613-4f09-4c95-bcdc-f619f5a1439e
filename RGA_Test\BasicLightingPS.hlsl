// Basic Lighting Pixel Shader
// Tests basic Phong lighting model

cbuffer Material : register(b0)
{
    float4 DiffuseColor;
    float4 SpecularColor;
    float SpecularPower;
    float3 AmbientColor;
};

Texture2D DiffuseTexture : register(t0);
SamplerState LinearSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float4 Color : TEXCOORD3;
    float3 ViewDir : TEXCOORD4;
    float3 LightDir : TEXCOORD5;
};

float4 main(PSInput input) : SV_TARGET
{
    // Normalize interpolated vectors
    float3 normal = normalize(input.Normal);
    float3 lightDir = normalize(input.LightDir);
    float3 viewDir = normalize(input.ViewDir);
    
    // Sample diffuse texture
    float4 texColor = DiffuseTexture.Sample(LinearSampler, input.TexCoord);
    
    // Ambient lighting
    float3 ambient = AmbientColor * texColor.rgb;
    
    // Diffuse lighting
    float NdotL = max(0.0, dot(normal, lightDir));
    float3 diffuse = DiffuseColor.rgb * texColor.rgb * NdotL;
    
    // Specular lighting (Phong)
    float3 reflectDir = reflect(-lightDir, normal);
    float RdotV = max(0.0, dot(reflectDir, viewDir));
    float3 specular = SpecularColor.rgb * pow(RdotV, SpecularPower);
    
    // Combine lighting
    float3 finalColor = ambient + diffuse + specular;
    
    return float4(finalColor, texColor.a * DiffuseColor.a);
}
