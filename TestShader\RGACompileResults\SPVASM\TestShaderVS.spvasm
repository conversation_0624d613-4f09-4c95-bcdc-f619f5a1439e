; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 97
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TANGENT %in_var_TEXCOORD0 %in_var_COLOR0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6 %out_var_TEXCOORD7
               OpSource HLSL 600
               OpName %type_MaterialConstants "type.MaterialConstants"
               OpMemberName %type_MaterialConstants 0 "WorldMatrix"
               OpMemberName %type_MaterialConstants 1 "ViewMatrix"
               OpMemberName %type_MaterialConstants 2 "ProjectionMatrix"
               OpMemberName %type_MaterialConstants 3 "WorldViewProjectionMatrix"
               OpMemberName %type_MaterialConstants 4 "LightPosition"
               OpMemberName %type_MaterialConstants 5 "LightColor"
               OpMemberName %type_MaterialConstants 6 "MaterialDiffuse"
               OpMemberName %type_MaterialConstants 7 "MaterialSpecular"
               OpMemberName %type_MaterialConstants 8 "CameraPosition"
               OpMemberName %type_MaterialConstants 9 "Time"
               OpMemberName %type_MaterialConstants 10 "SpecularPower"
               OpMemberName %type_MaterialConstants 11 "TextureScale"
               OpName %MaterialConstants "MaterialConstants"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %out_var_TEXCOORD7 "out.var.TEXCOORD7"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TANGENT Location 2
               OpDecorate %in_var_TEXCOORD0 Location 3
               OpDecorate %in_var_COLOR0 Location 4
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %out_var_TEXCOORD7 Location 7
               OpDecorate %MaterialConstants DescriptorSet 0
               OpDecorate %MaterialConstants Binding 0
               OpMemberDecorate %type_MaterialConstants 0 Offset 0
               OpMemberDecorate %type_MaterialConstants 0 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 0 RowMajor
               OpMemberDecorate %type_MaterialConstants 1 Offset 64
               OpMemberDecorate %type_MaterialConstants 1 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 1 RowMajor
               OpMemberDecorate %type_MaterialConstants 2 Offset 128
               OpMemberDecorate %type_MaterialConstants 2 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 2 RowMajor
               OpMemberDecorate %type_MaterialConstants 3 Offset 192
               OpMemberDecorate %type_MaterialConstants 3 MatrixStride 16
               OpMemberDecorate %type_MaterialConstants 3 RowMajor
               OpMemberDecorate %type_MaterialConstants 4 Offset 256
               OpMemberDecorate %type_MaterialConstants 5 Offset 272
               OpMemberDecorate %type_MaterialConstants 6 Offset 288
               OpMemberDecorate %type_MaterialConstants 7 Offset 304
               OpMemberDecorate %type_MaterialConstants 8 Offset 320
               OpMemberDecorate %type_MaterialConstants 9 Offset 336
               OpMemberDecorate %type_MaterialConstants 10 Offset 340
               OpMemberDecorate %type_MaterialConstants 11 Offset 344
               OpDecorate %type_MaterialConstants Block
      %float = OpTypeFloat 32
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
    %float_1 = OpConstant %float 1
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
    %v3float = OpTypeVector %float 3
      %int_4 = OpConstant %int 4
     %int_11 = OpConstant %int 11
    %v2float = OpTypeVector %float 2
         %29 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_8 = OpConstant %int 8
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_MaterialConstants = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %v4float %v4float %v4float %v4float %v4float %float %float %v2float
%_ptr_Uniform_type_MaterialConstants = OpTypePointer Uniform %type_MaterialConstants
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
       %void = OpTypeVoid
         %41 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%MaterialConstants = OpVariable %_ptr_Uniform_type_MaterialConstants Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD7 = OpVariable %_ptr_Output_v3float Output
       %main = OpFunction %void None %41
         %46 = OpLabel
         %47 = OpLoad %v3float %in_var_POSITION
         %48 = OpLoad %v3float %in_var_NORMAL
         %49 = OpLoad %v3float %in_var_TANGENT
         %50 = OpLoad %v2float %in_var_TEXCOORD0
         %51 = OpLoad %v4float %in_var_COLOR0
         %52 = OpCompositeExtract %float %47 0
         %53 = OpCompositeExtract %float %47 1
         %54 = OpCompositeExtract %float %47 2
         %55 = OpCompositeConstruct %v4float %52 %53 %54 %float_1
         %56 = OpAccessChain %_ptr_Uniform_mat4v4float %MaterialConstants %int_0
         %57 = OpLoad %mat4v4float %56
         %58 = OpMatrixTimesVector %v4float %57 %55
         %59 = OpAccessChain %_ptr_Uniform_mat4v4float %MaterialConstants %int_1
         %60 = OpLoad %mat4v4float %59
         %61 = OpMatrixTimesVector %v4float %60 %58
         %62 = OpAccessChain %_ptr_Uniform_mat4v4float %MaterialConstants %int_2
         %63 = OpLoad %mat4v4float %62
         %64 = OpMatrixTimesVector %v4float %63 %61
         %65 = OpVectorShuffle %v3float %58 %58 0 1 2
         %66 = OpCompositeExtract %v4float %57 0
         %67 = OpVectorShuffle %v3float %66 %66 0 1 2
         %68 = OpCompositeExtract %v4float %57 1
         %69 = OpVectorShuffle %v3float %68 %68 0 1 2
         %70 = OpCompositeExtract %v4float %57 2
         %71 = OpVectorShuffle %v3float %70 %70 0 1 2
         %72 = OpCompositeConstruct %mat3v3float %67 %69 %71
         %73 = OpMatrixTimesVector %v3float %72 %48
         %74 = OpExtInst %v3float %1 Normalize %73
         %75 = OpMatrixTimesVector %v3float %72 %49
         %76 = OpVectorTimesScalar %v3float %75 %float_1
         %77 = OpExtInst %v3float %1 Normalize %76
         %78 = OpExtInst %v3float %1 Cross %74 %77
         %79 = OpAccessChain %_ptr_Uniform_v2float %MaterialConstants %int_11
         %80 = OpLoad %v2float %79
         %81 = OpFMul %v2float %50 %80
         %82 = OpVectorShuffle %v3float %51 %51 0 1 2
         %83 = OpExtInst %v3float %1 Pow %82 %29
         %84 = OpVectorShuffle %v4float %51 %83 4 5 6 3
         %85 = OpCompositeExtract %float %51 3
         %86 = OpCompositeInsert %v4float %85 %84 3
         %87 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_8
         %88 = OpLoad %v4float %87
         %89 = OpVectorShuffle %v3float %88 %88 0 1 2
         %90 = OpFSub %v3float %89 %65
         %91 = OpExtInst %v3float %1 Normalize %90
         %92 = OpAccessChain %_ptr_Uniform_v4float %MaterialConstants %int_4
         %93 = OpLoad %v4float %92
         %94 = OpVectorShuffle %v3float %93 %93 0 1 2
         %95 = OpFSub %v3float %94 %65
         %96 = OpExtInst %v3float %1 Normalize %95
               OpStore %gl_Position %64
               OpStore %out_var_TEXCOORD0 %65
               OpStore %out_var_TEXCOORD1 %74
               OpStore %out_var_TEXCOORD2 %77
               OpStore %out_var_TEXCOORD3 %78
               OpStore %out_var_TEXCOORD4 %81
               OpStore %out_var_TEXCOORD5 %86
               OpStore %out_var_TEXCOORD6 %91
               OpStore %out_var_TEXCOORD7 %96
               OpReturn
               OpFunctionEnd
