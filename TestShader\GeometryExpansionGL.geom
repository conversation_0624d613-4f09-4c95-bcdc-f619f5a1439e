#version 320 es

// Geometry Expansion Geometry Shader - OpenGL ES Version
// Tests geometry amplification and primitive generation

layout(triangles) in;
layout(triangle_strip, max_vertices = 18) out;

in vec3 vWorldPos[];
in vec3 vNormal[];
in vec2 vTexCoord[];
in vec4 vColor[];
in float vTime[];

out vec3 gWorldPos;
out vec3 gNormal;
out vec2 gTexCoord;
out vec4 gColor;
out float gExpansionFactor;

uniform mat4 uProjectionMatrix;
uniform float uExpansionAmount;
uniform vec3 uCameraPosition;

void emitVertex(vec3 position, vec3 normal, vec2 texCoord, vec4 color, float expansionFactor)
{
    gWorldPos = position;
    gNormal = normal;
    gTexCoord = texCoord;
    gColor = color;
    gExpansionFactor = expansionFactor;
    gl_Position = uProjectionMatrix * vec4(position, 1.0);
    EmitVertex();
}

void main()
{
    // Calculate triangle center and normal
    vec3 center = (vWorldPos[0] + vWorldPos[1] + vWorldPos[2]) / 3.0;
    vec3 normal = normalize(vNormal[0] + vNormal[1] + vNormal[2]);
    
    // Calculate expansion based on distance from camera
    float distanceToCamera = length(uCameraPosition - center);
    float expansionFactor = uExpansionAmount / (1.0 + distanceToCamera * 0.1);
    
    // Emit original triangle
    for(int i = 0; i < 3; i++)
    {
        emitVertex(vWorldPos[i], vNormal[i], vTexCoord[i], vColor[i], 0.0);
    }
    EndPrimitive();
    
    // Emit expanded triangle
    vec3 expandedPositions[3];
    for(int i = 0; i < 3; i++)
    {
        vec3 directionFromCenter = normalize(vWorldPos[i] - center);
        expandedPositions[i] = vWorldPos[i] + directionFromCenter * expansionFactor;
    }
    
    for(int i = 0; i < 3; i++)
    {
        emitVertex(expandedPositions[i], vNormal[i], vTexCoord[i], vColor[i] * 0.8, 1.0);
    }
    EndPrimitive();
    
    // Emit connecting quads between original and expanded triangles
    for(int i = 0; i < 3; i++)
    {
        int next = (i + 1) % 3;
        
        // Create quad connecting edge
        emitVertex(vWorldPos[i], vNormal[i], vTexCoord[i], vColor[i] * 0.6, 0.5);
        emitVertex(expandedPositions[i], vNormal[i], vTexCoord[i], vColor[i] * 0.6, 0.5);
        emitVertex(vWorldPos[next], vNormal[next], vTexCoord[next], vColor[next] * 0.6, 0.5);
        emitVertex(expandedPositions[next], vNormal[next], vTexCoord[next], vColor[next] * 0.6, 0.5);
        
        EndPrimitive();
    }
}
