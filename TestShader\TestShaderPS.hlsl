// Pixel Shader - Ultra-comprehensive shader optimization test
// This shader contains numerous optimization opportunities across all categories

// Global constants and variables
static const float PI = 3.14159265359;
static const float TWO_PI = 2.0 * PI;
static const float HALF_PI = PI / 2.0;

// Texture and sampler declarations
Texture2D DiffuseTexture : register(t0);
Texture2D NormalTexture : register(t1);
Texture2D SpecularTexture : register(t2);
Texture2D EmissiveTexture : register(t3);
TextureCube EnvironmentTexture : register(t4);
SamplerState LinearSampler : register(s0);
SamplerState PointSampler : register(s1);

// Constant buffer
cbuffer MaterialConstants : register(b0)
{
    float4x4 WorldMatrix;
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 WorldViewProjectionMatrix;
    float4 LightPosition;
    float4 LightColor;
    float4 MaterialDiffuse;
    float4 MaterialSpecular;
    float4 CameraPosition;
    float Time;
    float SpecularPower;
    float2 TextureScale;
};

// Pixel shader input
struct PSInput
{
    float4 position : SV_POSITION;
    float3 worldPos : TEXCOORD0;
    float3 normal : TEXCOORD1;
    float3 tangent : TEXCOORD2;
    float3 binormal : TEXCOORD3;
    float2 texcoord : TEXCOORD4;
    float4 color : TEXCOORD5;
    float3 viewDir : TEXCOORD6;
    float3 lightDir : TEXCOORD7;
};

// Ultra-complex pixel shader with extensive optimization opportunities
float4 main(PSInput input) : SV_TARGET
{
    // Precision optimization candidates (color/texture related variables)
    float4 diffuseColor = float4(0.8, 0.6, 0.4, 1.0);
    float4 specularColor = float4(1.0, 1.0, 1.0, 1.0);
    float3 emissiveColor = float3(0.1, 0.05, 0.0);
    float2 uv = input.texcoord;
    float2 uvOffset = float2(0.01, 0.01);
    float alpha = 1.0;

    // Mathematical optimizations - trigonometric functions
    float timeOffset = Time * 0.5;
    float sinTime = sin(timeOffset);
    float cosTime = cos(timeOffset);
    float sinZero = sin(0.0); // Should become 0.0
    float cosZero = cos(0.0); // Should become 1.0
    float sinPiHalf = sin(PI / 2.0); // Should become 1.0
    float cosPi = cos(PI); // Should become -1.0

    // Mathematical optimizations - exponential and logarithmic functions
    float expZero = exp(0.0); // Should become 1.0
    float expOne = exp(1.0); // Should become e
    float logOne = log(1.0); // Should become 0.0
    float logE = log(2.718281828459045); // Should become 1.0

    // Mathematical optimizations - power functions
    float powerTests1 = pow(diffuseColor.r, 2.0); // Should become diffuseColor.r * diffuseColor.r
    float powerTests2 = pow(specularColor.g, 3.0); // Should become specularColor.g * specularColor.g * specularColor.g
    float powerTests3 = pow(emissiveColor.b, 0.5); // Should become sqrt(emissiveColor.b)
    float powerTests4 = pow(alpha, -0.5); // Should become rsqrt(alpha)
    float powerTests5 = pow(timeOffset, 1.0); // Should become timeOffset
    float powerTests6 = pow(sinTime, 0.0); // Should become 1.0
    float powerTests7 = pow(cosTime, -1.0); // Should become 1.0 / cosTime
    float powerTests8 = pow(2.0, timeOffset); // Should become exp2(timeOffset)

    // Redundant arithmetic operations
    float redundant1 = powerTests1 * 1.0; // Should become powerTests1
    float redundant2 = powerTests2 + 0.0; // Should become powerTests2
    float redundant3 = powerTests3 - 0.0; // Should become powerTests3
    float redundant4 = powerTests4 * 0.0; // Should become 0.0
    float redundant5 = 0.0 * powerTests5; // Should become 0.0
    float redundant6 = 1.0 * powerTests6; // Should become powerTests6

    // Expression simplification opportunities
    float expr1 = redundant1 - redundant1; // Should become 0.0
    float expr2 = redundant2 / redundant2; // Should become 1.0 (for non-zero)
    float expr3 = -(-redundant3); // Should become redundant3

    // Ternary operator optimizations
    float ternary1 = true ? 10.0 : 5.0; // Should become 10.0
    float ternary2 = false ? 8.0 : 12.0; // Should become 12.0
    float ternary3 = (redundant1 > 0.0) ? 15.0 : 15.0; // Should become 15.0
    bool ternary4 = true ? true : false; // Should become true
    bool ternary5 = false ? true : false; // Should become false

    // Logical operations optimization
    bool logical1 = true && (redundant1 > 0.0); // Should become (redundant1 > 0.0)
    bool logical2 = false || (redundant2 < 1.0); // Should become (redundant2 < 1.0)
    bool logical3 = false && (redundant3 > 0.5); // Should become false
    bool logical4 = true || (redundant4 < 0.1); // Should become true

    // Complex texture sampling with optimization opportunities
    float2 uvAnimated = uv + uvOffset * sinTime;
    uvAnimated = uvAnimated * 1.0 + 0.0; // Redundant operations

    float4 diffuseSample = DiffuseTexture.Sample(LinearSampler, uvAnimated);
    float4 normalSample = NormalTexture.Sample(LinearSampler, uvAnimated + 0.0); // +0.0 optimized
    float4 specularSample = SpecularTexture.Sample(LinearSampler, uvAnimated - 0.0); // -0.0 optimized
    float4 emissiveSample = EmissiveTexture.Sample(LinearSampler, uvAnimated * 1.0); // *1.0 optimized

    // Normal mapping calculations
    float3 normalMap = normalSample.rgb * 2.0 - 1.0;
    normalMap = normalize(normalMap + 0.0); // +0.0 optimized

    // Tangent space to world space transformation
    float3x3 tangentToWorld = float3x3(
        normalize(input.tangent),
        normalize(input.binormal),
        normalize(input.normal)
    );
    float3 worldNormal = mul(normalMap, tangentToWorld);
    worldNormal = normalize(worldNormal * 1.0); // *1.0 optimized

    // Lighting calculations with optimization opportunities
    float3 lightDir = normalize(input.lightDir + 0.0); // +0.0 optimized
    float3 viewDir = normalize(input.viewDir - 0.0); // -0.0 optimized
    float3 halfDir = normalize(lightDir + viewDir);

    // Diffuse lighting
    float NdotL = dot(worldNormal, lightDir);
    NdotL = max(0.0, NdotL * 1.0 + 0.0); // Redundant operations

    // Specular lighting with power optimizations
    float NdotH = dot(worldNormal, halfDir);
    NdotH = max(0.0, NdotH);
    float specularTerm = pow(NdotH, SpecularPower); // Could be optimized if SpecularPower is constant

    // Fresnel effect
    float VdotN = dot(viewDir, worldNormal);
    float fresnel = pow(1.0 - VdotN, 5.0); // pow(x, 5.0) could be optimized to x*x*x*x*x

    // Environment mapping
    float3 reflectDir = reflect(-viewDir, worldNormal);
    float4 envSample = EnvironmentTexture.Sample(LinearSampler, reflectDir);

    // Final color composition with many optimization opportunities
    float3 finalDiffuse = diffuseSample.rgb * diffuseColor.rgb * MaterialDiffuse.rgb;
    finalDiffuse = finalDiffuse * NdotL * LightColor.rgb;
    finalDiffuse = finalDiffuse + 0.0; // +0.0 optimized

    float3 finalSpecular = specularSample.rgb * specularColor.rgb * MaterialSpecular.rgb;
    finalSpecular = finalSpecular * specularTerm * LightColor.rgb;
    finalSpecular = finalSpecular * 1.0; // *1.0 optimized

    float3 finalEmissive = emissiveSample.rgb * emissiveColor * MaterialDiffuse.rgb;
    finalEmissive = finalEmissive - 0.0; // -0.0 optimized

    float3 finalEnvironment = envSample.rgb * fresnel;
    finalEnvironment = finalEnvironment * 0.3 + 0.0; // +0.0 optimized

    // Combine all lighting components
    float3 finalColor = finalDiffuse + finalSpecular + finalEmissive + finalEnvironment;

    // Add some complex mathematical expressions for optimization
    finalColor.r = finalColor.r + sinZero + cosZero + expZero + logOne; // Many constants
    finalColor.g = finalColor.g + powerTests1 + powerTests2 + redundant1 + expr1;
    finalColor.b = finalColor.b + ternary1 + ternary2 + ternary3;

    // Tone mapping and gamma correction with optimizations
    finalColor = finalColor / (finalColor + 1.0);
    finalColor = pow(finalColor, 1.0 / 2.2); // Could be optimized if 1.0/2.2 is precomputed

    // Alpha blending calculations
    float finalAlpha = diffuseSample.a * alpha * MaterialDiffuse.a;
    finalAlpha = finalAlpha * 1.0 + 0.0 - 0.0; // Multiple redundant operations

    // Conditional logic for optimization
    if (logical1 && logical2)
    {
        finalColor = finalColor * 1.1;
    }
    else if (logical3 || logical4)
    {
        finalColor = finalColor * 0.9;
    }

    // Loop with constant iterations (should be unrolled)
    for (int i = 0; i < 3; i++)
    {
        finalColor = finalColor * 1.01;
    }

    // Dead code that should be eliminated
    float unusedVar1 = 42.0;
    float unusedVar2 = pow(unusedVar1, 2.0);
    // These variables are never used in the final result

    return float4(finalColor, finalAlpha);
}
