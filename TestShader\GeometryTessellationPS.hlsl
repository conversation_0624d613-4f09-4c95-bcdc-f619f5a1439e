// Geometry and Tessellation Pixel Shader
// Tests advanced pixel shader features with tessellated geometry

// Material layer structure
struct MaterialLayer
{
    float4 Albedo;
    float Metallic;
    float Roughness;
    float NormalStrength;
    float TilingScale;
    float2 Offset;
    float BlendSharpness;
    float _padding;
};

// Constant buffers
cbuffer PerFrame : register(b0)
{
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 ViewProjectionMatrix;
    float3 CameraPosition;
    float Time;
    float3 LightDirection;
    float TessellationLevel;
    float2 HeightmapSize;
    float HeightScale;
    float DetailScale;
    float LODDistance;
};

cbuffer MaterialParams : register(b1)
{
    MaterialLayer GrassLayer;
    MaterialLayer RockLayer;
    MaterialLayer SnowLayer;
    MaterialLayer SandLayer;
    float3 FogColor;
    float FogDensity;
    float FogStart;
    float FogEnd;
    float2 WindDirection;
    float WindStrength;
    float _padding2;
};

// Texture arrays for different material layers
Texture2DArray AlbedoTextures : register(t0);      // 0=Grass, 1=Rock, 2=Snow, 3=Sand
Texture2DArray NormalTextures : register(t1);
Texture2DArray RoughnessTextures : register(t2);
Texture2DArray AOTextures : register(t3);

// Additional textures
Texture2D HeightmapTexture : register(t4);
Texture2D SplatmapTexture : register(t5);          // RGBA channels for 4 materials
Texture2D DetailNormalTexture : register(t6);
TextureCube SkyboxTexture : register(t7);
Texture2D ShadowMap : register(t8);

// Samplers
SamplerState LinearSampler : register(s0);
SamplerState TrilinearSampler : register(s1);
SamplerComparisonState ShadowSampler : register(s2);

// Input structure from tessellation/geometry stage
struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float3 Tangent : TEXCOORD2;
    float3 Bitangent : TEXCOORD3;
    float2 TexCoord : TEXCOORD4;
    float2 DetailUV : TEXCOORD5;
    float4 Color : COLOR0;
    float Height : TEXCOORD6;
    float Slope : TEXCOORD7;
    float3 ViewDir : TEXCOORD8;
    float4 ShadowCoord : TEXCOORD9;
    float FogFactor : TEXCOORD10;
};

// Output structure for multiple render targets
struct PSOutput
{
    float4 Albedo : SV_Target0;
    float4 Normal : SV_Target1;
    float4 Material : SV_Target2;  // Metallic, Roughness, AO, Height
    float4 Motion : SV_Target3;    // Motion vectors + depth
};

// Utility functions
float3 UnpackNormal(float3 packedNormal)
{
    return packedNormal * 2.0 - 1.0;
}

float3 BlendNormals(float3 normal1, float3 normal2, float strength)
{
    float3 t = normal1 + float3(0, 0, 1);
    float3 u = normal2 * float3(-1, -1, 1);
    float3 r = (t / t.z) * dot(t, u) - u;
    return normalize(lerp(normal1, r, strength));
}

float4 SampleMaterialLayer(MaterialLayer layer, uint textureIndex, float2 uv)
{
    float2 tiledUV = uv * layer.TilingScale + layer.Offset;
    
    // Add wind animation for grass
    if (textureIndex == 0) // Grass
    {
        float2 windOffset = WindDirection * WindStrength * sin(Time + tiledUV.x * 10.0) * 0.01;
        tiledUV += windOffset;
    }
    
    float4 albedo = AlbedoTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex));
    return albedo * layer.Albedo;
}

float3 SampleNormalLayer(MaterialLayer layer, uint textureIndex, float2 uv)
{
    float2 tiledUV = uv * layer.TilingScale + layer.Offset;
    float3 normal = NormalTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex)).rgb;
    normal = UnpackNormal(normal);
    return normal * layer.NormalStrength;
}

float2 SampleMaterialProperties(MaterialLayer layer, uint textureIndex, float2 uv)
{
    float2 tiledUV = uv * layer.TilingScale + layer.Offset;
    float2 roughnessAO = RoughnessTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex)).rg;
    return float2(layer.Roughness * roughnessAO.r, roughnessAO.g);
}

float CalculateBlendWeight(float splatValue, float height, float slope, uint materialType)
{
    float weight = splatValue;
    
    // Height-based blending
    if (materialType == 2) // Snow
    {
        weight *= saturate((height - HeightScale * 0.7) / (HeightScale * 0.3));
    }
    else if (materialType == 3) // Sand
    {
        weight *= saturate(1.0 - height / (HeightScale * 0.3));
    }
    
    // Slope-based blending
    if (materialType == 1) // Rock
    {
        weight *= saturate((slope - 0.3) / 0.4);
    }
    else if (materialType == 0) // Grass
    {
        weight *= saturate(1.0 - slope / 0.5);
    }
    
    return weight;
}

float SampleShadow(float4 shadowCoord)
{
    // Perspective divide
    float3 projCoords = shadowCoord.xyz / shadowCoord.w;
    
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    
    // Check if position is in shadow map bounds
    if (projCoords.x < 0.0 || projCoords.x > 1.0 || 
        projCoords.y < 0.0 || projCoords.y > 1.0)
        return 1.0;
    
    // PCF sampling
    float shadow = 0.0;
    float2 texelSize = 1.0 / float2(2048, 2048); // Shadow map size
    
    for (int x = -1; x <= 1; ++x)
    {
        for (int y = -1; y <= 1; ++y)
        {
            float2 offset = float2(x, y) * texelSize;
            shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, projCoords.xy + offset, projCoords.z);
        }
    }
    
    return shadow / 9.0;
}

float3 CalculateEnvironmentReflection(float3 normal, float3 viewDir, float roughness)
{
    float3 reflectDir = reflect(-viewDir, normal);
    float mipLevel = roughness * 7.0; // Assuming 8 mip levels
    return SkyboxTexture.SampleLevel(TrilinearSampler, reflectDir, mipLevel).rgb;
}

PSOutput main(PSInput input)
{
    PSOutput output;
    
    // Normalize interpolated vectors
    float3 N = normalize(input.Normal);
    float3 T = normalize(input.Tangent);
    float3 B = normalize(input.Bitangent);
    float3 V = normalize(input.ViewDir);
    
    // Sample splatmap for material blending
    float4 splatWeights = SplatmapTexture.Sample(LinearSampler, input.TexCoord);
    
    // Calculate blend weights based on height and slope
    float4 blendWeights;
    blendWeights.r = CalculateBlendWeight(splatWeights.r, input.Height, input.Slope, 0); // Grass
    blendWeights.g = CalculateBlendWeight(splatWeights.g, input.Height, input.Slope, 1); // Rock
    blendWeights.b = CalculateBlendWeight(splatWeights.b, input.Height, input.Slope, 2); // Snow
    blendWeights.a = CalculateBlendWeight(splatWeights.a, input.Height, input.Slope, 3); // Sand
    
    // Normalize blend weights
    float totalWeight = dot(blendWeights, 1.0);
    if (totalWeight > 0.0)
        blendWeights /= totalWeight;
    else
        blendWeights = float4(1, 0, 0, 0); // Default to grass
    
    // Sample and blend materials
    float4 finalAlbedo = float4(0, 0, 0, 0);
    float3 finalNormal = float3(0, 0, 0);
    float finalRoughness = 0.0;
    float finalAO = 0.0;
    float finalMetallic = 0.0;
    
    // Grass layer
    if (blendWeights.r > 0.0)
    {
        float4 grassAlbedo = SampleMaterialLayer(GrassLayer, 0, input.TexCoord);
        float3 grassNormal = SampleNormalLayer(GrassLayer, 0, input.TexCoord);
        float2 grassProps = SampleMaterialProperties(GrassLayer, 0, input.TexCoord);
        
        finalAlbedo += grassAlbedo * blendWeights.r;
        finalNormal += grassNormal * blendWeights.r;
        finalRoughness += grassProps.r * blendWeights.r;
        finalAO += grassProps.g * blendWeights.r;
        finalMetallic += GrassLayer.Metallic * blendWeights.r;
    }
    
    // Rock layer
    if (blendWeights.g > 0.0)
    {
        float4 rockAlbedo = SampleMaterialLayer(RockLayer, 1, input.TexCoord);
        float3 rockNormal = SampleNormalLayer(RockLayer, 1, input.TexCoord);
        float2 rockProps = SampleMaterialProperties(RockLayer, 1, input.TexCoord);
        
        finalAlbedo += rockAlbedo * blendWeights.g;
        finalNormal += rockNormal * blendWeights.g;
        finalRoughness += rockProps.r * blendWeights.g;
        finalAO += rockProps.g * blendWeights.g;
        finalMetallic += RockLayer.Metallic * blendWeights.g;
    }
    
    // Snow layer
    if (blendWeights.b > 0.0)
    {
        float4 snowAlbedo = SampleMaterialLayer(SnowLayer, 2, input.TexCoord);
        float3 snowNormal = SampleNormalLayer(SnowLayer, 2, input.TexCoord);
        float2 snowProps = SampleMaterialProperties(SnowLayer, 2, input.TexCoord);
        
        finalAlbedo += snowAlbedo * blendWeights.b;
        finalNormal += snowNormal * blendWeights.b;
        finalRoughness += snowProps.r * blendWeights.b;
        finalAO += snowProps.g * blendWeights.b;
        finalMetallic += SnowLayer.Metallic * blendWeights.b;
    }
    
    // Sand layer
    if (blendWeights.a > 0.0)
    {
        float4 sandAlbedo = SampleMaterialLayer(SandLayer, 3, input.TexCoord);
        float3 sandNormal = SampleNormalLayer(SandLayer, 3, input.TexCoord);
        float2 sandProps = SampleMaterialProperties(SandLayer, 3, input.TexCoord);
        
        finalAlbedo += sandAlbedo * blendWeights.a;
        finalNormal += sandNormal * blendWeights.a;
        finalRoughness += sandProps.r * blendWeights.a;
        finalAO += sandProps.g * blendWeights.a;
        finalMetallic += SandLayer.Metallic * blendWeights.a;
    }
    
    // Sample detail normal and blend
    float3 detailNormal = DetailNormalTexture.Sample(TrilinearSampler, input.DetailUV).rgb;
    detailNormal = UnpackNormal(detailNormal);
    finalNormal = BlendNormals(finalNormal, detailNormal, 0.5);
    
    // Transform normal to world space
    float3x3 TBN = float3x3(T, B, N);
    float3 worldNormal = normalize(mul(finalNormal, TBN));
    
    // Calculate lighting
    float3 L = normalize(-LightDirection);
    float NdotL = max(dot(worldNormal, L), 0.0);
    
    // Sample shadow
    float shadow = SampleShadow(input.ShadowCoord);
    
    // Calculate environment reflection
    float3 envReflection = CalculateEnvironmentReflection(worldNormal, V, finalRoughness);
    
    // Simple lighting calculation
    float3 ambient = float3(0.1, 0.1, 0.15) * finalAO;
    float3 diffuse = finalAlbedo.rgb * NdotL * shadow;
    float3 specular = envReflection * (1.0 - finalRoughness) * finalMetallic;
    
    float3 finalColor = ambient + diffuse + specular;
    
    // Apply fog
    float fogFactor = saturate((input.FogFactor - FogStart) / (FogEnd - FogStart));
    finalColor = lerp(finalColor, FogColor, fogFactor * FogDensity);
    
    // Output to multiple render targets
    output.Albedo = float4(finalColor, finalAlbedo.a);
    output.Normal = float4(worldNormal * 0.5 + 0.5, finalRoughness);
    output.Material = float4(finalMetallic, finalRoughness, finalAO, input.Height / HeightScale);
    output.Motion = float4(0, 0, input.Position.z / input.Position.w, 1.0); // Simplified motion vectors
    
    return output;
}
