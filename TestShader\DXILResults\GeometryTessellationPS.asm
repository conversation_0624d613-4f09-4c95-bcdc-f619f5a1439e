;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float     zw
; TEXCOORD                 0   xyz         1     NONE   float       
; TEXCOORD                 6      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 7      w        2     NONE   float      w
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                10      w        3     NONE   float      w
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5     zw        5     NONE   float     zw
; COLOR                    0   xyzw        6     NONE   float       
; TEXCOORD                 8   xyz         7     NONE   float   xyz 
; TEXCOORD                 9   xyzw        8     NONE   float   xyzw
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
; SV_Target                1   xyzw        1   TARGET   float   xyzw
; SV_Target                2   xyzw        2   TARGET   float   xyzw
; SV_Target                3   xyzw        3   TARGET   float   xyzw
;
; shader hash: d16aad5713b2bd6cb8c364febfa96ebe
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 13
; SigOutputElements: 4
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 9
; SigOutputVectors[0]: 4
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; COLOR                    0                 linear       
; TEXCOORD                 6                 linear       
; TEXCOORD                 7                 linear       
; TEXCOORD                 8                 linear       
; TEXCOORD                 9                 linear       
; TEXCOORD                10                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
; SV_Target                1                              
; SV_Target                2                              
; SV_Target                3                              
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 ViewMatrix;             ; Offset:    0
;       column_major float4x4 ProjectionMatrix;       ; Offset:   64
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:  128
;       float3 CameraPosition;                        ; Offset:  192
;       float Time;                                   ; Offset:  204
;       float3 LightDirection;                        ; Offset:  208
;       float TessellationLevel;                      ; Offset:  220
;       float2 HeightmapSize;                         ; Offset:  224
;       float HeightScale;                            ; Offset:  232
;       float DetailScale;                            ; Offset:  236
;       float LODDistance;                            ; Offset:  240
;   
;   } PerFrame;                                       ; Offset:    0 Size:   244
;
; }
;
; cbuffer MaterialParams
; {
;
;   struct MaterialParams
;   {
;
;       struct struct.MaterialLayer
;       {
;
;           float4 Albedo;                            ; Offset:    0
;           float Metallic;                           ; Offset:   16
;           float Roughness;                          ; Offset:   20
;           float NormalStrength;                     ; Offset:   24
;           float TilingScale;                        ; Offset:   28
;           float2 Offset;                            ; Offset:   32
;           float BlendSharpness;                     ; Offset:   40
;           float _padding;                           ; Offset:   44
;       
;       } GrassLayer;                                 ; Offset:    0
;
;       struct struct.MaterialLayer
;       {
;
;           float4 Albedo;                            ; Offset:   48
;           float Metallic;                           ; Offset:   64
;           float Roughness;                          ; Offset:   68
;           float NormalStrength;                     ; Offset:   72
;           float TilingScale;                        ; Offset:   76
;           float2 Offset;                            ; Offset:   80
;           float BlendSharpness;                     ; Offset:   88
;           float _padding;                           ; Offset:   92
;       
;       } RockLayer;                                  ; Offset:   48
;
;       struct struct.MaterialLayer
;       {
;
;           float4 Albedo;                            ; Offset:   96
;           float Metallic;                           ; Offset:  112
;           float Roughness;                          ; Offset:  116
;           float NormalStrength;                     ; Offset:  120
;           float TilingScale;                        ; Offset:  124
;           float2 Offset;                            ; Offset:  128
;           float BlendSharpness;                     ; Offset:  136
;           float _padding;                           ; Offset:  140
;       
;       } SnowLayer;                                  ; Offset:   96
;
;       struct struct.MaterialLayer
;       {
;
;           float4 Albedo;                            ; Offset:  144
;           float Metallic;                           ; Offset:  160
;           float Roughness;                          ; Offset:  164
;           float NormalStrength;                     ; Offset:  168
;           float TilingScale;                        ; Offset:  172
;           float2 Offset;                            ; Offset:  176
;           float BlendSharpness;                     ; Offset:  184
;           float _padding;                           ; Offset:  188
;       
;       } SandLayer;                                  ; Offset:  144
;
;       float3 FogColor;                              ; Offset:  192
;       float FogDensity;                             ; Offset:  204
;       float FogStart;                               ; Offset:  208
;       float FogEnd;                                 ; Offset:  212
;       float2 WindDirection;                         ; Offset:  216
;       float WindStrength;                           ; Offset:  224
;       float _padding2;                              ; Offset:  228
;   
;   } MaterialParams;                                 ; Offset:    0 Size:   232
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
; MaterialParams                    cbuffer      NA          NA     CB1            cb1     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; TrilinearSampler                  sampler      NA          NA      S1             s1     1
; ShadowSampler                     sampler      NA          NA      S2             s2     1
; AlbedoTextures                    texture     f32     2darray      T0             t0     1
; NormalTextures                    texture     f32     2darray      T1             t1     1
; RoughnessTextures                 texture     f32     2darray      T2             t2     1
; SplatmapTexture                   texture     f32          2d      T3             t5     1
; DetailNormalTexture               texture     f32          2d      T4             t6     1
; SkyboxTexture                     texture     f32        cube      T5             t7     1
; ShadowMap                         texture     f32          2d      T6             t8     1
;
;
; ViewId state:
;
; Number of inputs: 36, outputs: 16
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 28, 29, 30, 32, 33, 34, 35 }
;   output 1 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 28, 29, 30, 32, 33, 34, 35 }
;   output 2 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 23, 28, 29, 30, 32, 33, 34, 35 }
;   output 3 depends on inputs: { 7, 11, 20, 21 }
;   output 4 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23 }
;   output 5 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23 }
;   output 6 depends on inputs: { 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 20, 21, 22, 23 }
;   output 7 depends on inputs: { 7, 11, 20, 21 }
;   output 8 depends on inputs: { 7, 11, 20, 21 }
;   output 9 depends on inputs: { 7, 11, 20, 21 }
;   output 10 depends on inputs: { 7, 11, 20, 21 }
;   output 11 depends on inputs: { 7 }
;   output 14 depends on inputs: { 2, 3 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.Texture2DArray<vector<float, 4> >" = type { <4 x float>, %"class.Texture2DArray<vector<float, 4> >::mips_type" }
%"class.Texture2DArray<vector<float, 4> >::mips_type" = type { i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.TextureCube<vector<float, 4> >" = type { <4 x float> }
%hostlayout.PerFrame = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, <3 x float>, float, <2 x float>, float, float, float }
%MaterialParams = type { %struct.MaterialLayer, %struct.MaterialLayer, %struct.MaterialLayer, %struct.MaterialLayer, <3 x float>, float, float, float, <2 x float>, float, float }
%struct.MaterialLayer = type { <4 x float>, float, float, float, float, <2 x float>, float, float }
%struct.SamplerState = type { i32 }
%struct.SamplerComparisonState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 6, i32 8, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 5, i32 7, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 4, i32 6, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 5, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %9 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %10 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %11 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %12 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 12, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 11, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 11, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 11, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 11, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 10, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 9, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 8, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %27 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %28 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %29 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %30 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %31 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %32 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %33 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %34 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %35 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %36 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %37 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %38 = call float @dx.op.dot3.f32(i32 55, float %33, float %34, float %35, float %33, float %34, float %35)  ; Dot3(ax,ay,az,bx,by,bz)
  %39 = call float @dx.op.unary.f32(i32 25, float %38)  ; Rsqrt(value)
  %40 = fmul fast float %39, %33
  %41 = fmul fast float %39, %34
  %42 = fmul fast float %39, %35
  %43 = call float @dx.op.dot3.f32(i32 55, float %30, float %31, float %32, float %30, float %31, float %32)  ; Dot3(ax,ay,az,bx,by,bz)
  %44 = call float @dx.op.unary.f32(i32 25, float %43)  ; Rsqrt(value)
  %45 = call float @dx.op.dot3.f32(i32 55, float %27, float %28, float %29, float %27, float %28, float %29)  ; Dot3(ax,ay,az,bx,by,bz)
  %46 = call float @dx.op.unary.f32(i32 25, float %45)  ; Rsqrt(value)
  %47 = fmul fast float %46, %27
  %48 = fmul fast float %46, %28
  %49 = fmul fast float %46, %29
  %50 = call float @dx.op.dot3.f32(i32 55, float %18, float %19, float %20, float %18, float %19, float %20)  ; Dot3(ax,ay,az,bx,by,bz)
  %51 = call float @dx.op.unary.f32(i32 25, float %50)  ; Rsqrt(value)
  %52 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %10, float %25, float %26, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %53 = extractvalue %dx.types.ResRet.f32 %52, 0
  %54 = extractvalue %dx.types.ResRet.f32 %52, 1
  %55 = extractvalue %dx.types.ResRet.f32 %52, 2
  %56 = extractvalue %dx.types.ResRet.f32 %52, 3
  %57 = fmul fast float %21, 2.000000e+00
  %58 = fsub fast float 1.000000e+00, %57
  %59 = call float @dx.op.unary.f32(i32 7, float %58)  ; Saturate(value)
  %60 = fmul fast float %53, %59
  %61 = fadd fast float %21, 0xBFD3333340000000
  %62 = fmul fast float %61, 2.500000e+00
  %63 = call float @dx.op.unary.f32(i32 7, float %62)  ; Saturate(value)
  %64 = fmul fast float %63, %54
  %65 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 14)  ; CBufferLoadLegacy(handle,regIndex)
  %66 = extractvalue %dx.types.CBufRet.f32 %65, 2
  %67 = fmul fast float %66, 0x3FE6666660000000
  %68 = fsub fast float %22, %67
  %69 = fmul fast float %66, 0x3FD3333340000000
  %70 = fdiv fast float %68, %69
  %71 = call float @dx.op.unary.f32(i32 7, float %70)  ; Saturate(value)
  %72 = fmul fast float %71, %55
  %73 = fdiv fast float %22, %69
  %74 = fsub fast float 1.000000e+00, %73
  %75 = call float @dx.op.unary.f32(i32 7, float %74)  ; Saturate(value)
  %76 = fmul fast float %75, %56
  %77 = call float @dx.op.dot4.f32(i32 56, float %60, float %64, float %72, float %76, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00)  ; Dot4(ax,ay,az,aw,bx,by,bz,bw)
  %78 = fcmp fast ogt float %77, 0.000000e+00
  br i1 %78, label %79, label %84

; <label>:79                                      ; preds = %0
  %80 = fdiv fast float %60, %77
  %81 = fdiv fast float %64, %77
  %82 = fdiv fast float %72, %77
  %83 = fdiv fast float %76, %77
  br label %84

; <label>:84                                      ; preds = %79, %0
  %85 = phi float [ %80, %79 ], [ 1.000000e+00, %0 ]
  %86 = phi float [ %81, %79 ], [ 0.000000e+00, %0 ]
  %87 = phi float [ %82, %79 ], [ 0.000000e+00, %0 ]
  %88 = phi float [ %83, %79 ], [ 0.000000e+00, %0 ]
  %89 = fcmp fast ogt float %85, 0.000000e+00
  br i1 %89, label %90, label %158

; <label>:90                                      ; preds = %84
  %91 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %92 = extractvalue %dx.types.CBufRet.f32 %91, 3
  %93 = fmul fast float %92, %25
  %94 = fmul fast float %92, %26
  %95 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %96 = extractvalue %dx.types.CBufRet.f32 %95, 0
  %97 = extractvalue %dx.types.CBufRet.f32 %95, 1
  %98 = fadd fast float %93, %96
  %99 = fadd fast float %94, %97
  %100 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 13)  ; CBufferLoadLegacy(handle,regIndex)
  %101 = extractvalue %dx.types.CBufRet.f32 %100, 2
  %102 = extractvalue %dx.types.CBufRet.f32 %100, 3
  %103 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 14)  ; CBufferLoadLegacy(handle,regIndex)
  %104 = extractvalue %dx.types.CBufRet.f32 %103, 0
  %105 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %106 = extractvalue %dx.types.CBufRet.f32 %105, 3
  %107 = fmul fast float %98, 1.000000e+01
  %108 = fadd fast float %106, %107
  %109 = call float @dx.op.unary.f32(i32 13, float %108)  ; Sin(value)
  %110 = fmul fast float %104, 0x3F847AE140000000
  %111 = fmul fast float %109, %110
  %112 = fmul fast float %111, %101
  %113 = fmul fast float %111, %102
  %114 = fadd fast float %112, %98
  %115 = fadd fast float %113, %99
  %116 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %9, float %114, float %115, float 0.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %117 = extractvalue %dx.types.ResRet.f32 %116, 0
  %118 = extractvalue %dx.types.ResRet.f32 %116, 1
  %119 = extractvalue %dx.types.ResRet.f32 %116, 2
  %120 = extractvalue %dx.types.ResRet.f32 %116, 3
  %121 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %122 = extractvalue %dx.types.CBufRet.f32 %121, 0
  %123 = extractvalue %dx.types.CBufRet.f32 %121, 1
  %124 = extractvalue %dx.types.CBufRet.f32 %121, 2
  %125 = extractvalue %dx.types.CBufRet.f32 %121, 3
  %126 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %9, float %98, float %99, float 0.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %127 = extractvalue %dx.types.ResRet.f32 %126, 0
  %128 = extractvalue %dx.types.ResRet.f32 %126, 1
  %129 = extractvalue %dx.types.ResRet.f32 %126, 2
  %130 = fmul fast float %127, 2.000000e+00
  %131 = fmul fast float %128, 2.000000e+00
  %132 = fmul fast float %129, 2.000000e+00
  %133 = fadd fast float %130, -1.000000e+00
  %134 = fadd fast float %131, -1.000000e+00
  %135 = fadd fast float %132, -1.000000e+00
  %136 = extractvalue %dx.types.CBufRet.f32 %91, 2
  %137 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %9, float %98, float %99, float 0.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %138 = extractvalue %dx.types.ResRet.f32 %137, 0
  %139 = extractvalue %dx.types.ResRet.f32 %137, 1
  %140 = extractvalue %dx.types.CBufRet.f32 %91, 1
  %141 = fmul fast float %117, %85
  %142 = fmul fast float %141, %122
  %143 = fmul fast float %118, %85
  %144 = fmul fast float %143, %123
  %145 = fmul fast float %119, %85
  %146 = fmul fast float %145, %124
  %147 = fmul fast float %120, %85
  %148 = fmul fast float %147, %125
  %149 = fmul fast float %136, %85
  %150 = fmul fast float %133, %149
  %151 = fmul fast float %134, %149
  %152 = fmul fast float %135, %149
  %153 = fmul fast float %140, %85
  %154 = fmul fast float %153, %138
  %155 = fmul fast float %139, %85
  %156 = extractvalue %dx.types.CBufRet.f32 %91, 0
  %157 = fmul fast float %156, %85
  br label %158

; <label>:158                                     ; preds = %90, %84
  %159 = phi float [ %142, %90 ], [ 0.000000e+00, %84 ]
  %160 = phi float [ %144, %90 ], [ 0.000000e+00, %84 ]
  %161 = phi float [ %146, %90 ], [ 0.000000e+00, %84 ]
  %162 = phi float [ %148, %90 ], [ 0.000000e+00, %84 ]
  %163 = phi float [ %150, %90 ], [ 0.000000e+00, %84 ]
  %164 = phi float [ %151, %90 ], [ 0.000000e+00, %84 ]
  %165 = phi float [ %152, %90 ], [ 0.000000e+00, %84 ]
  %166 = phi float [ %154, %90 ], [ 0.000000e+00, %84 ]
  %167 = phi float [ %155, %90 ], [ 0.000000e+00, %84 ]
  %168 = phi float [ %157, %90 ], [ 0.000000e+00, %84 ]
  %169 = fcmp fast ogt float %86, 0.000000e+00
  br i1 %169, label %170, label %232

; <label>:170                                     ; preds = %158
  %171 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %172 = extractvalue %dx.types.CBufRet.f32 %171, 3
  %173 = fmul fast float %172, %25
  %174 = fmul fast float %172, %26
  %175 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %176 = extractvalue %dx.types.CBufRet.f32 %175, 0
  %177 = extractvalue %dx.types.CBufRet.f32 %175, 1
  %178 = fadd fast float %173, %176
  %179 = fadd fast float %174, %177
  %180 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %9, float %178, float %179, float 1.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %181 = extractvalue %dx.types.ResRet.f32 %180, 0
  %182 = extractvalue %dx.types.ResRet.f32 %180, 1
  %183 = extractvalue %dx.types.ResRet.f32 %180, 2
  %184 = extractvalue %dx.types.ResRet.f32 %180, 3
  %185 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %186 = extractvalue %dx.types.CBufRet.f32 %185, 0
  %187 = extractvalue %dx.types.CBufRet.f32 %185, 1
  %188 = extractvalue %dx.types.CBufRet.f32 %185, 2
  %189 = extractvalue %dx.types.CBufRet.f32 %185, 3
  %190 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %9, float %178, float %179, float 1.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %191 = extractvalue %dx.types.ResRet.f32 %190, 0
  %192 = extractvalue %dx.types.ResRet.f32 %190, 1
  %193 = extractvalue %dx.types.ResRet.f32 %190, 2
  %194 = fmul fast float %191, 2.000000e+00
  %195 = fmul fast float %192, 2.000000e+00
  %196 = fmul fast float %193, 2.000000e+00
  %197 = fadd fast float %194, -1.000000e+00
  %198 = fadd fast float %195, -1.000000e+00
  %199 = fadd fast float %196, -1.000000e+00
  %200 = extractvalue %dx.types.CBufRet.f32 %171, 2
  %201 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %9, float %178, float %179, float 1.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %202 = extractvalue %dx.types.ResRet.f32 %201, 0
  %203 = extractvalue %dx.types.ResRet.f32 %201, 1
  %204 = extractvalue %dx.types.CBufRet.f32 %171, 1
  %205 = fmul fast float %181, %86
  %206 = fmul fast float %205, %186
  %207 = fmul fast float %182, %86
  %208 = fmul fast float %207, %187
  %209 = fmul fast float %183, %86
  %210 = fmul fast float %209, %188
  %211 = fmul fast float %184, %86
  %212 = fmul fast float %211, %189
  %213 = fadd fast float %206, %159
  %214 = fadd fast float %208, %160
  %215 = fadd fast float %210, %161
  %216 = fadd fast float %212, %162
  %217 = fmul fast float %200, %86
  %218 = fmul fast float %197, %217
  %219 = fmul fast float %198, %217
  %220 = fmul fast float %199, %217
  %221 = fadd fast float %218, %163
  %222 = fadd fast float %219, %164
  %223 = fadd fast float %220, %165
  %224 = fmul fast float %204, %86
  %225 = fmul fast float %224, %202
  %226 = fadd fast float %225, %166
  %227 = fmul fast float %203, %86
  %228 = fadd fast float %227, %167
  %229 = extractvalue %dx.types.CBufRet.f32 %171, 0
  %230 = fmul fast float %229, %86
  %231 = fadd fast float %230, %168
  br label %232

; <label>:232                                     ; preds = %170, %158
  %233 = phi float [ %213, %170 ], [ %159, %158 ]
  %234 = phi float [ %214, %170 ], [ %160, %158 ]
  %235 = phi float [ %215, %170 ], [ %161, %158 ]
  %236 = phi float [ %216, %170 ], [ %162, %158 ]
  %237 = phi float [ %221, %170 ], [ %163, %158 ]
  %238 = phi float [ %222, %170 ], [ %164, %158 ]
  %239 = phi float [ %223, %170 ], [ %165, %158 ]
  %240 = phi float [ %226, %170 ], [ %166, %158 ]
  %241 = phi float [ %228, %170 ], [ %167, %158 ]
  %242 = phi float [ %231, %170 ], [ %168, %158 ]
  %243 = fcmp fast ogt float %87, 0.000000e+00
  br i1 %243, label %244, label %306

; <label>:244                                     ; preds = %232
  %245 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 7)  ; CBufferLoadLegacy(handle,regIndex)
  %246 = extractvalue %dx.types.CBufRet.f32 %245, 3
  %247 = fmul fast float %246, %25
  %248 = fmul fast float %246, %26
  %249 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %250 = extractvalue %dx.types.CBufRet.f32 %249, 0
  %251 = extractvalue %dx.types.CBufRet.f32 %249, 1
  %252 = fadd fast float %247, %250
  %253 = fadd fast float %248, %251
  %254 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %9, float %252, float %253, float 2.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %255 = extractvalue %dx.types.ResRet.f32 %254, 0
  %256 = extractvalue %dx.types.ResRet.f32 %254, 1
  %257 = extractvalue %dx.types.ResRet.f32 %254, 2
  %258 = extractvalue %dx.types.ResRet.f32 %254, 3
  %259 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %260 = extractvalue %dx.types.CBufRet.f32 %259, 0
  %261 = extractvalue %dx.types.CBufRet.f32 %259, 1
  %262 = extractvalue %dx.types.CBufRet.f32 %259, 2
  %263 = extractvalue %dx.types.CBufRet.f32 %259, 3
  %264 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %9, float %252, float %253, float 2.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %265 = extractvalue %dx.types.ResRet.f32 %264, 0
  %266 = extractvalue %dx.types.ResRet.f32 %264, 1
  %267 = extractvalue %dx.types.ResRet.f32 %264, 2
  %268 = fmul fast float %265, 2.000000e+00
  %269 = fmul fast float %266, 2.000000e+00
  %270 = fmul fast float %267, 2.000000e+00
  %271 = fadd fast float %268, -1.000000e+00
  %272 = fadd fast float %269, -1.000000e+00
  %273 = fadd fast float %270, -1.000000e+00
  %274 = extractvalue %dx.types.CBufRet.f32 %245, 2
  %275 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %9, float %252, float %253, float 2.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %276 = extractvalue %dx.types.ResRet.f32 %275, 0
  %277 = extractvalue %dx.types.ResRet.f32 %275, 1
  %278 = extractvalue %dx.types.CBufRet.f32 %245, 1
  %279 = fmul fast float %255, %87
  %280 = fmul fast float %279, %260
  %281 = fmul fast float %256, %87
  %282 = fmul fast float %281, %261
  %283 = fmul fast float %257, %87
  %284 = fmul fast float %283, %262
  %285 = fmul fast float %258, %87
  %286 = fmul fast float %285, %263
  %287 = fadd fast float %280, %233
  %288 = fadd fast float %282, %234
  %289 = fadd fast float %284, %235
  %290 = fadd fast float %286, %236
  %291 = fmul fast float %274, %87
  %292 = fmul fast float %271, %291
  %293 = fmul fast float %272, %291
  %294 = fmul fast float %273, %291
  %295 = fadd fast float %292, %237
  %296 = fadd fast float %293, %238
  %297 = fadd fast float %294, %239
  %298 = fmul fast float %278, %87
  %299 = fmul fast float %298, %276
  %300 = fadd fast float %299, %240
  %301 = fmul fast float %277, %87
  %302 = fadd fast float %301, %241
  %303 = extractvalue %dx.types.CBufRet.f32 %245, 0
  %304 = fmul fast float %303, %87
  %305 = fadd fast float %304, %242
  br label %306

; <label>:306                                     ; preds = %244, %232
  %307 = phi float [ %287, %244 ], [ %233, %232 ]
  %308 = phi float [ %288, %244 ], [ %234, %232 ]
  %309 = phi float [ %289, %244 ], [ %235, %232 ]
  %310 = phi float [ %290, %244 ], [ %236, %232 ]
  %311 = phi float [ %295, %244 ], [ %237, %232 ]
  %312 = phi float [ %296, %244 ], [ %238, %232 ]
  %313 = phi float [ %297, %244 ], [ %239, %232 ]
  %314 = phi float [ %300, %244 ], [ %240, %232 ]
  %315 = phi float [ %302, %244 ], [ %241, %232 ]
  %316 = phi float [ %305, %244 ], [ %242, %232 ]
  %317 = fcmp fast ogt float %88, 0.000000e+00
  br i1 %317, label %318, label %380

; <label>:318                                     ; preds = %306
  %319 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %320 = extractvalue %dx.types.CBufRet.f32 %319, 3
  %321 = fmul fast float %320, %25
  %322 = fmul fast float %320, %26
  %323 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %324 = extractvalue %dx.types.CBufRet.f32 %323, 0
  %325 = extractvalue %dx.types.CBufRet.f32 %323, 1
  %326 = fadd fast float %321, %324
  %327 = fadd fast float %322, %325
  %328 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %7, %dx.types.Handle %9, float %326, float %327, float 3.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %329 = extractvalue %dx.types.ResRet.f32 %328, 0
  %330 = extractvalue %dx.types.ResRet.f32 %328, 1
  %331 = extractvalue %dx.types.ResRet.f32 %328, 2
  %332 = extractvalue %dx.types.ResRet.f32 %328, 3
  %333 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %334 = extractvalue %dx.types.CBufRet.f32 %333, 0
  %335 = extractvalue %dx.types.CBufRet.f32 %333, 1
  %336 = extractvalue %dx.types.CBufRet.f32 %333, 2
  %337 = extractvalue %dx.types.CBufRet.f32 %333, 3
  %338 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %6, %dx.types.Handle %9, float %326, float %327, float 3.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %339 = extractvalue %dx.types.ResRet.f32 %338, 0
  %340 = extractvalue %dx.types.ResRet.f32 %338, 1
  %341 = extractvalue %dx.types.ResRet.f32 %338, 2
  %342 = fmul fast float %339, 2.000000e+00
  %343 = fmul fast float %340, 2.000000e+00
  %344 = fmul fast float %341, 2.000000e+00
  %345 = fadd fast float %342, -1.000000e+00
  %346 = fadd fast float %343, -1.000000e+00
  %347 = fadd fast float %344, -1.000000e+00
  %348 = extractvalue %dx.types.CBufRet.f32 %319, 2
  %349 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %9, float %326, float %327, float 3.000000e+00, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %350 = extractvalue %dx.types.ResRet.f32 %349, 0
  %351 = extractvalue %dx.types.ResRet.f32 %349, 1
  %352 = extractvalue %dx.types.CBufRet.f32 %319, 1
  %353 = fmul fast float %329, %88
  %354 = fmul fast float %353, %334
  %355 = fmul fast float %330, %88
  %356 = fmul fast float %355, %335
  %357 = fmul fast float %331, %88
  %358 = fmul fast float %357, %336
  %359 = fmul fast float %332, %88
  %360 = fmul fast float %359, %337
  %361 = fadd fast float %354, %307
  %362 = fadd fast float %356, %308
  %363 = fadd fast float %358, %309
  %364 = fadd fast float %360, %310
  %365 = fmul fast float %348, %88
  %366 = fmul fast float %345, %365
  %367 = fmul fast float %346, %365
  %368 = fmul fast float %347, %365
  %369 = fadd fast float %366, %311
  %370 = fadd fast float %367, %312
  %371 = fadd fast float %368, %313
  %372 = fmul fast float %352, %88
  %373 = fmul fast float %372, %350
  %374 = fadd fast float %373, %314
  %375 = fmul fast float %351, %88
  %376 = fadd fast float %375, %315
  %377 = extractvalue %dx.types.CBufRet.f32 %319, 0
  %378 = fmul fast float %377, %88
  %379 = fadd fast float %378, %316
  br label %380

; <label>:380                                     ; preds = %318, %306
  %381 = phi float [ %361, %318 ], [ %307, %306 ]
  %382 = phi float [ %362, %318 ], [ %308, %306 ]
  %383 = phi float [ %363, %318 ], [ %309, %306 ]
  %384 = phi float [ %364, %318 ], [ %310, %306 ]
  %385 = phi float [ %369, %318 ], [ %311, %306 ]
  %386 = phi float [ %370, %318 ], [ %312, %306 ]
  %387 = phi float [ %371, %318 ], [ %313, %306 ]
  %388 = phi float [ %374, %318 ], [ %314, %306 ]
  %389 = phi float [ %376, %318 ], [ %315, %306 ]
  %390 = phi float [ %379, %318 ], [ %316, %306 ]
  %391 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %9, float %23, float %24, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %392 = extractvalue %dx.types.ResRet.f32 %391, 0
  %393 = extractvalue %dx.types.ResRet.f32 %391, 1
  %394 = extractvalue %dx.types.ResRet.f32 %391, 2
  %395 = fmul fast float %392, 2.000000e+00
  %396 = fmul fast float %393, 2.000000e+00
  %397 = fmul fast float %394, 2.000000e+00
  %398 = fadd fast float %395, -1.000000e+00
  %399 = fadd fast float %396, -1.000000e+00
  %400 = fadd fast float %397, -1.000000e+00
  %401 = fadd fast float %387, 1.000000e+00
  %402 = fsub fast float -0.000000e+00, %398
  %403 = fsub fast float -0.000000e+00, %399
  %404 = fdiv fast float %385, %401
  %405 = fdiv fast float %386, %401
  %406 = call float @dx.op.dot3.f32(i32 55, float %385, float %386, float %401, float %402, float %403, float %400)  ; Dot3(ax,ay,az,bx,by,bz)
  %407 = fmul fast float %406, %404
  %408 = fmul fast float %406, %405
  %409 = fsub fast float %398, %385
  %410 = fadd fast float %409, %407
  %411 = fsub fast float %399, %386
  %412 = fadd fast float %411, %408
  %413 = fsub fast float -0.000000e+00, %387
  %414 = fsub fast float %413, %400
  %415 = fadd fast float %414, %406
  %416 = fmul fast float %410, 5.000000e-01
  %417 = fmul fast float %412, 5.000000e-01
  %418 = fmul fast float %415, 5.000000e-01
  %419 = fadd fast float %416, %385
  %420 = fadd fast float %417, %386
  %421 = fadd fast float %418, %387
  %422 = call float @dx.op.dot3.f32(i32 55, float %419, float %420, float %421, float %419, float %420, float %421)  ; Dot3(ax,ay,az,bx,by,bz)
  %423 = call float @dx.op.unary.f32(i32 25, float %422)  ; Rsqrt(value)
  %424 = fmul fast float %420, %423
  %425 = fmul fast float %421, %423
  %426 = fmul fast float %423, %44
  %427 = fmul fast float %426, %419
  %428 = fmul fast float %427, %30
  %429 = call float @dx.op.tertiary.f32(i32 46, float %424, float %47, float %428)  ; FMad(a,b,c)
  %430 = call float @dx.op.tertiary.f32(i32 46, float %425, float %40, float %429)  ; FMad(a,b,c)
  %431 = fmul fast float %427, %31
  %432 = call float @dx.op.tertiary.f32(i32 46, float %424, float %48, float %431)  ; FMad(a,b,c)
  %433 = call float @dx.op.tertiary.f32(i32 46, float %425, float %41, float %432)  ; FMad(a,b,c)
  %434 = fmul fast float %427, %32
  %435 = call float @dx.op.tertiary.f32(i32 46, float %424, float %49, float %434)  ; FMad(a,b,c)
  %436 = call float @dx.op.tertiary.f32(i32 46, float %425, float %42, float %435)  ; FMad(a,b,c)
  %437 = call float @dx.op.dot3.f32(i32 55, float %430, float %433, float %436, float %430, float %433, float %436)  ; Dot3(ax,ay,az,bx,by,bz)
  %438 = call float @dx.op.unary.f32(i32 25, float %437)  ; Rsqrt(value)
  %439 = fmul fast float %438, %430
  %440 = fmul fast float %438, %433
  %441 = fmul fast float %438, %436
  %442 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %12, i32 13)  ; CBufferLoadLegacy(handle,regIndex)
  %443 = extractvalue %dx.types.CBufRet.f32 %442, 0
  %444 = extractvalue %dx.types.CBufRet.f32 %442, 1
  %445 = extractvalue %dx.types.CBufRet.f32 %442, 2
  %446 = fsub fast float -0.000000e+00, %443
  %447 = fsub fast float -0.000000e+00, %444
  %448 = fsub fast float -0.000000e+00, %445
  %449 = call float @dx.op.dot3.f32(i32 55, float %446, float %447, float %448, float %446, float %447, float %448)  ; Dot3(ax,ay,az,bx,by,bz)
  %450 = call float @dx.op.unary.f32(i32 25, float %449)  ; Rsqrt(value)
  %451 = fmul fast float %450, %446
  %452 = fmul fast float %450, %447
  %453 = fmul fast float %450, %448
  %454 = call float @dx.op.dot3.f32(i32 55, float %439, float %440, float %441, float %451, float %452, float %453)  ; Dot3(ax,ay,az,bx,by,bz)
  %455 = call float @dx.op.binary.f32(i32 35, float %454, float 0.000000e+00)  ; FMax(a,b)
  %456 = fdiv fast float %14, %17
  %457 = fdiv fast float %15, %17
  %458 = fdiv fast float %16, %17
  %459 = fmul fast float %456, 5.000000e-01
  %460 = fmul fast float %457, 5.000000e-01
  %461 = fmul fast float %458, 5.000000e-01
  %462 = fadd fast float %459, 5.000000e-01
  %463 = fadd fast float %460, 5.000000e-01
  %464 = fadd fast float %461, 5.000000e-01
  %465 = fcmp fast olt float %462, 0.000000e+00
  %466 = fcmp fast ogt float %462, 1.000000e+00
  %467 = or i1 %465, %466
  %468 = fcmp fast olt float %463, 0.000000e+00
  %469 = or i1 %467, %468
  %470 = fcmp fast ogt float %463, 1.000000e+00
  %471 = or i1 %470, %469
  br i1 %471, label %494, label %472

; <label>:472                                     ; preds = %380
  br label %473

; <label>:473                                     ; preds = %473, %472
  %474 = phi float [ %489, %473 ], [ 0.000000e+00, %472 ]
  %475 = phi i32 [ %490, %473 ], [ -1, %472 ]
  %476 = sitofp i32 %475 to float
  %477 = fmul fast float %476, 0x3F40000000000000
  %478 = fadd fast float %477, %462
  %479 = fadd fast float %460, 0x3FDFF80000000000
  %480 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %8, float %478, float %479, float undef, float undef, i32 0, i32 0, i32 undef, float %464)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %481 = extractvalue %dx.types.ResRet.f32 %480, 0
  %482 = fadd fast float %481, %474
  %483 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %8, float %478, float %463, float undef, float undef, i32 0, i32 0, i32 undef, float %464)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %484 = extractvalue %dx.types.ResRet.f32 %483, 0
  %485 = fadd fast float %482, %484
  %486 = fadd fast float %460, 0x3FE0040000000000
  %487 = call %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32 65, %dx.types.Handle %1, %dx.types.Handle %8, float %478, float %486, float undef, float undef, i32 0, i32 0, i32 undef, float %464)  ; SampleCmpLevelZero(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,compareValue)
  %488 = extractvalue %dx.types.ResRet.f32 %487, 0
  %489 = fadd fast float %485, %488
  %490 = add nsw i32 %475, 1
  %491 = icmp eq i32 %490, 2
  br i1 %491, label %492, label %473

; <label>:492                                     ; preds = %473
  %493 = fmul fast float %489, 0x3FBC71C720000000
  br label %494

; <label>:494                                     ; preds = %492, %380
  %495 = phi float [ %493, %492 ], [ 1.000000e+00, %380 ]
  %496 = fmul fast float %18, %51
  %497 = fsub fast float -0.000000e+00, %496
  %498 = fmul fast float %19, %51
  %499 = fsub fast float -0.000000e+00, %498
  %500 = fmul fast float %20, %51
  %501 = fsub fast float -0.000000e+00, %500
  %502 = call float @dx.op.dot3.f32(i32 55, float %497, float %499, float %501, float %439, float %440, float %441)  ; Dot3(ax,ay,az,bx,by,bz)
  %503 = fmul fast float %502, 2.000000e+00
  %504 = fmul fast float %503, %439
  %505 = fmul fast float %503, %440
  %506 = fmul fast float %503, %441
  %507 = fsub fast float %497, %504
  %508 = fsub fast float %499, %505
  %509 = fsub fast float %501, %506
  %510 = fmul fast float %388, 7.000000e+00
  %511 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %9, float %507, float %508, float %509, float undef, i32 undef, i32 undef, i32 undef, float %510)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %512 = extractvalue %dx.types.ResRet.f32 %511, 0
  %513 = extractvalue %dx.types.ResRet.f32 %511, 1
  %514 = extractvalue %dx.types.ResRet.f32 %511, 2
  %515 = fmul fast float %389, 0x3FB99999A0000000
  %516 = fmul fast float %389, 0x3FC3333340000000
  %517 = fmul fast float %495, %455
  %518 = fmul fast float %517, %381
  %519 = fmul fast float %517, %382
  %520 = fmul fast float %517, %383
  %521 = fsub fast float 1.000000e+00, %388
  %522 = fmul fast float %390, %521
  %523 = fmul fast float %512, %522
  %524 = fmul fast float %513, %522
  %525 = fmul fast float %514, %522
  %526 = fadd fast float %518, %515
  %527 = fadd fast float %519, %515
  %528 = fadd fast float %520, %516
  %529 = fadd fast float %526, %523
  %530 = fadd fast float %527, %524
  %531 = fadd fast float %528, %525
  %532 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 13)  ; CBufferLoadLegacy(handle,regIndex)
  %533 = extractvalue %dx.types.CBufRet.f32 %532, 0
  %534 = fsub fast float %13, %533
  %535 = extractvalue %dx.types.CBufRet.f32 %532, 1
  %536 = fsub fast float %535, %533
  %537 = fdiv fast float %534, %536
  %538 = call float @dx.op.unary.f32(i32 7, float %537)  ; Saturate(value)
  %539 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %11, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %540 = extractvalue %dx.types.CBufRet.f32 %539, 3
  %541 = fmul fast float %540, %538
  %542 = extractvalue %dx.types.CBufRet.f32 %539, 0
  %543 = extractvalue %dx.types.CBufRet.f32 %539, 1
  %544 = extractvalue %dx.types.CBufRet.f32 %539, 2
  %545 = fsub fast float %542, %529
  %546 = fsub fast float %543, %530
  %547 = fsub fast float %544, %531
  %548 = fmul fast float %545, %541
  %549 = fmul fast float %546, %541
  %550 = fmul fast float %547, %541
  %551 = fadd fast float %548, %529
  %552 = fadd fast float %549, %530
  %553 = fadd fast float %550, %531
  %554 = fmul fast float %439, 5.000000e-01
  %555 = fmul fast float %440, 5.000000e-01
  %556 = fmul fast float %441, 5.000000e-01
  %557 = fadd fast float %554, 5.000000e-01
  %558 = fadd fast float %555, 5.000000e-01
  %559 = fadd fast float %556, 5.000000e-01
  %560 = fdiv fast float %22, %66
  %561 = fdiv fast float %36, %37
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %551)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %552)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %553)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %384)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %557)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %558)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %559)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 3, float %388)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %390)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %388)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %389)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 3, float %560)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float 0.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float 0.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %561)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 3, float 1.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot4.f32(i32, float, float, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleCmpLevelZero.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!21}
!dx.entryPoints = !{!22}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !14, !17}
!5 = !{!6, !8, !9, !10, !11, !12, !13}
!6 = !{i32 0, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 7, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 7, i32 0, !7}
!9 = !{i32 2, %"class.Texture2DArray<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 7, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 5, i32 1, i32 2, i32 0, !7}
!11 = !{i32 4, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 6, i32 1, i32 2, i32 0, !7}
!12 = !{i32 5, %"class.TextureCube<vector<float, 4> >"* undef, !"", i32 0, i32 7, i32 1, i32 5, i32 0, !7}
!13 = !{i32 6, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 8, i32 1, i32 2, i32 0, !7}
!14 = !{!15, !16}
!15 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 244, null}
!16 = !{i32 1, %MaterialParams* undef, !"", i32 0, i32 1, i32 1, i32 232, null}
!17 = !{!18, !19, !20}
!18 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!19 = !{i32 1, %struct.SamplerState* undef, !"", i32 0, i32 1, i32 1, i32 0, null}
!20 = !{i32 2, %struct.SamplerComparisonState* undef, !"", i32 0, i32 2, i32 1, i32 1, null}
!21 = !{[38 x i32] [i32 36, i32 16, i32 0, i32 0, i32 16384, i32 16384, i32 0, i32 0, i32 0, i32 4095, i32 119, i32 119, i32 119, i32 2047, i32 119, i32 119, i32 119, i32 7, i32 119, i32 119, i32 119, i32 0, i32 2047, i32 2047, i32 119, i32 119, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 7, i32 7]}
!22 = !{void ()* @main, !"main", !23, !4, null}
!23 = !{!24, !54, null}
!24 = !{!25, !28, !29, !32, !34, !36, !39, !41, !42, !45, !47, !49, !52}
!25 = !{i32 0, !"SV_Position", i8 9, i8 3, !26, i8 4, i32 1, i8 4, i32 0, i8 0, !27}
!26 = !{i32 0}
!27 = !{i32 3, i32 12}
!28 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 1, i8 0, null}
!29 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 3, i32 2, i8 0, !31}
!30 = !{i32 1}
!31 = !{i32 3, i32 7}
!32 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !33, i8 2, i32 1, i8 3, i32 3, i8 0, !31}
!33 = !{i32 2}
!34 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 3, i32 4, i8 0, !31}
!35 = !{i32 3}
!36 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !37, i8 2, i32 1, i8 2, i32 5, i8 0, !38}
!37 = !{i32 4}
!38 = !{i32 3, i32 3}
!39 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !40, i8 2, i32 1, i8 2, i32 5, i8 2, !38}
!40 = !{i32 5}
!41 = !{i32 7, !"COLOR", i8 9, i8 0, !26, i8 2, i32 1, i8 4, i32 6, i8 0, null}
!42 = !{i32 8, !"TEXCOORD", i8 9, i8 0, !43, i8 2, i32 1, i8 1, i32 1, i8 3, !44}
!43 = !{i32 6}
!44 = !{i32 3, i32 1}
!45 = !{i32 9, !"TEXCOORD", i8 9, i8 0, !46, i8 2, i32 1, i8 1, i32 2, i8 3, !44}
!46 = !{i32 7}
!47 = !{i32 10, !"TEXCOORD", i8 9, i8 0, !48, i8 2, i32 1, i8 3, i32 7, i8 0, !31}
!48 = !{i32 8}
!49 = !{i32 11, !"TEXCOORD", i8 9, i8 0, !50, i8 2, i32 1, i8 4, i32 8, i8 0, !51}
!50 = !{i32 9}
!51 = !{i32 3, i32 15}
!52 = !{i32 12, !"TEXCOORD", i8 9, i8 0, !53, i8 2, i32 1, i8 1, i32 3, i8 3, !44}
!53 = !{i32 10}
!54 = !{!55, !56, !57, !58}
!55 = !{i32 0, !"SV_Target", i8 9, i8 16, !26, i8 0, i32 1, i8 4, i32 0, i8 0, !51}
!56 = !{i32 1, !"SV_Target", i8 9, i8 16, !30, i8 0, i32 1, i8 4, i32 1, i8 0, !51}
!57 = !{i32 2, !"SV_Target", i8 9, i8 16, !33, i8 0, i32 1, i8 4, i32 2, i8 0, !51}
!58 = !{i32 3, !"SV_Target", i8 9, i8 16, !35, i8 0, i32 1, i8 4, i32 3, i8 0, !51}
