#version 320 es

// Particle Simulation Compute Shader - OpenGL ES Version
// Tests compute shader performance with particle physics

layout(local_size_x = 64, local_size_y = 1, local_size_z = 1) in;

struct Particle
{
    vec3 position;
    float mass;
    vec3 velocity;
    float life;
    vec3 force;
    float size;
    vec4 color;
    float padding; // Ensure 16-byte alignment
};

layout(std430, binding = 0) restrict buffer ParticleBuffer
{
    Particle particles[];
};

uniform float uDeltaTime;
uniform vec3 uGravity;
uniform vec3 uWindForce;
uniform float uDamping;
uniform uint uParticleCount;
uniform vec3 uBoundsMin;
uniform vec3 uBoundsMax;
uniform float uGroundHeight;
uniform float uRestitution;

void main()
{
    uint index = gl_GlobalInvocationID.x;
    if (index >= uParticleCount)
        return;
    
    Particle particle = particles[index];
    
    // Skip dead particles
    if (particle.life <= 0.0)
        return;
    
    // Apply forces
    vec3 totalForce = particle.force;
    totalForce += uGravity * particle.mass;
    totalForce += uWindForce;
    
    // Update velocity using Verlet integration
    vec3 acceleration = totalForce / particle.mass;
    particle.velocity += acceleration * uDeltaTime;
    particle.velocity *= uDamping;
    
    // Update position
    particle.position += particle.velocity * uDeltaTime;
    
    // Collision detection with ground
    if (particle.position.y <= uGroundHeight)
    {
        particle.position.y = uGroundHeight;
        particle.velocity.y = -particle.velocity.y * uRestitution;
        
        // Apply friction
        particle.velocity.x *= 0.8;
        particle.velocity.z *= 0.8;
    }
    
    // Collision detection with bounds
    if (particle.position.x < uBoundsMin.x || particle.position.x > uBoundsMax.x)
    {
        particle.velocity.x = -particle.velocity.x * uRestitution;
        particle.position.x = clamp(particle.position.x, uBoundsMin.x, uBoundsMax.x);
    }
    
    if (particle.position.z < uBoundsMin.z || particle.position.z > uBoundsMax.z)
    {
        particle.velocity.z = -particle.velocity.z * uRestitution;
        particle.position.z = clamp(particle.position.z, uBoundsMin.z, uBoundsMax.z);
    }
    
    // Update life
    particle.life -= uDeltaTime;
    
    // Update color based on life (fade out)
    float lifeRatio = clamp(particle.life / 5.0, 0.0, 1.0); // Assuming max life is 5 seconds
    particle.color.a = lifeRatio;
    
    // Update size based on life
    particle.size = mix(0.1, 1.0, lifeRatio);
    
    // Reset force for next frame
    particle.force = vec3(0.0);
    
    // Write back to buffer
    particles[index] = particle;
}
