// Stress Test Compute Shader
// Tests compute shader performance with heavy workloads

cbuffer StressTestParams : register(b0)
{
    uint WorkGroupSize;
    uint TotalWorkItems;
    uint ComplexityLevel;
    uint TestType; // 0=Math, 1=Memory, 2=Texture, 3=Mixed
    float Time;
    float3 Padding;
};

RWStructuredBuffer<float4> InputBuffer : register(u0);
RWStructuredBuffer<float4> OutputBuffer : register(u1);
RWTexture2D<float4> OutputTexture : register(u2);
Texture2D<float4> InputTexture : register(t0);
SamplerState LinearSampler : register(s0);

[numthreads(64, 1, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
    uint index = id.x;
    if (index >= TotalWorkItems)
        return;
    
    float4 result = float4(0, 0, 0, 0);
    
    if (TestType == 0) // Mathematical stress test
    {
        float4 data = InputBuffer[index];
        
        // Heavy mathematical operations
        for(uint i = 0; i < ComplexityLevel; i++)
        {
            float t = Time + float(i) * 0.01;
            
            // Trigonometric functions
            result.x += sin(data.x * t) * cos(data.y * t);
            result.y += tan(data.z * t) * atan(data.w * t);
            
            // Exponential and logarithmic functions
            result.z += exp(data.x * 0.1) * log(abs(data.y) + 1.0);
            result.w += pow(abs(data.z), 2.5) * sqrt(abs(data.w) + 1.0);
            
            // Complex mathematical expressions
            float complex1 = sin(data.x + t) * cos(data.y + t) * exp(-data.z * 0.1);
            float complex2 = pow(abs(sin(data.w + t)), 3.0) * log(abs(data.x * data.y) + 1.0);
            
            result += float4(complex1, complex2, complex1 * complex2, complex1 + complex2);
            
            // Update data for next iteration
            data = data * 1.01 + result * 0.01;
        }
    }
    else if (TestType == 1) // Memory access stress test
    {
        // Random memory access patterns
        for(uint i = 0; i < ComplexityLevel; i++)
        {
            uint randomIndex = (index * 1664525 + 1013904223 + i) % TotalWorkItems;
            float4 data = InputBuffer[randomIndex];
            
            // Perform operations on loaded data
            result += data * sin(float(i) + Time);
            
            // Write back to different location
            uint writeIndex = (randomIndex * 1103515245 + 12345) % TotalWorkItems;
            OutputBuffer[writeIndex] = result * 0.1;
        }
    }
    else if (TestType == 2) // Texture sampling stress test
    {
        uint2 texSize;
        InputTexture.GetDimensions(texSize.x, texSize.y);
        
        for(uint i = 0; i < ComplexityLevel; i++)
        {
            float2 uv = float2(
                (float(index + i) / float(TotalWorkItems)),
                (sin(Time + float(i)) * 0.5 + 0.5)
            );
            
            // Multiple texture samples with different filtering
            result += InputTexture.SampleLevel(LinearSampler, uv, 0);
            result += InputTexture.SampleLevel(LinearSampler, uv * 2.0, 1);
            result += InputTexture.SampleLevel(LinearSampler, uv * 4.0, 2);
            result += InputTexture.SampleLevel(LinearSampler, uv * 8.0, 3);
            
            // Gradient-based sampling
            float2 ddx = float2(1.0 / texSize.x, 0);
            float2 ddy = float2(0, 1.0 / texSize.y);
            result += InputTexture.SampleGrad(LinearSampler, uv, ddx, ddy);
        }
        
        // Write to output texture
        uint2 outputCoord = uint2(index % texSize.x, index / texSize.x);
        if (outputCoord.y < texSize.y)
        {
            OutputTexture[outputCoord] = result / float(ComplexityLevel * 5);
        }
    }
    else if (TestType == 3) // Mixed workload stress test
    {
        float4 data = InputBuffer[index];
        
        for(uint i = 0; i < ComplexityLevel; i++)
        {
            // Mathematical operations
            float mathResult = sin(data.x + Time) * cos(data.y + Time);
            mathResult += pow(abs(data.z), 2.0) * exp(-data.w * 0.1);
            
            // Memory operations
            uint memIndex = (index + i * 17) % TotalWorkItems;
            float4 memData = InputBuffer[memIndex];
            
            // Texture operations
            float2 uv = float2(mathResult * 0.5 + 0.5, float(i) / float(ComplexityLevel));
            float4 texData = InputTexture.SampleLevel(LinearSampler, uv, 0);
            
            // Combine results
            result += float4(mathResult, 0, 0, 0) + memData * 0.1 + texData * 0.1;
            
            // Update data
            data = lerp(data, result, 0.1);
        }
        
        // Additional complex calculations
        result = normalize(result) * length(data);
        result += float4(
            sin(result.x * 10.0),
            cos(result.y * 10.0),
            tan(result.z * 5.0),
            atan(result.w * 5.0)
        );
    }
    
    // Final output
    OutputBuffer[index] = result;
}
