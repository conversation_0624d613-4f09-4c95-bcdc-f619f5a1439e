; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 501
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_RWStructuredBuffer_MeshVertex "type.RWStructuredBuffer.MeshVertex"
               OpName %MeshVertex "MeshVertex"
               OpMemberName %MeshVertex 0 "Position"
               OpMemberName %MeshVertex 1 "Normal"
               OpMemberName %MeshVertex 2 "TexCoord"
               OpMemberName %MeshVertex 3 "Color"
               OpName %VertexBuffer "VertexBuffer"
               OpName %type_RWStructuredBuffer_uint "type.RWStructuredBuffer.uint"
               OpName %IndexBuffer "IndexBuffer"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %VertexBuffer DescriptorSet 0
               OpDecorate %VertexBuffer Binding 0
               OpDecorate %IndexBuffer DescriptorSet 0
               OpDecorate %IndexBuffer Binding 1
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
               OpMemberDecorate %MeshVertex 0 Offset 0
               OpMemberDecorate %MeshVertex 1 Offset 16
               OpMemberDecorate %MeshVertex 2 Offset 32
               OpMemberDecorate %MeshVertex 3 Offset 48
               OpDecorate %_runtimearr_MeshVertex ArrayStride 64
               OpMemberDecorate %type_RWStructuredBuffer_MeshVertex 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_MeshVertex BufferBlock
               OpDecorate %_runtimearr_uint ArrayStride 4
               OpMemberDecorate %type_RWStructuredBuffer_uint 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_uint BufferBlock
       %uint = OpTypeInt 32 0
    %uint_64 = OpConstant %uint 64
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
     %uint_1 = OpConstant %uint 1
      %float = OpTypeFloat 32
  %float_100 = OpConstant %float 100
   %float_50 = OpConstant %float 50
    %float_0 = OpConstant %float 0
%float_0_00999999978 = OpConstant %float 0.00999999978
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
   %float_10 = OpConstant %float 10
%float_0_0500000007 = OpConstant %float 0.0500000007
    %float_2 = OpConstant %float 2
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
         %32 = OpConstantComposite %v3float %float_0 %float_1 %float_0
     %uint_0 = OpConstant %uint 0
      %false = OpConstantFalse %bool
   %float_n1 = OpConstant %float -1
         %36 = OpConstantComposite %v3float %float_n1 %float_0 %float_0
         %37 = OpConstantComposite %v3float %float_1 %float_0 %float_0
         %38 = OpConstantComposite %v3float %float_0 %float_0 %float_n1
         %39 = OpConstantComposite %v3float %float_0 %float_0 %float_1
  %float_0_5 = OpConstant %float 0.5
     %uint_6 = OpConstant %uint 6
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
     %uint_4 = OpConstant %uint 4
     %uint_5 = OpConstant %uint 5
    %float_3 = OpConstant %float 3
         %47 = OpConstantComposite %v3float %float_3 %float_3 %float_3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
    %v4float = OpTypeVector %float 4
 %MeshVertex = OpTypeStruct %v3float %v3float %v2float %v4float
%_runtimearr_MeshVertex = OpTypeRuntimeArray %MeshVertex
%type_RWStructuredBuffer_MeshVertex = OpTypeStruct %_runtimearr_MeshVertex
%_ptr_Uniform_type_RWStructuredBuffer_MeshVertex = OpTypePointer Uniform %type_RWStructuredBuffer_MeshVertex
%_runtimearr_uint = OpTypeRuntimeArray %uint
%type_RWStructuredBuffer_uint = OpTypeStruct %_runtimearr_uint
%_ptr_Uniform_type_RWStructuredBuffer_uint = OpTypePointer Uniform %type_RWStructuredBuffer_uint
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %63 = OpTypeFunction %void
     %v2uint = OpTypeVector %uint 2
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_MeshVertex = OpTypePointer Uniform %MeshVertex
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%VertexBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_MeshVertex Uniform
%IndexBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_uint Uniform
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
    %uint_63 = OpConstant %uint 63
%float_0_0158730168 = OpConstant %float 0.0158730168
         %70 = OpConstantComposite %v2float %float_0_0158730168 %float_0_0158730168
       %main = OpFunction %void None %63
         %71 = OpLabel
         %72 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %73 None
               OpSwitch %uint_0 %74
         %74 = OpLabel
         %75 = OpCompositeExtract %uint %72 0
         %76 = OpUGreaterThanEqual %bool %75 %uint_64
         %77 = OpLogicalNot %bool %76
               OpSelectionMerge %78 None
               OpBranchConditional %77 %79 %78
         %79 = OpLabel
         %80 = OpCompositeExtract %uint %72 1
         %81 = OpUGreaterThanEqual %bool %80 %uint_64
               OpBranch %78
         %78 = OpLabel
         %82 = OpPhi %bool %true %74 %81 %79
               OpSelectionMerge %83 None
               OpBranchConditional %82 %84 %83
         %84 = OpLabel
               OpBranch %73
         %83 = OpLabel
         %85 = OpCompositeExtract %uint %72 1
         %86 = OpIMul %uint %85 %uint_64
         %87 = OpIAdd %uint %86 %75
         %88 = OpVectorShuffle %v2uint %72 %72 0 1
         %89 = OpConvertUToF %v2float %88
         %90 = OpFMul %v2float %89 %70
         %91 = OpCompositeExtract %float %90 0
         %92 = OpFMul %float %91 %float_100
         %93 = OpFSub %float %92 %float_50
         %94 = OpCompositeExtract %float %90 1
         %95 = OpFMul %float %94 %float_100
         %96 = OpFSub %float %95 %float_50
         %97 = OpCompositeConstruct %v3float %93 %float_0 %96
         %98 = OpVectorTimesScalar %v3float %97 %float_0_00999999978
         %99 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
        %100 = OpLoad %float %99
        %101 = OpFMul %float %100 %float_0_100000001
        %102 = OpCompositeConstruct %v3float %101 %101 %101
        %103 = OpFAdd %v3float %98 %102
        %104 = OpExtInst %v3float %1 Floor %103
        %105 = OpExtInst %v3float %1 Fract %103
        %106 = OpFMul %v3float %105 %105
        %107 = OpVectorTimesScalar %v3float %105 %float_2
        %108 = OpFSub %v3float %47 %107
        %109 = OpFMul %v3float %106 %108
        %110 = OpCompositeExtract %float %104 0
        %111 = OpCompositeExtract %float %104 1
        %112 = OpFMul %float %111 %float_57
        %113 = OpFAdd %float %110 %112
        %114 = OpCompositeExtract %float %104 2
        %115 = OpFMul %float %float_113 %114
        %116 = OpFAdd %float %113 %115
        %117 = OpExtInst %float %1 Sin %116
        %118 = OpFMul %float %117 %float_43758_5469
        %119 = OpExtInst %float %1 Fract %118
        %120 = OpFAdd %float %116 %float_1
        %121 = OpExtInst %float %1 Sin %120
        %122 = OpFMul %float %121 %float_43758_5469
        %123 = OpExtInst %float %1 Fract %122
        %124 = OpCompositeExtract %float %109 0
        %125 = OpExtInst %float %1 FMix %119 %123 %124
        %126 = OpFAdd %float %116 %float_57
        %127 = OpExtInst %float %1 Sin %126
        %128 = OpFMul %float %127 %float_43758_5469
        %129 = OpExtInst %float %1 Fract %128
        %130 = OpFAdd %float %116 %float_58
        %131 = OpExtInst %float %1 Sin %130
        %132 = OpFMul %float %131 %float_43758_5469
        %133 = OpExtInst %float %1 Fract %132
        %134 = OpExtInst %float %1 FMix %129 %133 %124
        %135 = OpCompositeExtract %float %109 1
        %136 = OpExtInst %float %1 FMix %125 %134 %135
        %137 = OpFAdd %float %116 %float_113
        %138 = OpExtInst %float %1 Sin %137
        %139 = OpFMul %float %138 %float_43758_5469
        %140 = OpExtInst %float %1 Fract %139
        %141 = OpFAdd %float %116 %float_114
        %142 = OpExtInst %float %1 Sin %141
        %143 = OpFMul %float %142 %float_43758_5469
        %144 = OpExtInst %float %1 Fract %143
        %145 = OpExtInst %float %1 FMix %140 %144 %124
        %146 = OpFAdd %float %116 %float_170
        %147 = OpExtInst %float %1 Sin %146
        %148 = OpFMul %float %147 %float_43758_5469
        %149 = OpExtInst %float %1 Fract %148
        %150 = OpFAdd %float %116 %float_171
        %151 = OpExtInst %float %1 Sin %150
        %152 = OpFMul %float %151 %float_43758_5469
        %153 = OpExtInst %float %1 Fract %152
        %154 = OpExtInst %float %1 FMix %149 %153 %124
        %155 = OpExtInst %float %1 FMix %145 %154 %135
        %156 = OpCompositeExtract %float %109 2
        %157 = OpExtInst %float %1 FMix %136 %155 %156
        %158 = OpFMul %float %157 %float_10
        %159 = OpVectorTimesScalar %v3float %97 %float_0_0500000007
        %160 = OpFMul %float %100 %float_0_0500000007
        %161 = OpCompositeConstruct %v3float %160 %160 %160
        %162 = OpFAdd %v3float %159 %161
        %163 = OpExtInst %v3float %1 Floor %162
        %164 = OpExtInst %v3float %1 Fract %162
        %165 = OpFMul %v3float %164 %164
        %166 = OpVectorTimesScalar %v3float %164 %float_2
        %167 = OpFSub %v3float %47 %166
        %168 = OpFMul %v3float %165 %167
        %169 = OpCompositeExtract %float %163 0
        %170 = OpCompositeExtract %float %163 1
        %171 = OpFMul %float %170 %float_57
        %172 = OpFAdd %float %169 %171
        %173 = OpCompositeExtract %float %163 2
        %174 = OpFMul %float %float_113 %173
        %175 = OpFAdd %float %172 %174
        %176 = OpExtInst %float %1 Sin %175
        %177 = OpFMul %float %176 %float_43758_5469
        %178 = OpExtInst %float %1 Fract %177
        %179 = OpFAdd %float %175 %float_1
        %180 = OpExtInst %float %1 Sin %179
        %181 = OpFMul %float %180 %float_43758_5469
        %182 = OpExtInst %float %1 Fract %181
        %183 = OpCompositeExtract %float %168 0
        %184 = OpExtInst %float %1 FMix %178 %182 %183
        %185 = OpFAdd %float %175 %float_57
        %186 = OpExtInst %float %1 Sin %185
        %187 = OpFMul %float %186 %float_43758_5469
        %188 = OpExtInst %float %1 Fract %187
        %189 = OpFAdd %float %175 %float_58
        %190 = OpExtInst %float %1 Sin %189
        %191 = OpFMul %float %190 %float_43758_5469
        %192 = OpExtInst %float %1 Fract %191
        %193 = OpExtInst %float %1 FMix %188 %192 %183
        %194 = OpCompositeExtract %float %168 1
        %195 = OpExtInst %float %1 FMix %184 %193 %194
        %196 = OpFAdd %float %175 %float_113
        %197 = OpExtInst %float %1 Sin %196
        %198 = OpFMul %float %197 %float_43758_5469
        %199 = OpExtInst %float %1 Fract %198
        %200 = OpFAdd %float %175 %float_114
        %201 = OpExtInst %float %1 Sin %200
        %202 = OpFMul %float %201 %float_43758_5469
        %203 = OpExtInst %float %1 Fract %202
        %204 = OpExtInst %float %1 FMix %199 %203 %183
        %205 = OpFAdd %float %175 %float_170
        %206 = OpExtInst %float %1 Sin %205
        %207 = OpFMul %float %206 %float_43758_5469
        %208 = OpExtInst %float %1 Fract %207
        %209 = OpFAdd %float %175 %float_171
        %210 = OpExtInst %float %1 Sin %209
        %211 = OpFMul %float %210 %float_43758_5469
        %212 = OpExtInst %float %1 Fract %211
        %213 = OpExtInst %float %1 FMix %208 %212 %183
        %214 = OpExtInst %float %1 FMix %204 %213 %194
        %215 = OpCompositeExtract %float %168 2
        %216 = OpExtInst %float %1 FMix %195 %214 %215
        %217 = OpFMul %float %216 %float_2
        %218 = OpFAdd %float %158 %217
        %219 = OpCompositeInsert %v3float %218 %97 1
        %220 = OpUGreaterThan %bool %75 %uint_0
               OpSelectionMerge %221 None
               OpBranchConditional %220 %222 %221
        %222 = OpLabel
        %223 = OpULessThan %bool %75 %uint_63
               OpBranch %221
        %221 = OpLabel
        %224 = OpPhi %bool %false %83 %223 %222
               OpSelectionMerge %225 None
               OpBranchConditional %224 %226 %225
        %226 = OpLabel
        %227 = OpUGreaterThan %bool %85 %uint_0
               OpBranch %225
        %225 = OpLabel
        %228 = OpPhi %bool %false %221 %227 %226
               OpSelectionMerge %229 None
               OpBranchConditional %228 %230 %229
        %230 = OpLabel
        %231 = OpULessThan %bool %85 %uint_63
               OpBranch %229
        %229 = OpLabel
        %232 = OpPhi %bool %false %225 %231 %230
               OpSelectionMerge %233 None
               OpBranchConditional %232 %234 %233
        %234 = OpLabel
        %235 = OpFAdd %v3float %219 %36
        %236 = OpVectorTimesScalar %v3float %235 %float_0_00999999978
        %237 = OpFAdd %v3float %236 %102
        %238 = OpExtInst %v3float %1 Floor %237
        %239 = OpExtInst %v3float %1 Fract %237
        %240 = OpFMul %v3float %239 %239
        %241 = OpVectorTimesScalar %v3float %239 %float_2
        %242 = OpFSub %v3float %47 %241
        %243 = OpFMul %v3float %240 %242
        %244 = OpCompositeExtract %float %238 0
        %245 = OpCompositeExtract %float %238 1
        %246 = OpFMul %float %245 %float_57
        %247 = OpFAdd %float %244 %246
        %248 = OpCompositeExtract %float %238 2
        %249 = OpFMul %float %float_113 %248
        %250 = OpFAdd %float %247 %249
        %251 = OpExtInst %float %1 Sin %250
        %252 = OpFMul %float %251 %float_43758_5469
        %253 = OpExtInst %float %1 Fract %252
        %254 = OpFAdd %float %250 %float_1
        %255 = OpExtInst %float %1 Sin %254
        %256 = OpFMul %float %255 %float_43758_5469
        %257 = OpExtInst %float %1 Fract %256
        %258 = OpCompositeExtract %float %243 0
        %259 = OpExtInst %float %1 FMix %253 %257 %258
        %260 = OpFAdd %float %250 %float_57
        %261 = OpExtInst %float %1 Sin %260
        %262 = OpFMul %float %261 %float_43758_5469
        %263 = OpExtInst %float %1 Fract %262
        %264 = OpFAdd %float %250 %float_58
        %265 = OpExtInst %float %1 Sin %264
        %266 = OpFMul %float %265 %float_43758_5469
        %267 = OpExtInst %float %1 Fract %266
        %268 = OpExtInst %float %1 FMix %263 %267 %258
        %269 = OpCompositeExtract %float %243 1
        %270 = OpExtInst %float %1 FMix %259 %268 %269
        %271 = OpFAdd %float %250 %float_113
        %272 = OpExtInst %float %1 Sin %271
        %273 = OpFMul %float %272 %float_43758_5469
        %274 = OpExtInst %float %1 Fract %273
        %275 = OpFAdd %float %250 %float_114
        %276 = OpExtInst %float %1 Sin %275
        %277 = OpFMul %float %276 %float_43758_5469
        %278 = OpExtInst %float %1 Fract %277
        %279 = OpExtInst %float %1 FMix %274 %278 %258
        %280 = OpFAdd %float %250 %float_170
        %281 = OpExtInst %float %1 Sin %280
        %282 = OpFMul %float %281 %float_43758_5469
        %283 = OpExtInst %float %1 Fract %282
        %284 = OpFAdd %float %250 %float_171
        %285 = OpExtInst %float %1 Sin %284
        %286 = OpFMul %float %285 %float_43758_5469
        %287 = OpExtInst %float %1 Fract %286
        %288 = OpExtInst %float %1 FMix %283 %287 %258
        %289 = OpExtInst %float %1 FMix %279 %288 %269
        %290 = OpCompositeExtract %float %243 2
        %291 = OpExtInst %float %1 FMix %270 %289 %290
        %292 = OpFMul %float %291 %float_10
        %293 = OpFAdd %v3float %219 %37
        %294 = OpVectorTimesScalar %v3float %293 %float_0_00999999978
        %295 = OpFAdd %v3float %294 %102
        %296 = OpExtInst %v3float %1 Floor %295
        %297 = OpExtInst %v3float %1 Fract %295
        %298 = OpFMul %v3float %297 %297
        %299 = OpVectorTimesScalar %v3float %297 %float_2
        %300 = OpFSub %v3float %47 %299
        %301 = OpFMul %v3float %298 %300
        %302 = OpCompositeExtract %float %296 0
        %303 = OpCompositeExtract %float %296 1
        %304 = OpFMul %float %303 %float_57
        %305 = OpFAdd %float %302 %304
        %306 = OpCompositeExtract %float %296 2
        %307 = OpFMul %float %float_113 %306
        %308 = OpFAdd %float %305 %307
        %309 = OpExtInst %float %1 Sin %308
        %310 = OpFMul %float %309 %float_43758_5469
        %311 = OpExtInst %float %1 Fract %310
        %312 = OpFAdd %float %308 %float_1
        %313 = OpExtInst %float %1 Sin %312
        %314 = OpFMul %float %313 %float_43758_5469
        %315 = OpExtInst %float %1 Fract %314
        %316 = OpCompositeExtract %float %301 0
        %317 = OpExtInst %float %1 FMix %311 %315 %316
        %318 = OpFAdd %float %308 %float_57
        %319 = OpExtInst %float %1 Sin %318
        %320 = OpFMul %float %319 %float_43758_5469
        %321 = OpExtInst %float %1 Fract %320
        %322 = OpFAdd %float %308 %float_58
        %323 = OpExtInst %float %1 Sin %322
        %324 = OpFMul %float %323 %float_43758_5469
        %325 = OpExtInst %float %1 Fract %324
        %326 = OpExtInst %float %1 FMix %321 %325 %316
        %327 = OpCompositeExtract %float %301 1
        %328 = OpExtInst %float %1 FMix %317 %326 %327
        %329 = OpFAdd %float %308 %float_113
        %330 = OpExtInst %float %1 Sin %329
        %331 = OpFMul %float %330 %float_43758_5469
        %332 = OpExtInst %float %1 Fract %331
        %333 = OpFAdd %float %308 %float_114
        %334 = OpExtInst %float %1 Sin %333
        %335 = OpFMul %float %334 %float_43758_5469
        %336 = OpExtInst %float %1 Fract %335
        %337 = OpExtInst %float %1 FMix %332 %336 %316
        %338 = OpFAdd %float %308 %float_170
        %339 = OpExtInst %float %1 Sin %338
        %340 = OpFMul %float %339 %float_43758_5469
        %341 = OpExtInst %float %1 Fract %340
        %342 = OpFAdd %float %308 %float_171
        %343 = OpExtInst %float %1 Sin %342
        %344 = OpFMul %float %343 %float_43758_5469
        %345 = OpExtInst %float %1 Fract %344
        %346 = OpExtInst %float %1 FMix %341 %345 %316
        %347 = OpExtInst %float %1 FMix %337 %346 %327
        %348 = OpCompositeExtract %float %301 2
        %349 = OpExtInst %float %1 FMix %328 %347 %348
        %350 = OpFMul %float %349 %float_10
        %351 = OpFAdd %v3float %219 %38
        %352 = OpVectorTimesScalar %v3float %351 %float_0_00999999978
        %353 = OpFAdd %v3float %352 %102
        %354 = OpExtInst %v3float %1 Floor %353
        %355 = OpExtInst %v3float %1 Fract %353
        %356 = OpFMul %v3float %355 %355
        %357 = OpVectorTimesScalar %v3float %355 %float_2
        %358 = OpFSub %v3float %47 %357
        %359 = OpFMul %v3float %356 %358
        %360 = OpCompositeExtract %float %354 0
        %361 = OpCompositeExtract %float %354 1
        %362 = OpFMul %float %361 %float_57
        %363 = OpFAdd %float %360 %362
        %364 = OpCompositeExtract %float %354 2
        %365 = OpFMul %float %float_113 %364
        %366 = OpFAdd %float %363 %365
        %367 = OpExtInst %float %1 Sin %366
        %368 = OpFMul %float %367 %float_43758_5469
        %369 = OpExtInst %float %1 Fract %368
        %370 = OpFAdd %float %366 %float_1
        %371 = OpExtInst %float %1 Sin %370
        %372 = OpFMul %float %371 %float_43758_5469
        %373 = OpExtInst %float %1 Fract %372
        %374 = OpCompositeExtract %float %359 0
        %375 = OpExtInst %float %1 FMix %369 %373 %374
        %376 = OpFAdd %float %366 %float_57
        %377 = OpExtInst %float %1 Sin %376
        %378 = OpFMul %float %377 %float_43758_5469
        %379 = OpExtInst %float %1 Fract %378
        %380 = OpFAdd %float %366 %float_58
        %381 = OpExtInst %float %1 Sin %380
        %382 = OpFMul %float %381 %float_43758_5469
        %383 = OpExtInst %float %1 Fract %382
        %384 = OpExtInst %float %1 FMix %379 %383 %374
        %385 = OpCompositeExtract %float %359 1
        %386 = OpExtInst %float %1 FMix %375 %384 %385
        %387 = OpFAdd %float %366 %float_113
        %388 = OpExtInst %float %1 Sin %387
        %389 = OpFMul %float %388 %float_43758_5469
        %390 = OpExtInst %float %1 Fract %389
        %391 = OpFAdd %float %366 %float_114
        %392 = OpExtInst %float %1 Sin %391
        %393 = OpFMul %float %392 %float_43758_5469
        %394 = OpExtInst %float %1 Fract %393
        %395 = OpExtInst %float %1 FMix %390 %394 %374
        %396 = OpFAdd %float %366 %float_170
        %397 = OpExtInst %float %1 Sin %396
        %398 = OpFMul %float %397 %float_43758_5469
        %399 = OpExtInst %float %1 Fract %398
        %400 = OpFAdd %float %366 %float_171
        %401 = OpExtInst %float %1 Sin %400
        %402 = OpFMul %float %401 %float_43758_5469
        %403 = OpExtInst %float %1 Fract %402
        %404 = OpExtInst %float %1 FMix %399 %403 %374
        %405 = OpExtInst %float %1 FMix %395 %404 %385
        %406 = OpCompositeExtract %float %359 2
        %407 = OpExtInst %float %1 FMix %386 %405 %406
        %408 = OpFMul %float %407 %float_10
        %409 = OpFAdd %v3float %219 %39
        %410 = OpVectorTimesScalar %v3float %409 %float_0_00999999978
        %411 = OpFAdd %v3float %410 %102
        %412 = OpExtInst %v3float %1 Floor %411
        %413 = OpExtInst %v3float %1 Fract %411
        %414 = OpFMul %v3float %413 %413
        %415 = OpVectorTimesScalar %v3float %413 %float_2
        %416 = OpFSub %v3float %47 %415
        %417 = OpFMul %v3float %414 %416
        %418 = OpCompositeExtract %float %412 0
        %419 = OpCompositeExtract %float %412 1
        %420 = OpFMul %float %419 %float_57
        %421 = OpFAdd %float %418 %420
        %422 = OpCompositeExtract %float %412 2
        %423 = OpFMul %float %float_113 %422
        %424 = OpFAdd %float %421 %423
        %425 = OpExtInst %float %1 Sin %424
        %426 = OpFMul %float %425 %float_43758_5469
        %427 = OpExtInst %float %1 Fract %426
        %428 = OpFAdd %float %424 %float_1
        %429 = OpExtInst %float %1 Sin %428
        %430 = OpFMul %float %429 %float_43758_5469
        %431 = OpExtInst %float %1 Fract %430
        %432 = OpCompositeExtract %float %417 0
        %433 = OpExtInst %float %1 FMix %427 %431 %432
        %434 = OpFAdd %float %424 %float_57
        %435 = OpExtInst %float %1 Sin %434
        %436 = OpFMul %float %435 %float_43758_5469
        %437 = OpExtInst %float %1 Fract %436
        %438 = OpFAdd %float %424 %float_58
        %439 = OpExtInst %float %1 Sin %438
        %440 = OpFMul %float %439 %float_43758_5469
        %441 = OpExtInst %float %1 Fract %440
        %442 = OpExtInst %float %1 FMix %437 %441 %432
        %443 = OpCompositeExtract %float %417 1
        %444 = OpExtInst %float %1 FMix %433 %442 %443
        %445 = OpFAdd %float %424 %float_113
        %446 = OpExtInst %float %1 Sin %445
        %447 = OpFMul %float %446 %float_43758_5469
        %448 = OpExtInst %float %1 Fract %447
        %449 = OpFAdd %float %424 %float_114
        %450 = OpExtInst %float %1 Sin %449
        %451 = OpFMul %float %450 %float_43758_5469
        %452 = OpExtInst %float %1 Fract %451
        %453 = OpExtInst %float %1 FMix %448 %452 %432
        %454 = OpFAdd %float %424 %float_170
        %455 = OpExtInst %float %1 Sin %454
        %456 = OpFMul %float %455 %float_43758_5469
        %457 = OpExtInst %float %1 Fract %456
        %458 = OpFAdd %float %424 %float_171
        %459 = OpExtInst %float %1 Sin %458
        %460 = OpFMul %float %459 %float_43758_5469
        %461 = OpExtInst %float %1 Fract %460
        %462 = OpExtInst %float %1 FMix %457 %461 %432
        %463 = OpExtInst %float %1 FMix %453 %462 %443
        %464 = OpCompositeExtract %float %417 2
        %465 = OpExtInst %float %1 FMix %444 %463 %464
        %466 = OpFMul %float %465 %float_10
        %467 = OpFSub %float %292 %350
        %468 = OpFSub %float %408 %466
        %469 = OpCompositeConstruct %v3float %467 %float_2 %468
        %470 = OpExtInst %v3float %1 Normalize %469
               OpBranch %233
        %233 = OpLabel
        %471 = OpPhi %v3float %32 %229 %470 %234
        %472 = OpCompositeConstruct %v4float %91 %94 %float_0_5 %float_1
        %473 = OpAccessChain %_ptr_Uniform_MeshVertex %VertexBuffer %int_0 %87
        %474 = OpCompositeConstruct %MeshVertex %219 %471 %90 %472
               OpStore %473 %474
        %475 = OpULessThan %bool %75 %uint_63
               OpSelectionMerge %476 None
               OpBranchConditional %475 %477 %476
        %477 = OpLabel
        %478 = OpULessThan %bool %85 %uint_63
               OpBranch %476
        %476 = OpLabel
        %479 = OpPhi %bool %false %233 %478 %477
               OpSelectionMerge %480 None
               OpBranchConditional %479 %481 %480
        %481 = OpLabel
        %482 = OpIMul %uint %85 %uint_63
        %483 = OpIAdd %uint %482 %75
        %484 = OpIMul %uint %483 %uint_6
        %485 = OpIAdd %uint %87 %uint_1
        %486 = OpIAdd %uint %85 %uint_1
        %487 = OpIMul %uint %486 %uint_64
        %488 = OpIAdd %uint %487 %75
        %489 = OpIAdd %uint %488 %uint_1
        %490 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %484
               OpStore %490 %87
        %491 = OpIAdd %uint %484 %uint_1
        %492 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %491
               OpStore %492 %488
        %493 = OpIAdd %uint %484 %uint_2
        %494 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %493
               OpStore %494 %485
        %495 = OpIAdd %uint %484 %uint_3
        %496 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %495
               OpStore %496 %485
        %497 = OpIAdd %uint %484 %uint_4
        %498 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %497
               OpStore %498 %488
        %499 = OpIAdd %uint %484 %uint_5
        %500 = OpAccessChain %_ptr_Uniform_uint %IndexBuffer %int_0 %499
               OpStore %500 %489
               OpBranch %480
        %480 = OpLabel
               OpBranch %73
         %73 = OpLabel
               OpReturn
               OpFunctionEnd
