_amdgpu_cs_main:
	s_getpc_b64 s[4:5]                                         // 000000000000: BE841F00
	s_mov_b32 s0, s1                                           // 000000000004: BE800301
	s_mov_b32 s1, s5                                           // 000000000008: BE810305
	v_lshl_add_u32 v1, s2, 3, v0                               // 00000000000C: D7460001 04010602
	s_load_dwordx8 s[4:11], s[0:1], null                       // 000000000014: F40C0100 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000001C: BF8CC07F
	s_buffer_load_dword s3, s[4:7], null                       // 000000000020: F42000C2 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000028: BF8CC07F
	v_cmp_gt_u32_e32 vcc_lo, s3, v1                            // 00000000002C: 7D880203
	s_and_saveexec_b64 s[2:3], vcc                             // 000000000030: BE82246A
	s_cbranch_execz _L0                                        // 000000000034: BF880318
	v_mul_lo_u32 v23, 0x50, v1                                 // 000000000038: D5690017 000202FF 00000050
	s_buffer_load_dword s18, s[4:7], 0x8                       // 000000000044: F4200482 FA000008
	s_mov_b64 s[2:3], exec                                     // 00000000004C: BE82047E
	s_clause 0x4                                               // 000000000050: BFA10004
	buffer_load_dwordx4 v[13:16], v23, s[4:7], 0 offen         // 000000000054: E0381000 80010D17
	buffer_load_dwordx3 v[17:19], v23, s[4:7], 0 offen offset:16// 00000000005C: E03C1010 80011117
	buffer_load_dwordx4 v[9:12], v23, s[4:7], 0 offen offset:32// 000000000064: E0381020 80010917
	buffer_load_dwordx4 v[1:4], v23, s[4:7], 0 offen offset:60 // 00000000006C: E038103C 80010117
	buffer_load_dword v8, v23, s[4:7], 0 offen offset:76       // 000000000074: E030104C 80010817
	v_or_b32_e32 v29, 12, v23                                  // 00000000007C: 383A2E8C
	v_add_nc_u32_e32 v28, 16, v23                              // 000000000080: 4A382E90
	v_add_nc_u32_e32 v27, 32, v23                              // 000000000084: 4A362EA0
	v_add_nc_u32_e32 v25, 60, v23                              // 000000000088: 4A322EBC
	v_add_nc_u32_e32 v26, 64, v23                              // 00000000008C: 4A342EC0
	v_add_nc_u32_e32 v24, 0x44, v23                            // 000000000090: 4A302EFF 00000044
	s_waitcnt vmcnt(4) lgkmcnt(0)                              // 000000000098: BF8C0074
	v_subrev_f32_e32 v16, s18, v16                             // 00000000009C: 0A202012
	v_cmpx_ge_f32_e32 0, v16                                   // 0000000000A0: 7C2C2080
	s_xor_b64 s[12:13], exec, s[2:3]                           // 0000000000A4: 898C027E
	s_cbranch_execz _L1                                        // 0000000000A8: BF880037
	s_load_dwordx4 s[0:3], s[0:1], 0x20                        // 0000000000AC: F4080000 FA000020
	s_mov_b64 s[16:17], exec                                   // 0000000000B4: BE90047E
	s_mov_b64 s[14:15], exec                                   // 0000000000B8: BE8E047E
	v_mbcnt_lo_u32_b32 v0, s16, 0                              // 0000000000BC: D7650000 00010010
	v_mbcnt_hi_u32_b32 v0, s17, v0                             // 0000000000C4: D7660000 00020011
	v_cmpx_eq_u32_e32 0, v0                                    // 0000000000CC: 7DA40080
	s_cbranch_execz _L2                                        // 0000000000D0: BF880005
	s_bcnt1_i32_b64 s16, s[16:17]                              // 0000000000D4: BE901010
	v_mov_b32_e32 v5, s16                                      // 0000000000D8: 7E0A0210
	s_waitcnt lgkmcnt(0)                                       // 0000000000DC: BF8CC07F
	buffer_atomic_add v5, off, s[0:3], 0 glc                   // 0000000000E0: E0C84000 80000500
_L2:
	s_or_b64 exec, exec, s[14:15]                              // 0000000000E8: 88FE0E7E
	s_clause 0x1                                               // 0000000000EC: BFA10001
	buffer_load_dword v20, v23, s[4:7], 0 offen offset:28      // 0000000000F0: E030101C 80011417
	buffer_load_dwordx3 v[30:32], v23, s[4:7], 0 offen offset:48// 0000000000F8: E03C1030 80011E17
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 000000000100: BF8C0072
	v_readfirstlane_b32 s0, v5                                 // 000000000104: 7E000505
	v_mov_b32_e32 v6, v3                                       // 000000000108: 7E0C0303
	v_mov_b32_e32 v7, v4                                       // 00000000010C: 7E0E0304
	v_mov_b32_e32 v5, v2                                       // 000000000110: 7E0A0302
	v_mov_b32_e32 v33, v1                                      // 000000000114: 7E420301
	v_add_nc_u32_e32 v0, s0, v0                                // 000000000118: 4A000000
	v_mul_lo_u32 v0, 0x50, v0                                  // 00000000011C: D5690000 000200FF 00000050
	buffer_store_dwordx4 v[13:16], v0, s[8:11], 0 offen        // 000000000128: E0781000 80020D00
	buffer_store_dwordx4 v[9:12], v0, s[8:11], 0 offen offset:32// 000000000130: E0781020 80020900
	buffer_store_dwordx4 v[5:8], v0, s[8:11], 0 offen offset:64// 000000000138: E0781040 80020500
	s_waitcnt vmcnt(1)                                         // 000000000140: BF8C3F71
	buffer_store_dwordx4 v[17:20], v0, s[8:11], 0 offen offset:16// 000000000144: E0781010 80021100
	s_waitcnt vmcnt(0)                                         // 00000000014C: BF8C3F70
	buffer_store_dwordx4 v[30:33], v0, s[8:11], 0 offen offset:48// 000000000150: E0781030 80021E00
	buffer_store_dwordx3 v[13:15], v23, s[4:7], 0 offen        // 000000000158: E07C1000 80010D17
	buffer_store_dword v16, v29, s[4:7], 0 offen               // 000000000160: E0701000 8001101D
	buffer_store_dwordx3 v[17:19], v28, s[4:7], 0 offen        // 000000000168: E07C1000 8001111C
	buffer_store_dword v20, v23, s[4:7], 0 offen offset:28     // 000000000170: E070101C 80011417
	buffer_store_dwordx4 v[9:12], v27, s[4:7], 0 offen         // 000000000178: E0781000 8001091B
	buffer_store_dwordx3 v[30:32], v23, s[4:7], 0 offen offset:48// 000000000180: E07C1030 80011E17
_L1:
	s_or_saveexec_b64 s[2:3], s[12:13]                         // 000000000188: BE82250C
	v_mov_b32_e32 v20, 0                                       // 00000000018C: 7E280280
	s_xor_b64 exec, exec, s[2:3]                               // 000000000190: 89FE027E
	s_cbranch_execz _L3                                        // 000000000194: BF8802B5
	s_clause 0x1                                               // 000000000198: BFA10001
	s_buffer_load_dwordx4 s[8:11], s[4:7], 0xc                 // 00000000019C: F4280202 FA00000C
	s_buffer_load_dwordx2 s[0:1], s[4:7], 0x54                 // 0000000001A4: F4240002 FA000054
	s_mov_b32 s12, 0                                           // 0000000001AC: BE8C0380
	s_mov_b32 s13, 0                                           // 0000000001B0: BE8D0380
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 0000000001B4: BF8C0072
	v_mul_f32_e64 v12, 0x3dcccccd, s8                          // 0000000001B8: D508000C 000010FF 3DCCCCCD
	s_waitcnt vmcnt(1)                                         // 0000000001C4: BF8C3F71
	v_mul_f32_e32 v5, s9, v1                                   // 0000000001C8: 100A0209
	v_mul_f32_e32 v6, s10, v1                                  // 0000000001CC: 100C020A
	v_mul_f32_e32 v7, s11, v1                                  // 0000000001D0: 100E020B
	s_buffer_load_dwordx2 s[8:9], s[4:7], 0x48                 // 0000000001D4: F4240202 FA000048
	v_fma_f32 v20, s0, v13, v12                                // 0000000001DC: D54B0014 04321A00
	v_fma_f32 v21, s0, v14, v12                                // 0000000001E4: D54B0015 04321C00
	v_fmac_f32_e32 v12, s0, v15                                // 0000000001EC: 56181E00
	s_buffer_load_dword s0, s[4:7], 0x1c                       // 0000000001F0: F4200002 FA00001C
	v_add_f32_e32 v22, 0xbdcccccd, v20                         // 0000000001F8: 062C28FF BDCCCCCD
	v_floor_f32_e32 v31, v21                                   // 000000000200: 7E3E4915
	v_floor_f32_e32 v32, v12                                   // 000000000204: 7E40490C
	v_fract_f32_e32 v33, v21                                   // 000000000208: 7E424115
	v_fract_f32_e32 v34, v12                                   // 00000000020C: 7E44410C
	v_floor_f32_e32 v30, v22                                   // 000000000210: 7E3C4916
	v_fract_f32_e32 v22, v22                                   // 000000000214: 7E2C4116
	v_mul_f32_e32 v36, v33, v33                                // 000000000218: 10484321
	v_add_f32_e32 v33, v33, v33                                // 00000000021C: 06424321
	v_fmac_f32_e32 v30, 0x42640000, v31                        // 000000000220: 563C3EFF 42640000
	v_mul_f32_e32 v35, v22, v22                                // 000000000228: 10462D16
	v_add_f32_e32 v22, v22, v22                                // 00000000022C: 062C2D16
	v_mul_f32_e32 v37, v34, v34                                // 000000000230: 104A4522
	v_fma_f32 v33, 0x40400000, v36, -v33                       // 000000000234: D54B0021 848648FF 40400000
	v_fmac_f32_e32 v30, 0x42e20000, v32                        // 000000000240: 563C40FF 42E20000
	v_add_f32_e32 v34, v34, v34                                // 000000000248: 06444522
	v_fma_f32 v22, 0x40400000, v35, -v22                       // 00000000024C: D54B0016 845A46FF 40400000
	v_add_f32_e32 v36, 1.0, v30                                // 000000000258: 06483CF2
	v_mul_f32_e32 v35, 0.15915494, v30                         // 00000000025C: 10463CF8
	v_fma_f32 v34, 0x40400000, v37, -v34                       // 000000000260: D54B0022 848A4AFF 40400000
	v_add_f32_e32 v37, 0x42680000, v30                         // 00000000026C: 064A3CFF 42680000
	v_mul_f32_e32 v36, 0.15915494, v36                         // 000000000274: 104848F8
	v_sin_f32_e32 v35, v35                                     // 000000000278: 7E466B23
	v_mul_f32_e32 v37, 0.15915494, v37                         // 00000000027C: 104A4AF8
	v_sin_f32_e32 v36, v36                                     // 000000000280: 7E486B24
	v_sin_f32_e32 v37, v37                                     // 000000000284: 7E4A6B25
	v_mul_f32_e32 v35, 0x472aee8c, v35                         // 000000000288: 104646FF 472AEE8C
	v_mul_f32_e32 v36, 0x472aee8c, v36                         // 000000000290: 104848FF 472AEE8C
	v_fract_f32_e32 v35, v35                                   // 000000000298: 7E464123
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 00000000029C: 104A4AFF 472AEE8C
	v_fract_f32_e32 v36, v36                                   // 0000000002A4: 7E484124
	v_fract_f32_e32 v37, v37                                   // 0000000002A8: 7E4A4125
	v_sub_f32_e32 v36, v36, v35                                // 0000000002AC: 08484724
	v_fmac_f32_e32 v35, v36, v22                               // 0000000002B0: 56462D24
	v_add_f32_e32 v36, 0x42640000, v30                         // 0000000002B4: 06483CFF 42640000
	v_mul_f32_e32 v36, 0.15915494, v36                         // 0000000002BC: 104848F8
	v_sin_f32_e32 v36, v36                                     // 0000000002C0: 7E486B24
	v_mul_f32_e32 v36, 0x472aee8c, v36                         // 0000000002C4: 104848FF 472AEE8C
	v_fract_f32_e32 v36, v36                                   // 0000000002CC: 7E484124
	v_sub_f32_e32 v37, v37, v36                                // 0000000002D0: 084A4925
	v_fmac_f32_e32 v36, v37, v22                               // 0000000002D4: 56482D25
	v_add_f32_e32 v37, 0x42e40000, v30                         // 0000000002D8: 064A3CFF 42E40000
	v_sub_f32_e32 v36, v36, v35                                // 0000000002E0: 08484724
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000002E4: 104A4AF8
	v_fmac_f32_e32 v35, v36, v33                               // 0000000002E8: 56464324
	v_add_f32_e32 v36, 0x42e20000, v30                         // 0000000002EC: 06483CFF 42E20000
	v_sin_f32_e32 v37, v37                                     // 0000000002F4: 7E4A6B25
	v_mul_f32_e32 v36, 0.15915494, v36                         // 0000000002F8: 104848F8
	v_sin_f32_e32 v36, v36                                     // 0000000002FC: 7E486B24
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000300: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 000000000308: 7E4A4125
	v_mul_f32_e32 v36, 0x472aee8c, v36                         // 00000000030C: 104848FF 472AEE8C
	v_fract_f32_e32 v36, v36                                   // 000000000314: 7E484124
	v_sub_f32_e32 v37, v37, v36                                // 000000000318: 084A4925
	v_fmac_f32_e32 v36, v37, v22                               // 00000000031C: 56482D25
	v_add_f32_e32 v37, 0x432a0000, v30                         // 000000000320: 064A3CFF 432A0000
	v_add_f32_e32 v30, 0x432b0000, v30                         // 000000000328: 063C3CFF 432B0000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 000000000330: 104A4AF8
	v_mul_f32_e32 v30, 0.15915494, v30                         // 000000000334: 103C3CF8
	v_sin_f32_e32 v37, v37                                     // 000000000338: 7E4A6B25
	v_sin_f32_e32 v30, v30                                     // 00000000033C: 7E3C6B1E
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000340: 104A4AFF 472AEE8C
	v_mul_f32_e32 v30, 0x472aee8c, v30                         // 000000000348: 103C3CFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 000000000350: 7E4A4125
	v_fract_f32_e32 v30, v30                                   // 000000000354: 7E3C411E
	v_sub_f32_e32 v30, v30, v37                                // 000000000358: 083C4B1E
	v_fmac_f32_e32 v37, v30, v22                               // 00000000035C: 564A2D1E
	v_sub_f32_e32 v22, v37, v36                                // 000000000360: 082C4925
	v_fmac_f32_e32 v36, v22, v33                               // 000000000364: 56484316
	v_sub_f32_e32 v22, v36, v35                                // 000000000368: 082C4724
	v_fmac_f32_e32 v35, v22, v34                               // 00000000036C: 56464516
	v_add_f32_e32 v22, 0x3dcccccd, v20                         // 000000000370: 062C28FF 3DCCCCCD
	v_floor_f32_e32 v30, v22                                   // 000000000378: 7E3C4916
	v_fract_f32_e32 v22, v22                                   // 00000000037C: 7E2C4116
	v_fmac_f32_e32 v30, 0x42640000, v31                        // 000000000380: 563C3EFF 42640000
	v_mul_f32_e32 v36, v22, v22                                // 000000000388: 10482D16
	v_add_f32_e32 v22, v22, v22                                // 00000000038C: 062C2D16
	v_fmac_f32_e32 v30, 0x42e20000, v32                        // 000000000390: 563C40FF 42E20000
	v_fma_f32 v22, 0x40400000, v36, -v22                       // 000000000398: D54B0016 845A48FF 40400000
	v_add_f32_e32 v37, 1.0, v30                                // 0000000003A4: 064A3CF2
	v_mul_f32_e32 v36, 0.15915494, v30                         // 0000000003A8: 10483CF8
	v_add_f32_e32 v38, 0x42680000, v30                         // 0000000003AC: 064C3CFF 42680000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000003B4: 104A4AF8
	v_sin_f32_e32 v36, v36                                     // 0000000003B8: 7E486B24
	v_mul_f32_e32 v38, 0.15915494, v38                         // 0000000003BC: 104C4CF8
	v_sin_f32_e32 v37, v37                                     // 0000000003C0: 7E4A6B25
	v_sin_f32_e32 v38, v38                                     // 0000000003C4: 7E4C6B26
	v_mul_f32_e32 v36, 0x472aee8c, v36                         // 0000000003C8: 104848FF 472AEE8C
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 0000000003D0: 104A4AFF 472AEE8C
	v_fract_f32_e32 v36, v36                                   // 0000000003D8: 7E484124
	v_mul_f32_e32 v38, 0x472aee8c, v38                         // 0000000003DC: 104C4CFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 0000000003E4: 7E4A4125
	v_fract_f32_e32 v38, v38                                   // 0000000003E8: 7E4C4126
	v_sub_f32_e32 v37, v37, v36                                // 0000000003EC: 084A4925
	v_fmac_f32_e32 v36, v37, v22                               // 0000000003F0: 56482D25
	v_add_f32_e32 v37, 0x42640000, v30                         // 0000000003F4: 064A3CFF 42640000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000003FC: 104A4AF8
	v_sin_f32_e32 v37, v37                                     // 000000000400: 7E4A6B25
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000404: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 00000000040C: 7E4A4125
	v_sub_f32_e32 v38, v38, v37                                // 000000000410: 084C4B26
	v_fmac_f32_e32 v37, v38, v22                               // 000000000414: 564A2D26
	v_add_f32_e32 v38, 0x42e40000, v30                         // 000000000418: 064C3CFF 42E40000
	v_sub_f32_e32 v37, v37, v36                                // 000000000420: 084A4925
	v_mul_f32_e32 v38, 0.15915494, v38                         // 000000000424: 104C4CF8
	v_fmac_f32_e32 v36, v37, v33                               // 000000000428: 56484325
	v_add_f32_e32 v37, 0x42e20000, v30                         // 00000000042C: 064A3CFF 42E20000
	v_sin_f32_e32 v38, v38                                     // 000000000434: 7E4C6B26
	v_mul_f32_e32 v37, 0.15915494, v37                         // 000000000438: 104A4AF8
	v_sin_f32_e32 v37, v37                                     // 00000000043C: 7E4A6B25
	v_mul_f32_e32 v38, 0x472aee8c, v38                         // 000000000440: 104C4CFF 472AEE8C
	v_fract_f32_e32 v38, v38                                   // 000000000448: 7E4C4126
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 00000000044C: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 000000000454: 7E4A4125
	v_sub_f32_e32 v38, v38, v37                                // 000000000458: 084C4B26
	v_fmac_f32_e32 v37, v38, v22                               // 00000000045C: 564A2D26
	v_add_f32_e32 v38, 0x432a0000, v30                         // 000000000460: 064C3CFF 432A0000
	v_add_f32_e32 v30, 0x432b0000, v30                         // 000000000468: 063C3CFF 432B0000
	v_mul_f32_e32 v38, 0.15915494, v38                         // 000000000470: 104C4CF8
	v_mul_f32_e32 v30, 0.15915494, v30                         // 000000000474: 103C3CF8
	v_sin_f32_e32 v38, v38                                     // 000000000478: 7E4C6B26
	v_sin_f32_e32 v30, v30                                     // 00000000047C: 7E3C6B1E
	v_mul_f32_e32 v38, 0x472aee8c, v38                         // 000000000480: 104C4CFF 472AEE8C
	v_mul_f32_e32 v30, 0x472aee8c, v30                         // 000000000488: 103C3CFF 472AEE8C
	v_fract_f32_e32 v38, v38                                   // 000000000490: 7E4C4126
	v_fract_f32_e32 v30, v30                                   // 000000000494: 7E3C411E
	v_sub_f32_e32 v30, v30, v38                                // 000000000498: 083C4D1E
	v_fmac_f32_e32 v38, v30, v22                               // 00000000049C: 564C2D1E
	v_floor_f32_e32 v30, v20                                   // 0000000004A0: 7E3C4914
	v_fract_f32_e32 v20, v20                                   // 0000000004A4: 7E284114
	v_sub_f32_e32 v22, v38, v37                                // 0000000004A8: 082C4B26
	v_mul_f32_e32 v38, v20, v20                                // 0000000004AC: 104C2914
	v_add_f32_e32 v20, v20, v20                                // 0000000004B0: 06282914
	v_fmac_f32_e32 v37, v22, v33                               // 0000000004B4: 564A4316
	v_fma_f32 v20, 0x40400000, v38, -v20                       // 0000000004B8: D54B0014 84524CFF 40400000
	v_sub_f32_e32 v22, v37, v36                                // 0000000004C4: 082C4925
	v_fmac_f32_e32 v36, v22, v34                               // 0000000004C8: 56484516
	v_add_f32_e32 v22, 0xbdcccccd, v21                         // 0000000004CC: 062C2AFF BDCCCCCD
	v_add_f32_e32 v21, 0x3dcccccd, v21                         // 0000000004D4: 062A2AFF 3DCCCCCD
	v_floor_f32_e32 v37, v22                                   // 0000000004DC: 7E4A4916
	v_fract_f32_e32 v22, v22                                   // 0000000004E0: 7E2C4116
	v_fmamk_f32 v37, v37, 0x42640000, v30                      // 0000000004E4: 584A3D25 42640000
	v_mul_f32_e32 v39, v22, v22                                // 0000000004EC: 104E2D16
	v_add_f32_e32 v22, v22, v22                                // 0000000004F0: 062C2D16
	v_fmac_f32_e32 v37, 0x42e20000, v32                        // 0000000004F4: 564A40FF 42E20000
	v_fma_f32 v22, 0x40400000, v39, -v22                       // 0000000004FC: D54B0016 845A4EFF 40400000
	v_add_f32_e32 v39, 1.0, v37                                // 000000000508: 064E4AF2
	v_mul_f32_e32 v38, 0.15915494, v37                         // 00000000050C: 104C4AF8
	v_add_f32_e32 v40, 0x42680000, v37                         // 000000000510: 06504AFF 42680000
	v_mul_f32_e32 v39, 0.15915494, v39                         // 000000000518: 104E4EF8
	v_sin_f32_e32 v38, v38                                     // 00000000051C: 7E4C6B26
	v_mul_f32_e32 v40, 0.15915494, v40                         // 000000000520: 105050F8
	v_sin_f32_e32 v39, v39                                     // 000000000524: 7E4E6B27
	v_sin_f32_e32 v40, v40                                     // 000000000528: 7E506B28
	v_mul_f32_e32 v38, 0x472aee8c, v38                         // 00000000052C: 104C4CFF 472AEE8C
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 000000000534: 104E4EFF 472AEE8C
	v_fract_f32_e32 v38, v38                                   // 00000000053C: 7E4C4126
	v_mul_f32_e32 v40, 0x472aee8c, v40                         // 000000000540: 105050FF 472AEE8C
	v_fract_f32_e32 v39, v39                                   // 000000000548: 7E4E4127
	v_fract_f32_e32 v40, v40                                   // 00000000054C: 7E504128
	v_sub_f32_e32 v39, v39, v38                                // 000000000550: 084E4D27
	v_fmac_f32_e32 v38, v39, v20                               // 000000000554: 564C2927
	v_add_f32_e32 v39, 0x42640000, v37                         // 000000000558: 064E4AFF 42640000
	v_mul_f32_e32 v39, 0.15915494, v39                         // 000000000560: 104E4EF8
	v_sin_f32_e32 v39, v39                                     // 000000000564: 7E4E6B27
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 000000000568: 104E4EFF 472AEE8C
	v_fract_f32_e32 v39, v39                                   // 000000000570: 7E4E4127
	v_sub_f32_e32 v40, v40, v39                                // 000000000574: 08504F28
	v_fmac_f32_e32 v39, v40, v20                               // 000000000578: 564E2928
	v_add_f32_e32 v40, 0x42e40000, v37                         // 00000000057C: 06504AFF 42E40000
	v_sub_f32_e32 v39, v39, v38                                // 000000000584: 084E4D27
	v_mul_f32_e32 v40, 0.15915494, v40                         // 000000000588: 105050F8
	v_fmac_f32_e32 v38, v39, v22                               // 00000000058C: 564C2D27
	v_add_f32_e32 v39, 0x42e20000, v37                         // 000000000590: 064E4AFF 42E20000
	v_sin_f32_e32 v40, v40                                     // 000000000598: 7E506B28
	v_mul_f32_e32 v39, 0.15915494, v39                         // 00000000059C: 104E4EF8
	v_sin_f32_e32 v39, v39                                     // 0000000005A0: 7E4E6B27
	v_mul_f32_e32 v40, 0x472aee8c, v40                         // 0000000005A4: 105050FF 472AEE8C
	v_fract_f32_e32 v40, v40                                   // 0000000005AC: 7E504128
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 0000000005B0: 104E4EFF 472AEE8C
	v_fract_f32_e32 v39, v39                                   // 0000000005B8: 7E4E4127
	v_sub_f32_e32 v40, v40, v39                                // 0000000005BC: 08504F28
	v_fmac_f32_e32 v39, v40, v20                               // 0000000005C0: 564E2928
	v_add_f32_e32 v40, 0x432a0000, v37                         // 0000000005C4: 06504AFF 432A0000
	v_add_f32_e32 v37, 0x432b0000, v37                         // 0000000005CC: 064A4AFF 432B0000
	v_mul_f32_e32 v40, 0.15915494, v40                         // 0000000005D4: 105050F8
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000005D8: 104A4AF8
	v_sin_f32_e32 v40, v40                                     // 0000000005DC: 7E506B28
	v_sin_f32_e32 v37, v37                                     // 0000000005E0: 7E4A6B25
	v_mul_f32_e32 v40, 0x472aee8c, v40                         // 0000000005E4: 105050FF 472AEE8C
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 0000000005EC: 104A4AFF 472AEE8C
	v_fract_f32_e32 v40, v40                                   // 0000000005F4: 7E504128
	v_fract_f32_e32 v37, v37                                   // 0000000005F8: 7E4A4125
	v_sub_f32_e32 v37, v37, v40                                // 0000000005FC: 084A5125
	v_fmac_f32_e32 v40, v37, v20                               // 000000000600: 56502925
	v_sub_f32_e32 v37, v40, v39                                // 000000000604: 084A4F28
	v_fmac_f32_e32 v39, v37, v22                               // 000000000608: 564E2D25
	v_sub_f32_e32 v22, v39, v38                                // 00000000060C: 082C4D27
	v_fmac_f32_e32 v38, v22, v34                               // 000000000610: 564C4516
	v_floor_f32_e32 v22, v21                                   // 000000000614: 7E2C4915
	v_fract_f32_e32 v21, v21                                   // 000000000618: 7E2A4115
	v_fmamk_f32 v22, v22, 0x42640000, v30                      // 00000000061C: 582C3D16 42640000
	v_mul_f32_e32 v37, v21, v21                                // 000000000624: 104A2B15
	v_add_f32_e32 v21, v21, v21                                // 000000000628: 062A2B15
	v_fmac_f32_e32 v30, 0x42640000, v31                        // 00000000062C: 563C3EFF 42640000
	v_fmac_f32_e32 v22, 0x42e20000, v32                        // 000000000634: 562C40FF 42E20000
	v_fma_f32 v21, 0x40400000, v37, -v21                       // 00000000063C: D54B0015 84564AFF 40400000
	v_add_f32_e32 v37, 1.0, v22                                // 000000000648: 064A2CF2
	v_mul_f32_e32 v32, 0.15915494, v22                         // 00000000064C: 10402CF8
	v_add_f32_e32 v39, 0x42680000, v22                         // 000000000650: 064E2CFF 42680000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 000000000658: 104A4AF8
	v_sin_f32_e32 v32, v32                                     // 00000000065C: 7E406B20
	v_mul_f32_e32 v39, 0.15915494, v39                         // 000000000660: 104E4EF8
	v_sin_f32_e32 v37, v37                                     // 000000000664: 7E4A6B25
	v_sin_f32_e32 v39, v39                                     // 000000000668: 7E4E6B27
	v_mul_f32_e32 v32, 0x472aee8c, v32                         // 00000000066C: 104040FF 472AEE8C
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000674: 104A4AFF 472AEE8C
	v_fract_f32_e32 v32, v32                                   // 00000000067C: 7E404120
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 000000000680: 104E4EFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 000000000688: 7E4A4125
	v_fract_f32_e32 v39, v39                                   // 00000000068C: 7E4E4127
	v_sub_f32_e32 v37, v37, v32                                // 000000000690: 084A4125
	v_fmac_f32_e32 v32, v37, v20                               // 000000000694: 56402925
	v_add_f32_e32 v37, 0x42640000, v22                         // 000000000698: 064A2CFF 42640000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000006A0: 104A4AF8
	v_sin_f32_e32 v37, v37                                     // 0000000006A4: 7E4A6B25
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 0000000006A8: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 0000000006B0: 7E4A4125
	v_sub_f32_e32 v39, v39, v37                                // 0000000006B4: 084E4B27
	v_fmac_f32_e32 v37, v39, v20                               // 0000000006B8: 564A2927
	v_add_f32_e32 v39, 0x42e40000, v22                         // 0000000006BC: 064E2CFF 42E40000
	v_sub_f32_e32 v37, v37, v32                                // 0000000006C4: 084A4125
	v_mul_f32_e32 v39, 0.15915494, v39                         // 0000000006C8: 104E4EF8
	v_fmac_f32_e32 v32, v37, v21                               // 0000000006CC: 56402B25
	v_add_f32_e32 v37, 0x42e20000, v22                         // 0000000006D0: 064A2CFF 42E20000
	v_sin_f32_e32 v39, v39                                     // 0000000006D8: 7E4E6B27
	v_mul_f32_e32 v37, 0.15915494, v37                         // 0000000006DC: 104A4AF8
	v_sin_f32_e32 v37, v37                                     // 0000000006E0: 7E4A6B25
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 0000000006E4: 104E4EFF 472AEE8C
	v_fract_f32_e32 v39, v39                                   // 0000000006EC: 7E4E4127
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 0000000006F0: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 0000000006F8: 7E4A4125
	v_sub_f32_e32 v39, v39, v37                                // 0000000006FC: 084E4B27
	v_fmac_f32_e32 v37, v39, v20                               // 000000000700: 564A2927
	v_add_f32_e32 v39, 0x432a0000, v22                         // 000000000704: 064E2CFF 432A0000
	v_add_f32_e32 v22, 0x432b0000, v22                         // 00000000070C: 062C2CFF 432B0000
	v_mul_f32_e32 v39, 0.15915494, v39                         // 000000000714: 104E4EF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000718: 102C2CF8
	v_sin_f32_e32 v39, v39                                     // 00000000071C: 7E4E6B27
	v_sin_f32_e32 v22, v22                                     // 000000000720: 7E2C6B16
	v_mul_f32_e32 v39, 0x472aee8c, v39                         // 000000000724: 104E4EFF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 00000000072C: 102C2CFF 472AEE8C
	v_fract_f32_e32 v39, v39                                   // 000000000734: 7E4E4127
	v_fract_f32_e32 v22, v22                                   // 000000000738: 7E2C4116
	v_sub_f32_e32 v22, v22, v39                                // 00000000073C: 082C4F16
	v_fmac_f32_e32 v39, v22, v20                               // 000000000740: 564E2916
	v_sub_f32_e32 v22, v39, v37                                // 000000000744: 082C4B27
	v_fmac_f32_e32 v37, v22, v21                               // 000000000748: 564A2B16
	v_sub_f32_e32 v21, v37, v32                                // 00000000074C: 082A4125
	v_fmac_f32_e32 v32, v21, v34                               // 000000000750: 56404515
	v_add_f32_e32 v21, 0xbdcccccd, v12                         // 000000000754: 062A18FF BDCCCCCD
	v_add_f32_e32 v12, 0x3dcccccd, v12                         // 00000000075C: 061818FF 3DCCCCCD
	v_floor_f32_e32 v22, v21                                   // 000000000764: 7E2C4915
	v_fract_f32_e32 v21, v21                                   // 000000000768: 7E2A4115
	v_fmamk_f32 v22, v22, 0x42e20000, v30                      // 00000000076C: 582C3D16 42E20000
	v_mul_f32_e32 v34, v21, v21                                // 000000000774: 10442B15
	v_add_f32_e32 v21, v21, v21                                // 000000000778: 062A2B15
	v_mul_f32_e32 v31, 0.15915494, v22                         // 00000000077C: 103E2CF8
	v_add_f32_e32 v37, 0x42680000, v22                         // 000000000780: 064A2CFF 42680000
	v_fma_f32 v21, 0x40400000, v34, -v21                       // 000000000788: D54B0015 845644FF 40400000
	v_add_f32_e32 v34, 1.0, v22                                // 000000000794: 06442CF2
	v_sin_f32_e32 v31, v31                                     // 000000000798: 7E3E6B1F
	v_mul_f32_e32 v37, 0.15915494, v37                         // 00000000079C: 104A4AF8
	v_mul_f32_e32 v34, 0.15915494, v34                         // 0000000007A0: 104444F8
	v_sin_f32_e32 v37, v37                                     // 0000000007A4: 7E4A6B25
	v_sin_f32_e32 v34, v34                                     // 0000000007A8: 7E446B22
	v_mul_f32_e32 v31, 0x472aee8c, v31                         // 0000000007AC: 103E3EFF 472AEE8C
	v_fract_f32_e32 v31, v31                                   // 0000000007B4: 7E3E411F
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 0000000007B8: 104A4AFF 472AEE8C
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 0000000007C0: 104444FF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 0000000007C8: 7E4A4125
	v_fract_f32_e32 v34, v34                                   // 0000000007CC: 7E444122
	v_sub_f32_e32 v34, v34, v31                                // 0000000007D0: 08443F22
	v_fmac_f32_e32 v31, v34, v20                               // 0000000007D4: 563E2922
	v_add_f32_e32 v34, 0x42640000, v22                         // 0000000007D8: 06442CFF 42640000
	v_mul_f32_e32 v34, 0.15915494, v34                         // 0000000007E0: 104444F8
	v_sin_f32_e32 v34, v34                                     // 0000000007E4: 7E446B22
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 0000000007E8: 104444FF 472AEE8C
	v_fract_f32_e32 v34, v34                                   // 0000000007F0: 7E444122
	v_sub_f32_e32 v37, v37, v34                                // 0000000007F4: 084A4525
	v_fmac_f32_e32 v34, v37, v20                               // 0000000007F8: 56442925
	v_add_f32_e32 v37, 0x42e40000, v22                         // 0000000007FC: 064A2CFF 42E40000
	v_sub_f32_e32 v34, v34, v31                                // 000000000804: 08443F22
	v_mul_f32_e32 v37, 0.15915494, v37                         // 000000000808: 104A4AF8
	v_fmac_f32_e32 v31, v34, v33                               // 00000000080C: 563E4322
	v_add_f32_e32 v34, 0x42e20000, v22                         // 000000000810: 06442CFF 42E20000
	v_sin_f32_e32 v37, v37                                     // 000000000818: 7E4A6B25
	v_mul_f32_e32 v34, 0.15915494, v34                         // 00000000081C: 104444F8
	v_sin_f32_e32 v34, v34                                     // 000000000820: 7E446B22
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000824: 104A4AFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 00000000082C: 7E4A4125
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 000000000830: 104444FF 472AEE8C
	v_fract_f32_e32 v34, v34                                   // 000000000838: 7E444122
	v_sub_f32_e32 v37, v37, v34                                // 00000000083C: 084A4525
	v_fmac_f32_e32 v34, v37, v20                               // 000000000840: 56442925
	v_add_f32_e32 v37, 0x432a0000, v22                         // 000000000844: 064A2CFF 432A0000
	v_add_f32_e32 v22, 0x432b0000, v22                         // 00000000084C: 062C2CFF 432B0000
	v_mul_f32_e32 v37, 0.15915494, v37                         // 000000000854: 104A4AF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000858: 102C2CF8
	v_sin_f32_e32 v37, v37                                     // 00000000085C: 7E4A6B25
	v_sin_f32_e32 v22, v22                                     // 000000000860: 7E2C6B16
	v_mul_f32_e32 v37, 0x472aee8c, v37                         // 000000000864: 104A4AFF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 00000000086C: 102C2CFF 472AEE8C
	v_fract_f32_e32 v37, v37                                   // 000000000874: 7E4A4125
	v_fract_f32_e32 v22, v22                                   // 000000000878: 7E2C4116
	v_sub_f32_e32 v22, v22, v37                                // 00000000087C: 082C4B16
	v_fmac_f32_e32 v37, v22, v20                               // 000000000880: 564A2916
	v_sub_f32_e32 v22, v37, v34                                // 000000000884: 082C4525
	v_fmac_f32_e32 v34, v22, v33                               // 000000000888: 56444316
	v_sub_f32_e32 v22, v34, v31                                // 00000000088C: 082C3F22
	v_fmac_f32_e32 v31, v22, v21                               // 000000000890: 563E2B16
	v_floor_f32_e32 v21, v12                                   // 000000000894: 7E2A490C
	v_fract_f32_e32 v12, v12                                   // 000000000898: 7E18410C
	v_fmac_f32_e32 v30, 0x42e20000, v21                        // 00000000089C: 563C2AFF 42E20000
	v_mul_f32_e32 v22, v12, v12                                // 0000000008A4: 102C190C
	v_add_f32_e32 v12, v12, v12                                // 0000000008A8: 0618190C
	v_mul_f32_e32 v21, 0.15915494, v30                         // 0000000008AC: 102A3CF8
	v_add_f32_e32 v34, 0x42680000, v30                         // 0000000008B0: 06443CFF 42680000
	v_fma_f32 v12, 0x40400000, v22, -v12                       // 0000000008B8: D54B000C 84322CFF 40400000
	v_add_f32_e32 v22, 1.0, v30                                // 0000000008C4: 062C3CF2
	v_sin_f32_e32 v21, v21                                     // 0000000008C8: 7E2A6B15
	v_mul_f32_e32 v34, 0.15915494, v34                         // 0000000008CC: 104444F8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000008D0: 102C2CF8
	v_sin_f32_e32 v34, v34                                     // 0000000008D4: 7E446B22
	v_sin_f32_e32 v22, v22                                     // 0000000008D8: 7E2C6B16
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 0000000008DC: 102A2AFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 0000000008E4: 7E2A4115
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 0000000008E8: 104444FF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000008F0: 102C2CFF 472AEE8C
	v_fract_f32_e32 v34, v34                                   // 0000000008F8: 7E444122
	v_fract_f32_e32 v22, v22                                   // 0000000008FC: 7E2C4116
	v_sub_f32_e32 v22, v22, v21                                // 000000000900: 082C2B16
	v_fmac_f32_e32 v21, v22, v20                               // 000000000904: 562A2916
	v_add_f32_e32 v22, 0x42640000, v30                         // 000000000908: 062C3CFF 42640000
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000910: 102C2CF8
	v_sin_f32_e32 v22, v22                                     // 000000000914: 7E2C6B16
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 000000000918: 102C2CFF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 000000000920: 7E2C4116
	v_sub_f32_e32 v34, v34, v22                                // 000000000924: 08442D22
	v_fmac_f32_e32 v22, v34, v20                               // 000000000928: 562C2922
	v_add_f32_e32 v34, 0x42e40000, v30                         // 00000000092C: 06443CFF 42E40000
	v_sub_f32_e32 v22, v22, v21                                // 000000000934: 082C2B16
	v_mul_f32_e32 v34, 0.15915494, v34                         // 000000000938: 104444F8
	v_fmac_f32_e32 v21, v22, v33                               // 00000000093C: 562A4316
	v_add_f32_e32 v22, 0x42e20000, v30                         // 000000000940: 062C3CFF 42E20000
	v_sin_f32_e32 v34, v34                                     // 000000000948: 7E446B22
	v_mul_f32_e32 v22, 0.15915494, v22                         // 00000000094C: 102C2CF8
	v_sin_f32_e32 v22, v22                                     // 000000000950: 7E2C6B16
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 000000000954: 104444FF 472AEE8C
	v_fract_f32_e32 v34, v34                                   // 00000000095C: 7E444122
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 000000000960: 102C2CFF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 000000000968: 7E2C4116
	v_sub_f32_e32 v34, v34, v22                                // 00000000096C: 08442D22
	v_fmac_f32_e32 v22, v34, v20                               // 000000000970: 562C2922
	v_add_f32_e32 v34, 0x432a0000, v30                         // 000000000974: 06443CFF 432A0000
	v_add_f32_e32 v30, 0x432b0000, v30                         // 00000000097C: 063C3CFF 432B0000
	v_mul_f32_e32 v34, 0.15915494, v34                         // 000000000984: 104444F8
	v_mul_f32_e32 v30, 0.15915494, v30                         // 000000000988: 103C3CF8
	v_sin_f32_e32 v34, v34                                     // 00000000098C: 7E446B22
	v_sin_f32_e32 v30, v30                                     // 000000000990: 7E3C6B1E
	v_mul_f32_e32 v34, 0x472aee8c, v34                         // 000000000994: 104444FF 472AEE8C
	v_mul_f32_e32 v30, 0x472aee8c, v30                         // 00000000099C: 103C3CFF 472AEE8C
	v_fract_f32_e32 v34, v34                                   // 0000000009A4: 7E444122
	v_fract_f32_e32 v30, v30                                   // 0000000009A8: 7E3C411E
	v_sub_f32_e32 v30, v30, v34                                // 0000000009AC: 083C451E
	v_fmac_f32_e32 v34, v30, v20                               // 0000000009B0: 5644291E
	v_mov_b32_e32 v30, 0                                       // 0000000009B4: 7E3C0280
	v_sub_f32_e32 v20, v34, v22                                // 0000000009B8: 08282D22
	v_mov_b32_e32 v34, 0                                       // 0000000009BC: 7E440280
	v_fmac_f32_e32 v22, v20, v33                               // 0000000009C0: 562C4314
	v_mov_b32_e32 v33, 0                                       // 0000000009C4: 7E420280
	v_sub_f32_e32 v20, v22, v21                                // 0000000009C8: 08282B16
	v_mul_f32_e64 v22, 0x40a00000, s1                          // 0000000009CC: D5080016 000002FF 40A00000
	v_fmac_f32_e32 v21, v20, v12                               // 0000000009D8: 562A1914
	v_add_f32_e32 v20, v36, v31                                // 0000000009DC: 06283F24
	v_add_f32_e32 v12, v21, v38                                // 0000000009E0: 06184D15
	v_sub_f32_e32 v20, v21, v20                                // 0000000009E4: 08282915
	v_add_f32_e32 v21, v32, v35                                // 0000000009E8: 062A4720
	v_sub_f32_e32 v12, v32, v12                                // 0000000009EC: 08181920
	v_add_f32_e32 v20, v20, v35                                // 0000000009F0: 06284714
	v_sub_f32_e32 v21, v36, v21                                // 0000000009F4: 082A2B24
	v_mov_b32_e32 v32, 0                                       // 0000000009F8: 7E400280
	v_mov_b32_e32 v36, v0                                      // 0000000009FC: 7E480300
	v_add_f32_e32 v12, v12, v31                                // 000000000A00: 06183F0C
	v_fmac_f32_e32 v6, v20, v22                                // 000000000A04: 560C2D14
	v_add_f32_e32 v21, v21, v38                                // 000000000A08: 062A4D15
	v_mov_b32_e32 v35, 0                                       // 000000000A0C: 7E460280
	v_mov_b32_e32 v31, 0                                       // 000000000A10: 7E3E0280
	v_fmac_f32_e32 v5, v12, v22                                // 000000000A14: 560A2D0C
	v_rcp_f32_e32 v12, v1                                      // 000000000A18: 7E185501
	v_fmac_f32_e32 v7, v21, v22                                // 000000000A1C: 560E2D15
	v_mul_f32_e32 v5, v5, v12                                  // 000000000A20: 100A1905
	v_mul_f32_e32 v6, v6, v12                                  // 000000000A24: 100C1906
	v_mul_f32_e32 v7, v7, v12                                  // 000000000A28: 100E1907
	v_fma_f32 v12, s18, v5, v17                                // 000000000A2C: D54B000C 04460A12
	v_fmac_f32_e32 v18, s18, v6                                // 000000000A34: 56240C12
	v_fmac_f32_e32 v19, s18, v7                                // 000000000A38: 56260E12
	s_waitcnt lgkmcnt(0)                                       // 000000000A3C: BF8CC07F
	v_mul_f32_e32 v17, s0, v12                                 // 000000000A40: 10221800
	v_mul_f32_e32 v18, s0, v18                                 // 000000000A44: 10242400
	v_mul_f32_e32 v19, s0, v19                                 // 000000000A48: 10262600
	v_lshlrev_b32_e32 v12, 4, v0                               // 000000000A4C: 34180084
	v_fma_f32 v13, s18, v17, v13                               // 000000000A50: D54B000D 04362212
	v_fmac_f32_e32 v14, s18, v18                               // 000000000A58: 561C2412
	v_fmac_f32_e32 v15, s18, v19                               // 000000000A5C: 561E2612
	ds_write_b96 v12, v[13:15]                                 // 000000000A60: DB780000 00000D0C
	v_mov_b32_e32 v12, 0                                       // 000000000A68: 7E180280
	s_waitcnt lgkmcnt(0)                                       // 000000000A6C: BF8CC07F
	s_waitcnt lgkmcnt(0)                                       // 000000000A70: BF8CC07F
	s_branch _L4                                               // 000000000A74: BF820007
_L9:
	s_or_b64 exec, exec, s[0:1]                                // 000000000A78: 88FE007E
_L8:
	s_or_b64 exec, exec, s[10:11]                              // 000000000A7C: 88FE0A7E
	v_add_nc_u32_e32 v36, -2, v36                              // 000000000A80: 4A4848C2
	s_add_i32 s13, s13, 2                                      // 000000000A84: 810D820D
	s_add_i32 s12, s12, 32                                     // 000000000A88: 810CA00C
	s_cmp_lg_u32 s13, 64                                       // 000000000A8C: BF07C00D
	s_cbranch_scc0 _L5                                         // 000000000A90: BF840045
_L4:
	s_mov_b64 s[10:11], exec                                   // 000000000A94: BE8A047E
	v_cmpx_ne_u32_e32 0, v36                                   // 000000000A98: 7DAA4880
	s_cbranch_execz _L6                                        // 000000000A9C: BF88001E
	v_mov_b32_e32 v20, s12                                     // 000000000AA0: 7E28020C
	ds_read_b96 v[20:22], v20                                  // 000000000AA4: DBF80000 14000014
	s_waitcnt lgkmcnt(0)                                       // 000000000AAC: BF8CC07F
	v_sub_f32_e32 v37, v14, v21                                // 000000000AB0: 084A2B0E
	v_sub_f32_e32 v38, v13, v20                                // 000000000AB4: 084C290D
	v_sub_f32_e32 v39, v15, v22                                // 000000000AB8: 084E2D0F
	v_mul_f32_e32 v41, v37, v37                                // 000000000ABC: 10524B25
	v_fmac_f32_e32 v41, v38, v38                               // 000000000AC0: 56524D26
	v_fmac_f32_e32 v41, v39, v39                               // 000000000AC4: 56524F27
	v_sqrt_f32_e32 v40, v41                                    // 000000000AC8: 7E506729
	v_cmp_lt_f32_e64 s0, 0, v41                                // 000000000ACC: D4010000 00025280
	v_cmp_gt_f32_e32 vcc_lo, 0x40a00000, v40                   // 000000000AD4: 7C0850FF 40A00000
	s_and_b64 s[14:15], s[0:1], vcc                            // 000000000ADC: 878E6A00
	s_and_saveexec_b64 s[0:1], s[14:15]                        // 000000000AE0: BE80240E
	s_cbranch_execz _L7                                        // 000000000AE4: BF88000B
	v_rcp_f32_e32 v40, v40                                     // 000000000AE8: 7E505528
	v_add_f32_e32 v32, v20, v32                                // 000000000AEC: 06404114
	v_add_f32_e32 v35, v21, v35                                // 000000000AF0: 06464715
	v_add_f32_e32 v33, v22, v33                                // 000000000AF4: 06424316
	v_add_nc_u32_e32 v34, 1, v34                               // 000000000AF8: 4A444481
	v_mul_legacy_f32_e32 v20, v38, v40                         // 000000000AFC: 0E285126
	v_mul_legacy_f32_e32 v37, v37, v40                         // 000000000B00: 0E4A5125
	v_mul_legacy_f32_e32 v38, v39, v40                         // 000000000B04: 0E4C5127
	v_fmac_f32_e32 v12, v20, v40                               // 000000000B08: 56185114
	v_fmac_f32_e32 v30, v37, v40                               // 000000000B0C: 563C5125
	v_fmac_f32_e32 v31, v38, v40                               // 000000000B10: 563E5126
_L7:
	s_or_b64 exec, exec, s[0:1]                                // 000000000B14: 88FE007E
_L6:
	s_or_b64 exec, exec, s[10:11]                              // 000000000B18: 88FE0A7E
	s_or_b32 s0, s13, 1                                        // 000000000B1C: 8800810D
	s_mov_b64 s[10:11], exec                                   // 000000000B20: BE8A047E
	v_cmpx_ne_u32_e64 s0, v0                                   // 000000000B24: D4D5007E 00020000
	s_cbranch_execz _L8                                        // 000000000B2C: BF88FFD3
	v_mov_b32_e32 v20, s12                                     // 000000000B30: 7E28020C
	ds_read_b96 v[20:22], v20 offset:16                        // 000000000B34: DBF80010 14000014
	s_waitcnt lgkmcnt(0)                                       // 000000000B3C: BF8CC07F
	v_sub_f32_e32 v37, v14, v21                                // 000000000B40: 084A2B0E
	v_sub_f32_e32 v38, v13, v20                                // 000000000B44: 084C290D
	v_sub_f32_e32 v39, v15, v22                                // 000000000B48: 084E2D0F
	v_mul_f32_e32 v41, v37, v37                                // 000000000B4C: 10524B25
	v_fmac_f32_e32 v41, v38, v38                               // 000000000B50: 56524D26
	v_fmac_f32_e32 v41, v39, v39                               // 000000000B54: 56524F27
	v_sqrt_f32_e32 v40, v41                                    // 000000000B58: 7E506729
	v_cmp_lt_f32_e64 s0, 0, v41                                // 000000000B5C: D4010000 00025280
	v_cmp_gt_f32_e32 vcc_lo, 0x40a00000, v40                   // 000000000B64: 7C0850FF 40A00000
	s_and_b64 s[14:15], s[0:1], vcc                            // 000000000B6C: 878E6A00
	s_and_saveexec_b64 s[0:1], s[14:15]                        // 000000000B70: BE80240E
	s_cbranch_execz _L9                                        // 000000000B74: BF88FFC0
	v_rcp_f32_e32 v40, v40                                     // 000000000B78: 7E505528
	v_add_f32_e32 v32, v20, v32                                // 000000000B7C: 06404114
	v_add_f32_e32 v35, v21, v35                                // 000000000B80: 06464715
	v_add_f32_e32 v33, v22, v33                                // 000000000B84: 06424316
	v_add_nc_u32_e32 v34, 1, v34                               // 000000000B88: 4A444481
	v_mul_legacy_f32_e32 v20, v38, v40                         // 000000000B8C: 0E285126
	v_mul_legacy_f32_e32 v37, v37, v40                         // 000000000B90: 0E4A5125
	v_mul_legacy_f32_e32 v38, v39, v40                         // 000000000B94: 0E4C5127
	v_fmac_f32_e32 v12, v20, v40                               // 000000000B98: 56185114
	v_fmac_f32_e32 v30, v37, v40                               // 000000000B9C: 563C5125
	v_fmac_f32_e32 v31, v38, v40                               // 000000000BA0: 563E5126
	s_branch _L9                                               // 000000000BA4: BF82FFB4
_L5:
	s_mov_b64 s[0:1], exec                                     // 000000000BA8: BE80047E
	v_cmpx_ne_u32_e32 0, v34                                   // 000000000BAC: 7DAA4480
	s_cbranch_execz _L10                                       // 000000000BB0: BF88001B
	v_cvt_f32_u32_e32 v0, v34                                  // 000000000BB4: 7E000D22
	v_rcp_iflag_f32_e32 v0, v0                                 // 000000000BB8: 7E005700
	v_fma_f32 v20, v0, v35, -v14                               // 000000000BBC: D54B0014 843A4700
	v_fma_f32 v21, v0, v32, -v13                               // 000000000BC4: D54B0015 84364100
	v_fma_f32 v0, v0, v33, -v15                                // 000000000BCC: D54B0000 843E4300
	v_mul_f32_e64 v32, 0x3dcccccd, s18                         // 000000000BD4: D5080020 000024FF 3DCCCCCD
	v_mul_f32_e64 v33, 0x3d4ccccd, s18                         // 000000000BE0: D5080021 000024FF 3D4CCCCD
	v_mul_f32_e32 v22, v20, v20                                // 000000000BEC: 102C2914
	v_fmac_f32_e32 v17, v12, v32                               // 000000000BF0: 5622410C
	v_fmac_f32_e32 v18, v30, v32                               // 000000000BF4: 5624411E
	v_fmac_f32_e32 v22, v21, v21                               // 000000000BF8: 562C2B15
	v_fmac_f32_e32 v19, v31, v32                               // 000000000BFC: 5626411F
	v_fmac_f32_e32 v22, v0, v0                                 // 000000000C00: 562C0100
	v_rsq_f32_e32 v22, v22                                     // 000000000C04: 7E2C5D16
	v_mul_legacy_f32_e32 v12, v21, v22                         // 000000000C08: 0E182D15
	v_mul_legacy_f32_e32 v20, v20, v22                         // 000000000C0C: 0E282D14
	v_mul_legacy_f32_e32 v0, v0, v22                           // 000000000C10: 0E002D00
	v_fmac_f32_e32 v17, v12, v33                               // 000000000C14: 5622430C
	v_fmac_f32_e32 v18, v20, v33                               // 000000000C18: 56244314
	v_fmac_f32_e32 v19, v0, v33                                // 000000000C1C: 56264300
_L10:
	s_or_b64 exec, exec, s[0:1]                                // 000000000C20: 88FE007E
	v_mul_f32_e32 v12, 0.5, v16                                // 000000000C24: 101820F0
	v_sub_f32_e64 v0, s9, s8                                   // 000000000C28: D5040000 00001009
	v_mov_b32_e32 v20, v2                                      // 000000000C30: 7E280302
	buffer_store_dwordx3 v[13:15], v23, s[4:7], 0 offen        // 000000000C34: E07C1000 80010D17
	buffer_store_dword v16, v29, s[4:7], 0 offen               // 000000000C3C: E0701000 8001101D
	v_fma_f32 v0, v0, v12, s8                                  // 000000000C44: D54B0000 00221900
	buffer_store_dwordx3 v[17:19], v28, s[4:7], 0 offen        // 000000000C4C: E07C1000 8001111C
	buffer_store_dword v0, v23, s[4:7], 0 offen offset:28      // 000000000C54: E070101C 80010017
	buffer_store_dwordx4 v[9:12], v27, s[4:7], 0 offen         // 000000000C5C: E0781000 8001091B
	buffer_store_dwordx3 v[5:7], v23, s[4:7], 0 offen offset:48// 000000000C64: E07C1030 80010517
_L3:
	s_or_b64 exec, exec, s[2:3]                                // 000000000C6C: 88FE027E
	s_waitcnt vmcnt(1)                                         // 000000000C70: BF8C3F71
	v_mov_b32_e32 v6, v3                                       // 000000000C74: 7E0C0303
	v_mov_b32_e32 v7, v4                                       // 000000000C78: 7E0E0304
	buffer_store_dword v1, v25, s[4:7], 0 offen                // 000000000C7C: E0701000 80010119
	buffer_store_dword v20, v26, s[4:7], 0 offen               // 000000000C84: E0701000 8001141A
	s_waitcnt vmcnt(0)                                         // 000000000C8C: BF8C3F70
	buffer_store_dwordx3 v[6:8], v24, s[4:7], 0 offen          // 000000000C90: E07C1000 80010618
_L0:
	s_endpgm                                                   // 000000000C98: BF810000
