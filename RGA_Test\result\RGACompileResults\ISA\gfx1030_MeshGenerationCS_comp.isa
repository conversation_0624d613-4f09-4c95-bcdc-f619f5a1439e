_amdgpu_cs_main:
	v_lshl_add_u32 v11, s2, 3, v0                              // 000000000000: D746000B 04010602
	v_lshl_add_u32 v12, s3, 3, v1                              // 000000000008: D746000C 04050603
	s_mov_b64 s[2:3], exec                                     // 000000000010: BE82047E
	v_or_b32_e32 v0, v11, v12                                  // 000000000014: 3800190B
	v_cmpx_gt_u32_e32 64, v0                                   // 000000000018: 7DA800C0
	s_cbranch_execz _L0                                        // 00000000001C: BF88026F
	s_getpc_b64 s[2:3]                                         // 000000000020: BE821F00
	s_mov_b32 s0, s1                                           // 000000000024: BE800301
	s_mov_b32 s1, s3                                           // 000000000028: BE810303
	v_cvt_f32_u32_e32 v0, v11                                  // 00000000002C: 7E000D0B
	s_load_dwordx8 s[0:7], s[0:1], null                        // 000000000030: F40C0000 FA000000
	s_mov_b32 s9, 0x3fcb2cb4                                   // 000000000038: BE8903FF 3FCB2CB4
	v_cvt_f32_u32_e32 v1, v12                                  // 000000000040: 7E020D0C
	v_fmaak_f32 v4, s9, v0, 0xc2480000                         // 000000000044: 5A080009 C2480000
	v_fmaak_f32 v6, s9, v1, 0xc2480000                         // 00000000004C: 5A0C0209 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000054: BF8CC07F
	s_buffer_load_dword s8, s[0:3], 0xc                        // 000000000058: F4200200 FA00000C
	s_waitcnt lgkmcnt(0)                                       // 000000000060: BF8CC07F
	v_mul_f32_e64 v2, 0x3dcccccd, s8                           // 000000000064: D5080002 000010FF 3DCCCCCD
	v_mul_f32_e64 v3, 0x3d4ccccd, s8                           // 000000000070: D5080003 000010FF 3D4CCCCD
	v_fmamk_f32 v5, v4, 0x3c23d70a, v2                         // 00000000007C: 580A0504 3C23D70A
	v_fmamk_f32 v7, v6, 0x3c23d70a, v2                         // 000000000084: 580E0506 3C23D70A
	v_floor_f32_e32 v8, v2                                     // 00000000008C: 7E104902
	v_fmamk_f32 v10, v4, 0x3d4ccccd, v3                        // 000000000090: 58140704 3D4CCCCD
	v_fract_f32_e32 v9, v2                                     // 000000000098: 7E124102
	v_floor_f32_e32 v15, v5                                    // 00000000009C: 7E1E4905
	v_fmamk_f32 v13, v6, 0x3d4ccccd, v3                        // 0000000000A0: 581A0706 3D4CCCCD
	v_floor_f32_e32 v14, v3                                    // 0000000000A8: 7E1C4903
	v_floor_f32_e32 v16, v7                                    // 0000000000AC: 7E204907
	v_fract_f32_e32 v5, v5                                     // 0000000000B0: 7E0A4105
	v_floor_f32_e32 v18, v10                                   // 0000000000B4: 7E24490A
	v_fmac_f32_e32 v15, 0x42640000, v8                         // 0000000000B8: 561E10FF 42640000
	v_mul_f32_e32 v17, v9, v9                                  // 0000000000C0: 10221309
	v_add_f32_e32 v9, v9, v9                                   // 0000000000C4: 06121309
	v_floor_f32_e32 v19, v13                                   // 0000000000C8: 7E26490D
	v_mul_f32_e32 v21, v5, v5                                  // 0000000000CC: 102A0B05
	v_add_f32_e32 v5, v5, v5                                   // 0000000000D0: 060A0B05
	v_fmac_f32_e32 v18, 0x42640000, v14                        // 0000000000D4: 56241CFF 42640000
	v_fmac_f32_e32 v15, 0x42e20000, v16                        // 0000000000DC: 561E20FF 42E20000
	v_fma_f32 v9, 0x40400000, v17, -v9                         // 0000000000E4: D54B0009 842622FF 40400000
	v_fract_f32_e32 v10, v10                                   // 0000000000F0: 7E14410A
	v_fma_f32 v5, 0x40400000, v21, -v5                         // 0000000000F4: D54B0005 84162AFF 40400000
	v_fmac_f32_e32 v18, 0x42e20000, v19                        // 000000000100: 562426FF 42E20000
	v_mul_f32_e32 v16, 0.15915494, v15                         // 000000000108: 10201EF8
	v_add_f32_e32 v17, 1.0, v15                                // 00000000010C: 06221EF2
	v_add_f32_e32 v19, 0x42640000, v15                         // 000000000110: 06261EFF 42640000
	v_add_f32_e32 v21, 0x42680000, v15                         // 000000000118: 062A1EFF 42680000
	v_add_f32_e32 v22, 0x42e20000, v15                         // 000000000120: 062C1EFF 42E20000
	v_add_f32_e32 v23, 0x42e40000, v15                         // 000000000128: 062E1EFF 42E40000
	v_add_f32_e32 v24, 0x432a0000, v15                         // 000000000130: 06301EFF 432A0000
	v_add_f32_e32 v15, 0x432b0000, v15                         // 000000000138: 061E1EFF 432B0000
	v_mul_f32_e32 v17, 0.15915494, v17                         // 000000000140: 102222F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000144: 102626F8
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000148: 102A2AF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 00000000014C: 102C2CF8
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000150: 102E2EF8
	v_mul_f32_e32 v24, 0.15915494, v24                         // 000000000154: 103030F8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 000000000158: 101E1EF8
	v_sin_f32_e32 v16, v16                                     // 00000000015C: 7E206B10
	v_sin_f32_e32 v17, v17                                     // 000000000160: 7E226B11
	v_sin_f32_e32 v19, v19                                     // 000000000164: 7E266B13
	v_sin_f32_e32 v21, v21                                     // 000000000168: 7E2A6B15
	v_sin_f32_e32 v22, v22                                     // 00000000016C: 7E2C6B16
	v_sin_f32_e32 v23, v23                                     // 000000000170: 7E2E6B17
	v_sin_f32_e32 v24, v24                                     // 000000000174: 7E306B18
	v_sin_f32_e32 v15, v15                                     // 000000000178: 7E1E6B0F
	v_mul_f32_e32 v25, 0.15915494, v18                         // 00000000017C: 103224F8
	v_add_f32_e32 v26, 1.0, v18                                // 000000000180: 063424F2
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 000000000184: 102020FF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 00000000018C: 102222FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000194: 102626FF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 00000000019C: 102A2AFF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000001A4: 102C2CFF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 0000000001AC: 102E2EFF 472AEE8C
	v_mul_f32_e32 v24, 0x472aee8c, v24                         // 0000000001B4: 103030FF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000001BC: 101E1EFF 472AEE8C
	v_fract_f32_e32 v16, v16                                   // 0000000001C4: 7E204110
	v_fract_f32_e32 v17, v17                                   // 0000000001C8: 7E224111
	v_fract_f32_e32 v19, v19                                   // 0000000001CC: 7E264113
	v_fract_f32_e32 v21, v21                                   // 0000000001D0: 7E2A4115
	v_fract_f32_e32 v22, v22                                   // 0000000001D4: 7E2C4116
	v_fract_f32_e32 v23, v23                                   // 0000000001D8: 7E2E4117
	v_fract_f32_e32 v24, v24                                   // 0000000001DC: 7E304118
	v_fract_f32_e32 v15, v15                                   // 0000000001E0: 7E1E410F
	v_sub_f32_e32 v17, v17, v16                                // 0000000001E4: 08222111
	v_sub_f32_e32 v21, v21, v19                                // 0000000001E8: 082A2715
	v_sub_f32_e32 v23, v23, v22                                // 0000000001EC: 082E2D17
	v_mul_f32_e32 v26, 0.15915494, v26                         // 0000000001F0: 103434F8
	v_sub_f32_e32 v15, v15, v24                                // 0000000001F4: 081E310F
	v_fmac_f32_e32 v16, v17, v5                                // 0000000001F8: 56200B11
	v_add_f32_e32 v17, 0x42640000, v18                         // 0000000001FC: 062224FF 42640000
	v_fmac_f32_e32 v19, v21, v5                                // 000000000204: 56260B15
	v_add_f32_e32 v21, 0x42680000, v18                         // 000000000208: 062A24FF 42680000
	v_fmac_f32_e32 v22, v23, v5                                // 000000000210: 562C0B17
	v_add_f32_e32 v23, 0x42e20000, v18                         // 000000000214: 062E24FF 42E20000
	v_fmac_f32_e32 v24, v15, v5                                // 00000000021C: 56300B0F
	v_add_f32_e32 v5, 0x42e40000, v18                          // 000000000220: 060A24FF 42E40000
	v_add_f32_e32 v15, 0x432a0000, v18                         // 000000000228: 061E24FF 432A0000
	v_add_f32_e32 v18, 0x432b0000, v18                         // 000000000230: 062424FF 432B0000
	v_mul_f32_e32 v17, 0.15915494, v17                         // 000000000238: 102222F8
	v_mul_f32_e32 v21, 0.15915494, v21                         // 00000000023C: 102A2AF8
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000240: 102E2EF8
	v_mul_f32_e32 v5, 0.15915494, v5                           // 000000000244: 100A0AF8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 000000000248: 101E1EF8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 00000000024C: 102424F8
	v_sin_f32_e32 v25, v25                                     // 000000000250: 7E326B19
	v_sin_f32_e32 v26, v26                                     // 000000000254: 7E346B1A
	v_sin_f32_e32 v17, v17                                     // 000000000258: 7E226B11
	v_sin_f32_e32 v21, v21                                     // 00000000025C: 7E2A6B15
	v_sin_f32_e32 v23, v23                                     // 000000000260: 7E2E6B17
	v_sin_f32_e32 v5, v5                                       // 000000000264: 7E0A6B05
	v_sin_f32_e32 v15, v15                                     // 000000000268: 7E1E6B0F
	v_sin_f32_e32 v18, v18                                     // 00000000026C: 7E246B12
	v_sub_f32_e32 v19, v19, v16                                // 000000000270: 08262113
	v_mul_f32_e32 v14, v10, v10                                // 000000000274: 101C150A
	v_mul_f32_e32 v25, 0x472aee8c, v25                         // 000000000278: 103232FF 472AEE8C
	v_mul_f32_e32 v26, 0x472aee8c, v26                         // 000000000280: 103434FF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 000000000288: 102222FF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000290: 102A2AFF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 000000000298: 102E2EFF 472AEE8C
	v_mul_f32_e32 v5, 0x472aee8c, v5                           // 0000000002A0: 100A0AFF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000002A8: 101E1EFF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 0000000002B0: 102424FF 472AEE8C
	v_fmac_f32_e32 v16, v19, v9                                // 0000000002B8: 56201313
	v_add_f32_e32 v10, v10, v10                                // 0000000002BC: 0614150A
	v_fract_f32_e32 v19, v25                                   // 0000000002C0: 7E264119
	v_fract_f32_e32 v25, v26                                   // 0000000002C4: 7E32411A
	v_fract_f32_e32 v17, v17                                   // 0000000002C8: 7E224111
	v_fract_f32_e32 v21, v21                                   // 0000000002CC: 7E2A4115
	v_fract_f32_e32 v23, v23                                   // 0000000002D0: 7E2E4117
	v_fract_f32_e32 v5, v5                                     // 0000000002D4: 7E0A4105
	v_fract_f32_e32 v15, v15                                   // 0000000002D8: 7E1E410F
	v_fract_f32_e32 v18, v18                                   // 0000000002DC: 7E244112
	v_fract_f32_e32 v3, v3                                     // 0000000002E0: 7E064103
	v_fma_f32 v10, 0x40400000, v14, -v10                       // 0000000002E4: D54B000A 842A1CFF 40400000
	v_sub_f32_e32 v14, v25, v19                                // 0000000002F0: 081C2719
	v_sub_f32_e32 v21, v21, v17                                // 0000000002F4: 082A2315
	v_sub_f32_e32 v5, v5, v23                                  // 0000000002F8: 080A2F05
	v_sub_f32_e32 v18, v18, v15                                // 0000000002FC: 08241F12
	v_mul_f32_e32 v20, v3, v3                                  // 000000000300: 10280703
	v_add_f32_e32 v3, v3, v3                                   // 000000000304: 06060703
	v_fmac_f32_e32 v19, v14, v10                               // 000000000308: 5626150E
	v_fmac_f32_e32 v17, v21, v10                               // 00000000030C: 56221515
	v_fmac_f32_e32 v23, v5, v10                                // 000000000310: 562E1505
	v_fmac_f32_e32 v15, v18, v10                               // 000000000314: 561E1512
	v_fract_f32_e32 v7, v7                                     // 000000000318: 7E0E4107
	v_sub_f32_e32 v5, v24, v22                                 // 00000000031C: 080A2D18
	v_fract_f32_e32 v10, v13                                   // 000000000320: 7E14410D
	v_fma_f32 v3, 0x40400000, v20, -v3                         // 000000000324: D54B0003 840E28FF 40400000
	v_sub_f32_e32 v13, v17, v19                                // 000000000330: 081A2711
	v_sub_f32_e32 v14, v15, v23                                // 000000000334: 081C2F0F
	v_mul_f32_e32 v8, v7, v7                                   // 000000000338: 10100F07
	v_add_f32_e32 v7, v7, v7                                   // 00000000033C: 060E0F07
	v_fmac_f32_e32 v22, v5, v9                                 // 000000000340: 562C1305
	v_mul_f32_e32 v5, v10, v10                                 // 000000000344: 100A150A
	v_add_f32_e32 v9, v10, v10                                 // 000000000348: 0612150A
	v_fmac_f32_e32 v19, v13, v3                                // 00000000034C: 5626070D
	v_fmac_f32_e32 v23, v14, v3                                // 000000000350: 562E070E
	v_fma_f32 v3, 0x40400000, v8, -v7                          // 000000000354: D54B0003 841E10FF 40400000
	v_sub_f32_e32 v7, v22, v16                                 // 000000000360: 080E2116
	v_fma_f32 v5, 0x40400000, v5, -v9                          // 000000000364: D54B0005 84260AFF 40400000
	v_add_nc_u32_e32 v9, -1, v11                               // 000000000370: 4A1216C1
	v_sub_f32_e32 v8, v23, v19                                 // 000000000374: 08102717
	v_add_nc_u32_e32 v10, -1, v12                              // 000000000378: 4A1418C1
	v_fmac_f32_e32 v16, v7, v3                                 // 00000000037C: 56200707
	v_mov_b32_e32 v7, 0                                        // 000000000380: 7E0E0280
	v_fma_f32 v5, v8, v5, v19 mul:2                            // 000000000384: D54B0005 0C4E0B08
	v_max_u32_e32 v3, v9, v10                                  // 00000000038C: 28061509
	v_mov_b32_e32 v8, 1.0                                      // 000000000390: 7E1002F2
	v_mov_b32_e32 v9, v7                                       // 000000000394: 7E120307
	v_fmac_f32_e32 v5, 0x41200000, v16                         // 000000000398: 560A20FF 41200000
	v_cmp_gt_u32_e32 vcc_lo, 62, v3                            // 0000000003A0: 7D8806BE
	v_mov_b32_e32 v3, 1.0                                      // 0000000003A4: 7E0602F2
	s_and_saveexec_b64 s[8:9], vcc                             // 0000000003A8: BE88246A
	s_cbranch_execz _L1                                        // 0000000003AC: BF880162
	v_add_f32_e32 v13, v4, v2                                  // 0000000003B0: 061A0504
	v_add_f32_e32 v7, v5, v2                                   // 0000000003B4: 060E0505
	v_add_f32_e32 v14, v6, v2                                  // 0000000003B8: 061C0506
	v_add_f32_e32 v2, 0xbc23d70a, v13                          // 0000000003BC: 06041AFF BC23D70A
	v_floor_f32_e32 v15, v7                                    // 0000000003C4: 7E1E4907
	v_floor_f32_e32 v16, v14                                   // 0000000003C8: 7E20490E
	v_fract_f32_e32 v7, v7                                     // 0000000003CC: 7E0E4107
	v_fract_f32_e32 v8, v14                                    // 0000000003D0: 7E10410E
	v_floor_f32_e32 v9, v2                                     // 0000000003D4: 7E124902
	v_fract_f32_e32 v2, v2                                     // 0000000003D8: 7E044102
	v_mul_f32_e32 v18, v7, v7                                  // 0000000003DC: 10240F07
	v_add_f32_e32 v7, v7, v7                                   // 0000000003E0: 060E0F07
	v_fmac_f32_e32 v9, 0x42640000, v15                         // 0000000003E4: 56121EFF 42640000
	v_mul_f32_e32 v19, v8, v8                                  // 0000000003EC: 10261108
	v_add_f32_e32 v20, v8, v8                                  // 0000000003F0: 06281108
	v_mul_f32_e32 v17, v2, v2                                  // 0000000003F4: 10220502
	v_fma_f32 v8, 0x40400000, v18, -v7                         // 0000000003F8: D54B0008 841E24FF 40400000
	v_fmac_f32_e32 v9, 0x42e20000, v16                         // 000000000404: 561220FF 42E20000
	v_add_f32_e32 v2, v2, v2                                   // 00000000040C: 06040502
	v_add_f32_e32 v18, 1.0, v9                                 // 000000000410: 062412F2
	v_mul_f32_e32 v7, 0.15915494, v9                           // 000000000414: 100E12F8
	v_fma_f32 v17, 0x40400000, v17, -v2                        // 000000000418: D54B0011 840A22FF 40400000
	v_fma_f32 v2, 0x40400000, v19, -v20                        // 000000000424: D54B0002 845226FF 40400000
	v_add_f32_e32 v19, 0x42680000, v9                          // 000000000430: 062612FF 42680000
	v_mul_f32_e32 v18, 0.15915494, v18                         // 000000000438: 102424F8
	v_sin_f32_e32 v7, v7                                       // 00000000043C: 7E0E6B07
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000440: 102626F8
	v_sin_f32_e32 v18, v18                                     // 000000000444: 7E246B12
	v_sin_f32_e32 v19, v19                                     // 000000000448: 7E266B13
	v_mul_f32_e32 v7, 0x472aee8c, v7                           // 00000000044C: 100E0EFF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 000000000454: 102424FF 472AEE8C
	v_fract_f32_e32 v7, v7                                     // 00000000045C: 7E0E4107
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000460: 102626FF 472AEE8C
	v_fract_f32_e32 v18, v18                                   // 000000000468: 7E244112
	v_fract_f32_e32 v19, v19                                   // 00000000046C: 7E264113
	v_sub_f32_e32 v18, v18, v7                                 // 000000000470: 08240F12
	v_fmac_f32_e32 v7, v18, v17                                // 000000000474: 560E2312
	v_add_f32_e32 v18, 0x42640000, v9                          // 000000000478: 062412FF 42640000
	v_mul_f32_e32 v18, 0.15915494, v18                         // 000000000480: 102424F8
	v_sin_f32_e32 v18, v18                                     // 000000000484: 7E246B12
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 000000000488: 102424FF 472AEE8C
	v_fract_f32_e32 v18, v18                                   // 000000000490: 7E244112
	v_sub_f32_e32 v19, v19, v18                                // 000000000494: 08262513
	v_fmac_f32_e32 v18, v19, v17                               // 000000000498: 56242313
	v_add_f32_e32 v19, 0x42e40000, v9                          // 00000000049C: 062612FF 42E40000
	v_sub_f32_e32 v18, v18, v7                                 // 0000000004A4: 08240F12
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000004A8: 102626F8
	v_fmac_f32_e32 v7, v18, v8                                 // 0000000004AC: 560E1112
	v_add_f32_e32 v18, 0x42e20000, v9                          // 0000000004B0: 062412FF 42E20000
	v_sin_f32_e32 v19, v19                                     // 0000000004B8: 7E266B13
	v_mul_f32_e32 v18, 0.15915494, v18                         // 0000000004BC: 102424F8
	v_sin_f32_e32 v18, v18                                     // 0000000004C0: 7E246B12
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 0000000004C4: 102626FF 472AEE8C
	v_fract_f32_e32 v19, v19                                   // 0000000004CC: 7E264113
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 0000000004D0: 102424FF 472AEE8C
	v_fract_f32_e32 v18, v18                                   // 0000000004D8: 7E244112
	v_sub_f32_e32 v19, v19, v18                                // 0000000004DC: 08262513
	v_fmac_f32_e32 v18, v19, v17                               // 0000000004E0: 56242313
	v_add_f32_e32 v19, 0x432a0000, v9                          // 0000000004E4: 062612FF 432A0000
	v_add_f32_e32 v9, 0x432b0000, v9                           // 0000000004EC: 061212FF 432B0000
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000004F4: 102626F8
	v_mul_f32_e32 v9, 0.15915494, v9                           // 0000000004F8: 101212F8
	v_sin_f32_e32 v19, v19                                     // 0000000004FC: 7E266B13
	v_sin_f32_e32 v9, v9                                       // 000000000500: 7E126B09
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000504: 102626FF 472AEE8C
	v_mul_f32_e32 v9, 0x472aee8c, v9                           // 00000000050C: 101212FF 472AEE8C
	v_fract_f32_e32 v19, v19                                   // 000000000514: 7E264113
	v_fract_f32_e32 v9, v9                                     // 000000000518: 7E124109
	v_sub_f32_e32 v9, v9, v19                                  // 00000000051C: 08122709
	v_fmac_f32_e32 v19, v9, v17                                // 000000000520: 56262309
	v_add_f32_e32 v17, 0x3c23d70a, v13                         // 000000000524: 06221AFF 3C23D70A
	v_sub_f32_e32 v9, v19, v18                                 // 00000000052C: 08122513
	v_fmac_f32_e32 v18, v9, v8                                 // 000000000530: 56241109
	v_sub_f32_e32 v9, v18, v7                                  // 000000000534: 08120F12
	v_floor_f32_e32 v18, v17                                   // 000000000538: 7E244911
	v_fract_f32_e32 v17, v17                                   // 00000000053C: 7E224111
	v_fmac_f32_e32 v7, v9, v2                                  // 000000000540: 560E0509
	v_fmac_f32_e32 v18, 0x42640000, v15                        // 000000000544: 56241EFF 42640000
	v_mul_f32_e32 v19, v17, v17                                // 00000000054C: 10262311
	v_add_f32_e32 v17, v17, v17                                // 000000000550: 06222311
	v_fmac_f32_e32 v18, 0x42e20000, v16                        // 000000000554: 562420FF 42E20000
	v_fma_f32 v17, 0x40400000, v19, -v17                       // 00000000055C: D54B0011 844626FF 40400000
	v_add_f32_e32 v19, 1.0, v18                                // 000000000568: 062624F2
	v_mul_f32_e32 v16, 0.15915494, v18                         // 00000000056C: 102024F8
	v_add_f32_e32 v20, 0x42680000, v18                         // 000000000570: 062824FF 42680000
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000578: 102626F8
	v_sin_f32_e32 v16, v16                                     // 00000000057C: 7E206B10
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000580: 102828F8
	v_sin_f32_e32 v19, v19                                     // 000000000584: 7E266B13
	v_sin_f32_e32 v20, v20                                     // 000000000588: 7E286B14
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 00000000058C: 102020FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000594: 102626FF 472AEE8C
	v_fract_f32_e32 v16, v16                                   // 00000000059C: 7E204110
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 0000000005A0: 102828FF 472AEE8C
	v_fract_f32_e32 v19, v19                                   // 0000000005A8: 7E264113
	v_fract_f32_e32 v20, v20                                   // 0000000005AC: 7E284114
	v_sub_f32_e32 v19, v19, v16                                // 0000000005B0: 08262113
	v_fmac_f32_e32 v16, v19, v17                               // 0000000005B4: 56202313
	v_add_f32_e32 v19, 0x42640000, v18                         // 0000000005B8: 062624FF 42640000
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000005C0: 102626F8
	v_sin_f32_e32 v19, v19                                     // 0000000005C4: 7E266B13
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 0000000005C8: 102626FF 472AEE8C
	v_fract_f32_e32 v19, v19                                   // 0000000005D0: 7E264113
	v_sub_f32_e32 v20, v20, v19                                // 0000000005D4: 08282714
	v_fmac_f32_e32 v19, v20, v17                               // 0000000005D8: 56262314
	v_add_f32_e32 v20, 0x42e40000, v18                         // 0000000005DC: 062824FF 42E40000
	v_sub_f32_e32 v19, v19, v16                                // 0000000005E4: 08262113
	v_mul_f32_e32 v20, 0.15915494, v20                         // 0000000005E8: 102828F8
	v_fmac_f32_e32 v16, v19, v8                                // 0000000005EC: 56201113
	v_add_f32_e32 v19, 0x42e20000, v18                         // 0000000005F0: 062624FF 42E20000
	v_sin_f32_e32 v20, v20                                     // 0000000005F8: 7E286B14
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000005FC: 102626F8
	v_sin_f32_e32 v19, v19                                     // 000000000600: 7E266B13
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 000000000604: 102828FF 472AEE8C
	v_fract_f32_e32 v20, v20                                   // 00000000060C: 7E284114
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000610: 102626FF 472AEE8C
	v_fract_f32_e32 v19, v19                                   // 000000000618: 7E264113
	v_sub_f32_e32 v20, v20, v19                                // 00000000061C: 08282714
	v_fmac_f32_e32 v19, v20, v17                               // 000000000620: 56262314
	v_add_f32_e32 v20, 0x432a0000, v18                         // 000000000624: 062824FF 432A0000
	v_add_f32_e32 v18, 0x432b0000, v18                         // 00000000062C: 062424FF 432B0000
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000634: 102828F8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 000000000638: 102424F8
	v_sin_f32_e32 v20, v20                                     // 00000000063C: 7E286B14
	v_sin_f32_e32 v18, v18                                     // 000000000640: 7E246B12
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 000000000644: 102828FF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 00000000064C: 102424FF 472AEE8C
	v_fract_f32_e32 v20, v20                                   // 000000000654: 7E284114
	v_fract_f32_e32 v18, v18                                   // 000000000658: 7E244112
	v_sub_f32_e32 v18, v18, v20                                // 00000000065C: 08242912
	v_fmac_f32_e32 v20, v18, v17                               // 000000000660: 56282312
	v_add_f32_e32 v18, 0xbc23d70a, v14                         // 000000000664: 06241CFF BC23D70A
	v_add_f32_e32 v14, 0x3c23d70a, v14                         // 00000000066C: 061C1CFF 3C23D70A
	v_sub_f32_e32 v17, v20, v19                                // 000000000674: 08222714
	v_floor_f32_e32 v20, v18                                   // 000000000678: 7E284912
	v_fract_f32_e32 v18, v18                                   // 00000000067C: 7E244112
	v_fmac_f32_e32 v19, v17, v8                                // 000000000680: 56261111
	v_mul_f32_e32 v22, v18, v18                                // 000000000684: 102C2512
	v_add_f32_e32 v18, v18, v18                                // 000000000688: 06242512
	v_sub_f32_e32 v17, v19, v16                                // 00000000068C: 08222113
	v_floor_f32_e32 v19, v13                                   // 000000000690: 7E26490D
	v_fract_f32_e32 v13, v13                                   // 000000000694: 7E1A410D
	v_fma_f32 v18, 0x40400000, v22, -v18                       // 000000000698: D54B0012 844A2CFF 40400000
	v_fmac_f32_e32 v16, v2, v17                                // 0000000006A4: 56202302
	v_fmac_f32_e32 v19, 0x42640000, v15                        // 0000000006A8: 56261EFF 42640000
	v_mul_f32_e32 v21, v13, v13                                // 0000000006B0: 102A1B0D
	v_add_f32_e32 v13, v13, v13                                // 0000000006B4: 061A1B0D
	v_sub_f32_e32 v2, v7, v16                                  // 0000000006B8: 08042107
	v_fmamk_f32 v15, v20, 0x42e20000, v19                      // 0000000006BC: 581E2714 42E20000
	v_fma_f32 v13, 0x40400000, v21, -v13                       // 0000000006C4: D54B000D 84362AFF 40400000
	v_mul_f32_e32 v2, 0x41200000, v2                           // 0000000006D0: 100404FF 41200000
	v_add_f32_e32 v21, 1.0, v15                                // 0000000006D8: 062A1EF2
	v_mul_f32_e32 v20, 0.15915494, v15                         // 0000000006DC: 10281EF8
	v_add_f32_e32 v22, 0x42680000, v15                         // 0000000006E0: 062C1EFF 42680000
	v_mul_f32_e32 v21, 0.15915494, v21                         // 0000000006E8: 102A2AF8
	v_sin_f32_e32 v20, v20                                     // 0000000006EC: 7E286B14
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000006F0: 102C2CF8
	v_sin_f32_e32 v21, v21                                     // 0000000006F4: 7E2A6B15
	v_sin_f32_e32 v22, v22                                     // 0000000006F8: 7E2C6B16
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 0000000006FC: 102828FF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000704: 102A2AFF 472AEE8C
	v_fract_f32_e32 v20, v20                                   // 00000000070C: 7E284114
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 000000000710: 102C2CFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 000000000718: 7E2A4115
	v_fract_f32_e32 v22, v22                                   // 00000000071C: 7E2C4116
	v_sub_f32_e32 v21, v21, v20                                // 000000000720: 082A2915
	v_fmac_f32_e32 v20, v21, v13                               // 000000000724: 56281B15
	v_add_f32_e32 v21, 0x42640000, v15                         // 000000000728: 062A1EFF 42640000
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000730: 102A2AF8
	v_sin_f32_e32 v21, v21                                     // 000000000734: 7E2A6B15
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000738: 102A2AFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 000000000740: 7E2A4115
	v_sub_f32_e32 v22, v22, v21                                // 000000000744: 082C2B16
	v_fmac_f32_e32 v21, v22, v13                               // 000000000748: 562A1B16
	v_add_f32_e32 v22, 0x42e40000, v15                         // 00000000074C: 062C1EFF 42E40000
	v_sub_f32_e32 v21, v21, v20                                // 000000000754: 082A2915
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000758: 102C2CF8
	v_fmac_f32_e32 v20, v21, v8                                // 00000000075C: 56281115
	v_add_f32_e32 v21, 0x42e20000, v15                         // 000000000760: 062A1EFF 42E20000
	v_sin_f32_e32 v22, v22                                     // 000000000768: 7E2C6B16
	v_mul_f32_e32 v21, 0.15915494, v21                         // 00000000076C: 102A2AF8
	v_sin_f32_e32 v21, v21                                     // 000000000770: 7E2A6B15
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 000000000774: 102C2CFF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 00000000077C: 7E2C4116
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000780: 102A2AFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 000000000788: 7E2A4115
	v_sub_f32_e32 v22, v22, v21                                // 00000000078C: 082C2B16
	v_fmac_f32_e32 v21, v22, v13                               // 000000000790: 562A1B16
	v_add_f32_e32 v22, 0x432a0000, v15                         // 000000000794: 062C1EFF 432A0000
	v_add_f32_e32 v15, 0x432b0000, v15                         // 00000000079C: 061E1EFF 432B0000
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000007A4: 102C2CF8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 0000000007A8: 101E1EF8
	v_sin_f32_e32 v22, v22                                     // 0000000007AC: 7E2C6B16
	v_sin_f32_e32 v15, v15                                     // 0000000007B0: 7E1E6B0F
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 0000000007B4: 102C2CFF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000007BC: 101E1EFF 472AEE8C
	v_fract_f32_e32 v22, v22                                   // 0000000007C4: 7E2C4116
	v_fract_f32_e32 v15, v15                                   // 0000000007C8: 7E1E410F
	v_sub_f32_e32 v15, v15, v22                                // 0000000007CC: 081E2D0F
	v_fmac_f32_e32 v22, v15, v13                               // 0000000007D0: 562C1B0F
	v_sub_f32_e32 v15, v22, v21                                // 0000000007D4: 081E2B16
	v_fmac_f32_e32 v21, v15, v8                                // 0000000007D8: 562A110F
	v_sub_f32_e32 v15, v21, v20                                // 0000000007DC: 081E2915
	v_floor_f32_e32 v21, v14                                   // 0000000007E0: 7E2A490E
	v_fract_f32_e32 v14, v14                                   // 0000000007E4: 7E1C410E
	v_fmac_f32_e32 v20, v15, v18                               // 0000000007E8: 5628250F
	v_fmac_f32_e32 v19, 0x42e20000, v21                        // 0000000007EC: 56262AFF 42E20000
	v_mul_f32_e32 v22, v14, v14                                // 0000000007F4: 102C1D0E
	v_add_f32_e32 v21, 1.0, v19                                // 0000000007F8: 062A26F2
	v_mul_f32_e32 v22, 0x40400000, v22                         // 0000000007FC: 102C2CFF 40400000
	v_add_f32_e32 v23, 0x42680000, v19                         // 000000000804: 062E26FF 42680000
	v_mul_f32_e32 v21, 0.15915494, v21                         // 00000000080C: 102A2AF8
	v_fmac_f32_e32 v22, -2.0, v14                              // 000000000810: 562C1CF5
	v_mul_f32_e32 v14, 0.15915494, v19                         // 000000000814: 101C26F8
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000818: 102E2EF8
	v_sin_f32_e32 v21, v21                                     // 00000000081C: 7E2A6B15
	v_sin_f32_e32 v14, v14                                     // 000000000820: 7E1C6B0E
	v_sin_f32_e32 v23, v23                                     // 000000000824: 7E2E6B17
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000828: 102A2AFF 472AEE8C
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 000000000830: 101C1CFF 472AEE8C
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 000000000838: 102E2EFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 000000000840: 7E2A4115
	v_fract_f32_e32 v14, v14                                   // 000000000844: 7E1C410E
	v_fract_f32_e32 v23, v23                                   // 000000000848: 7E2E4117
	v_sub_f32_e32 v21, v21, v14                                // 00000000084C: 082A1D15
	v_fmac_f32_e32 v14, v21, v13                               // 000000000850: 561C1B15
	v_add_f32_e32 v21, 0x42640000, v19                         // 000000000854: 062A26FF 42640000
	v_mul_f32_e32 v21, 0.15915494, v21                         // 00000000085C: 102A2AF8
	v_sin_f32_e32 v21, v21                                     // 000000000860: 7E2A6B15
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000864: 102A2AFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 00000000086C: 7E2A4115
	v_sub_f32_e32 v23, v23, v21                                // 000000000870: 082E2B17
	v_fmac_f32_e32 v21, v23, v13                               // 000000000874: 562A1B17
	v_add_f32_e32 v23, 0x42e40000, v19                         // 000000000878: 062E26FF 42E40000
	v_sub_f32_e32 v21, v21, v14                                // 000000000880: 082A1D15
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000884: 102E2EF8
	v_fmac_f32_e32 v14, v21, v8                                // 000000000888: 561C1115
	v_add_f32_e32 v21, 0x42e20000, v19                         // 00000000088C: 062A26FF 42E20000
	v_sin_f32_e32 v23, v23                                     // 000000000894: 7E2E6B17
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000898: 102A2AF8
	v_sin_f32_e32 v21, v21                                     // 00000000089C: 7E2A6B15
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 0000000008A0: 102E2EFF 472AEE8C
	v_fract_f32_e32 v23, v23                                   // 0000000008A8: 7E2E4117
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 0000000008AC: 102A2AFF 472AEE8C
	v_fract_f32_e32 v21, v21                                   // 0000000008B4: 7E2A4115
	v_sub_f32_e32 v23, v23, v21                                // 0000000008B8: 082E2B17
	v_fmac_f32_e32 v21, v23, v13                               // 0000000008BC: 562A1B17
	v_add_f32_e32 v23, 0x432a0000, v19                         // 0000000008C0: 062E26FF 432A0000
	v_add_f32_e32 v19, 0x432b0000, v19                         // 0000000008C8: 062626FF 432B0000
	v_mul_f32_e32 v23, 0.15915494, v23                         // 0000000008D0: 102E2EF8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000008D4: 102626F8
	v_sin_f32_e32 v23, v23                                     // 0000000008D8: 7E2E6B17
	v_sin_f32_e32 v19, v19                                     // 0000000008DC: 7E266B13
	v_mul_f32_e32 v23, 0x472aee8c, v23                         // 0000000008E0: 102E2EFF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 0000000008E8: 102626FF 472AEE8C
	v_fract_f32_e32 v23, v23                                   // 0000000008F0: 7E2E4117
	v_fract_f32_e32 v19, v19                                   // 0000000008F4: 7E264113
	v_sub_f32_e32 v19, v19, v23                                // 0000000008F8: 08262F13
	v_fmac_f32_e32 v23, v19, v13                               // 0000000008FC: 562E1B13
	v_sub_f32_e32 v13, v23, v21                                // 000000000900: 081A2B17
	v_fmac_f32_e32 v21, v13, v8                                // 000000000904: 562A110D
	v_sub_f32_e32 v8, v21, v14                                 // 000000000908: 08101D15
	v_fmac_f32_e32 v14, v22, v8                                // 00000000090C: 561C1116
	v_sub_f32_e32 v7, v20, v14                                 // 000000000910: 080E1D14
	v_mul_f32_e32 v9, 0x41200000, v7                           // 000000000914: 10120EFF 41200000
	v_fma_f32 v7, v2, v2, 4.0                                  // 00000000091C: D54B0007 03DA0502
	v_fmac_f32_e32 v7, v9, v9                                  // 000000000924: 560E1309
	v_rsq_f32_e32 v13, v7                                      // 000000000928: 7E1A5D07
	v_mul_legacy_f32_e32 v7, v2, v13                           // 00000000092C: 0E0E1B02
	v_add_f32_e32 v8, v13, v13                                 // 000000000930: 06101B0D
	v_mul_legacy_f32_e32 v9, v9, v13                           // 000000000934: 0E121B09
_L1:
	s_or_b64 exec, exec, s[8:9]                                // 000000000938: 88FE087E
	v_lshlrev_b32_e32 v14, 6, v12                              // 00000000093C: 341C1886
	v_max_u32_e32 v15, v11, v12                                // 000000000940: 281E190B
	v_mul_f32_e32 v0, 0x3c820821, v0                           // 000000000944: 100000FF 3C820821
	v_mul_f32_e32 v1, 0x3c820821, v1                           // 00000000094C: 100202FF 3C820821
	v_mov_b32_e32 v2, 0.5                                      // 000000000954: 7E0402F0
	v_or_b32_e32 v13, v14, v11                                 // 000000000958: 381A170E
	v_cmp_gt_u32_e32 vcc_lo, 63, v15                           // 00000000095C: 7D881EBF
	v_lshlrev_b32_e32 v16, 6, v13                              // 000000000960: 34201A86
	buffer_store_dwordx3 v[4:6], v16, s[0:3], 0 offen          // 000000000964: E07C1000 80000410
	buffer_store_dwordx3 v[7:9], v16, s[0:3], 0 offen offset:16// 00000000096C: E07C1010 80000710
	buffer_store_dwordx2 v[0:1], v16, s[0:3], 0 offen offset:32// 000000000974: E0741020 80000010
	buffer_store_dwordx4 v[0:3], v16, s[0:3], 0 offen offset:48// 00000000097C: E0781030 80000010
	s_and_b64 exec, exec, vcc                                  // 000000000984: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000988: BF880014
	v_mad_u64_u32 v[1:2], null, v12, 63, v[10:11]              // 00000000098C: D5767D01 04297F0C
	v_mul_lo_u32 v4, v11, 6                                    // 000000000994: D5690004 00010D0B
	v_add_nc_u32_e32 v3, v11, v12                              // 00000000099C: 4A06190B
	v_add_nc_u32_e32 v0, 1, v13                                // 0000000009A0: 4A001A81
	v_add_nc_u32_e32 v2, 64, v3                                // 0000000009A4: 4A0406C0
	v_add_nc_u32_e32 v3, 0x41, v3                              // 0000000009A8: 4A0606FF 00000041
	v_add_lshl_u32 v5, v1, v4, 2                               // 0000000009B0: D7470005 020A0901
	v_add_lshl_u32 v4, v14, v4, 2                              // 0000000009B8: D7470004 020A090E
	v_mov_b32_e32 v1, v0                                       // 0000000009C0: 7E020300
	buffer_store_dword v13, v5, s[4:7], 0 offen                // 0000000009C4: E0701000 80010D05
	buffer_store_dword v2, v4, s[4:7], 0 offen                 // 0000000009CC: E0701000 80010204
	buffer_store_dwordx4 v[0:3], v5, s[4:7], 0 offen offset:8  // 0000000009D4: E0781008 80010005
_L0:
	s_endpgm                                                   // 0000000009DC: BF810000
