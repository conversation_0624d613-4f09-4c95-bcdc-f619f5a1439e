#version 320 es

// Tessellation Vertex Shader - OpenGL ES Version
// Tests vertex processing for tessellation input

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;

uniform mat4 uModelMatrix;

out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;

void main()
{
    // Transform to world space
    vec4 worldPos = uModelMatrix * vec4(aPosition, 1.0);
    vWorldPos = worldPos.xyz;
    vNormal = aNormal;
    vTexCoord = aTexCoord;
    
    // Pass position to tessellation control shader
    gl_Position = worldPos;
}
