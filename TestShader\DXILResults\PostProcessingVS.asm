;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; TEXCOORD                 0   xy          1     NONE   float   xy  
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xy          1     NONE   float   xy  
; TEXCOORD                 1     zw        1     NONE   float     zw
; TEXCOORD                 2     zw        2     NONE   float     zw
; TEXCOORD                 3     zw        3     NONE   float     zw
; TEXCOORD                 4     zw        4     NONE   float     zw
; TEXCOORD                 5     zw        5     NONE   float     zw
; TEXCOORD                 6     zw        6     NONE   float     zw
; TEXCOORD                 7     zw        7     NONE   float     zw
; TEXCOORD                 8     zw        8     NONE   float     zw
;
; shader hash: 0fa841285c7ef7d21f82ade904f227d5
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 2
; SigOutputElements: 3
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 2
; SigOutputVectors[0]: 9
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; TEXCOORD                 0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
;
; Buffer Definitions:
;
; cbuffer PostProcessParams
; {
;
;   struct PostProcessParams
;   {
;
;       float2 TexelSize;                             ; Offset:    0
;       float BlurRadius;                             ; Offset:    8
;       float Time;                                   ; Offset:   12
;   
;   } PostProcessParams;                              ; Offset:    0 Size:    16
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PostProcessParams                 cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 6, outputs: 36
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0 }
;   output 1 depends on inputs: { 1 }
;   output 2 depends on inputs: { 2 }
;   output 4 depends on inputs: { 4 }
;   output 5 depends on inputs: { 5 }
;   output 6 depends on inputs: { 4 }
;   output 7 depends on inputs: { 5 }
;   output 10 depends on inputs: { 4 }
;   output 11 depends on inputs: { 5 }
;   output 14 depends on inputs: { 4 }
;   output 15 depends on inputs: { 5 }
;   output 18 depends on inputs: { 4 }
;   output 19 depends on inputs: { 5 }
;   output 22 depends on inputs: { 4 }
;   output 23 depends on inputs: { 5 }
;   output 26 depends on inputs: { 4 }
;   output 27 depends on inputs: { 5 }
;   output 30 depends on inputs: { 4 }
;   output 31 depends on inputs: { 5 }
;   output 34 depends on inputs: { 4 }
;   output 35 depends on inputs: { 5 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%PostProcessParams = type { <2 x float>, float, float }

@offsets.0.hca = internal unnamed_addr constant [8 x float] [float -1.000000e+00, float 0.000000e+00, float 1.000000e+00, float -1.000000e+00, float 1.000000e+00, float -1.000000e+00, float 0.000000e+00, float 1.000000e+00]
@offsets.1.hca = internal unnamed_addr constant [8 x float] [float -1.000000e+00, float -1.000000e+00, float -1.000000e+00, float 0.000000e+00, float 0.000000e+00, float 1.000000e+00, float 1.000000e+00, float 1.000000e+00]

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = alloca [8 x float], align 4
  %6 = alloca [8 x float], align 4
  br label %7

; <label>:7                                       ; preds = %25, %0
  %8 = phi float [ -1.000000e+00, %0 ], [ %29, %25 ]
  %9 = phi float [ -1.000000e+00, %0 ], [ %27, %25 ]
  %10 = phi i32 [ 0, %0 ], [ %23, %25 ]
  %11 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %12 = extractvalue %dx.types.CBufRet.f32 %11, 0
  %13 = extractvalue %dx.types.CBufRet.f32 %11, 1
  %14 = fmul fast float %12, %9
  %15 = fmul fast float %13, %8
  %16 = extractvalue %dx.types.CBufRet.f32 %11, 2
  %17 = fmul fast float %14, %16
  %18 = fmul fast float %15, %16
  %19 = fadd fast float %17, %2
  %20 = fadd fast float %18, %3
  %21 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 %10
  %22 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 %10
  store float %19, float* %21, align 4
  store float %20, float* %22, align 4
  %23 = add nuw nsw i32 %10, 1
  %24 = icmp eq i32 %23, 8
  br i1 %24, label %30, label %25

; <label>:25                                      ; preds = %7
  %26 = getelementptr [8 x float], [8 x float]* @offsets.0.hca, i32 0, i32 %23
  %27 = load float, float* %26, align 4
  %28 = getelementptr [8 x float], [8 x float]* @offsets.1.hca, i32 0, i32 %23
  %29 = load float, float* %28, align 4
  br label %7

; <label>:30                                      ; preds = %7
  %31 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %32 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %31)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %4)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %32)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float 1.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %2)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %3)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %33 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 0
  %34 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 0
  %35 = load float, float* %33, align 4
  %36 = load float, float* %34, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %35)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %36)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %37 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 1
  %38 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 1
  %39 = load float, float* %37, align 4
  %40 = load float, float* %38, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 1, i8 0, float %39)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 1, i8 1, float %40)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %41 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 2
  %42 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 2
  %43 = load float, float* %41, align 4
  %44 = load float, float* %42, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 2, i8 0, float %43)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 2, i8 1, float %44)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %45 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 3
  %46 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 3
  %47 = load float, float* %45, align 4
  %48 = load float, float* %46, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 3, i8 0, float %47)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 3, i8 1, float %48)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %49 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 4
  %50 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 4
  %51 = load float, float* %49, align 4
  %52 = load float, float* %50, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 4, i8 0, float %51)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 4, i8 1, float %52)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %53 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 5
  %54 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 5
  %55 = load float, float* %53, align 4
  %56 = load float, float* %54, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 5, i8 0, float %55)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 5, i8 1, float %56)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %57 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 6
  %58 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 6
  %59 = load float, float* %57, align 4
  %60 = load float, float* %58, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 6, i8 0, float %59)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 6, i8 1, float %60)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  %61 = getelementptr [8 x float], [8 x float]* %5, i32 0, i32 7
  %62 = getelementptr [8 x float], [8 x float]* %6, i32 0, i32 7
  %63 = load float, float* %61, align 4
  %64 = load float, float* %62, align 4
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 7, i8 0, float %63)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 7, i8 1, float %64)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %PostProcessParams* undef, !"", i32 0, i32 0, i32 1, i32 16, null}
!7 = !{[14 x i32] [i32 6, i32 36, i32 1, i32 0, i32 2, i32 0, i32 4, i32 0, i32 0, i32 0, i32 1145324624, i32 4, i32 -2004318048, i32 8]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !16, null}
!10 = !{!11, !14}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 1, i8 0, !15}
!15 = !{i32 3, i32 3}
!16 = !{!17, !19, !20}
!17 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !18}
!18 = !{i32 3, i32 15}
!19 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 2, i32 1, i8 0, !15}
!20 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 8, i8 2, i32 1, i8 2, !15}
!21 = !{i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8}
