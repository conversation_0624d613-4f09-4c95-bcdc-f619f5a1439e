﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Tessellation Evaluation  Main shader ===========  Work registers: 32 (100% used at 100% occupancy) Uniform registers: 38 (29% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    4.03   23.00    0.50       LS Shortest path cycles:        4.03   23.00    0.50       LS Longest path cycles:         4.03   23.00    0.50       LS  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: false 
