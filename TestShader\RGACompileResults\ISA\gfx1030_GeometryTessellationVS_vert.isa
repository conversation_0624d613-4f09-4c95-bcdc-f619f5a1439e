_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v4, s3, 5, v1                                // 000000000040: D76F0004 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v4                            // 000000000048: 7D880801
	s_and_saveexec_b32 s52, vcc_lo                             // 00000000004C: BEB43C6A
	s_cbranch_execz _L1                                        // 000000000050: BF88014F
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s5, s3                                           // 000000000060: BE850303
	s_load_dwordx8 s[24:31], s[10:11], 0x30                    // 000000000064: F40C0605 FA000030
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xy v[1:2], v5, s[24:27], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000070: EA012000 80060105
	s_clause 0x1                                               // 000000000078: BFA10001
	s_load_dwordx4 s[48:51], s[4:5], null                      // 00000000007C: F4080C02 FA000000
	s_load_dwordx16 s[12:27], s[4:5], null                     // 000000000084: F4100302 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000008C: BF8CC07F
	s_buffer_load_dwordx4 s[44:47], s[48:51], 0xdc             // 000000000090: F4280B18 FA0000DC
	s_buffer_load_dwordx2 s[0:1], s[24:27], 0xa0               // 000000000098: F424000C FA0000A0
	s_buffer_load_dwordx2 s[34:35], s[48:51], 0xec             // 0000000000A0: F4240898 FA0000EC
	s_load_dwordx8 s[36:43], s[4:5], 0x40                      // 0000000000A8: F40C0902 FA000040
	s_clause 0x1                                               // 0000000000B0: BFA10001
	s_buffer_load_dword s8, s[24:27], 0xa8                     // 0000000000B4: F420020C FA0000A8
	s_buffer_load_dword s53, s[24:27], 0x68                    // 0000000000BC: F4200D4C FA000068
	s_waitcnt lgkmcnt(0)                                       // 0000000000C4: BF8CC07F
	v_rcp_f32_e32 v3, s46                                      // 0000000000C8: 7E06542E
	v_rcp_f32_e32 v15, s45                                     // 0000000000CC: 7E1E542D
	s_waitcnt vmcnt(0)                                         // 0000000000D0: BF8C3F70
	v_mul_f32_e32 v8, s0, v1                                   // 0000000000D4: 10100200
	v_fma_f32 v10, s1, v2, -v3                                 // 0000000000D8: D54B000A 840E0401
	v_mul_f32_e32 v9, s1, v2                                   // 0000000000E0: 10120401
	v_fma_f32 v11, s0, v1, v15                                 // 0000000000E4: D54B000B 043E0200
	v_fma_f32 v1, s0, v1, -v15                                 // 0000000000EC: D54B0001 843E0200
	v_mul_f32_e32 v6, s34, v8                                  // 0000000000F4: 100C1022
	v_mul_f32_e32 v12, s34, v10                                // 0000000000F8: 10181422
	v_mul_f32_e32 v7, s34, v9                                  // 0000000000FC: 100E1222
	v_mul_f32_e32 v13, s34, v11                                // 000000000100: 101A1622
	s_clause 0x1                                               // 000000000104: BFA10001
	image_sample_lz  v11, [v11, v9], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000108: F09C010A 00A30B0B 00000009
	image_sample_lz  v16, [v8, v10], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000114: F09C010A 00A31008 0000000A
	s_clause 0x1                                               // 000000000120: BFA10001
	image_sample_lz  v17, [v6, v12], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000124: F09C010A 00A91106 0000000C
	image_sample_lz  v18, [v13, v7], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000130: F09C010A 00A9120D 00000007
	v_fmac_f32_e32 v3, s1, v2                                  // 00000000013C: 56060401
	v_mul_f32_e32 v2, s34, v1                                  // 000000000140: 10040222
	v_mul_f32_e32 v10, s34, v3                                 // 000000000144: 10140622
	s_clause 0x1                                               // 000000000148: BFA10001
	image_sample_lz  v19, [v2, v7], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000014C: F09C010A 00A91302 00000007
	image_sample_lz  v20, [v6, v10], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000158: F09C010A 00A91406 0000000A
	s_clause 0x1                                               // 000000000164: BFA10001
	image_sample_lz  v21, [v8, v3], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000168: F09C010A 00A31508 00000003
	image_sample_lz  v22, [v1, v9], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000174: F09C010A 00A31601 00000009
	s_load_dwordx8 s[0:7], s[10:11], null                      // 000000000180: F40C0005 FA000000
	image_sample_lz v23, v[8:9], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000188: F09C0108 00A31708
	image_sample_lz v24, v[6:7], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000190: F09C0108 00A91806
	s_waitcnt lgkmcnt(0)                                       // 000000000198: BF8CC07F
	tbuffer_load_format_xyz v[12:14], v5, s[4:7], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000019C: EA522000 80010C05
	tbuffer_load_format_xyz v[1:3], v5, s[0:3], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 0000000001A4: EA522000 80000105
	tbuffer_load_format_x v10, v5, s[28:31], 0 format:[BUF_FMT_32_FLOAT] idxen offset:12// 0000000001AC: E8B0200C 80070A05
	s_clause 0x4                                               // 0000000001B4: BFA10004
	s_buffer_load_dwordx2 s[12:13], s[24:27], 0x40             // 0000000001B8: F424030C FA000040
	s_buffer_load_dwordx2 s[14:15], s[24:27], 0x50             // 0000000001C0: F424038C FA000050
	s_buffer_load_dwordx2 s[16:17], s[24:27], 0x60             // 0000000001C8: F424040C FA000060
	s_buffer_load_dword s20, s[24:27], 0x58                    // 0000000001D0: F420050C FA000058
	s_buffer_load_dword s21, s[24:27], 0x48                    // 0000000001D8: F420054C FA000048
	s_waitcnt vmcnt(12)                                        // 0000000001E0: BF8C3F7C
	v_mul_f32_e32 v5, s47, v11                                 // 0000000001E4: 100A162F
	s_waitcnt vmcnt(11)                                        // 0000000001E8: BF8C3F7B
	v_mul_f32_e32 v11, s47, v16                                // 0000000001EC: 1016202F
	s_waitcnt vmcnt(10)                                        // 0000000001F0: BF8C3F7A
	v_sub_f32_e32 v16, 0.5, v17                                // 0000000001F4: 082022F0
	s_waitcnt vmcnt(9)                                         // 0000000001F8: BF8C3F79
	v_add_f32_e32 v17, -0.5, v18                               // 0000000001FC: 062224F1
	v_fma_f32 v11, v16, s8, -v11                               // 000000000200: D54B000B 842C1110
	v_fmac_f32_e32 v5, s8, v17                                 // 000000000208: 560A2208
	s_waitcnt vmcnt(8)                                         // 00000000020C: BF8C3F78
	v_add_f32_e32 v16, -0.5, v19                               // 000000000210: 062026F1
	s_waitcnt vmcnt(7)                                         // 000000000214: BF8C3F77
	v_add_f32_e32 v17, -0.5, v20                               // 000000000218: 062228F1
	s_waitcnt vmcnt(6)                                         // 00000000021C: BF8C3F76
	v_fmac_f32_e32 v11, s47, v21                               // 000000000220: 56162A2F
	s_waitcnt vmcnt(5)                                         // 000000000224: BF8C3F75
	v_fma_f32 v18, v22, s47, -v5                               // 000000000228: D54B0012 84145F16
	s_waitcnt vmcnt(3)                                         // 000000000230: BF8C3F73
	v_add_f32_e32 v19, -0.5, v24                               // 000000000234: 062630F1
	v_fmac_f32_e32 v11, s8, v17                                // 000000000238: 56162208
	v_fma_f32 v16, v16, s8, v18 div:2                          // 00000000023C: D54B0010 1C481110
	v_mul_f32_e32 v11, -0.5, v11                               // 000000000244: 101616F1
	v_mul_f32_e32 v16, s45, v16                                // 000000000248: 1020202D
	v_mul_f32_e32 v17, s46, v11                                // 00000000024C: 1022162E
	v_fma_f32 v11, v16, v16, 1.0                               // 000000000250: D54B000B 03CA2110
	v_fmac_f32_e32 v11, v17, v17                               // 000000000258: 56162311
	v_rsq_f32_e32 v18, v11                                     // 00000000025C: 7E245D0B
	v_cmp_neq_f32_e64 s0, 0, v11                               // 000000000260: D40D0000 00021680
	v_mul_f32_e32 v11, s47, v23                                // 000000000268: 10162E2F
	v_fmac_f32_e32 v11, s8, v19                                // 00000000026C: 56162608
	s_buffer_load_dwordx4 s[8:11], s[24:27], 0x20              // 000000000270: F428020C FA000020
	v_cndmask_b32_e64 v18, 0, v18, s0                          // 000000000278: D5010012 00022480
	v_sub_f32_e32 v5, v5, v11                                  // 000000000280: 080A1705
	s_waitcnt vmcnt(1)                                         // 000000000284: BF8C3F71
	v_add_f32_e32 v2, v11, v2                                  // 000000000288: 0604050B
	v_sub_f32_e32 v20, v18, v13                                // 00000000028C: 08281B12
	v_fma_f32 v16, v18, v16, -v12                              // 000000000290: D54B0010 84322112
	v_fma_f32 v17, v18, v17, -v14                              // 000000000298: D54B0011 843A2312
	v_mul_f32_e32 v18, v5, v5                                  // 0000000002A0: 10240B05
	v_fmamk_f32 v13, v20, 0x3f4ccccd, v13                      // 0000000002A4: 581A1B14 3F4CCCCD
	v_fmamk_f32 v12, v16, 0x3f4ccccd, v12                      // 0000000002AC: 58181910 3F4CCCCD
	v_fmac_f32_e32 v14, 0x3f4ccccd, v17                        // 0000000002B4: 561C22FF 3F4CCCCD
	v_fmac_f32_e32 v18, v15, v15                               // 0000000002BC: 56241F0F
	v_mul_f32_e32 v16, v13, v13                                // 0000000002C0: 10201B0D
	v_rsq_f32_e32 v17, v18                                     // 0000000002C4: 7E225D12
	v_cmp_neq_f32_e64 s0, 0, v18                               // 0000000002C8: D40D0000 00022480
	v_fmac_f32_e32 v16, v12, v12                               // 0000000002D0: 5620190C
	v_fmac_f32_e32 v16, v14, v14                               // 0000000002D4: 56201D0E
	v_cndmask_b32_e64 v17, 0, v17, s0                          // 0000000002D8: D5010011 00022280
	v_rsq_f32_e32 v19, v16                                     // 0000000002E0: 7E265D10
	v_cmp_neq_f32_e64 s0, 0, v16                               // 0000000002E4: D40D0000 00022080
	v_mul_f32_e32 v5, v17, v5                                  // 0000000002EC: 100A0B11
	v_cndmask_b32_e64 v16, 0, v19, s0                          // 0000000002F0: D5010010 00022680
	s_buffer_load_dwordx8 s[0:7], s[24:27], null               // 0000000002F8: F42C000C FA000000
	v_mul_f32_e32 v18, v16, v13                                // 000000000300: 10241B10
	v_mul_f32_e32 v13, v17, v15                                // 000000000304: 101A1F11
	v_mul_f32_e32 v15, v16, v12                                // 000000000308: 101E1910
	v_mul_f32_e32 v17, 0, v17                                  // 00000000030C: 10222280
	v_mul_f32_e32 v16, v16, v14                                // 000000000310: 10201D10
	v_mul_f32_e32 v12, v18, v5                                 // 000000000314: 10180B12
	s_waitcnt lgkmcnt(0)                                       // 000000000318: BF8CC07F
	v_mul_f32_e32 v22, s14, v15                                // 00000000031C: 102C1E0E
	v_mul_f32_e32 v21, s12, v15                                // 000000000320: 102A1E0C
	v_mul_f32_e32 v23, s16, v15                                // 000000000324: 102E1E10
	v_fmac_f32_e32 v12, v15, v13                               // 000000000328: 56181B0F
	v_fmac_f32_e32 v22, s15, v18                               // 00000000032C: 562C240F
	v_fmac_f32_e32 v21, s13, v18                               // 000000000330: 562A240D
	v_fmac_f32_e32 v23, s17, v18                               // 000000000334: 562E2411
	v_fmac_f32_e32 v12, v16, v17                               // 000000000338: 56182310
	v_fmac_f32_e32 v22, s20, v16                               // 00000000033C: 562C2014
	v_fmac_f32_e32 v21, s21, v16                               // 000000000340: 562A2015
	v_fmac_f32_e32 v23, s53, v16                               // 000000000344: 562E2035
	v_fma_f32 v5, -v12, v18, v5                                // 000000000348: D54B0005 2416250C
	v_fma_f32 v19, -v12, v15, v13                              // 000000000350: D54B0013 24361F0C
	v_fma_f32 v17, -v12, v16, v17                              // 000000000358: D54B0011 2446210C
	v_fma_f32 v12, s0, v1, s3                                  // 000000000360: D54B000C 000E0200
	v_mul_f32_e32 v18, v22, v22                                // 000000000368: 10242D16
	v_mul_f32_e32 v13, v5, v5                                  // 00000000036C: 101A0B05
	s_mov_b32 s3, 0xbf4ccccc                                   // 000000000370: BE8303FF BF4CCCCC
	v_fmac_f32_e32 v12, s1, v2                                 // 000000000378: 56180401
	v_fmac_f32_e32 v18, v21, v21                               // 00000000037C: 56242B15
	v_fmac_f32_e32 v13, v19, v19                               // 000000000380: 561A2713
	s_mov_b32 s1, 0x3f19999a                                   // 000000000384: BE8103FF 3F19999A
	v_fmac_f32_e32 v12, s2, v3                                 // 00000000038C: 56180602
	v_fmac_f32_e32 v18, v23, v23                               // 000000000390: 56242F17
	v_fmac_f32_e32 v13, v17, v17                               // 000000000394: 561A2311
	s_mov_b32 s2, 0x3f4ccccc                                   // 000000000398: BE8203FF 3F4CCCCC
	v_rsq_f32_e32 v14, v13                                     // 0000000003A0: 7E1C5D0D
	v_cmp_neq_f32_e64 s0, 0, v13                               // 0000000003A4: D40D0000 00021A80
	v_fma_f32 v13, s4, v1, s7                                  // 0000000003AC: D54B000D 001E0204
	v_fmac_f32_e32 v13, s5, v2                                 // 0000000003B4: 561A0405
	v_cndmask_b32_e64 v20, 0, v14, s0                          // 0000000003B8: D5010014 00021C80
	s_clause 0x1                                               // 0000000003C0: BFA10001
	s_buffer_load_dwordx2 s[18:19], s[48:51], 0xc0             // 0000000003C4: F4240498 FA0000C0
	s_buffer_load_dword s0, s[48:51], 0xc8                     // 0000000003CC: F4200018 FA0000C8
	v_fma_f32 v14, s8, v1, s11                                 // 0000000003D4: D54B000E 002E0208
	v_fmac_f32_e32 v13, s6, v3                                 // 0000000003DC: 561A0606
	v_mul_f32_e32 v5, v20, v5                                  // 0000000003E0: 100A0B14
	v_mul_f32_e32 v15, v20, v19                                // 0000000003E4: 101E2714
	v_mul_f32_e32 v17, v20, v17                                // 0000000003E8: 10222314
	v_fmac_f32_e32 v14, s9, v2                                 // 0000000003EC: 561C0409
	v_mul_f32_e32 v24, s15, v5                                 // 0000000003F0: 10300A0F
	v_mul_f32_e32 v19, s13, v5                                 // 0000000003F4: 10260A0D
	v_mul_f32_e32 v5, s17, v5                                  // 0000000003F8: 100A0A11
	v_fmac_f32_e32 v14, s10, v3                                // 0000000003FC: 561C060A
	v_fmac_f32_e32 v24, s14, v15                               // 000000000400: 56301E0E
	v_fmac_f32_e32 v19, s12, v15                               // 000000000404: 56261E0C
	v_fmac_f32_e32 v5, s16, v15                                // 000000000408: 560A1E10
	v_fmac_f32_e32 v24, s20, v17                               // 00000000040C: 56302214
	v_fmac_f32_e32 v19, s21, v17                               // 000000000410: 56262215
	v_fmac_f32_e32 v5, s53, v17                                // 000000000414: 560A2235
	v_rsq_f32_e32 v17, v18                                     // 000000000418: 7E225D12
	s_waitcnt lgkmcnt(0)                                       // 00000000041C: BF8CC07F
	v_sub_f32_e32 v16, s19, v13                                // 000000000420: 08201A13
	v_mul_f32_e32 v15, v24, v24                                // 000000000424: 101E3118
	v_sub_f32_e32 v26, s0, v14                                 // 000000000428: 08341C00
	v_cmp_neq_f32_e64 s0, 0, v18                               // 00000000042C: D40D0000 00022480
	v_sub_f32_e32 v20, s18, v12                                // 000000000434: 08281812
	v_mul_f32_e32 v16, v16, v16                                // 000000000438: 10202110
	v_fmac_f32_e32 v15, v19, v19                               // 00000000043C: 561E2713
	v_cndmask_b32_e64 v18, 0, v17, s0                          // 000000000440: D5010012 00022280
	v_fmac_f32_e32 v16, v20, v20                               // 000000000448: 56202914
	v_fmac_f32_e32 v15, v5, v5                                 // 00000000044C: 561E0B05
	v_fmac_f32_e32 v16, v26, v26                               // 000000000450: 5620351A
	v_rsq_f32_e32 v25, v15                                     // 000000000454: 7E325D0F
	v_cmp_neq_f32_e64 s0, 0, v15                               // 000000000458: D40D0000 00021E80
	v_mul_f32_e32 v15, v18, v21                                // 000000000460: 101E2B12
	v_mul_f32_e64 v26, s35, s44                                // 000000000464: D508001A 00005823
	v_cndmask_b32_e64 v20, 0, v25, s0                          // 00000000046C: D5010014 00023280
	v_rcp_f32_e32 v25, s47                                     // 000000000474: 7E32542F
	s_mov_b32 s0, 0xbf19999a                                   // 000000000478: BE8003FF BF19999A
	v_mul_f32_e32 v17, v20, v5                                 // 000000000480: 10220B14
	v_sqrt_f32_e32 v5, v16                                     // 000000000484: 7E0A6710
	v_mul_f32_e32 v16, v18, v23                                // 000000000488: 10202F12
	v_mul_f32_e32 v19, v20, v19                                // 00000000048C: 10262714
	v_mul_f32_e32 v20, v20, v24                                // 000000000490: 10283114
	v_mul_f32_e32 v21, v17, v15                                // 000000000494: 102A1F11
	v_mul_f32_e32 v18, v18, v22                                // 000000000498: 10242D12
	v_mul_f32_e64 v23, v11, v25 clamp                          // 00000000049C: D5088017 0002330B
	v_mul_f32_e32 v22, v20, v16                                // 0000000004A4: 102C2114
	v_fma_f32 v24, v19, v16, -v21                              // 0000000004A8: D54B0018 84562113
	v_max_f32_e32 v5, 1.0, v5                                  // 0000000004B0: 200A0AF2
	v_mul_f32_e32 v21, v19, v18                                // 0000000004B4: 102A2513
	v_mul_f32_e32 v27, 0x3ecccccd, v23                         // 0000000004B8: 10362EFF 3ECCCCCD
	v_fma_f32 v22, v17, v18, -v22                              // 0000000004C0: D54B0016 845A2511
	v_mul_f32_e32 v25, v24, v24                                // 0000000004C8: 10323118
	v_rcp_f32_e32 v5, v5                                       // 0000000004CC: 7E0A5505
	v_fma_f32 v29, v20, v15, -v21                              // 0000000004D0: D54B001D 84561F14
	v_fmaak_f32 v31, s0, v23, 0x3e99999a                       // 0000000004D8: 5A3E2E00 3E99999A
	v_fma_f32 v32, v18, -2.0, 1.0                              // 0000000004E0: D54B0020 03C9EB12
	v_fmac_f32_e32 v25, v22, v22                               // 0000000004E8: 56322D16
	v_fmaak_f32 v33, s3, v23, 0x3e4cccce                       // 0000000004EC: 5A422E03 3E4CCCCE
	v_sub_f32_e32 v21, 1.0, v18                                // 0000000004F4: 082A24F2
	v_fmaak_f32 v28, s1, v23, 0x3e4ccccd                       // 0000000004F8: 5A382E01 3E4CCCCD
	v_mul_f32_e32 v27, v27, v32                                // 000000000500: 1036411B
	v_fmac_f32_e32 v25, v29, v29                               // 000000000504: 56323B1D
	v_mul_f32_e32 v5, v26, v5                                  // 000000000508: 100A0B1A
	v_mul_f32_e32 v26, v32, v31                                // 00000000050C: 10343F20
	v_cmp_lt_f32_e64 s0, 0.5, v21                              // 000000000510: D4010000 00022AF0
	v_mul_f32_e32 v32, v32, v33                                // 000000000518: 10404320
	v_rsq_f32_e32 v31, v25                                     // 00000000051C: 7E3E5D19
	v_fmaak_f32 v30, 0x3ecccccd, v23, 0x3ecccccd               // 000000000520: 5A3C2EFF 3ECCCCCD
	v_fmaak_f32 v33, s2, v23, 0x3dcccccd                       // 000000000528: 5A422E02 3DCCCCCD
	v_cndmask_b32_e64 v23, 0x80000000, v26, s0                 // 000000000530: D5010017 000234FF 80000000
	v_cndmask_b32_e64 v26, 0, v27, s0                          // 00000000053C: D501001A 00023680
	v_cndmask_b32_e64 v27, 0x80000000, v32, s0                 // 000000000544: D501001B 000240FF 80000000
	v_cmp_neq_f32_e64 s0, 0, v25                               // 000000000550: D40D0000 00023280
	v_med3_f32 v5, v5, 1.0, 0x42800000                         // 000000000558: D5570005 03FDE505 42800000
	v_fma_f32 v32, v21, 2.0, 1.0                               // 000000000564: D54B0020 03C9E915
	v_add_f32_e32 v23, v28, v23                                // 00000000056C: 062E2F1C
	v_sub_f32_e32 v25, v30, v26                                // 000000000570: 0832351E
	v_cndmask_b32_e64 v31, 0, v31, s0                          // 000000000574: D501001F 00023E80
	v_add_f32_e32 v28, v33, v27                                // 00000000057C: 06383721
	v_mul_f32_e32 v27, v32, v5                                 // 000000000580: 10360B20
	v_mul_f32_e32 v22, v31, v22                                // 000000000584: 102C2D1F
	v_mul_f32_e32 v24, v31, v24                                // 000000000588: 1030311F
	v_mul_f32_e32 v26, v31, v29                                // 00000000058C: 10343B1F
_L1:
	s_or_b32 exec_lo, exec_lo, s52                             // 000000000590: 887E347E
	s_mov_b32 s1, exec_lo                                      // 000000000594: BE81037E
	v_cmpx_gt_u32_e64 s33, v4                                  // 000000000598: D4D4007E 00020821
	s_cbranch_execz _L2                                        // 0000000005A0: BF880002
	exp prim v0, off, off, off done                            // 0000000005A4: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000005AC: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000005B0: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000005B4: BE803C6A
	s_cbranch_execz _L3                                        // 0000000005B8: BF88001B
	v_mov_b32_e32 v0, 1.0                                      // 0000000005BC: 7E0002F2
	v_mov_b32_e32 v4, 0                                        // 0000000005C0: 7E080280
	exp pos0 v4, v4, v4, v0 done                               // 0000000005C4: F80008CF 00040404
	exp param5 v6, v7, off, off                                // 0000000005CC: F8000253 00000706
	exp param10 v21, off, off, off                             // 0000000005D4: F80002A1 00000015
	exp param3 v22, v24, v26, off                              // 0000000005DC: F8000237 001A1816
	exp param8 v27, off, off, off                              // 0000000005E4: F8000281 0000001B
	exp param1 v15, v18, v16, off                              // 0000000005EC: F8000217 0010120F
	s_waitcnt vmcnt(0)                                         // 0000000005F4: BF8C3F70
	exp param6 v23, v25, v28, v10                              // 0000000005F8: F800026F 0A1C1917
	exp param4 v8, v9, off, off                                // 000000000600: F8000243 00000908
	exp param9 v11, off, off, off                              // 000000000608: F8000291 0000000B
	exp param2 v19, v20, v17, off                              // 000000000610: F8000227 00111413
	exp param7 v12, v13, v14, off                              // 000000000618: F8000277 000E0D0C
	exp param0 v1, v2, v3, off                                 // 000000000620: F8000207 00030201
_L3:
	s_endpgm                                                   // 000000000628: BF810000
