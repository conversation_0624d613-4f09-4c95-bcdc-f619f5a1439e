;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 928b70aa38e4103c67015645680e4bc6
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(64,1,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer SimulationParams
; {
;
;   struct SimulationParams
;   {
;
;       float DeltaTime;                              ; Offset:    0
;       float3 Gravity;                               ; Offset:    4
;       float3 WindForce;                             ; Offset:   16
;       float Damping;                                ; Offset:   28
;       uint ParticleCount;                           ; Offset:   32
;       float3 BoundsMin;                             ; Offset:   36
;       float3 BoundsMax;                             ; Offset:   48
;       float GroundHeight;                           ; Offset:   60
;       float Restitution;                            ; Offset:   64
;   
;   } SimulationParams;                               ; Offset:    0 Size:    68
;
; }
;
; Resource bind info for ParticleBuffer
; {
;
;   struct struct.Particle
;   {
;
;       float3 Position;                              ; Offset:    0
;       float Mass;                                   ; Offset:   12
;       float3 Velocity;                              ; Offset:   16
;       float Life;                                   ; Offset:   28
;       float3 Force;                                 ; Offset:   32
;       float Size;                                   ; Offset:   44
;       float4 Color;                                 ; Offset:   48
;   
;   } $Element;                                       ; Offset:    0 Size:    64
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; SimulationParams                  cbuffer      NA          NA     CB0            cb0     1
; ParticleBuffer                        UAV  struct         r/w      U0             u0     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.RWStructuredBuffer<Particle>" = type { %struct.Particle }
%struct.Particle = type { <3 x float>, float, <3 x float>, float, <3 x float>, float, <4 x float> }
%SimulationParams = type { float, <3 x float>, <3 x float>, float, i32, <3 x float>, <3 x float>, float, float }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %4 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %2, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %5 = extractvalue %dx.types.CBufRet.i32 %4, 0
  %6 = icmp ult i32 %3, %5
  br i1 %6, label %7, label %118

; <label>:7                                       ; preds = %0
  %8 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 0)  ; BufferLoad(srv,index,wot)
  %9 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 12)  ; BufferLoad(srv,index,wot)
  %10 = extractvalue %dx.types.ResRet.f32 %9, 0
  %11 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 16)  ; BufferLoad(srv,index,wot)
  %12 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 28)  ; BufferLoad(srv,index,wot)
  %13 = extractvalue %dx.types.ResRet.f32 %12, 0
  %14 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 32)  ; BufferLoad(srv,index,wot)
  %15 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %1, i32 %3, i32 48)  ; BufferLoad(srv,index,wot)
  %16 = extractvalue %dx.types.ResRet.f32 %15, 0
  %17 = extractvalue %dx.types.ResRet.f32 %15, 1
  %18 = extractvalue %dx.types.ResRet.f32 %15, 2
  %19 = fcmp fast ugt float %13, 0.000000e+00
  br i1 %19, label %20, label %118

; <label>:20                                      ; preds = %7
  %21 = extractvalue %dx.types.ResRet.f32 %14, 2
  %22 = extractvalue %dx.types.ResRet.f32 %14, 1
  %23 = extractvalue %dx.types.ResRet.f32 %14, 0
  %24 = extractvalue %dx.types.ResRet.f32 %11, 2
  %25 = extractvalue %dx.types.ResRet.f32 %11, 1
  %26 = extractvalue %dx.types.ResRet.f32 %11, 0
  %27 = extractvalue %dx.types.ResRet.f32 %8, 2
  %28 = extractvalue %dx.types.ResRet.f32 %8, 1
  %29 = extractvalue %dx.types.ResRet.f32 %8, 0
  %30 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %31 = extractvalue %dx.types.CBufRet.f32 %30, 1
  %32 = extractvalue %dx.types.CBufRet.f32 %30, 2
  %33 = extractvalue %dx.types.CBufRet.f32 %30, 3
  %34 = fmul fast float %31, %10
  %35 = fmul fast float %32, %10
  %36 = fmul fast float %33, %10
  %37 = fadd fast float %34, %23
  %38 = fadd fast float %35, %22
  %39 = fadd fast float %36, %21
  %40 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %41 = extractvalue %dx.types.CBufRet.f32 %40, 0
  %42 = extractvalue %dx.types.CBufRet.f32 %40, 1
  %43 = extractvalue %dx.types.CBufRet.f32 %40, 2
  %44 = fadd fast float %37, %41
  %45 = fadd fast float %38, %42
  %46 = fadd fast float %39, %43
  %47 = fdiv fast float %44, %10
  %48 = fdiv fast float %45, %10
  %49 = fdiv fast float %46, %10
  %50 = extractvalue %dx.types.CBufRet.f32 %30, 0
  %51 = fmul fast float %47, %50
  %52 = fmul fast float %48, %50
  %53 = fmul fast float %49, %50
  %54 = fadd fast float %51, %26
  %55 = fadd fast float %52, %25
  %56 = fadd fast float %53, %24
  %57 = extractvalue %dx.types.CBufRet.f32 %40, 3
  %58 = fmul fast float %54, %57
  %59 = fmul fast float %55, %57
  %60 = fmul fast float %56, %57
  %61 = fmul fast float %58, %50
  %62 = fmul fast float %59, %50
  %63 = fmul fast float %60, %50
  %64 = fadd fast float %61, %29
  %65 = fadd fast float %62, %28
  %66 = fadd fast float %63, %27
  %67 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %68 = extractvalue %dx.types.CBufRet.f32 %67, 3
  %69 = fcmp fast ugt float %65, %68
  br i1 %69, label %77, label %70

; <label>:70                                      ; preds = %20
  %71 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %72 = extractvalue %dx.types.CBufRet.f32 %71, 0
  %73 = fmul fast float %59, %72
  %74 = fsub fast float -0.000000e+00, %73
  %75 = fmul fast float %58, 0x3FE99999A0000000
  %76 = fmul fast float %60, 0x3FE99999A0000000
  br label %77

; <label>:77                                      ; preds = %70, %20
  %78 = phi float [ %75, %70 ], [ %58, %20 ]
  %79 = phi float [ %74, %70 ], [ %59, %20 ]
  %80 = phi float [ %76, %70 ], [ %60, %20 ]
  %81 = phi float [ %68, %70 ], [ %65, %20 ]
  %82 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %83 = extractvalue %dx.types.CBufRet.f32 %82, 1
  %84 = fcmp fast olt float %64, %83
  %85 = extractvalue %dx.types.CBufRet.f32 %67, 0
  %86 = fcmp fast ogt float %64, %85
  %87 = or i1 %84, %86
  br i1 %87, label %88, label %95

; <label>:88                                      ; preds = %77
  %89 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %90 = extractvalue %dx.types.CBufRet.f32 %89, 0
  %91 = fmul fast float %78, %90
  %92 = fsub fast float -0.000000e+00, %91
  %93 = call float @dx.op.binary.f32(i32 35, float %64, float %83)  ; FMax(a,b)
  %94 = call float @dx.op.binary.f32(i32 36, float %93, float %85)  ; FMin(a,b)
  br label %95

; <label>:95                                      ; preds = %88, %77
  %96 = phi float [ %92, %88 ], [ %78, %77 ]
  %97 = phi float [ %94, %88 ], [ %64, %77 ]
  %98 = extractvalue %dx.types.CBufRet.f32 %82, 3
  %99 = fcmp fast olt float %66, %98
  %100 = extractvalue %dx.types.CBufRet.f32 %67, 2
  %101 = fcmp fast ogt float %66, %100
  %102 = or i1 %99, %101
  br i1 %102, label %103, label %110

; <label>:103                                     ; preds = %95
  %104 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %2, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %105 = extractvalue %dx.types.CBufRet.f32 %104, 0
  %106 = fmul fast float %80, %105
  %107 = fsub fast float -0.000000e+00, %106
  %108 = call float @dx.op.binary.f32(i32 35, float %66, float %98)  ; FMax(a,b)
  %109 = call float @dx.op.binary.f32(i32 36, float %108, float %100)  ; FMin(a,b)
  br label %110

; <label>:110                                     ; preds = %103, %95
  %111 = phi float [ %107, %103 ], [ %80, %95 ]
  %112 = phi float [ %109, %103 ], [ %66, %95 ]
  %113 = fsub fast float %13, %50
  %114 = fmul fast float %113, 0x3FC99999A0000000
  %115 = call float @dx.op.unary.f32(i32 7, float %114)  ; Saturate(value)
  %116 = fmul fast float %115, 0x3FECCCCCC0000000
  %117 = fadd fast float %116, 0x3FB99999A0000000
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 0, float %97, float %81, float %112, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 12, float %10, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 16, float %96, float %79, float %111, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 28, float %113, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 32, float 0.000000e+00, float 0.000000e+00, float 0.000000e+00, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 44, float %117, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %3, i32 48, float %16, float %17, float %18, float %115, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  br label %118

; <label>:118                                     ; preds = %110, %7, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #1

; Function Attrs: nounwind
declare void @dx.op.bufferStore.f32(i32, %dx.types.Handle, i32, i32, float, float, float, float, i8) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind readonly }
attributes #2 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!10}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{null, !5, !8, null}
!5 = !{!6}
!6 = !{i32 0, %"class.RWStructuredBuffer<Particle>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i1 false, i1 false, i1 false, !7}
!7 = !{i32 1, i32 64}
!8 = !{!9}
!9 = !{i32 0, %SimulationParams* undef, !"", i32 0, i32 0, i32 1, i32 68, null}
!10 = !{void ()* @main, !"main", null, !4, !11}
!11 = !{i32 0, i64 16, i32 4, !12}
!12 = !{i32 64, i32 1, i32 1}
