﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Fragment  Main shader ===========  Work registers: 63 (98% used at 50% occupancy) Uniform registers: 38 (29% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       V       T    Bound Total instruction cycles:   12.97    0.00    0.75    0.50        A Shortest path cycles:        5.50    0.00    0.75    0.50        A Longest path cycles:          N/A     N/A     N/A     N/A      N/A  A = Arithmetic, LS = Load/Store, V = Varying, T = Texture  Shader properties =================  Has uniform computation: true Has side-effects: false Modifies coverage: false Uses late ZS test: false Uses late ZS update: false Reads color buffer: false  Note: This tool shows only the shader-visible property state. API configuration may also impact the value of some properties. 
