; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 142
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %out_var_SV_TARGET0 %out_var_SV_TARGET1 %out_var_SV_TARGET2 %out_var_SV_TARGET3
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "Albedo"
               OpMemberName %type_Material 1 "Metallic"
               OpMemberName %type_Material 2 "Roughness"
               OpMemberName %type_Material 3 "AO"
               OpMemberName %type_Material 4 "EmissiveColor"
               OpMemberName %type_Material 5 "EmissiveStrength"
               OpMemberName %type_Material 6 "NormalStrength"
               OpName %Material "Material"
               OpName %type_2d_image "type.2d.image"
               OpName %AlbedoTexture "AlbedoTexture"
               OpName %NormalTexture "NormalTexture"
               OpName %MetallicTexture "MetallicTexture"
               OpName %RoughnessTexture "RoughnessTexture"
               OpName %AOTexture "AOTexture"
               OpName %EmissiveTexture "EmissiveTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET0 "out.var.SV_TARGET0"
               OpName %out_var_SV_TARGET1 "out.var.SV_TARGET1"
               OpName %out_var_SV_TARGET2 "out.var.SV_TARGET2"
               OpName %out_var_SV_TARGET3 "out.var.SV_TARGET3"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET0 Location 0
               OpDecorate %out_var_SV_TARGET1 Location 1
               OpDecorate %out_var_SV_TARGET2 Location 2
               OpDecorate %out_var_SV_TARGET3 Location 3
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %AlbedoTexture DescriptorSet 0
               OpDecorate %AlbedoTexture Binding 0
               OpDecorate %NormalTexture DescriptorSet 0
               OpDecorate %NormalTexture Binding 1
               OpDecorate %MetallicTexture DescriptorSet 0
               OpDecorate %MetallicTexture Binding 2
               OpDecorate %RoughnessTexture DescriptorSet 0
               OpDecorate %RoughnessTexture Binding 3
               OpDecorate %AOTexture DescriptorSet 0
               OpDecorate %AOTexture Binding 4
               OpDecorate %EmissiveTexture DescriptorSet 0
               OpDecorate %EmissiveTexture Binding 5
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 12
               OpMemberDecorate %type_Material 2 Offset 16
               OpMemberDecorate %type_Material 3 Offset 20
               OpMemberDecorate %type_Material 4 Offset 32
               OpMemberDecorate %type_Material 5 Offset 44
               OpMemberDecorate %type_Material 6 Offset 48
               OpDecorate %type_Material Block
        %int = OpTypeInt 32 1
      %int_5 = OpConstant %int 5
      %int_0 = OpConstant %int 0
      %int_6 = OpConstant %int 6
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
  %float_0_5 = OpConstant %float 0.5
    %v3float = OpTypeVector %float 3
         %37 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
    %float_2 = OpConstant %float 2
    %float_1 = OpConstant %float 1
         %40 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%type_Material = OpTypeStruct %v3float %float %float %float %v3float %float %float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %52 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%mat3v3float = OpTypeMatrix %v3float 3
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
%AlbedoTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%NormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%MetallicTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%RoughnessTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %AOTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%EmissiveTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_float Input
%out_var_SV_TARGET0 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_TARGET1 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_TARGET2 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_TARGET3 = OpVariable %_ptr_Output_v4float Output
       %main = OpFunction %void None %52
         %56 = OpLabel
         %57 = OpLoad %v3float %in_var_TEXCOORD0
         %58 = OpLoad %v3float %in_var_TEXCOORD1
         %59 = OpLoad %v3float %in_var_TEXCOORD2
         %60 = OpLoad %v3float %in_var_TEXCOORD3
         %61 = OpLoad %v2float %in_var_TEXCOORD4
         %62 = OpLoad %v4float %in_var_TEXCOORD5
         %63 = OpLoad %float %in_var_TEXCOORD6
         %64 = OpLoad %type_2d_image %AlbedoTexture
         %65 = OpLoad %type_sampler %LinearSampler
         %66 = OpSampledImage %type_sampled_image %64 %65
         %67 = OpImageSampleImplicitLod %v4float %66 %61 None
         %68 = OpVectorShuffle %v3float %67 %67 0 1 2
         %69 = OpAccessChain %_ptr_Uniform_v3float %Material %int_0
         %70 = OpLoad %v3float %69
         %71 = OpFMul %v3float %68 %70
         %72 = OpVectorShuffle %v3float %62 %62 0 1 2
         %73 = OpFMul %v3float %71 %72
         %74 = OpLoad %type_2d_image %MetallicTexture
         %75 = OpLoad %type_sampler %LinearSampler
         %76 = OpSampledImage %type_sampled_image %74 %75
         %77 = OpImageSampleImplicitLod %v4float %76 %61 None
         %78 = OpCompositeExtract %float %77 0
         %79 = OpAccessChain %_ptr_Uniform_float %Material %int_1
         %80 = OpLoad %float %79
         %81 = OpFMul %float %78 %80
         %82 = OpLoad %type_2d_image %RoughnessTexture
         %83 = OpLoad %type_sampler %LinearSampler
         %84 = OpSampledImage %type_sampled_image %82 %83
         %85 = OpImageSampleImplicitLod %v4float %84 %61 None
         %86 = OpCompositeExtract %float %85 0
         %87 = OpAccessChain %_ptr_Uniform_float %Material %int_2
         %88 = OpLoad %float %87
         %89 = OpFMul %float %86 %88
         %90 = OpLoad %type_2d_image %AOTexture
         %91 = OpLoad %type_sampler %LinearSampler
         %92 = OpSampledImage %type_sampled_image %90 %91
         %93 = OpImageSampleImplicitLod %v4float %92 %61 None
         %94 = OpCompositeExtract %float %93 0
         %95 = OpAccessChain %_ptr_Uniform_float %Material %int_3
         %96 = OpLoad %float %95
         %97 = OpFMul %float %94 %96
         %98 = OpLoad %type_2d_image %EmissiveTexture
         %99 = OpLoad %type_sampler %LinearSampler
        %100 = OpSampledImage %type_sampled_image %98 %99
        %101 = OpImageSampleImplicitLod %v4float %100 %61 None
        %102 = OpVectorShuffle %v3float %101 %101 0 1 2
        %103 = OpAccessChain %_ptr_Uniform_v3float %Material %int_4
        %104 = OpLoad %v3float %103
        %105 = OpFMul %v3float %102 %104
        %106 = OpAccessChain %_ptr_Uniform_float %Material %int_5
        %107 = OpLoad %float %106
        %108 = OpVectorTimesScalar %v3float %105 %107
        %109 = OpLoad %type_2d_image %NormalTexture
        %110 = OpLoad %type_sampler %LinearSampler
        %111 = OpSampledImage %type_sampled_image %109 %110
        %112 = OpImageSampleImplicitLod %v4float %111 %61 None
        %113 = OpVectorShuffle %v3float %112 %112 0 1 2
        %114 = OpVectorTimesScalar %v3float %113 %float_2
        %115 = OpFSub %v3float %114 %40
        %116 = OpAccessChain %_ptr_Uniform_float %Material %int_6
        %117 = OpLoad %float %116
        %118 = OpVectorShuffle %v2float %115 %115 0 1
        %119 = OpVectorTimesScalar %v2float %118 %117
        %120 = OpVectorShuffle %v3float %115 %119 3 4 2
        %121 = OpCompositeConstruct %mat3v3float %59 %60 %58
        %122 = OpMatrixTimesVector %v3float %121 %120
        %123 = OpExtInst %v3float %1 Normalize %122
        %124 = OpCompositeExtract %float %73 0
        %125 = OpCompositeExtract %float %73 1
        %126 = OpCompositeExtract %float %73 2
        %127 = OpCompositeConstruct %v4float %124 %125 %126 %81
        %128 = OpVectorTimesScalar %v3float %123 %float_0_5
        %129 = OpFAdd %v3float %128 %37
        %130 = OpCompositeExtract %float %129 0
        %131 = OpCompositeExtract %float %129 1
        %132 = OpCompositeExtract %float %129 2
        %133 = OpCompositeConstruct %v4float %130 %131 %132 %89
        %134 = OpCompositeExtract %float %57 0
        %135 = OpCompositeExtract %float %57 1
        %136 = OpCompositeExtract %float %57 2
        %137 = OpCompositeConstruct %v4float %134 %135 %136 %97
        %138 = OpCompositeExtract %float %108 0
        %139 = OpCompositeExtract %float %108 1
        %140 = OpCompositeExtract %float %108 2
        %141 = OpCompositeConstruct %v4float %138 %139 %140 %63
               OpStore %out_var_SV_TARGET0 %127
               OpStore %out_var_SV_TARGET1 %133
               OpStore %out_var_SV_TARGET2 %137
               OpStore %out_var_SV_TARGET3 %141
               OpReturn
               OpFunctionEnd
