// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
};

cbuffer LightingParams : register(b0)
{
  float3 CameraPosition;
  int NumLights;
  float3 AmbientColor;
  float AmbientStrength;
}

cbuffer LightData : register(b1)
{
  float4 LightPositions[32];
  float4 LightColors[32];
  float4 LightDirections[32];
  int4 LightTypes[8];
}

Texture2D GBufferAlbedo : register(t0);
Texture2D GBufferNormal : register(t1);
Texture2D GBufferWorldPos : register(t2);
Texture2D GBufferEmissive : register(t3);
SamplerState PointSampler : register(s0);
static const float PI = 3.14159265359f;
float DistributionGGX(float3 N, float3 H, float roughness)
{
  float a = (roughness * roughness);
  float a2 = (a * a);
  float NdotH = max(dot(N, H), 0.0f);
  float NdotH2 = (NdotH * NdotH);
  float num = a2;
  float denom = (NdotH2 * a2 - 1.0f) + 1.0f;
  denom = ((PI * denom) * denom);
  return num / denom;
}

float GeometrySchlickGGX(float NdotV, float roughness)
{
  float r = roughness + 1.0f;
  float k = (r * r) / 8.0f;
  float num = NdotV;
  float denom = (NdotV * 1.0f - k) + k;
  return num / denom;
}

float GeometrySmith(float3 N, float3 V, float3 L, float roughness)
{
  float NdotV = max(dot(N, V), 0.0f);
  float NdotL = max(dot(N, L), 0.0f);
  float ggx2 = GeometrySchlickGGX(NdotV, roughness);
  float ggx1 = GeometrySchlickGGX(NdotL, roughness);
  return (ggx1 * ggx2);
}

float3 fresnelSchlick(float cosTheta, float3 F0)
{
  return F0 + (1.0f - F0 * pow(clamp(1.0f - cosTheta, 0.0f, 1.0f), 5.0f));
}

float4 main(PSInput input) : SV_TARGET
{
  float4 albedoMetallic = GBufferAlbedo.Sample(PointSampler, input.TexCoord);
  float4 normalRoughness = GBufferNormal.Sample(PointSampler, input.TexCoord);
  float4 worldPosAO = GBufferWorldPos.Sample(PointSampler, input.TexCoord);
  float4 emissiveDepth = GBufferEmissive.Sample(PointSampler, input.TexCoord);
  float3 albedo = albedoMetallic.rgb;
  float metallic = albedoMetallic.a;
  float3 normal = normalize((normalRoughness.rgb * 2.0f) - 1.0f);
  float roughness = normalRoughness.a;
  float3 worldPos = worldPosAO.rgb;
  float ao = worldPosAO.a;
  float3 emissive = emissiveDepth.rgb;
  float3 V = normalize(CameraPosition - worldPos);
  float3 F0 = lerp(float3(0.04f, 0.04f, 0.04f), albedo, metallic);
  float3 Lo = float3(0.0f, 0.0f, 0.0f);
  for (int i = 0; i < NumLights && i < 32; (++i))
  {
    int lightType = LightTypes[i / 4][(i % 4)];
    float3 lightPos = LightPositions[i].xyz;
    float lightRange = LightPositions[i].w;
    float3 lightColor = LightColors[i].rgb;
    float lightIntensity = LightColors[i].a;
    float3 L;
    float attenuation = 1.0f;
    if (lightType == 0)
    {
      L = normalize(lightPos - worldPos);
      float distance = length(lightPos - worldPos);
      attenuation = 1.0f / 1.0f + (distance * distance) / (lightRange * lightRange);
    }
    else if (lightType == 1)
    {
      L = normalize((-LightDirections[i].xyz));
    }
    else if (lightType == 2)
    {
      L = normalize(lightPos - worldPos);
      float distance = length(lightPos - worldPos);
      attenuation = 1.0f / 1.0f + (distance * distance) / (lightRange * lightRange);
      float3 spotDir = normalize(LightDirections[i].xyz);
      float spotAngle = LightDirections[i].w;
      float theta = dot(L, (-spotDir));
      float epsilon = cos(spotAngle) - cos((spotAngle * 1.2f));
      float intensity = clamp(theta - cos((spotAngle * 1.2f)) / epsilon, 0.0f, 1.0f);
      attenuation *= intensity;
    }
    float3 H = normalize(V + L);
    float3 radiance = ((lightColor * lightIntensity) * attenuation);
    float NDF = DistributionGGX(normal, H, roughness);
    float G = GeometrySmith(normal, V, L, roughness);
    float3 F = fresnelSchlick(max(dot(H, V), 0.0f), F0);
    float3 kS = F;
    float3 kD = float3(1.0f, 1.0f, 1.0f) - kS;
    kD *= 1.0f - metallic;
    float3 numerator = ((NDF * G) * F);
    float denominator = ((4.0f * max(dot(normal, V), 0.0f)) * max(dot(normal, L), 0.0f)) + 0.0001f;
    float3 specular = numerator / denominator;
    float NdotL = max(dot(normal, L), 0.0f);
    Lo += (((kD * albedo) / PI + specular * radiance) * NdotL);
  }
  float3 ambient = (((AmbientColor * AmbientStrength) * albedo) * ao);
  float3 color = ambient + Lo + emissive;
  color = color / color + float3(1.0f, 1.0f, 1.0f);
  color = pow(color, 0.454545455f);
  return float4(color, 1.0f);
}

