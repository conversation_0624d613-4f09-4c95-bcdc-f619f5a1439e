﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Tessellation Control  Main shader ===========  Work registers: 30 (93% used at 100% occupancy) Uniform registers: 10 (7% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    1.80   27.50    0.00       LS Shortest path cycles:        0.08    0.00    0.00        A Longest path cycles:         1.80   29.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: false 
