#version 320 es

// Advanced PBR Vertex Shader - OpenGL ES Version
// Tests advanced PBR features and vertex processing

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec3 aTangent;
layout(location = 3) in vec3 aBitangent;
layout(location = 4) in vec2 aTexCoord;
layout(location = 5) in vec4 aColor;
layout(location = 6) in vec4 aBoneWeights;
layout(location = 7) in ivec4 aBoneIndices;

uniform mat4 uModelMatrix;
uniform mat4 uViewMatrix;
uniform mat4 uProjectionMatrix;
uniform mat4 uNormalMatrix;
uniform mat4 uBoneMatrices[64];
uniform vec3 uCameraPosition;
uniform float uTime;
uniform bool uUseSkinning;

out vec3 vWorldPos;
out vec3 vNormal;
out vec3 vTangent;
out vec3 vBitangent;
out vec2 vTexCoord;
out vec4 vColor;
out vec3 vViewDir;
out float vVertexAO;

void main()
{
    vec3 position = aPosition;
    vec3 normal = aNormal;
    vec3 tangent = aTangent;
    vec3 bitangent = aBitangent;
    
    // Skeletal animation
    if (uUseSkinning)
    {
        mat4 boneTransform = mat4(0.0);
        for(int i = 0; i < 4; i++)
        {
            if(aBoneWeights[i] > 0.0)
            {
                boneTransform += uBoneMatrices[aBoneIndices[i]] * aBoneWeights[i];
            }
        }
        
        vec4 skinnedPos = boneTransform * vec4(position, 1.0);
        vec4 skinnedNormal = boneTransform * vec4(normal, 0.0);
        vec4 skinnedTangent = boneTransform * vec4(tangent, 0.0);
        
        position = skinnedPos.xyz;
        normal = skinnedNormal.xyz;
        tangent = skinnedTangent.xyz;
        bitangent = cross(normal, tangent);
    }
    
    // Transform to world space
    vec4 worldPos = uModelMatrix * vec4(position, 1.0);
    vWorldPos = worldPos.xyz;
    
    // Transform TBN vectors to world space
    vNormal = normalize((uNormalMatrix * vec4(normal, 0.0)).xyz);
    vTangent = normalize((uNormalMatrix * vec4(tangent, 0.0)).xyz);
    vBitangent = normalize((uNormalMatrix * vec4(bitangent, 0.0)).xyz);
    
    // Pass through texture coordinates and color
    vTexCoord = aTexCoord;
    vColor = aColor;
    
    // Calculate view direction
    vViewDir = normalize(uCameraPosition - vWorldPos);
    
    // Calculate vertex-based ambient occlusion (simple approximation)
    vVertexAO = 1.0 - clamp(dot(vNormal, vec3(0.0, 1.0, 0.0)) * 0.5 + 0.5, 0.0, 1.0);
    vVertexAO = mix(0.3, 1.0, vVertexAO);
    
    // Transform to clip space
    gl_Position = uProjectionMatrix * uViewMatrix * worldPos;
}
