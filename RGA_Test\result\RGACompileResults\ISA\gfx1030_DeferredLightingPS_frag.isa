_amdgpu_ps_main:
	s_mov_b64 s[34:35], exec                                   // 000000000000: BEA2047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s36, s1                                          // 000000000008: BEA40301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s37, s1                                          // 000000000014: BEA50301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx16 s[0:15], s[36:37], null                    // 00000000001C: F4100012 FA000000
	s_load_dwordx16 s[16:31], s[36:37], 0x60                   // 000000000024: F4100412 FA000060
	v_interp_p1_f32_e32 v8, v0, attr0.x                        // 00000000002C: C8200000
	v_interp_p1_f32_e32 v9, v0, attr0.y                        // 000000000030: C8240100
	v_interp_p2_f32_e32 v8, v1, attr0.x                        // 000000000034: C8210001
	v_interp_p2_f32_e32 v9, v1, attr0.y                        // 000000000038: C8250101
	s_waitcnt lgkmcnt(0)                                       // 00000000003C: BF8CC07F
	image_sample v[0:3], v[8:9], s[0:7], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000040: F0800F08 00400008
	image_sample v[4:7], v[8:9], s[16:23], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000048: F0800F08 00440408
	s_load_dwordx4 s[0:3], s[36:37], null                      // 000000000050: F4080012 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000058: BF8CC07F
	s_buffer_load_dword s18, s[0:3], 0xc                       // 00000000005C: F4200480 FA00000C
	s_waitcnt lgkmcnt(0)                                       // 000000000064: BF8CC07F
	s_cmp_lt_i32 s18, 1                                        // 000000000068: BF048112
	s_cbranch_scc1 _L0                                         // 00000000006C: BF85011E
	s_load_dwordx8 s[36:43], s[36:37], 0x30                    // 000000000070: F40C0912 FA000030
	s_and_b64 exec, exec, s[34:35]                             // 000000000078: 87FE227E
	s_waitcnt lgkmcnt(0)                                       // 00000000007C: BF8CC07F
	image_sample v[13:16], v[8:9], s[36:43], s[8:11] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000080: F0800F08 00490D08
	s_clause 0x1                                               // 000000000088: BFA10001
	s_buffer_load_dwordx2 s[4:5], s[0:3], null                 // 00000000008C: F4240100 FA000000
	s_buffer_load_dword s6, s[0:3], 0x8                        // 000000000094: F4200180 FA000008
	s_waitcnt vmcnt(2)                                         // 00000000009C: BF8C3F72
	v_add_f32_e32 v21, 0xbd23d70a, v0                          // 0000000000A0: 062A00FF BD23D70A
	v_add_f32_e32 v23, 0xbd23d70a, v1                          // 0000000000A8: 062E02FF BD23D70A
	v_add_f32_e32 v24, 0xbd23d70a, v2                          // 0000000000B0: 063004FF BD23D70A
	s_movk_i32 s19, 0x40c                                      // 0000000000B8: B013040C
	s_mov_b32 s20, 0                                           // 0000000000BC: BE940380
	s_mov_b32 s21, 0                                           // 0000000000C0: BE950380
	s_waitcnt vmcnt(1) lgkmcnt(0)                              // 0000000000C4: BF8C0071
	v_sub_f32_e32 v10, s5, v5                                  // 0000000000C8: 08140A05
	v_sub_f32_e32 v18, s6, v6                                  // 0000000000CC: 08240C06
	v_mul_f32_e32 v11, v10, v10                                // 0000000000D0: 1016150A
	s_waitcnt vmcnt(0)                                         // 0000000000D4: BF8C3F70
	v_fma_f32 v12, v14, 2.0, -1.0                              // 0000000000D8: D54B000C 03CDE90E
	v_sub_f32_e32 v14, s4, v4                                  // 0000000000E0: 081C0804
	v_fma_f32 v17, v13, 2.0, -1.0                              // 0000000000E4: D54B0011 03CDE90D
	v_fma_f32 v15, v15, 2.0, -1.0                              // 0000000000EC: D54B000F 03CDE90F
	v_mul_f32_e32 v16, v16, v16                                // 0000000000F4: 10202110
	v_mul_f32_e32 v13, v12, v12                                // 0000000000F8: 101A190C
	v_fmac_f32_e32 v11, v14, v14                               // 0000000000FC: 56161D0E
	s_mov_b32 s4, 0xbea2f983                                   // 000000000100: BE8403FF BEA2F983
	v_fmaak_f32 v25, s4, v3, 0x3ea2f983                        // 000000000108: 5A320604 3EA2F983
	v_fmac_f32_e32 v13, v17, v17                               // 000000000110: 561A2311
	v_fmac_f32_e32 v11, v18, v18                               // 000000000114: 56162512
	v_fmac_f32_e32 v13, v15, v15                               // 000000000118: 561A1F0F
	v_rsq_f32_e32 v19, v11                                     // 00000000011C: 7E265D0B
	v_cmp_neq_f32_e32 vcc_lo, 0, v11                           // 000000000120: 7C1A1680
	v_rsq_f32_e32 v20, v13                                     // 000000000124: 7E285D0D
	v_cndmask_b32_e32 v19, 0, v19, vcc_lo                      // 000000000128: 02262680
	v_cmp_neq_f32_e32 vcc_lo, 0, v13                           // 00000000012C: 7C1A1A80
	v_mul_f32_e32 v11, v19, v10                                // 000000000130: 10161513
	v_cndmask_b32_e32 v20, 0, v20, vcc_lo                      // 000000000134: 02282880
	v_mul_f32_e32 v13, v19, v14                                // 000000000138: 101A1D13
	v_mov_b32_e32 v10, 0                                       // 00000000013C: 7E140280
	v_mul_f32_e32 v12, v20, v12                                // 000000000140: 10181914
	v_mul_f32_e32 v14, v20, v17                                // 000000000144: 101C2314
	v_mul_f32_e32 v17, v19, v18                                // 000000000148: 10222513
	v_mul_f32_e32 v18, v20, v15                                // 00000000014C: 10241F14
	v_fmaak_f32 v19, v21, v3, 0x3d23d70a                       // 000000000150: 5A260715 3D23D70A
	v_mul_f32_e32 v22, v11, v12                                // 000000000158: 102C190B
	v_mul_f32_e32 v21, v16, v16                                // 00000000015C: 102A2110
	v_mov_b32_e32 v15, 0                                       // 000000000160: 7E1E0280
	v_fmaak_f32 v20, v23, v3, 0x3d23d70a                       // 000000000164: 5A280717 3D23D70A
	v_fmaak_f32 v3, v24, v3, 0x3d23d70a                        // 00000000016C: 5A060718 3D23D70A
	v_fmac_f32_e32 v22, v13, v14                               // 000000000174: 562C1D0D
	v_mul_f32_e32 v23, v25, v1                                 // 000000000178: 102E0319
	v_mul_f32_e32 v24, v25, v2                                 // 00000000017C: 10300519
	v_fmac_f32_e32 v22, v17, v18                               // 000000000180: 562C2511
	v_max_f32_e32 v16, 0, v22                                  // 000000000184: 20202C80
	v_mul_f32_e32 v22, v25, v0                                 // 000000000188: 102C0119
	v_mul_f32_e32 v25, v16, v21                                // 00000000018C: 10322B10
	v_mul_f32_e32 v26, 0x40490fdb, v16                         // 000000000190: 103420FF 40490FDB
	v_mul_f32_e32 v27, 4.0, v16                                // 000000000198: 103620F6
	v_mov_b32_e32 v16, 0                                       // 00000000019C: 7E200280
_L7:
	s_and_b32 s4, s20, -4                                      // 0000000001A0: 8704C414
	s_add_i32 s5, s19, 0xfffffbf4                              // 0000000001A4: 8105FF13 FFFFFBF4
	s_addk_i32 s4, 0x600                                       // 0000000001AC: B7840600
	s_add_i32 s16, s19, 0xfffffc00                             // 0000000001B0: 8110FF13 FFFFFC00
	s_clause 0x1                                               // 0000000001B8: BFA10001
	s_buffer_load_dword s22, s[12:15], s4                      // 0000000001BC: F4200586 08000000
	s_buffer_load_dwordx4 s[4:7], s[12:15], s5                 // 0000000001C4: F4280106 0A000000
	s_waitcnt lgkmcnt(0)                                       // 0000000001CC: BF8CC07F
	s_buffer_load_dword s7, s[12:15], s16                      // 0000000001D0: F42001C6 20000000
	s_cmp_lg_u32 s22, 0                                        // 0000000001D8: BF078016
	s_cbranch_scc0 _L1                                         // 0000000001DC: BF8400C1
	s_cmp_lg_u32 s22, 1                                        // 0000000001E0: BF078116
	s_mov_b64 s[16:17], -1                                     // 0000000001E4: BE9004C1
	s_cbranch_scc0 _L2                                         // 0000000001E8: BF84003B
	v_mov_b32_e32 v31, 1.0                                     // 0000000001EC: 7E3E02F2
	s_cmp_lg_u32 s22, 2                                        // 0000000001F0: BF078216
	s_cbranch_scc1 _L3                                         // 0000000001F4: BF850037
	s_add_i32 s16, s19, -12                                    // 0000000001F8: 8110CC13
	v_sub_f32_e32 v30, s5, v5                                  // 0000000001FC: 083C0A05
	s_clause 0x1                                               // 000000000200: BFA10001
	s_buffer_load_dwordx4 s[36:39], s[12:15], s16              // 000000000204: F4280906 20000000
	s_buffer_load_dword s16, s[12:15], s19                     // 00000000020C: F4200406 26000000
	v_sub_f32_e32 v28, s4, v4                                  // 000000000214: 08380804
	v_sub_f32_e32 v34, s6, v6                                  // 000000000218: 08440C06
	v_mul_f32_e32 v29, v30, v30                                // 00000000021C: 103A3D1E
	v_fmac_f32_e32 v29, v28, v28                               // 000000000220: 563A391C
	v_fmac_f32_e32 v29, v34, v34                               // 000000000224: 563A4522
	v_rsq_f32_e32 v35, v29                                     // 000000000228: 7E465D1D
	s_waitcnt lgkmcnt(0)                                       // 00000000022C: BF8CC07F
	v_mul_f32_e64 v31, s37, s37                                // 000000000230: D508001F 00004A25
	v_mul_f32_e64 v32, s16, 0.15915494                         // 000000000238: D5080020 0001F010
	v_mul_f32_e64 v33, 0x3e4391d1, s16                         // 000000000240: D5080021 000020FF 3E4391D1
	v_cmp_neq_f32_e32 vcc_lo, 0, v29                           // 00000000024C: 7C1A3A80
	v_sqrt_f32_e32 v37, v29                                    // 000000000250: 7E4A671D
	v_fmac_f32_e64 v31, s36, s36                               // 000000000254: D52B001F 00004824
	v_cos_f32_e32 v32, v32                                     // 00000000025C: 7E406D20
	v_cos_f32_e32 v33, v33                                     // 000000000260: 7E426D21
	v_cndmask_b32_e32 v35, 0, v35, vcc_lo                      // 000000000264: 02464680
	v_fmac_f32_e64 v31, s38, s38                               // 000000000268: D52B001F 00004C26
	v_mul_f32_e32 v28, v35, v28                                // 000000000270: 10383923
	v_rsq_f32_e32 v36, v31                                     // 000000000274: 7E485D1F
	v_cmp_neq_f32_e32 vcc_lo, 0, v31                           // 000000000278: 7C1A3E80
	v_sub_f32_e32 v32, v33, v32                                // 00000000027C: 08404121
	v_mul_f32_e32 v29, v35, v34                                // 000000000280: 103A4523
	v_mul_f32_e32 v30, v35, v30                                // 000000000284: 103C3D23
	v_mul_f32_e32 v35, v37, v37                                // 000000000288: 10464B25
	v_rcp_f32_e32 v32, v32                                     // 00000000028C: 7E405520
	v_cndmask_b32_e32 v31, 0, v36, vcc_lo                      // 000000000290: 023E4880
	v_mul_f32_e64 v36, s7, s7                                  // 000000000294: D5080024 00000E07
	v_mul_f32_e32 v38, s36, v31                                // 00000000029C: 104C3E24
	v_mul_f32_e32 v34, s38, v31                                // 0000000002A0: 10443E26
	v_mul_f32_e32 v32, v33, v32                                // 0000000002A4: 10404121
	v_rcp_f32_e32 v33, v36                                     // 0000000002A8: 7E425524
	v_mul_f32_e32 v31, s37, v31                                // 0000000002AC: 103E3E25
	v_fma_f32 v32, -v28, v38, v32                              // 0000000002B0: D54B0020 24824D1C
	v_fma_f32 v32, -v29, v34, v32                              // 0000000002B8: D54B0020 2482451D
	v_fma_f32 v33, v35, v33, 1.0                               // 0000000002C0: D54B0021 03CA4323
	v_fma_f32 v31, -v31, v30, v32 clamp                        // 0000000002C8: D54B801F 24823D1F
	v_mul_f32_e32 v31, v31, v33                                // 0000000002D0: 103E431F
_L3:
	s_mov_b64 s[16:17], 0                                      // 0000000002D4: BE900480
_L2:
	s_andn2_b64 vcc, exec, s[16:17]                            // 0000000002D8: 8AEA107E
	s_cbranch_vccnz _L4                                        // 0000000002DC: BF870014
	s_add_i32 s16, s19, -12                                    // 0000000002E0: 8110CC13
	v_mov_b32_e32 v31, 1.0                                     // 0000000002E4: 7E3E02F2
	s_buffer_load_dwordx4 s[36:39], s[12:15], s16              // 0000000002E8: F4280906 20000000
	s_waitcnt lgkmcnt(0)                                       // 0000000002F0: BF8CC07F
	v_mul_f32_e64 v28, s37, s37                                // 0000000002F4: D508001C 00004A25
	v_fmac_f32_e64 v28, s36, s36                               // 0000000002FC: D52B001C 00004824
	v_fmac_f32_e64 v28, s38, s38                               // 000000000304: D52B001C 00004C26
	v_rsq_f32_e32 v29, v28                                     // 00000000030C: 7E3A5D1C
	v_cmp_neq_f32_e32 vcc_lo, 0, v28                           // 000000000310: 7C1A3880
	v_cndmask_b32_e32 v29, 0, v29, vcc_lo                      // 000000000314: 023A3A80
	v_mul_f32_e64 v28, v29, -s36                               // 000000000318: D508001C 4000491D
	v_mul_f32_e64 v30, v29, -s37                               // 000000000320: D508001E 40004B1D
	v_mul_f32_e64 v29, v29, -s38                               // 000000000328: D508001D 40004D1D
_L4:
	s_cbranch_execnz _L5                                       // 000000000330: BF890014
_L8:
	v_sub_f32_e32 v29, s5, v5                                  // 000000000334: 083A0A05
	v_sub_f32_e32 v28, s4, v4                                  // 000000000338: 08380804
	v_sub_f32_e32 v31, s6, v6                                  // 00000000033C: 083E0C06
	s_waitcnt lgkmcnt(0)                                       // 000000000340: BF8CC07F
	v_mul_f32_e64 v33, s7, s7                                  // 000000000344: D5080021 00000E07
	v_mul_f32_e32 v30, v29, v29                                // 00000000034C: 103C3B1D
	v_rcp_f32_e32 v33, v33                                     // 000000000350: 7E425521
	v_fmac_f32_e32 v30, v28, v28                               // 000000000354: 563C391C
	v_fmac_f32_e32 v30, v31, v31                               // 000000000358: 563C3F1F
	v_rsq_f32_e32 v32, v30                                     // 00000000035C: 7E405D1E
	v_sqrt_f32_e32 v34, v30                                    // 000000000360: 7E44671E
	v_cmp_neq_f32_e32 vcc_lo, 0, v30                           // 000000000364: 7C1A3C80
	v_cndmask_b32_e32 v32, 0, v32, vcc_lo                      // 000000000368: 02404080
	v_mul_f32_e32 v34, v34, v34                                // 00000000036C: 10444522
	v_mul_f32_e32 v28, v32, v28                                // 000000000370: 10383920
	v_mul_f32_e32 v30, v32, v29                                // 000000000374: 103C3B20
	v_mul_f32_e32 v29, v32, v31                                // 000000000378: 103A3F20
	v_fma_f32 v31, v34, v33, 1.0                               // 00000000037C: D54B001F 03CA4322
_L5:
	v_add_f32_e32 v32, v30, v11                                // 000000000384: 0640171E
	v_add_f32_e32 v33, v28, v13                                // 000000000388: 06421B1C
	v_add_f32_e32 v35, v29, v17                                // 00000000038C: 0646231D
	s_add_i32 s4, s19, 0xfffffe00                              // 000000000390: 8104FF13 FFFFFE00
	s_add_i32 s5, s19, 0xfffffdf4                              // 000000000398: 8105FF13 FFFFFDF4
	v_mul_f32_e32 v34, v32, v32                                // 0000000003A0: 10444120
	s_clause 0x1                                               // 0000000003A4: BFA10001
	s_buffer_load_dword s4, s[12:15], s4                       // 0000000003A8: F4200106 08000000
	s_buffer_load_dwordx4 s[36:39], s[12:15], s5               // 0000000003B0: F4280906 0A000000
	v_fmac_f32_e32 v34, v33, v33                               // 0000000003B8: 56444321
	v_fmac_f32_e32 v34, v35, v35                               // 0000000003BC: 56444723
	v_rsq_f32_e32 v36, v34                                     // 0000000003C0: 7E485D22
	v_cmp_neq_f32_e32 vcc_lo, 0, v34                           // 0000000003C4: 7C1A4480
	s_waitcnt lgkmcnt(0)                                       // 0000000003C8: BF8CC07F
	v_mul_f32_e32 v31, s4, v31                                 // 0000000003CC: 103E3E04
	s_add_i32 s4, s21, 1                                       // 0000000003D0: 81048115
	s_cmp_lt_u32 s4, s18                                       // 0000000003D4: BF0A1204
	v_cndmask_b32_e32 v34, 0, v36, vcc_lo                      // 0000000003D8: 02444880
	s_cselect_b64 s[6:7], -1, 0                                // 0000000003DC: 858680C1
	s_cmp_lt_u32 s21, 31                                       // 0000000003E0: BF0A9F15
	s_cselect_b64 s[16:17], -1, 0                              // 0000000003E4: 859080C1
	v_mul_f32_e32 v32, v34, v32                                // 0000000003E8: 10404122
	v_mul_f32_e32 v33, v34, v33                                // 0000000003EC: 10424322
	v_mul_f32_e32 v34, v34, v35                                // 0000000003F0: 10444722
	v_mul_f32_e32 v35, v30, v12                                // 0000000003F4: 1046191E
	s_and_b64 s[6:7], s[6:7], s[16:17]                         // 0000000003F8: 87861006
	v_mul_f32_e32 v36, v32, v12                                // 0000000003FC: 10481920
	v_mul_f32_e32 v32, v32, v11                                // 000000000400: 10401720
	s_add_i32 s19, s19, 16                                     // 000000000404: 81139013
	v_fmac_f32_e32 v35, v28, v14                               // 000000000408: 56461D1C
	s_and_b64 vcc, exec, s[6:7]                                // 00000000040C: 87EA067E
	v_fmac_f32_e32 v36, v33, v14                               // 000000000410: 56481D21
	v_fmac_f32_e32 v32, v33, v13                               // 000000000414: 56401B21
	s_add_i32 s20, s20, 4                                      // 000000000418: 81148414
	v_fmac_f32_e32 v35, v29, v18                               // 00000000041C: 5646251D
	v_fmac_f32_e32 v36, v34, v18                               // 000000000420: 56482522
	v_fmac_f32_e32 v32, v34, v17                               // 000000000424: 56402322
	v_max_f32_e32 v34, 0, v35                                  // 000000000428: 20444680
	v_max_f32_e32 v36, 0, v36                                  // 00000000042C: 20484880
	v_max_f32_e32 v32, 0, v32                                  // 000000000430: 20404080
	v_mul_f32_e32 v35, v34, v26                                // 000000000434: 10463522
	v_mul_f32_e32 v33, v36, v36                                // 000000000438: 10424924
	v_sub_f32_e64 v32, 1.0, v32 clamp                          // 00000000043C: D5048020 000240F2
	v_fmaak_f32 v36, v34, v27, 0x38d1b717                      // 000000000444: 5A483722 38D1B717
	v_mul_f32_e32 v33, v33, v21                                // 00000000044C: 10422B21
	v_mul_f32_e32 v33, v33, v33                                // 000000000450: 10424321
	v_mul_f32_e32 v33, v33, v35                                // 000000000454: 10424721
	v_mul_f32_e32 v35, v32, v32                                // 000000000458: 10464120
	v_mul_f32_e32 v33, v33, v36                                // 00000000045C: 10424921
	v_mul_f32_e32 v35, v35, v35                                // 000000000460: 10464723
	v_mul_f32_e32 v36, v34, v25                                // 000000000464: 10483322
	v_rcp_f32_e32 v33, v33                                     // 000000000468: 7E425521
	v_mul_f32_e32 v32, v32, v35                                // 00000000046C: 10404720
	v_mul_f32_e32 v31, v36, v31                                // 000000000470: 103E3F24
	v_fma_f32 v35, -v32, v19, 1.0                              // 000000000474: D54B0023 23CA2720
	v_fma_f32 v36, -v32, v20, 1.0                              // 00000000047C: D54B0024 23CA2920
	v_fma_f32 v32, -v32, v3, 1.0                               // 000000000484: D54B0020 23CA0720
	v_mul_f32_e32 v31, v31, v33                                // 00000000048C: 103E431F
	v_add_f32_e32 v33, v35, v19                                // 000000000490: 06422723
	v_add_f32_e32 v35, v36, v20                                // 000000000494: 06462924
	v_add_f32_e32 v32, v32, v3                                 // 000000000498: 06400720
	v_fma_f32 v36, v31, s36, -v22                              // 00000000049C: D54B0024 8458491F
	v_fma_f32 v37, v31, s37, -v23                              // 0000000004A4: D54B0025 845C4B1F
	v_fma_f32 v31, v31, s38, -v24                              // 0000000004AC: D54B001F 84604D1F
	v_fma_f32 v33, v33, v36, v22                               // 0000000004B4: D54B0021 045A4921
	v_fma_f32 v35, v35, v37, v23                               // 0000000004BC: D54B0023 045E4B23
	v_fma_f32 v31, v32, v31, v24                               // 0000000004C4: D54B001F 04623F20
	v_fmac_f32_e32 v10, v33, v34                               // 0000000004CC: 56144521
	v_fmac_f32_e32 v15, v35, v34                               // 0000000004D0: 561E4523
	v_fmac_f32_e32 v16, v31, v34                               // 0000000004D4: 5620451F
	s_cbranch_vccz _L6                                         // 0000000004D8: BF860007
	s_mov_b32 s21, s4                                          // 0000000004DC: BE950304
	s_branch _L7                                               // 0000000004E0: BF82FF2F
_L1:
	s_branch _L8                                               // 0000000004E4: BF82FF93
_L0:
	s_and_b64 exec, exec, s[34:35]                             // 0000000004E8: 87FE227E
	v_mov_b32_e32 v16, 0                                       // 0000000004EC: 7E200280
	v_mov_b32_e32 v15, 0                                       // 0000000004F0: 7E1E0280
	v_mov_b32_e32 v10, 0                                       // 0000000004F4: 7E140280
_L6:
	image_sample v[3:5], v[8:9], s[24:31], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D// 0000000004F8: F0800708 00460308
	s_buffer_load_dwordx4 s[0:3], s[0:3], 0x10                 // 000000000500: F4280000 FA000010
	s_waitcnt vmcnt(1)                                         // 000000000508: BF8C3F71
	v_mul_f32_e32 v0, v7, v0                                   // 00000000050C: 10000107
	v_mul_f32_e32 v1, v7, v1                                   // 000000000510: 10020307
	v_mul_f32_e32 v2, v7, v2                                   // 000000000514: 10040507
	s_waitcnt lgkmcnt(0)                                       // 000000000518: BF8CC07F
	v_mul_f32_e32 v0, s0, v0                                   // 00000000051C: 10000000
	v_mul_f32_e32 v1, s1, v1                                   // 000000000520: 10020201
	v_mul_f32_e32 v2, s2, v2                                   // 000000000524: 10040402
	s_waitcnt vmcnt(0)                                         // 000000000528: BF8C3F70
	v_add_f32_e32 v3, v10, v3                                  // 00000000052C: 0606070A
	v_add_f32_e32 v4, v15, v4                                  // 000000000530: 0608090F
	v_add_f32_e32 v5, v16, v5                                  // 000000000534: 060A0B10
	v_fmac_f32_e32 v3, s3, v0                                  // 000000000538: 56060003
	v_fmac_f32_e32 v4, s3, v1                                  // 00000000053C: 56080203
	v_fmac_f32_e32 v5, s3, v2                                  // 000000000540: 560A0403
	v_rcp_f32_e32 v0, v3                                       // 000000000544: 7E005503
	v_rcp_f32_e32 v1, v4                                       // 000000000548: 7E025504
	v_rcp_f32_e32 v2, v5                                       // 00000000054C: 7E045505
	v_fma_f32 v0, v3, v0, 1.0                                  // 000000000550: D54B0000 03CA0103
	v_fma_f32 v1, v4, v1, 1.0                                  // 000000000558: D54B0001 03CA0304
	v_fma_f32 v2, v5, v2, 1.0                                  // 000000000560: D54B0002 03CA0505
	v_mov_b32_e32 v3, 1.0                                      // 000000000568: 7E0602F2
	v_log_f32_e32 v0, v0                                       // 00000000056C: 7E004F00
	v_log_f32_e32 v1, v1                                       // 000000000570: 7E024F01
	v_log_f32_e32 v2, v2                                       // 000000000574: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 000000000578: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 000000000580: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 000000000588: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 000000000590: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 000000000594: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 000000000598: 7E044B02
	exp mrt0 v0, v1, v2, v3 done vm                            // 00000000059C: F800180F 03020100
	s_endpgm                                                   // 0000000005A4: BF810000
