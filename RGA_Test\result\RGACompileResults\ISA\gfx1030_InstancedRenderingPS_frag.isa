_amdgpu_ps_main:
	s_mov_b64 s[20:21], exec                                   // 000000000000: BE94047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_getpc_b64 s[4:5]                                         // 000000000008: BE841F00
	s_mov_b32 s0, s1                                           // 00000000000C: BE800301
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s1, s5                                           // 000000000014: BE810305
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx4 s[12:15], s[0:1], 0x20                      // 00000000001C: F4080300 FA000020
	s_load_dwordx8 s[4:11], s[0:1], 0x30                       // 000000000024: F40C0100 FA000030
	v_interp_p1_f32_e32 v8, v0, attr5.x                        // 00000000002C: C8201400
	v_interp_p1_f32_e32 v2, v0, attr2.x                        // 000000000030: C8080800
	v_interp_p1_f32_e32 v3, v0, attr2.y                        // 000000000034: C80C0900
	s_clause 0x1                                               // 000000000038: BFA10001
	s_load_dwordx8 s[24:31], s[0:1], null                      // 00000000003C: F40C0600 FA000000
	s_load_dwordx4 s[16:19], s[0:1], null                      // 000000000044: F4080400 FA000000
	v_interp_p2_f32_e32 v8, v1, attr5.x                        // 00000000004C: C8211401
	v_interp_p2_f32_e32 v2, v1, attr2.x                        // 000000000050: C8090801
	v_interp_p2_f32_e32 v3, v1, attr2.y                        // 000000000054: C80D0901
	v_interp_p1_f32_e32 v17, v0, attr4.y                       // 000000000058: C8441100
	v_interp_p1_f32_e32 v13, v0, attr4.x                       // 00000000005C: C8341000
	v_interp_p1_f32_e32 v18, v0, attr4.z                       // 000000000060: C8481200
	v_add_f32_e32 v4, v8, v2                                   // 000000000064: 06080508
	v_add_f32_e32 v5, v8, v3                                   // 000000000068: 060A0708
	v_interp_p2_f32_e32 v17, v1, attr4.y                       // 00000000006C: C8451101
	v_interp_p2_f32_e32 v13, v1, attr4.x                       // 000000000070: C8351001
	v_interp_p2_f32_e32 v18, v1, attr4.z                       // 000000000074: C8491201
	v_mul_f32_e32 v4, 0x3dcccccd, v4                           // 000000000078: 100808FF 3DCCCCCD
	v_mul_f32_e32 v5, 0x3dcccccd, v5                           // 000000000080: 100A0AFF 3DCCCCCD
	v_mul_f32_e32 v7, v17, v17                                 // 000000000088: 100E2311
	v_interp_p1_f32_e32 v12, v0, attr1.y                       // 00000000008C: C8300500
	v_interp_p1_f32_e32 v14, v0, attr1.x                       // 000000000090: C8380400
	v_interp_p1_f32_e32 v15, v0, attr1.z                       // 000000000094: C83C0600
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	image_sample v9, v[4:5], s[4:11], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000009C: F0800108 00610904
	v_cvt_i32_f32_e32 v4, v8                                   // 0000000000A4: 7E081108
	v_fmac_f32_e32 v7, v13, v13                                // 0000000000A8: 560E1B0D
	v_interp_p2_f32_e32 v12, v1, attr1.y                       // 0000000000AC: C8310501
	v_interp_p2_f32_e32 v14, v1, attr1.x                       // 0000000000B0: C8390401
	v_interp_p2_f32_e32 v15, v1, attr1.z                       // 0000000000B4: C83D0601
	v_cvt_f32_i32_e32 v4, v4                                   // 0000000000B8: 7E080B04
	v_fmac_f32_e32 v7, v18, v18                                // 0000000000BC: 560E2512
	image_sample v[2:5], v[2:4], s[24:31], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D_ARRAY// 0000000000C0: F0800F28 00660202
	s_clause 0x1                                               // 0000000000C8: BFA10001
	s_buffer_load_dwordx2 s[22:23], s[16:19], 0x20             // 0000000000CC: F4240588 FA000020
	s_buffer_load_dword s3, s[16:19], 0x28                     // 0000000000D4: F42000C8 FA000028
	v_rsq_f32_e32 v11, v7                                      // 0000000000DC: 7E165D07
	s_clause 0x1                                               // 0000000000E0: BFA10001
	s_buffer_load_dwordx2 s[0:1], s[16:19], 0x30               // 0000000000E4: F4240008 FA000030
	s_buffer_load_dword s24, s[16:19], 0x38                    // 0000000000EC: F4200608 FA000038
	s_waitcnt lgkmcnt(0)                                       // 0000000000F4: BF8CC07F
	v_mul_f32_e64 v6, s23, s23                                 // 0000000000F8: D5080006 00002E17
	v_fmac_f32_e64 v6, s22, s22                                // 000000000100: D52B0006 00002C16
	v_fmac_f32_e64 v6, s3, s3                                  // 000000000108: D52B0006 00000603
	v_rsq_f32_e32 v10, v6                                      // 000000000110: 7E145D06
	v_cmp_neq_f32_e32 vcc_lo, 0, v6                            // 000000000114: 7C1A0C80
	v_cndmask_b32_e32 v10, 0, v10, vcc_lo                      // 000000000118: 02141480
	v_cmp_neq_f32_e32 vcc_lo, 0, v7                            // 00000000011C: 7C1A0E80
	v_mul_f32_e64 v6, v10, -s23                                // 000000000120: D5080006 40002F0A
	v_cndmask_b32_e32 v19, 0, v11, vcc_lo                      // 000000000128: 02261680
	v_mul_f32_e64 v7, v10, -s22                                // 00000000012C: D5080007 40002D0A
	v_mul_f32_e32 v11, v12, v12                                // 000000000134: 1016190C
	v_mul_f32_e64 v10, v10, -s3                                // 000000000138: D508000A 4000070A
	s_mov_b32 s3, 0xc3600000                                   // 000000000140: BE8303FF C3600000
	v_fma_f32 v16, v19, v17, v6                                // 000000000148: D54B0010 041A2313
	v_fma_f32 v20, v19, v13, v7                                // 000000000150: D54B0014 041E1B13
	v_fmac_f32_e32 v11, v14, v14                               // 000000000158: 56161D0E
	v_fma_f32 v22, v19, v18, v10                               // 00000000015C: D54B0016 042A2513
	s_buffer_load_dwordx2 s[22:23], s[16:19], 0x40             // 000000000164: F4240588 FA000040
	v_mul_f32_e32 v21, v16, v16                                // 00000000016C: 102A2110
	v_mul_f32_e32 v17, v19, v17                                // 000000000170: 10222313
	v_fmac_f32_e32 v11, v15, v15                               // 000000000174: 56161F0F
	v_fmac_f32_e32 v21, v20, v20                               // 000000000178: 562A2914
	v_rsq_f32_e32 v23, v11                                     // 00000000017C: 7E2E5D0B
	v_cmp_neq_f32_e32 vcc_lo, 0, v11                           // 000000000180: 7C1A1680
	v_fmac_f32_e32 v21, v22, v22                               // 000000000184: 562A2D16
	v_rsq_f32_e32 v24, v21                                     // 000000000188: 7E305D15
	v_cndmask_b32_e32 v23, 0, v23, vcc_lo                      // 00000000018C: 022E2E80
	v_cmp_neq_f32_e32 vcc_lo, 0, v21                           // 000000000190: 7C1A2A80
	v_mul_f32_e32 v11, v23, v12                                // 000000000194: 10161917
	v_mul_f32_e32 v14, v23, v14                                // 000000000198: 101C1D17
	v_mul_f32_e32 v23, v23, v15                                // 00000000019C: 102E1F17
	v_cndmask_b32_e32 v21, 0, v24, vcc_lo                      // 0000000001A0: 022A3080
	v_cmp_gt_f32_e32 vcc_lo, 2.0, v8                           // 0000000001A4: 7C0810F4
	v_interp_p1_f32_e32 v24, v0, attr3.z                       // 0000000001A8: C8600E00
	v_mul_f32_e32 v12, v21, v16                                // 0000000001AC: 10182115
	v_mul_f32_e32 v16, v21, v20                                // 0000000001B0: 10202915
	v_mov_b32_e32 v20, 0x3f4ccccc                              // 0000000001B4: 7E2802FF 3F4CCCCC
	v_mul_f32_e32 v15, v21, v22                                // 0000000001BC: 101E2D15
	v_interp_p1_f32_e32 v22, v0, attr3.y                       // 0000000001C0: C8580D00
	v_mul_f32_e32 v12, v12, v11                                // 0000000001C4: 1018170C
	v_interp_p2_f32_e32 v24, v1, attr3.z                       // 0000000001C8: C8610E01
	v_cndmask_b32_e32 v20, 0x3dcccccd, v20, vcc_lo             // 0000000001CC: 022828FF 3DCCCCCD
	v_interp_p2_f32_e32 v22, v1, attr3.y                       // 0000000001D4: C8590D01
	v_fmac_f32_e32 v12, v16, v14                               // 0000000001D8: 56181D10
	v_cndmask_b32_e64 v16, 0x3f4ccccd, 0, vcc_lo               // 0000000001DC: D5010010 01A900FF 3F4CCCCD
	v_cmp_gt_f32_e32 vcc_lo, 1.0, v8                           // 0000000001E8: 7C0810F2
	v_fmac_f32_e32 v12, v15, v23                               // 0000000001EC: 56182F0F
	v_cndmask_b32_e64 v21, v16, 0x3dcccccd, vcc_lo             // 0000000001F0: D5010015 01A9FF10 3DCCCCCD
	v_mul_f32_e32 v16, v19, v13                                // 0000000001FC: 10201B13
	v_mul_f32_e32 v19, v19, v18                                // 000000000200: 10262513
	v_max_f32_e32 v8, 0, v12                                   // 000000000204: 20101880
	v_cndmask_b32_e64 v12, v20, 0x3f333333, vcc_lo             // 000000000208: D501000C 01A9FF14 3F333333
	v_interp_p1_f32_e32 v20, v0, attr3.x                       // 000000000214: C8500C00
	v_mul_f32_e32 v18, v10, v23                                // 000000000218: 10242F0A
	v_log_f32_e32 v15, v8                                      // 00000000021C: 7E1E4F08
	v_interp_p2_f32_e32 v20, v1, attr3.x                       // 000000000220: C8510C01
	s_waitcnt vmcnt(1)                                         // 000000000224: BF8C3F71
	v_add_f32_e32 v8, v12, v9                                  // 000000000228: 0610130C
	v_mul_f32_e32 v12, v7, v14                                 // 00000000022C: 10181D07
	v_fmaak_f32 v9, s3, v8, 0x43800000                         // 000000000230: 5A121003 43800000
	s_buffer_load_dword s3, s[16:19], 0x48                     // 000000000238: F42000C8 FA000048
	v_interp_p1_f32_e32 v8, v0, attr0.x                        // 000000000240: C8200000
	v_mul_legacy_f32_e32 v14, v9, v15                          // 000000000244: 0E1C1F09
	v_fma_f32 v15, v6, v11, v12                                // 000000000248: D54B000F 04321706
	s_waitcnt vmcnt(0)                                         // 000000000250: BF8C3F70
	v_mul_f32_e32 v13, v2, v20                                 // 000000000254: 101A2902
	v_interp_p1_f32_e32 v9, v0, attr0.z                        // 000000000258: C8240200
	v_interp_p2_f32_e32 v8, v1, attr0.x                        // 00000000025C: C8210001
	v_exp_f32_e32 v25, v14                                     // 000000000260: 7E324B0E
	v_fmac_f32_e32 v15, v10, v23                               // 000000000264: 561E2F0A
	v_mul_f32_e32 v14, v4, v24                                 // 000000000268: 101C3104
	s_waitcnt lgkmcnt(0)                                       // 00000000026C: BF8CC07F
	v_mul_f32_e32 v4, s22, v13                                 // 000000000270: 10081A16
	v_interp_p2_f32_e32 v9, v1, attr0.z                        // 000000000274: C8250201
	v_max_f32_e32 v26, 0, v15                                  // 000000000278: 20341E80
	v_mul_f32_e32 v15, v3, v22                                 // 00000000027C: 101E2D03
	v_mul_f32_e32 v20, v25, v21                                // 000000000280: 10282B19
	v_mul_f32_e32 v3, s3, v14                                  // 000000000284: 10061C03
	v_mul_f32_e32 v2, s23, v15                                 // 000000000288: 10041E17
	v_fma_f32 v21, v26, v13, v20                               // 00000000028C: D54B0015 04521B1A
	v_fma_f32 v22, v26, v15, v20                               // 000000000294: D54B0016 04521F1A
	v_fmac_f32_e32 v20, v26, v14                               // 00000000029C: 56281D1A
	v_fmac_f32_e32 v4, s0, v21                                 // 0000000002A0: 56082A00
	v_fmac_f32_e32 v2, s1, v22                                 // 0000000002A4: 56042C01
	v_fmac_f32_e32 v3, s24, v20                                // 0000000002A8: 56062818
	s_and_saveexec_b64 s[0:1], vcc                             // 0000000002AC: BE80246A
	s_cbranch_execz _L0                                        // 0000000002B0: BF88002C
	s_buffer_load_dword s3, s[16:19], 0x4c                     // 0000000002B4: F42000C8 FA00004C
	v_mul_f32_e32 v20, 0x3dcccccd, v8                          // 0000000002BC: 102810FF 3DCCCCCD
	v_mul_f32_e32 v10, v10, v19                                // 0000000002C4: 1014270A
	v_fmac_f32_e32 v10, v7, v16                                // 0000000002C8: 56142107
	v_fma_f32 v7, v17, -v6, -v10                               // 0000000002CC: D54B0007 C42A0D11
	v_add_f32_e32 v10, v12, v18                                // 0000000002D4: 0614250C
	v_max_f32_e32 v7, 0, v7                                    // 0000000002D8: 200E0E80
	v_fma_f32 v6, v11, -v6, -v10                               // 0000000002DC: D54B0006 C42A0D0B
	s_waitcnt lgkmcnt(0)                                       // 0000000002E4: BF8CC07F
	v_mul_f32_e64 v21, 0x3dcccccd, s3                          // 0000000002E8: D5080015 000006FF 3DCCCCCD
	v_fmamk_f32 v22, s3, 0x3dcccccd, v20                       // 0000000002F4: 582C2803 3DCCCCCD
	v_fmac_f32_e64 v20, s3, 2.0                                // 0000000002FC: D52B0014 0001E803
	v_mul_f32_e32 v7, v7, v7                                   // 000000000304: 100E0F07
	v_max_f32_e32 v6, 0, v6                                    // 000000000308: 200C0C80
	v_fmac_f32_e32 v21, 0x3dcccccd, v9                         // 00000000030C: 562A12FF 3DCCCCCD
	v_mul_f32_e32 v22, 0x3dcccccd, v22                         // 000000000314: 102C2CFF 3DCCCCCD
	v_mul_f32_e32 v10, 0.15915494, v20                         // 00000000031C: 101428F8
	v_mul_f32_e32 v7, v7, v7                                   // 000000000320: 100E0F07
	v_mul_f32_e32 v23, 0x3dcccccd, v21                         // 000000000324: 102E2AFF 3DCCCCCD
	v_sin_f32_e32 v10, v10                                     // 00000000032C: 7E146B0A
	v_mul_f32_e64 v6, v7, v6 div:2                             // 000000000330: D5080006 18020D07
	image_sample v21, v[22:23], s[4:11], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000338: F0800108 00611516
	v_fmac_f32_e32 v2, v6, v15                                 // 000000000340: 56041F06
	v_fmac_f32_e32 v3, v6, v14                                 // 000000000344: 56061D06
	v_fmac_f32_e32 v4, v6, v13                                 // 000000000348: 56081B06
	s_waitcnt vmcnt(0)                                         // 00000000034C: BF8C3F70
	v_mul_f32_e32 v7, v10, v21                                 // 000000000350: 100E2B0A
	v_fmac_f32_e32 v2, 0x3dcccccd, v7                          // 000000000354: 56040EFF 3DCCCCCD
	v_fmamk_f32 v3, v7, 0xbd4ccccd, v3                         // 00000000035C: 58060707 BD4CCCCD
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 000000000364: 88FE007E
	s_and_b64 exec, exec, s[20:21]                             // 000000000368: 87FE147E
	s_mov_b32 m0, s2                                           // 00000000036C: BEFC0302
	v_interp_p1_f32_e32 v6, v0, attr0.y                        // 000000000370: C8180100
	v_interp_p1_f32_e32 v7, v0, attr6.x                        // 000000000374: C81C1800
	v_interp_p1_f32_e32 v0, v0, attr3.w                        // 000000000378: C8000F00
	v_interp_p2_f32_e32 v6, v1, attr0.y                        // 00000000037C: C8190101
	v_interp_p2_f32_e32 v7, v1, attr6.x                        // 000000000380: C81D1801
	v_interp_p2_f32_e32 v0, v1, attr3.w                        // 000000000384: C8010F01
	v_mul_f32_e32 v6, v6, v6                                   // 000000000388: 100C0D06
	v_cmp_gt_f32_e32 vcc_lo, 0x3e99999a, v7                    // 00000000038C: 7C080EFF 3E99999A
	v_mul_f32_e32 v0, v5, v0                                   // 000000000394: 10000105
	v_fmac_f32_e32 v6, v8, v8                                  // 000000000398: 560C1108
	v_fmaak_f32 v8, -2.0, v7, 0x3e99999a                       // 00000000039C: 5A100EF5 3E99999A
	v_fmac_f32_e32 v6, v9, v9                                  // 0000000003A4: 560C1309
	v_fma_f32 v9, v7, 0.5, 0.5                                 // 0000000003A8: D54B0009 03C1E107
	v_cndmask_b32_e32 v8, 0x80000000, v8, vcc_lo               // 0000000003B0: 021010FF 80000000
	v_cmp_gt_f32_e32 vcc_lo, 1.0, v7                           // 0000000003B8: 7C080EF2
	v_sqrt_f32_e32 v6, v6                                      // 0000000003BC: 7E0C6706
	v_mul_f32_e32 v10, v2, v9                                  // 0000000003C0: 10141302
	v_fmac_f32_e32 v8, v4, v9                                  // 0000000003C4: 56101304
	v_mul_f32_e32 v7, v3, v9                                   // 0000000003C8: 100E1303
	v_cndmask_b32_e32 v2, v2, v10, vcc_lo                      // 0000000003CC: 02041502
	v_cndmask_b32_e32 v4, v4, v8, vcc_lo                       // 0000000003D0: 02081104
	v_cndmask_b32_e32 v3, v3, v7, vcc_lo                       // 0000000003D4: 02060F03
	v_mul_f32_e32 v6, 0xbc6c5f03, v6                           // 0000000003D8: 100C0CFF BC6C5F03
	v_add_f32_e32 v2, 0xbf4ccccd, v2                           // 0000000003E0: 060404FF BF4CCCCD
	v_add_f32_e32 v1, 0xbf333333, v4                           // 0000000003E8: 060208FF BF333333
	v_add_f32_e32 v3, 0xbf666666, v3                           // 0000000003F0: 060606FF BF666666
	v_exp_f32_e32 v6, v6                                       // 0000000003F8: 7E0C4B06
	v_fmaak_f32 v1, v1, v6, 0x3f333333                         // 0000000003FC: 5A020D01 3F333333
	v_fmaak_f32 v2, v2, v6, 0x3f4ccccd                         // 000000000404: 5A040D02 3F4CCCCD
	v_fmaak_f32 v3, v3, v6, 0x3f666666                         // 00000000040C: 5A060D03 3F666666
	exp mrt0 v1, v2, v3, v0 done vm                            // 000000000414: F800180F 00030201
	s_endpgm                                                   // 00000000041C: BF810000
