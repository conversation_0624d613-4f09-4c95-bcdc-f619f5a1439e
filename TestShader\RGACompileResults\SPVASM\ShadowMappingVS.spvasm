; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 86
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TEXCOORD0 %in_var_COLOR0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6 %out_var_TEXCOORD7
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "WorldMatrix"
               OpMemberName %type_PerFrame 1 "ViewMatrix"
               OpMemberName %type_PerFrame 2 "ProjectionMatrix"
               OpMemberName %type_PerFrame 3 "LightViewProjectionMatrix"
               OpMemberName %type_PerFrame 4 "LightPosition"
               OpMemberName %type_PerFrame 5 "CameraPosition"
               OpName %PerFrame "PerFrame"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %out_var_TEXCOORD7 "out.var.TEXCOORD7"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TEXCOORD0 Location 2
               OpDecorate %in_var_COLOR0 Location 3
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %out_var_TEXCOORD7 Location 7
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 3 MatrixStride 16
               OpMemberDecorate %type_PerFrame 3 RowMajor
               OpMemberDecorate %type_PerFrame 4 Offset 256
               OpMemberDecorate %type_PerFrame 5 Offset 272
               OpDecorate %type_PerFrame Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_5 = OpConstant %int 5
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
    %v3float = OpTypeVector %float 3
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %v3float %v3float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %40 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD7 = OpVariable %_ptr_Output_float Output
       %main = OpFunction %void None %40
         %44 = OpLabel
         %45 = OpLoad %v3float %in_var_POSITION
         %46 = OpLoad %v3float %in_var_NORMAL
         %47 = OpLoad %v2float %in_var_TEXCOORD0
         %48 = OpLoad %v4float %in_var_COLOR0
         %49 = OpCompositeExtract %float %45 0
         %50 = OpCompositeExtract %float %45 1
         %51 = OpCompositeExtract %float %45 2
         %52 = OpCompositeConstruct %v4float %49 %50 %51 %float_1
         %53 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_0
         %54 = OpLoad %mat4v4float %53
         %55 = OpMatrixTimesVector %v4float %54 %52
         %56 = OpVectorShuffle %v3float %55 %55 0 1 2
         %57 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_1
         %58 = OpLoad %mat4v4float %57
         %59 = OpMatrixTimesVector %v4float %58 %55
         %60 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
         %61 = OpLoad %mat4v4float %60
         %62 = OpMatrixTimesVector %v4float %61 %59
         %63 = OpCompositeExtract %v4float %54 0
         %64 = OpVectorShuffle %v3float %63 %63 0 1 2
         %65 = OpCompositeExtract %v4float %54 1
         %66 = OpVectorShuffle %v3float %65 %65 0 1 2
         %67 = OpCompositeExtract %v4float %54 2
         %68 = OpVectorShuffle %v3float %67 %67 0 1 2
         %69 = OpCompositeConstruct %mat3v3float %64 %66 %68
         %70 = OpMatrixTimesVector %v3float %69 %46
         %71 = OpExtInst %v3float %1 Normalize %70
         %72 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_3
         %73 = OpLoad %mat4v4float %72
         %74 = OpMatrixTimesVector %v4float %73 %55
         %75 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_4
         %76 = OpLoad %v3float %75
         %77 = OpFSub %v3float %76 %56
         %78 = OpExtInst %v3float %1 Normalize %77
         %79 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_5
         %80 = OpLoad %v3float %79
         %81 = OpFSub %v3float %80 %56
         %82 = OpExtInst %v3float %1 Normalize %81
         %83 = OpCompositeExtract %float %62 2
         %84 = OpCompositeExtract %float %62 3
         %85 = OpFDiv %float %83 %84
               OpStore %gl_Position %62
               OpStore %out_var_TEXCOORD0 %56
               OpStore %out_var_TEXCOORD1 %71
               OpStore %out_var_TEXCOORD2 %47
               OpStore %out_var_TEXCOORD3 %48
               OpStore %out_var_TEXCOORD4 %74
               OpStore %out_var_TEXCOORD5 %78
               OpStore %out_var_TEXCOORD6 %82
               OpStore %out_var_TEXCOORD7 %85
               OpReturn
               OpFunctionEnd
