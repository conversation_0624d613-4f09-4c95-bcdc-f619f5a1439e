﻿=== HLSL Optimization Log ===
Start time: 08/01/2025 10:23:35

Processing: AdvancedDataStructuresPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: AdvancedDataStructuresVS.hlsl
  Optimization: FAILED (Stage: vertex)
  Error: 

Processing: BasicLightingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: BasicLightingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: DeferredGBufferPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: DeferredGBufferVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: DeferredLightingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: DeferredLightingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: GeometryTessellationPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: GeometryTessellationVS.hlsl
  Optimization: FAILED (Stage: vertex)
  Error: 

Processing: ImageProcessingCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: InstancedRenderingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: InstancedRenderingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: MeshGenerationCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: MeshViewerPS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: MeshViewerVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: ParallelReductionCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: ParticleSimulationCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: ParticleUpdateCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: PbrShadingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: PbrShadingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: PostProcessingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: PostProcessingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: RayTracingTestCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: ShadowMappingPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: ShadowMappingVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: SimplePS.hlsl
  Optimization: SUCCESS (Stage: pixel)

Processing: SimpleVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: StressTestCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: TestShaderPS.hlsl
  Optimization: FAILED (Stage: pixel)
  Error: 

Processing: TestShaderVS.hlsl
  Optimization: SUCCESS (Stage: vertex)

Processing: TestSimpleCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: VolumeTextureCS.hlsl
  Optimization: FAILED (Stage: compute)
  Error: 

Processing: VolumetricRenderingPS.hlsl
