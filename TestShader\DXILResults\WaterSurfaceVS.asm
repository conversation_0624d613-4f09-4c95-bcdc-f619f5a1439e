;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float       
; TEXCOORD                 0   xy          2     NONE   float   xy  
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 6     zw        3     NONE   float     zw
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xyzw        5     NONE   float   xyzw
; TEXCOORD                 5   xyzw        6     NONE   float   xyzw
;
; shader hash: a11ee636babc67f8855735690d50d318
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 3
; SigOutputElements: 8
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 3
; SigOutputVectors[0]: 7
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TEXCOORD                 0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
;
; Buffer Definitions:
;
; cbuffer WaterParams
; {
;
;   struct hostlayout.WaterParams
;   {
;
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:    0
;       column_major float4x4 WorldMatrix;            ; Offset:   64
;       float3 CameraPosition;                        ; Offset:  128
;       float Time;                                   ; Offset:  140
;       float WaveAmplitude;                          ; Offset:  144
;       float WaveFrequency;                          ; Offset:  148
;       float WaveSpeed;                              ; Offset:  152
;       float2 WaveDirection1;                        ; Offset:  160
;       float2 WaveDirection2;                        ; Offset:  168
;       float2 WindDirection;                         ; Offset:  176
;       float WindStrength;                           ; Offset:  184
;   
;   } WaterParams;                                    ; Offset:    0 Size:   188
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; WaterParams                       cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 10, outputs: 28
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2 }
;   output 1 depends on inputs: { 0, 1, 2 }
;   output 2 depends on inputs: { 0, 1, 2 }
;   output 3 depends on inputs: { 0, 1, 2 }
;   output 4 depends on inputs: { 0, 1, 2 }
;   output 5 depends on inputs: { 0, 1, 2 }
;   output 6 depends on inputs: { 0, 1, 2 }
;   output 8 depends on inputs: { 0, 1, 2 }
;   output 9 depends on inputs: { 0, 1, 2 }
;   output 10 depends on inputs: { 0, 1, 2 }
;   output 12 depends on inputs: { 8 }
;   output 13 depends on inputs: { 9 }
;   output 14 depends on inputs: { 0, 1, 2 }
;   output 15 depends on inputs: { 0, 1, 2 }
;   output 16 depends on inputs: { 0, 1, 2 }
;   output 17 depends on inputs: { 0, 1, 2 }
;   output 18 depends on inputs: { 0, 1, 2 }
;   output 20 depends on inputs: { 0, 1, 2 }
;   output 21 depends on inputs: { 0, 1, 2 }
;   output 22 depends on inputs: { 0, 1, 2 }
;   output 23 depends on inputs: { 0, 1, 2 }
;   output 24 depends on inputs: { 0, 1, 2 }
;   output 25 depends on inputs: { 0, 1, 2 }
;   output 26 depends on inputs: { 0, 1, 2 }
;   output 27 depends on inputs: { 0, 1, 2 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%hostlayout.WaterParams = type { [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, float, float, float, <2 x float>, <2 x float>, <2 x float>, float }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %8 = extractvalue %dx.types.CBufRet.f32 %7, 0
  %9 = extractvalue %dx.types.CBufRet.f32 %7, 1
  %10 = extractvalue %dx.types.CBufRet.f32 %7, 2
  %11 = extractvalue %dx.types.CBufRet.f32 %7, 3
  %12 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %13 = extractvalue %dx.types.CBufRet.f32 %12, 0
  %14 = extractvalue %dx.types.CBufRet.f32 %12, 1
  %15 = extractvalue %dx.types.CBufRet.f32 %12, 2
  %16 = extractvalue %dx.types.CBufRet.f32 %12, 3
  %17 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %18 = extractvalue %dx.types.CBufRet.f32 %17, 0
  %19 = extractvalue %dx.types.CBufRet.f32 %17, 1
  %20 = extractvalue %dx.types.CBufRet.f32 %17, 2
  %21 = extractvalue %dx.types.CBufRet.f32 %17, 3
  %22 = fmul fast float %8, %4
  %23 = call float @dx.op.tertiary.f32(i32 46, float %5, float %9, float %22)  ; FMad(a,b,c)
  %24 = call float @dx.op.tertiary.f32(i32 46, float %6, float %10, float %23)  ; FMad(a,b,c)
  %25 = fadd fast float %24, %11
  %26 = fmul fast float %13, %4
  %27 = call float @dx.op.tertiary.f32(i32 46, float %5, float %14, float %26)  ; FMad(a,b,c)
  %28 = call float @dx.op.tertiary.f32(i32 46, float %6, float %15, float %27)  ; FMad(a,b,c)
  %29 = fmul fast float %18, %4
  %30 = call float @dx.op.tertiary.f32(i32 46, float %5, float %19, float %29)  ; FMad(a,b,c)
  %31 = call float @dx.op.tertiary.f32(i32 46, float %6, float %20, float %30)  ; FMad(a,b,c)
  %32 = fadd fast float %31, %21
  %33 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %34 = extractvalue %dx.types.CBufRet.f32 %33, 3
  %35 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %36 = extractvalue %dx.types.CBufRet.f32 %35, 1
  %37 = extractvalue %dx.types.CBufRet.f32 %35, 0
  %38 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %39 = extractvalue %dx.types.CBufRet.f32 %38, 0
  %40 = extractvalue %dx.types.CBufRet.f32 %38, 1
  %41 = call float @dx.op.dot2.f32(i32 54, float %39, float %40, float %39, float %40)  ; Dot2(ax,ay,bx,by)
  %42 = call float @dx.op.unary.f32(i32 25, float %41)  ; Rsqrt(value)
  %43 = fmul fast float %42, %39
  %44 = fmul fast float %42, %40
  %45 = fmul fast float %36, 0x401921FA00000000
  %46 = fdiv fast float 0x3FF8F49CA0000000, %36
  %47 = call float @dx.op.unary.f32(i32 24, float %46)  ; Sqrt(value)
  %48 = call float @dx.op.dot2.f32(i32 54, float %43, float %44, float %25, float %32)  ; Dot2(ax,ay,bx,by)
  %49 = fmul fast float %47, %34
  %50 = fsub fast float %48, %49
  %51 = fmul fast float %50, %45
  %52 = fdiv fast float %37, %45
  %53 = call float @dx.op.unary.f32(i32 13, float %51)  ; Sin(value)
  %54 = fmul fast float %43, %37
  %55 = fmul fast float %54, %43
  %56 = fmul fast float %55, %53
  %57 = call float @dx.op.unary.f32(i32 12, float %51)  ; Cos(value)
  %58 = fmul fast float %57, %54
  %59 = fmul fast float %53, %44
  %60 = fmul fast float %59, %54
  %61 = fmul fast float %44, %37
  %62 = fmul fast float %61, %57
  %63 = fmul fast float %59, %37
  %64 = fmul fast float %63, %44
  %65 = fmul fast float %53, %52
  %66 = fmul fast float %65, %43
  %67 = fmul fast float %57, %52
  %68 = fmul fast float %59, %52
  %69 = fmul fast float %37, 5.000000e-01
  %70 = extractvalue %dx.types.CBufRet.f32 %38, 2
  %71 = extractvalue %dx.types.CBufRet.f32 %38, 3
  %72 = call float @dx.op.dot2.f32(i32 54, float %70, float %71, float %70, float %71)  ; Dot2(ax,ay,bx,by)
  %73 = call float @dx.op.unary.f32(i32 25, float %72)  ; Rsqrt(value)
  %74 = fmul fast float %73, %70
  %75 = fmul fast float %73, %71
  %76 = fmul fast float %36, 0x4022D97B80000000
  %77 = fdiv fast float 0x3FF0A31320000000, %36
  %78 = call float @dx.op.unary.f32(i32 24, float %77)  ; Sqrt(value)
  %79 = call float @dx.op.dot2.f32(i32 54, float %74, float %75, float %25, float %32)  ; Dot2(ax,ay,bx,by)
  %80 = fmul fast float %78, %34
  %81 = fsub fast float %79, %80
  %82 = fmul fast float %81, %76
  %83 = fdiv fast float %69, %76
  %84 = call float @dx.op.unary.f32(i32 13, float %82)  ; Sin(value)
  %85 = fmul fast float %84, %74
  %86 = fsub fast float -0.000000e+00, %85
  %87 = fmul fast float %69, %86
  %88 = fmul fast float %87, %74
  %89 = call float @dx.op.unary.f32(i32 12, float %82)  ; Cos(value)
  %90 = fmul fast float %74, %69
  %91 = fmul fast float %90, %89
  %92 = fmul fast float %75, %69
  %93 = fmul fast float %92, %86
  %94 = fmul fast float %89, %92
  %95 = fmul fast float %92, %75
  %96 = fmul fast float %95, %84
  %97 = fsub fast float -0.000000e+00, %96
  %98 = fmul fast float %85, %83
  %99 = fmul fast float %89, %83
  %100 = fmul fast float %84, %83
  %101 = fmul fast float %100, %75
  %102 = fmul fast float %34, 2.000000e+00
  %103 = fadd fast float %32, %25
  %104 = fmul fast float %103, 0x3FB99999A0000000
  %105 = fadd fast float %104, %102
  %106 = call float @dx.op.unary.f32(i32 13, float %105)  ; Sin(value)
  %107 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %108 = extractvalue %dx.types.CBufRet.f32 %107, 2
  %109 = fmul fast float %108, %106
  %110 = extractvalue %dx.types.CBufRet.f32 %107, 0
  %111 = extractvalue %dx.types.CBufRet.f32 %107, 1
  %112 = fmul fast float %109, %110
  %113 = fmul fast float %109, %111
  %114 = fmul fast float %112, 0x3FB99999A0000000
  %115 = fmul fast float %113, 0x3FB99999A0000000
  %116 = fadd fast float %28, %16
  %117 = fadd fast float %116, %67
  %118 = fadd fast float %117, %99
  %119 = fadd fast float %66, %25
  %120 = fadd fast float %119, %98
  %121 = fadd fast float %120, %114
  %122 = fadd fast float %68, %32
  %123 = fadd fast float %122, %101
  %124 = fadd fast float %123, %115
  %125 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %126 = extractvalue %dx.types.CBufRet.f32 %125, 0
  %127 = extractvalue %dx.types.CBufRet.f32 %125, 1
  %128 = extractvalue %dx.types.CBufRet.f32 %125, 2
  %129 = extractvalue %dx.types.CBufRet.f32 %125, 3
  %130 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %131 = extractvalue %dx.types.CBufRet.f32 %130, 0
  %132 = extractvalue %dx.types.CBufRet.f32 %130, 1
  %133 = extractvalue %dx.types.CBufRet.f32 %130, 2
  %134 = extractvalue %dx.types.CBufRet.f32 %130, 3
  %135 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %136 = extractvalue %dx.types.CBufRet.f32 %135, 0
  %137 = extractvalue %dx.types.CBufRet.f32 %135, 1
  %138 = extractvalue %dx.types.CBufRet.f32 %135, 2
  %139 = extractvalue %dx.types.CBufRet.f32 %135, 3
  %140 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %141 = extractvalue %dx.types.CBufRet.f32 %140, 0
  %142 = extractvalue %dx.types.CBufRet.f32 %140, 1
  %143 = extractvalue %dx.types.CBufRet.f32 %140, 2
  %144 = extractvalue %dx.types.CBufRet.f32 %140, 3
  %145 = fmul fast float %121, %126
  %146 = call float @dx.op.tertiary.f32(i32 46, float %118, float %127, float %145)  ; FMad(a,b,c)
  %147 = call float @dx.op.tertiary.f32(i32 46, float %124, float %128, float %146)  ; FMad(a,b,c)
  %148 = fadd fast float %147, %129
  %149 = fmul fast float %121, %131
  %150 = call float @dx.op.tertiary.f32(i32 46, float %118, float %132, float %149)  ; FMad(a,b,c)
  %151 = call float @dx.op.tertiary.f32(i32 46, float %124, float %133, float %150)  ; FMad(a,b,c)
  %152 = fadd fast float %151, %134
  %153 = fmul fast float %121, %136
  %154 = call float @dx.op.tertiary.f32(i32 46, float %118, float %137, float %153)  ; FMad(a,b,c)
  %155 = call float @dx.op.tertiary.f32(i32 46, float %124, float %138, float %154)  ; FMad(a,b,c)
  %156 = fadd fast float %155, %139
  %157 = fmul fast float %141, %121
  %158 = call float @dx.op.tertiary.f32(i32 46, float %118, float %142, float %157)  ; FMad(a,b,c)
  %159 = call float @dx.op.tertiary.f32(i32 46, float %124, float %143, float %158)  ; FMad(a,b,c)
  %160 = fadd fast float %159, %144
  %161 = fsub fast float %88, %56
  %162 = fadd fast float %91, %58
  %163 = fsub fast float %93, %60
  %164 = fadd fast float %161, 1.000000e+00
  %165 = call float @dx.op.dot3.f32(i32 55, float %164, float %162, float %163, float %164, float %162, float %163)  ; Dot3(ax,ay,az,bx,by,bz)
  %166 = call float @dx.op.unary.f32(i32 25, float %165)  ; Rsqrt(value)
  %167 = fmul fast float %166, %164
  %168 = fmul fast float %166, %162
  %169 = fmul fast float %166, %163
  %170 = fadd fast float %94, %62
  %171 = fsub fast float %97, %64
  %172 = fadd fast float %171, 1.000000e+00
  %173 = call float @dx.op.dot3.f32(i32 55, float %163, float %170, float %172, float %163, float %170, float %172)  ; Dot3(ax,ay,az,bx,by,bz)
  %174 = call float @dx.op.unary.f32(i32 25, float %173)  ; Rsqrt(value)
  %175 = fmul fast float %174, %163
  %176 = fmul fast float %174, %170
  %177 = fmul fast float %174, %172
  %178 = fmul fast float %177, %168
  %179 = fmul fast float %176, %169
  %180 = fsub fast float %178, %179
  %181 = fmul fast float %175, %169
  %182 = fmul fast float %177, %167
  %183 = fsub fast float %181, %182
  %184 = fmul fast float %176, %167
  %185 = fmul fast float %175, %168
  %186 = fsub fast float %184, %185
  %187 = call float @dx.op.dot3.f32(i32 55, float %180, float %183, float %186, float %180, float %183, float %186)  ; Dot3(ax,ay,az,bx,by,bz)
  %188 = call float @dx.op.unary.f32(i32 25, float %187)  ; Rsqrt(value)
  %189 = fmul fast float %180, %188
  %190 = fmul fast float %183, %188
  %191 = fmul fast float %186, %188
  %192 = fmul fast float %34, 0x3FB99999A0000000
  %193 = fmul fast float %112, 0x3FA99999A0000000
  %194 = fadd fast float %193, %192
  %195 = fadd fast float %113, %34
  %196 = fmul fast float %195, 0x3FA99999A0000000
  %197 = extractvalue %dx.types.CBufRet.f32 %33, 0
  %198 = extractvalue %dx.types.CBufRet.f32 %33, 1
  %199 = extractvalue %dx.types.CBufRet.f32 %33, 2
  %200 = fsub fast float %197, %121
  %201 = fsub fast float %198, %118
  %202 = fsub fast float %199, %124
  %203 = call float @dx.op.dot3.f32(i32 55, float %200, float %201, float %202, float %200, float %201, float %202)  ; Dot3(ax,ay,az,bx,by,bz)
  %204 = call float @dx.op.unary.f32(i32 25, float %203)  ; Rsqrt(value)
  %205 = fmul fast float %204, %200
  %206 = fmul fast float %204, %201
  %207 = fmul fast float %204, %202
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %148)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %152)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %156)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %160)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %121)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %118)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %124)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %189)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %190)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %191)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %2)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %3)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %205)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %206)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 2, float %207)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %148)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %152)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 2, float %156)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 3, float %160)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %148)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 1, float %152)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 2, float %156)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 3, float %160)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %194)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 1, float %196)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot2.f32(i32, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %hostlayout.WaterParams* undef, !"", i32 0, i32 0, i32 1, i32 188, null}
!7 = !{[12 x i32] [i32 10, i32 28, i32 267896703, i32 267896703, i32 267896703, i32 0, i32 0, i32 0, i32 0, i32 0, i32 4096, i32 8192]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !17, null}
!10 = !{!11, !14, !15}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"NORMAL", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 1, i8 0, null}
!15 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 2, i8 0, !16}
!16 = !{i32 3, i32 3}
!17 = !{!18, !20, !21, !23, !25, !27, !29, !31}
!18 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !19}
!19 = !{i32 3, i32 15}
!20 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 1, i8 0, !13}
!21 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !22, i8 2, i32 1, i8 3, i32 2, i8 0, !13}
!22 = !{i32 1}
!23 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 2, i32 3, i8 0, !16}
!24 = !{i32 2}
!25 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 4, i8 0, !13}
!26 = !{i32 3}
!27 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 4, i32 5, i8 0, !19}
!28 = !{i32 4}
!29 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !30, i8 2, i32 1, i8 4, i32 6, i8 0, !19}
!30 = !{i32 5}
!31 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !32, i8 2, i32 1, i8 2, i32 3, i8 2, !16}
!32 = !{i32 6}
