; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 492
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %gl_FragCoord %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %in_var_TEXCOORD8 %in_var_TEXCOORD9 %in_var_TEXCOORD10 %in_var_TEXCOORD11 %out_var_SV_Target0 %out_var_SV_Target1 %out_var_SV_Target2 %out_var_SV_Target3
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "DeltaTime"
               OpMemberName %type_PerFrame 6 "FrameCount"
               OpMemberName %type_PerFrame 7 "ScreenResolution"
               OpName %PerFrame "PerFrame"
               OpName %type_LightingParams "type.LightingParams"
               OpMemberName %type_LightingParams 0 "NumLights"
               OpMemberName %type_LightingParams 1 "AmbientIntensity"
               OpMemberName %type_LightingParams 2 "AmbientColor"
               OpMemberName %type_LightingParams 3 "ShadowBias"
               OpMemberName %type_LightingParams 4 "ShadowNormalBias"
               OpMemberName %type_LightingParams 5 "ShadowMapSize"
               OpMemberName %type_LightingParams 6 "PCFRadius"
               OpName %LightingParams "LightingParams"
               OpName %type_StructuredBuffer_MaterialData "type.StructuredBuffer.MaterialData"
               OpName %MaterialData "MaterialData"
               OpMemberName %MaterialData 0 "Albedo"
               OpMemberName %MaterialData 1 "Metallic"
               OpMemberName %MaterialData 2 "Roughness"
               OpMemberName %MaterialData 3 "AO"
               OpMemberName %MaterialData 4 "Emission"
               OpMemberName %MaterialData 5 "TilingOffset"
               OpMemberName %MaterialData 6 "TextureFlags"
               OpMemberName %MaterialData 7 "_padding"
               OpName %Materials "Materials"
               OpName %type_StructuredBuffer_LightData "type.StructuredBuffer.LightData"
               OpName %LightData "LightData"
               OpMemberName %LightData 0 "Position"
               OpMemberName %LightData 1 "Range"
               OpMemberName %LightData 2 "Direction"
               OpMemberName %LightData 3 "SpotAngle"
               OpMemberName %LightData 4 "Color"
               OpMemberName %LightData 5 "Intensity"
               OpMemberName %LightData 6 "Type"
               OpMemberName %LightData 7 "_padding"
               OpName %Lights "Lights"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %AlbedoTextures "AlbedoTextures"
               OpName %NormalTextures "NormalTextures"
               OpName %MetallicRoughnessTextures "MetallicRoughnessTextures"
               OpName %AOTextures "AOTextures"
               OpName %EmissionTextures "EmissionTextures"
               OpName %type_2d_image "type.2d.image"
               OpName %LightmapTexture "LightmapTexture"
               OpName %ShadowMap "ShadowMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %ShadowSampler "ShadowSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %in_var_TEXCOORD8 "in.var.TEXCOORD8"
               OpName %in_var_TEXCOORD9 "in.var.TEXCOORD9"
               OpName %in_var_TEXCOORD10 "in.var.TEXCOORD10"
               OpName %in_var_TEXCOORD11 "in.var.TEXCOORD11"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %out_var_SV_Target1 "out.var.SV_Target1"
               OpName %out_var_SV_Target2 "out.var.SV_Target2"
               OpName %out_var_SV_Target3 "out.var.SV_Target3"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %gl_FragCoord BuiltIn FragCoord
               OpDecorate %in_var_TEXCOORD10 Flat
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %in_var_TEXCOORD7 Location 7
               OpDecorate %in_var_TEXCOORD8 Location 8
               OpDecorate %in_var_TEXCOORD9 Location 9
               OpDecorate %in_var_TEXCOORD10 Location 10
               OpDecorate %in_var_TEXCOORD11 Location 11
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %out_var_SV_Target1 Location 1
               OpDecorate %out_var_SV_Target2 Location 2
               OpDecorate %out_var_SV_Target3 Location 3
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %LightingParams DescriptorSet 0
               OpDecorate %LightingParams Binding 1
               OpDecorate %Materials DescriptorSet 0
               OpDecorate %Materials Binding 0
               OpDecorate %Lights DescriptorSet 0
               OpDecorate %Lights Binding 1
               OpDecorate %AlbedoTextures DescriptorSet 0
               OpDecorate %AlbedoTextures Binding 2
               OpDecorate %NormalTextures DescriptorSet 0
               OpDecorate %NormalTextures Binding 3
               OpDecorate %MetallicRoughnessTextures DescriptorSet 0
               OpDecorate %MetallicRoughnessTextures Binding 4
               OpDecorate %AOTextures DescriptorSet 0
               OpDecorate %AOTextures Binding 5
               OpDecorate %EmissionTextures DescriptorSet 0
               OpDecorate %EmissionTextures Binding 6
               OpDecorate %LightmapTexture DescriptorSet 0
               OpDecorate %LightmapTexture Binding 7
               OpDecorate %ShadowMap DescriptorSet 0
               OpDecorate %ShadowMap Binding 9
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %ShadowSampler DescriptorSet 0
               OpDecorate %ShadowSampler Binding 2
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 212
               OpMemberDecorate %type_PerFrame 7 Offset 216
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_LightingParams 0 Offset 0
               OpMemberDecorate %type_LightingParams 1 Offset 4
               OpMemberDecorate %type_LightingParams 2 Offset 16
               OpMemberDecorate %type_LightingParams 3 Offset 28
               OpMemberDecorate %type_LightingParams 4 Offset 32
               OpMemberDecorate %type_LightingParams 5 Offset 36
               OpMemberDecorate %type_LightingParams 6 Offset 44
               OpDecorate %type_LightingParams Block
               OpMemberDecorate %MaterialData 0 Offset 0
               OpMemberDecorate %MaterialData 1 Offset 16
               OpMemberDecorate %MaterialData 2 Offset 20
               OpMemberDecorate %MaterialData 3 Offset 24
               OpMemberDecorate %MaterialData 4 Offset 28
               OpMemberDecorate %MaterialData 5 Offset 32
               OpMemberDecorate %MaterialData 6 Offset 48
               OpMemberDecorate %MaterialData 7 Offset 52
               OpDecorate %_runtimearr_MaterialData ArrayStride 64
               OpMemberDecorate %type_StructuredBuffer_MaterialData 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_MaterialData 0 NonWritable
               OpDecorate %type_StructuredBuffer_MaterialData BufferBlock
               OpMemberDecorate %LightData 0 Offset 0
               OpMemberDecorate %LightData 1 Offset 12
               OpMemberDecorate %LightData 2 Offset 16
               OpMemberDecorate %LightData 3 Offset 28
               OpMemberDecorate %LightData 4 Offset 32
               OpMemberDecorate %LightData 5 Offset 44
               OpMemberDecorate %LightData 6 Offset 48
               OpMemberDecorate %LightData 7 Offset 52
               OpDecorate %_runtimearr_LightData ArrayStride 64
               OpMemberDecorate %type_StructuredBuffer_LightData 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_LightData 0 NonWritable
               OpDecorate %type_StructuredBuffer_LightData BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_5 = OpConstant %int 5
      %int_6 = OpConstant %int 6
       %uint = OpTypeInt 32 0
     %uint_1 = OpConstant %uint 1
     %uint_0 = OpConstant %uint 0
      %int_2 = OpConstant %int 2
     %uint_2 = OpConstant %uint 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_1 = OpConstant %int 1
     %uint_4 = OpConstant %uint 4
     %uint_8 = OpConstant %uint 8
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %63 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %uint_16 = OpConstant %uint 16
  %float_0_5 = OpConstant %float 0.5
    %v2float = OpTypeVector %float 2
         %67 = OpConstantComposite %v2float %float_0_5 %float_0_5
         %68 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
    %float_1 = OpConstant %float 1
    %float_2 = OpConstant %float 2
         %71 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%float_0_0399999991 = OpConstant %float 0.0399999991
         %73 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
%float_0_0900000036 = OpConstant %float 0.0900000036
%float_0_0320000015 = OpConstant %float 0.0320000015
%float_1_20000005 = OpConstant %float 1.20000005
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_3_14159274 = OpConstant %float 3.14159274
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
         %82 = OpConstantComposite %v2float %float_1 %float_1
     %int_n1 = OpConstant %int -1
    %float_5 = OpConstant %float 5
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %float %uint %v2float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_LightingParams = OpTypeStruct %uint %float %v3float %float %float %v2float %float
%_ptr_Uniform_type_LightingParams = OpTypePointer Uniform %type_LightingParams
%MaterialData = OpTypeStruct %v4float %float %float %float %float %v4float %uint %float
%_runtimearr_MaterialData = OpTypeRuntimeArray %MaterialData
%type_StructuredBuffer_MaterialData = OpTypeStruct %_runtimearr_MaterialData
%_ptr_Uniform_type_StructuredBuffer_MaterialData = OpTypePointer Uniform %type_StructuredBuffer_MaterialData
  %LightData = OpTypeStruct %v3float %float %v3float %float %v3float %float %uint %v3float
%_runtimearr_LightData = OpTypeRuntimeArray %LightData
%type_StructuredBuffer_LightData = OpTypeStruct %_runtimearr_LightData
%_ptr_Uniform_type_StructuredBuffer_LightData = OpTypePointer Uniform %type_StructuredBuffer_LightData
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
        %101 = OpTypeFunction %void
%_ptr_Uniform_MaterialData = OpTypePointer Uniform %MaterialData
%type_sampled_image = OpTypeSampledImage %type_2d_image_array
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Uniform_LightData = OpTypePointer Uniform %LightData
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%LightingParams = OpVariable %_ptr_Uniform_type_LightingParams Uniform
  %Materials = OpVariable %_ptr_Uniform_type_StructuredBuffer_MaterialData Uniform
     %Lights = OpVariable %_ptr_Uniform_type_StructuredBuffer_LightData Uniform
%AlbedoTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NormalTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%MetallicRoughnessTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
 %AOTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%EmissionTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%LightmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %ShadowMap = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%ShadowSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_FragCoord = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD8 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD9 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD10 = OpVariable %_ptr_Input_uint Input
%in_var_TEXCOORD11 = OpVariable %_ptr_Input_float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target1 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target2 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target3 = OpVariable %_ptr_Output_v4float Output
        %110 = OpUndef %v3float
%float_0_111111112 = OpConstant %float 0.111111112
%float_0_125 = OpConstant %float 0.125
%float_0_318309873 = OpConstant %float 0.318309873
        %114 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
     %uint_3 = OpConstant %uint 3
     %uint_5 = OpConstant %uint 5
     %uint_6 = OpConstant %uint 6
       %main = OpFunction %void None %101
        %119 = OpLabel
        %120 = OpLoad %v4float %gl_FragCoord
        %121 = OpLoad %v3float %in_var_TEXCOORD0
        %122 = OpLoad %v3float %in_var_TEXCOORD1
        %123 = OpLoad %v3float %in_var_TEXCOORD2
        %124 = OpLoad %v3float %in_var_TEXCOORD3
        %125 = OpLoad %v2float %in_var_TEXCOORD4
        %126 = OpLoad %v2float %in_var_TEXCOORD5
        %127 = OpLoad %v4float %in_var_TEXCOORD6
        %128 = OpLoad %v3float %in_var_TEXCOORD7
        %129 = OpLoad %v4float %in_var_TEXCOORD8
        %130 = OpLoad %v4float %in_var_TEXCOORD9
        %131 = OpLoad %uint %in_var_TEXCOORD10
        %132 = OpLoad %float %in_var_TEXCOORD11
        %133 = OpAccessChain %_ptr_Uniform_MaterialData %Materials %int_0 %131
        %134 = OpAccessChain %_ptr_Uniform_v4float %133 %uint_0
        %135 = OpLoad %v4float %134
        %136 = OpAccessChain %_ptr_Uniform_float %133 %uint_1
        %137 = OpLoad %float %136
        %138 = OpAccessChain %_ptr_Uniform_float %133 %uint_2
        %139 = OpLoad %float %138
        %140 = OpAccessChain %_ptr_Uniform_float %133 %uint_3
        %141 = OpLoad %float %140
        %142 = OpAccessChain %_ptr_Uniform_float %133 %uint_4
        %143 = OpLoad %float %142
        %144 = OpAccessChain %_ptr_Uniform_v4float %133 %uint_5
        %145 = OpLoad %v4float %144
        %146 = OpAccessChain %_ptr_Uniform_uint %133 %uint_6
        %147 = OpLoad %uint %146
        %148 = OpVectorShuffle %v2float %145 %145 0 1
        %149 = OpFMul %v2float %125 %148
        %150 = OpVectorShuffle %v2float %145 %145 2 3
        %151 = OpFAdd %v2float %149 %150
        %152 = OpVectorShuffle %v3float %135 %135 0 1 2
        %153 = OpBitwiseAnd %uint %147 %uint_1
        %154 = OpINotEqual %bool %153 %uint_0
               OpSelectionMerge %155 None
               OpBranchConditional %154 %156 %155
        %156 = OpLabel
        %157 = OpLoad %type_2d_image_array %AlbedoTextures
        %158 = OpLoad %type_sampler %LinearSampler
        %159 = OpConvertUToF %float %131
        %160 = OpCompositeExtract %float %151 0
        %161 = OpCompositeExtract %float %151 1
        %162 = OpCompositeConstruct %v3float %160 %161 %159
        %163 = OpSampledImage %type_sampled_image %157 %158
        %164 = OpImageSampleImplicitLod %v4float %163 %162 None
        %165 = OpVectorShuffle %v3float %164 %164 0 1 2
        %166 = OpFMul %v3float %152 %165
               OpBranch %155
        %155 = OpLabel
        %167 = OpPhi %v3float %152 %119 %166 %156
        %168 = OpVectorShuffle %v3float %127 %127 0 1 2
        %169 = OpFMul %v3float %167 %168
        %170 = OpBitwiseAnd %uint %147 %uint_2
        %171 = OpINotEqual %bool %170 %uint_0
               OpSelectionMerge %172 None
               OpBranchConditional %171 %173 %172
        %173 = OpLabel
        %174 = OpLoad %type_2d_image_array %NormalTextures
        %175 = OpLoad %type_sampler %LinearSampler
        %176 = OpConvertUToF %float %131
        %177 = OpCompositeExtract %float %151 0
        %178 = OpCompositeExtract %float %151 1
        %179 = OpCompositeConstruct %v3float %177 %178 %176
        %180 = OpSampledImage %type_sampled_image %174 %175
        %181 = OpImageSampleImplicitLod %v4float %180 %179 None
        %182 = OpVectorShuffle %v3float %181 %181 0 1 2
        %183 = OpVectorTimesScalar %v3float %182 %float_2
        %184 = OpFSub %v3float %183 %71
        %185 = OpCompositeConstruct %mat3v3float %123 %124 %122
        %186 = OpMatrixTimesVector %v3float %185 %184
        %187 = OpExtInst %v3float %1 Normalize %186
               OpBranch %172
        %172 = OpLabel
        %188 = OpPhi %v3float %122 %155 %187 %173
        %189 = OpBitwiseAnd %uint %147 %uint_4
        %190 = OpINotEqual %bool %189 %uint_0
               OpSelectionMerge %191 None
               OpBranchConditional %190 %192 %191
        %192 = OpLabel
        %193 = OpLoad %type_2d_image_array %MetallicRoughnessTextures
        %194 = OpLoad %type_sampler %LinearSampler
        %195 = OpConvertUToF %float %131
        %196 = OpCompositeExtract %float %151 0
        %197 = OpCompositeExtract %float %151 1
        %198 = OpCompositeConstruct %v3float %196 %197 %195
        %199 = OpSampledImage %type_sampled_image %193 %194
        %200 = OpImageSampleImplicitLod %v4float %199 %198 None
        %201 = OpCompositeExtract %float %200 0
        %202 = OpFMul %float %137 %201
        %203 = OpCompositeExtract %float %200 1
        %204 = OpFMul %float %139 %203
               OpBranch %191
        %191 = OpLabel
        %205 = OpPhi %float %139 %172 %204 %192
        %206 = OpPhi %float %137 %172 %202 %192
        %207 = OpBitwiseAnd %uint %147 %uint_8
        %208 = OpINotEqual %bool %207 %uint_0
               OpSelectionMerge %209 None
               OpBranchConditional %208 %210 %209
        %210 = OpLabel
        %211 = OpLoad %type_2d_image_array %AOTextures
        %212 = OpLoad %type_sampler %LinearSampler
        %213 = OpConvertUToF %float %131
        %214 = OpCompositeExtract %float %151 0
        %215 = OpCompositeExtract %float %151 1
        %216 = OpCompositeConstruct %v3float %214 %215 %213
        %217 = OpSampledImage %type_sampled_image %211 %212
        %218 = OpImageSampleImplicitLod %v4float %217 %216 None
        %219 = OpCompositeExtract %float %218 0
        %220 = OpFMul %float %141 %219
               OpBranch %209
        %209 = OpLabel
        %221 = OpPhi %float %141 %191 %220 %210
        %222 = OpBitwiseAnd %uint %147 %uint_16
        %223 = OpINotEqual %bool %222 %uint_0
               OpSelectionMerge %224 None
               OpBranchConditional %223 %225 %224
        %225 = OpLabel
        %226 = OpLoad %type_2d_image_array %EmissionTextures
        %227 = OpLoad %type_sampler %LinearSampler
        %228 = OpConvertUToF %float %131
        %229 = OpCompositeExtract %float %151 0
        %230 = OpCompositeExtract %float %151 1
        %231 = OpCompositeConstruct %v3float %229 %230 %228
        %232 = OpSampledImage %type_sampled_image %226 %227
        %233 = OpImageSampleImplicitLod %v4float %232 %231 None
        %234 = OpVectorShuffle %v3float %233 %233 0 1 2
        %235 = OpVectorTimesScalar %v3float %234 %143
               OpBranch %224
        %224 = OpLabel
        %236 = OpPhi %v3float %63 %209 %235 %225
        %237 = OpLoad %type_2d_image %LightmapTexture
        %238 = OpLoad %type_sampler %LinearSampler
        %239 = OpSampledImage %type_sampled_image_0 %237 %238
        %240 = OpImageSampleImplicitLod %v4float %239 %126 None
        %241 = OpVectorShuffle %v3float %240 %240 0 1 2
        %242 = OpExtInst %v3float %1 Normalize %128
        %243 = OpCompositeConstruct %v3float %206 %206 %206
        %244 = OpExtInst %v3float %1 FMix %73 %169 %243
               OpBranch %245
        %245 = OpLabel
        %246 = OpPhi %v3float %110 %224 %247 %248
        %249 = OpPhi %v3float %63 %224 %250 %248
        %251 = OpPhi %uint %uint_0 %224 %252 %248
        %253 = OpAccessChain %_ptr_Uniform_uint %LightingParams %int_0
        %254 = OpLoad %uint %253
        %255 = OpULessThan %bool %251 %254
               OpLoopMerge %256 %248 None
               OpBranchConditional %255 %257 %256
        %257 = OpLabel
        %258 = OpAccessChain %_ptr_Uniform_LightData %Lights %int_0 %251
        %259 = OpAccessChain %_ptr_Uniform_v3float %258 %uint_0
        %260 = OpLoad %v3float %259
        %261 = OpAccessChain %_ptr_Uniform_v3float %258 %uint_2
        %262 = OpLoad %v3float %261
        %263 = OpAccessChain %_ptr_Uniform_float %258 %uint_3
        %264 = OpLoad %float %263
        %265 = OpAccessChain %_ptr_Uniform_v3float %258 %uint_4
        %266 = OpLoad %v3float %265
        %267 = OpAccessChain %_ptr_Uniform_float %258 %uint_5
        %268 = OpLoad %float %267
        %269 = OpAccessChain %_ptr_Uniform_uint %258 %uint_6
        %270 = OpLoad %uint %269
        %271 = OpIEqual %bool %270 %uint_0
               OpSelectionMerge %272 None
               OpBranchConditional %271 %273 %274
        %273 = OpLabel
        %275 = OpFNegate %v3float %262
        %276 = OpExtInst %v3float %1 Normalize %275
               OpBranch %272
        %274 = OpLabel
        %277 = OpIEqual %bool %270 %uint_1
               OpSelectionMerge %278 None
               OpBranchConditional %277 %279 %280
        %279 = OpLabel
        %281 = OpFSub %v3float %260 %121
        %282 = OpExtInst %v3float %1 Normalize %281
        %283 = OpExtInst %float %1 Length %281
        %284 = OpFMul %float %float_0_0900000036 %283
        %285 = OpFAdd %float %float_1 %284
        %286 = OpFMul %float %float_0_0320000015 %283
        %287 = OpFMul %float %286 %283
        %288 = OpFAdd %float %285 %287
        %289 = OpFDiv %float %float_1 %288
               OpBranch %278
        %280 = OpLabel
        %290 = OpIEqual %bool %270 %uint_2
               OpSelectionMerge %291 None
               OpBranchConditional %290 %292 %291
        %292 = OpLabel
        %293 = OpFSub %v3float %260 %121
        %294 = OpExtInst %v3float %1 Normalize %293
        %295 = OpExtInst %float %1 Length %293
        %296 = OpFNegate %v3float %262
        %297 = OpExtInst %v3float %1 Normalize %296
        %298 = OpDot %float %294 %297
        %299 = OpExtInst %float %1 Cos %264
        %300 = OpFMul %float %264 %float_1_20000005
        %301 = OpExtInst %float %1 Cos %300
        %302 = OpFSub %float %299 %301
        %303 = OpFSub %float %298 %301
        %304 = OpFDiv %float %303 %302
        %305 = OpExtInst %float %1 FClamp %304 %float_0 %float_1
        %306 = OpFMul %float %float_0_0900000036 %295
        %307 = OpFAdd %float %float_1 %306
        %308 = OpFMul %float %float_0_0320000015 %295
        %309 = OpFMul %float %308 %295
        %310 = OpFAdd %float %307 %309
        %311 = OpFDiv %float %305 %310
               OpBranch %291
        %291 = OpLabel
        %312 = OpPhi %float %float_1 %280 %311 %292
        %313 = OpPhi %v3float %246 %280 %294 %292
               OpBranch %278
        %278 = OpLabel
        %314 = OpPhi %float %289 %279 %312 %291
        %315 = OpPhi %v3float %282 %279 %313 %291
               OpBranch %272
        %272 = OpLabel
        %316 = OpPhi %float %float_1 %273 %314 %278
        %247 = OpPhi %v3float %276 %273 %315 %278
        %317 = OpFAdd %v3float %242 %247
        %318 = OpExtInst %v3float %1 Normalize %317
        %319 = OpVectorTimesScalar %v3float %266 %268
        %320 = OpVectorTimesScalar %v3float %319 %316
               OpSelectionMerge %321 None
               OpSwitch %uint_0 %322
        %322 = OpLabel
        %323 = OpCompositeExtract %float %121 0
        %324 = OpCompositeExtract %float %121 1
        %325 = OpCompositeExtract %float %121 2
        %326 = OpCompositeConstruct %v4float %323 %324 %325 %float_1
        %327 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
        %328 = OpLoad %mat4v4float %327
        %329 = OpMatrixTimesVector %v4float %328 %326
        %330 = OpVectorShuffle %v3float %329 %329 0 1 2
        %331 = OpCompositeExtract %float %329 3
        %332 = OpCompositeConstruct %v3float %331 %331 %331
        %333 = OpFDiv %v3float %330 %332
        %334 = OpVectorTimesScalar %v3float %333 %float_0_5
        %335 = OpFAdd %v3float %334 %68
        %336 = OpCompositeExtract %float %335 0
        %337 = OpFOrdLessThan %bool %336 %float_0
        %338 = OpLogicalNot %bool %337
               OpSelectionMerge %339 None
               OpBranchConditional %338 %340 %339
        %340 = OpLabel
        %341 = OpFOrdGreaterThan %bool %336 %float_1
               OpBranch %339
        %339 = OpLabel
        %342 = OpPhi %bool %true %322 %341 %340
        %343 = OpLogicalNot %bool %342
               OpSelectionMerge %344 None
               OpBranchConditional %343 %345 %344
        %345 = OpLabel
        %346 = OpCompositeExtract %float %335 1
        %347 = OpFOrdLessThan %bool %346 %float_0
               OpBranch %344
        %344 = OpLabel
        %348 = OpPhi %bool %true %339 %347 %345
        %349 = OpLogicalNot %bool %348
               OpSelectionMerge %350 None
               OpBranchConditional %349 %351 %350
        %351 = OpLabel
        %352 = OpCompositeExtract %float %335 1
        %353 = OpFOrdGreaterThan %bool %352 %float_1
               OpBranch %350
        %350 = OpLabel
        %354 = OpPhi %bool %true %344 %353 %351
               OpSelectionMerge %355 None
               OpBranchConditional %354 %356 %355
        %356 = OpLabel
               OpBranch %321
        %355 = OpLabel
        %357 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_4
        %358 = OpLoad %float %357
        %359 = OpDot %float %188 %247
        %360 = OpFSub %float %float_1 %359
        %361 = OpFMul %float %358 %360
        %362 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_3
        %363 = OpLoad %float %362
        %364 = OpExtInst %float %1 NMax %361 %363
        %365 = OpCompositeExtract %float %335 2
        %366 = OpFSub %float %365 %364
        %367 = OpAccessChain %_ptr_Uniform_v2float %LightingParams %int_5
        %368 = OpLoad %v2float %367
        %369 = OpFDiv %v2float %82 %368
               OpBranch %370
        %370 = OpLabel
        %371 = OpPhi %float %float_0 %355 %372 %373
        %374 = OpPhi %int %int_n1 %355 %375 %373
        %376 = OpSLessThanEqual %bool %374 %int_1
               OpLoopMerge %377 %373 None
               OpBranchConditional %376 %378 %377
        %378 = OpLabel
               OpBranch %379
        %379 = OpLabel
        %372 = OpPhi %float %371 %378 %380 %381
        %382 = OpPhi %int %int_n1 %378 %383 %381
        %384 = OpSLessThanEqual %bool %382 %int_1
               OpLoopMerge %385 %381 None
               OpBranchConditional %384 %381 %385
        %381 = OpLabel
        %386 = OpConvertSToF %float %374
        %387 = OpConvertSToF %float %382
        %388 = OpCompositeConstruct %v2float %386 %387
        %389 = OpFMul %v2float %388 %369
        %390 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_6
        %391 = OpLoad %float %390
        %392 = OpVectorTimesScalar %v2float %389 %391
        %393 = OpLoad %type_2d_image %ShadowMap
        %394 = OpLoad %type_sampler %ShadowSampler
        %395 = OpVectorShuffle %v2float %335 %335 0 1
        %396 = OpFAdd %v2float %395 %392
        %397 = OpSampledImage %type_sampled_image_0 %393 %394
        %398 = OpImageSampleDrefExplicitLod %float %397 %396 %366 Lod %float_0
        %380 = OpFAdd %float %372 %398
        %383 = OpIAdd %int %382 %int_1
               OpBranch %379
        %385 = OpLabel
               OpBranch %373
        %373 = OpLabel
        %375 = OpIAdd %int %374 %int_1
               OpBranch %370
        %377 = OpLabel
        %399 = OpFMul %float %371 %float_0_111111112
               OpBranch %321
        %321 = OpLabel
        %400 = OpPhi %float %float_1 %356 %399 %377
        %401 = OpVectorTimesScalar %v3float %320 %400
        %402 = OpFMul %float %205 %205
        %403 = OpFMul %float %402 %402
        %404 = OpDot %float %188 %318
        %405 = OpExtInst %float %1 NMax %404 %float_0
        %406 = OpFMul %float %405 %405
        %407 = OpFSub %float %403 %float_1
        %408 = OpFMul %float %406 %407
        %409 = OpFAdd %float %408 %float_1
        %410 = OpFMul %float %float_3_14159274 %409
        %411 = OpFMul %float %410 %409
        %412 = OpFDiv %float %403 %411
        %413 = OpDot %float %188 %242
        %414 = OpExtInst %float %1 NMax %413 %float_0
        %415 = OpDot %float %188 %247
        %416 = OpExtInst %float %1 NMax %415 %float_0
        %417 = OpFAdd %float %205 %float_1
        %418 = OpFMul %float %417 %417
        %419 = OpFMul %float %418 %float_0_125
        %420 = OpFSub %float %float_1 %419
        %421 = OpFMul %float %414 %420
        %422 = OpFAdd %float %421 %419
        %423 = OpFDiv %float %414 %422
        %424 = OpFMul %float %416 %420
        %425 = OpFAdd %float %424 %419
        %426 = OpFDiv %float %416 %425
        %427 = OpFMul %float %426 %423
        %428 = OpDot %float %318 %242
        %429 = OpExtInst %float %1 NMax %428 %float_0
        %430 = OpFSub %v3float %71 %244
        %431 = OpFSub %float %float_1 %429
        %432 = OpExtInst %float %1 FClamp %431 %float_0 %float_1
        %433 = OpExtInst %float %1 Pow %432 %float_5
        %434 = OpVectorTimesScalar %v3float %430 %433
        %435 = OpFAdd %v3float %244 %434
        %436 = OpFSub %v3float %71 %435
        %437 = OpFSub %float %float_1 %206
        %438 = OpVectorTimesScalar %v3float %436 %437
        %439 = OpFMul %float %412 %427
        %440 = OpVectorTimesScalar %v3float %435 %439
        %441 = OpFMul %float %float_4 %414
        %442 = OpFMul %float %441 %416
        %443 = OpFAdd %float %442 %float_9_99999975en05
        %444 = OpCompositeConstruct %v3float %443 %443 %443
        %445 = OpFDiv %v3float %440 %444
        %446 = OpFMul %v3float %438 %169
        %447 = OpFMul %v3float %446 %114
        %448 = OpFAdd %v3float %447 %445
        %449 = OpFMul %v3float %448 %401
        %450 = OpVectorTimesScalar %v3float %449 %416
        %250 = OpFAdd %v3float %249 %450
               OpBranch %248
        %248 = OpLabel
        %252 = OpIAdd %uint %251 %uint_1
               OpBranch %245
        %256 = OpLabel
        %451 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_2
        %452 = OpLoad %v3float %451
        %453 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_1
        %454 = OpLoad %float %453
        %455 = OpVectorTimesScalar %v3float %452 %454
        %456 = OpFMul %v3float %455 %169
        %457 = OpVectorTimesScalar %v3float %456 %221
        %458 = OpFAdd %v3float %457 %249
        %459 = OpFMul %v3float %241 %169
        %460 = OpFAdd %v3float %236 %459
        %461 = OpFAdd %v3float %458 %460
        %462 = OpVectorTimesScalar %v3float %461 %132
        %463 = OpVectorShuffle %v2float %130 %130 0 1
        %464 = OpCompositeExtract %float %130 3
        %465 = OpCompositeConstruct %v2float %464 %464
        %466 = OpFDiv %v2float %463 %465
        %467 = OpVectorTimesScalar %v2float %466 %float_0_5
        %468 = OpFAdd %v2float %467 %67
        %469 = OpVectorShuffle %v2float %129 %129 0 1
        %470 = OpCompositeExtract %float %129 3
        %471 = OpCompositeConstruct %v2float %470 %470
        %472 = OpFDiv %v2float %469 %471
        %473 = OpVectorTimesScalar %v2float %472 %float_0_5
        %474 = OpFAdd %v2float %473 %67
        %475 = OpFSub %v2float %468 %474
        %476 = OpCompositeExtract %float %135 3
        %477 = OpCompositeExtract %float %462 0
        %478 = OpCompositeExtract %float %462 1
        %479 = OpCompositeExtract %float %462 2
        %480 = OpCompositeConstruct %v4float %477 %478 %479 %476
        %481 = OpVectorTimesScalar %v3float %188 %float_0_5
        %482 = OpFAdd %v3float %481 %68
        %483 = OpCompositeExtract %float %482 0
        %484 = OpCompositeExtract %float %482 1
        %485 = OpCompositeExtract %float %482 2
        %486 = OpCompositeConstruct %v4float %483 %484 %485 %205
        %487 = OpCompositeExtract %float %120 2
        %488 = OpCompositeExtract %float %475 0
        %489 = OpCompositeExtract %float %475 1
        %490 = OpCompositeConstruct %v4float %488 %489 %487 %float_1
        %491 = OpCompositeConstruct %v4float %206 %205 %221 %143
               OpStore %out_var_SV_Target0 %480
               OpStore %out_var_SV_Target1 %486
               OpStore %out_var_SV_Target2 %490
               OpStore %out_var_SV_Target3 %491
               OpReturn
               OpFunctionEnd
