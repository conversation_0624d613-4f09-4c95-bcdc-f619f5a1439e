// Deferred Rendering G-Buffer Vertex Shader
// Tests geometry buffer generation for deferred rendering

cbuffer PerFrame : register(b0)
{
    float4x4 WorldMatrix;
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 WorldViewProjectionMatrix;
    float4x4 NormalMatrix;
    float Time;
};

struct VSInput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float3 Tangent : TANGENT;
    float3 Bitangent : BITANGENT;
    float2 TexCoord : TEXCOORD0;
    float4 Color : COLOR0;
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float3 Tangent : TEXCOORD2;
    float3 Bitangent : TEXCOORD3;
    float2 TexCoord : TEXCOORD4;
    float4 Color : TEXCOORD5;
    float Depth : TEXCOORD6;
};

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Transform position to world space
    float4 worldPos = mul(float4(input.Position, 1.0), WorldMatrix);
    output.WorldPos = worldPos.xyz;
    
    // Transform to clip space
    output.Position = mul(float4(input.Position, 1.0), WorldViewProjectionMatrix);
    
    // Transform TBN vectors to world space
    output.Normal = normalize(mul(input.Normal, (float3x3)NormalMatrix));
    output.Tangent = normalize(mul(input.Tangent, (float3x3)NormalMatrix));
    output.Bitangent = normalize(mul(input.Bitangent, (float3x3)NormalMatrix));
    
    // Pass through texture coordinates and color
    output.TexCoord = input.TexCoord;
    output.Color = input.Color;
    
    // Calculate linear depth
    output.Depth = output.Position.z / output.Position.w;
    
    return output;
}
