// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VSInput
{
  float3 Position : POSITION;
  float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
  float2 TexCoordOffset[8] : TEXCOORD1;
};

cbuffer PostProcessParams : register(b0)
{
  float2 TexelSize;
  float BlurRadius;
  float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  output.Position = float4(input.Position, 1.0f);
  output.TexCoord = input.TexCoord;
  float2 offsets[8] = {float2(-1.0f, -1.0f), float2(0.0f, -1.0f), float2(1.0f, -1.0f), float2(-1.0f, 0.0f), float2(1.0f, 0.0f), float2(-1.0f, 1.0f), float2(0.0f, 1.0f), float2(1.0f, 1.0f)};
  for (int i = 0; i < 8; (i++))
  {
    output.TexCoordOffset[i] = input.TexCoord + ((offsets[i] * TexelSize) * BlurRadius);
  }
  return output;
}

