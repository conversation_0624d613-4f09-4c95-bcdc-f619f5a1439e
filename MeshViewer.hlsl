// MeshViewer.hlsl
// 基础网格渲染，固定球形光源，PBR材质系统
/*$(ShaderResources)*/

// Define some constants (与光栅化版本保持一致)
#define M_PI 3.1415926535897932384626433832795
#define M_INV_PI 0.31830988618379067153776752674503

// PBR材质参数
float3 fresnelSchlick(float cosTheta, float3 F0)
{
	return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
}

float D_GGX(float NoH, float alpha)
{
	float alpha2 = alpha * alpha;
	float d = (NoH * NoH) * (alpha2 - 1.0) + 1.0;
	return alpha2 * M_INV_PI / (d * d);
}

float G1_GGX_Schlick(float NoV, float alpha) {
  float k = alpha / 2.0;
  return max(NoV, 0.001) / (NoV * (1.0 - k) + k);
}

float G_<PERSON>(float NoV, float NoL, float alpha) {
  return G1_GGX_Schlick(NoL, alpha) * G1_GGX_Schlick(NoV, alpha);
}

float3 microfacetBRDF(float3 L, float3 V, float3 N, float metallic, float roughness, float3 baseColor, float specularlevel) 
{
  float3 H = normalize(V + L);
  
  float NoV = clamp(dot(N, V), 0.0, 1.0);
  float NoL = clamp(dot(N, L), 0.0, 1.0);
  float NoH = clamp(dot(N, H), 0.0, 1.0);
  float VoH = clamp(dot(V, H), 0.0, 1.0);
  
  float3 f0 = 0.16 * (specularlevel * specularlevel);
  f0 = lerp(f0, baseColor, metallic); 

  float alpha = roughness * roughness;
  
  float3 F = fresnelSchlick(VoH, f0);
  float D = D_GGX(NoH, alpha);
  float G = G_Smith(NoV, NoL, alpha);
  
  float3 specular = (F * D * G) / (4.0 * max(NoV, 0.001) * max(NoL, 0.001)); 
  
  float3 rhoD = baseColor; 
  rhoD *= 1.0 - F;
  rhoD *= (1.0 - metallic);

  float3 diffuse = rhoD * M_INV_PI;
  
  return diffuse + specular;
}

/*$(_compute:main)*/(uint3 DTid : SV_DispatchThreadID)
{
    uint2 px = DTid.xy;
    uint w, h;
    Output.GetDimensions(w, h);
    float2 dimensions = float2(w, h);


    float2 screenPos = (float2(px) + 0.5f) / dimensions * 2.0 - 1.0;
    screenPos.y = -screenPos.y;


    float4 world = mul(float4(screenPos, 0.99f, 1), /*$(Variable:InvViewProjMtx)*/);
    world.xyz /= world.w;


    RayDesc ray;
    ray.Origin = /*$(Variable:CameraPos)*/;
    ray.TMin = 0;
    ray.TMax = 10000.0f;
    ray.Direction = normalize(world.xyz - ray.Origin);


    RayQuery<RAY_FLAG_NONE> rayQuery;  // 移除所有标志，允许双面渲染

    rayQuery.TraceRayInline(
        Scene,
        0,
        255,
        ray
    );


    rayQuery.Proceed();
    
    uint rayStatus = rayQuery.CommittedStatus();
    
    if (rayStatus == COMMITTED_TRIANGLE_HIT)
    {
        // 获取重心坐标
        float3 barycentrics;
        barycentrics.yz = rayQuery.CommittedTriangleBarycentrics();
        barycentrics.x = 1.0f - (barycentrics.y + barycentrics.z);


        // 获取三角形顶点索引
        uint primitiveIndex = rayQuery.CommittedPrimitiveIndex();
        uint3 indices = uint3(
            primitiveIndex * 3 + 0,
            primitiveIndex * 3 + 1,
            primitiveIndex * 3 + 2
        );


        // 获取顶点法线并插值
        float3 normal0 = VertexBuffer[indices.x].Normal;
        float3 normal1 = VertexBuffer[indices.y].Normal;
        float3 normal2 = VertexBuffer[indices.z].Normal;
        
        float3 normal = normalize(normal0) * barycentrics.x +
                        normalize(normal1) * barycentrics.y +
                        normalize(normal2) * barycentrics.z;
        normal = normalize(normal);


        // 获取命中点世界坐标
        float3 hitPoint = ray.Origin + ray.Direction * rayQuery.CommittedRayT();


        // 光源参数 - 保持较高强度
        float3 lightPosition = float3(2.0f, 3.0f, 2.0f);
        float3 lightColor = float3(1.0f, 1.0f, 1.0f);
        float lightIntensity = 2.0f;  // 保持高光照强度


        // 材质参数 (与光栅化版本一致)
        float3 basecolor = float3(0.8f, 0.8f, 0.8f);
        float roughness = 0.4f;
        float specularlevel = 0.5f;
        float metallic = 0.0f;


        // 计算光照方向和距离
        float3 lightDirection = normalize(lightPosition - hitPoint);
        float distanceToLight = length(lightPosition - hitPoint);
        
        // 距离衰减
        float attenuation = 1.0f / (1.0f + distanceToLight * 0.1f);
        float3 lightPerpendicularIrradiance = lightIntensity * lightColor * attenuation;
        
        // 视角方向
        float3 viewDirection = normalize(/*$(Variable:CameraPos)*/ - hitPoint);


        // PBR光照计算
        float3 irradiance = max(dot(lightDirection, normal), 0.0) * lightPerpendicularIrradiance; 
        float3 brdf = microfacetBRDF(lightDirection, viewDirection, normal, metallic, roughness, basecolor, specularlevel);
        float3 radiance = irradiance * brdf;
        
        // 环境光照
        radiance += float3(0.02f, 0.02f, 0.02f);
        Output[px] = float4(radiance, 1.0f);
    }
    else
    {
        Output[px] = float4(0.0f, 0.0f, 0.0f, 0.0f);
    }
}

/*
Shader Resources:
    Texture Output (as UAV)
    Buffer Scene (as RTScene)
    Buffer VertexBuffer (as SRV)
Shader Samplers:
    samLinear filter: MinMagMipLinear addressmode: Wrap
*/ 