struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 Position : POSITION;
  float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
  float2 TexCoordOffset[8] : TEXCOORD1;
};

cbuffer PostProcessParams : register(b0)
{
  uniform float2 TexelSize;
  uniform float BlurRadius;
  uniform float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  output.Position = float4(input.Position, 1.0f);
  output.TexCoord = input.TexCoord;
  float2 offsets[8] = {float2(-1.0f, -1.0f), float2(0.0f, -1.0f), float2(1.0f, -1.0f), float2(-1.0f, 0.0f), float2(1.0f, 0.0f), float2(-1.0f, 1.0f), float2(0.0f, 1.0f), float2(1.0f, 1.0f)};
  for (int i = 0; i < 8; (i++))
  {
    output.TexCoordOffset[i] = input.TexCoord + ((offsets[i] * TexelSize) * BlurRadius);
  }
  return output;
}

