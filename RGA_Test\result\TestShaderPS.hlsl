// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 position : SV_POSITION;
  float3 worldPos : TEXCOORD0;
  float3 normal : TEXCOORD1;
  float3 tangent : TEXCOORD2;
  float3 binormal : TEXCOORD3;
  float2 texcoord : TEXCOORD4;
  float4 color : TEXCOORD5;
  float3 viewDir : TEXCOORD6;
  float3 lightDir : TEXCOORD7;
};

static const float PI = 3.14159265359f;
Texture2D DiffuseTexture : register(t0);
Texture2D NormalTexture : register(t1);
Texture2D SpecularTexture : register(t2);
Texture2D EmissiveTexture : register(t3);
TextureCube EnvironmentTexture : register(t4);
SamplerState LinearSampler : register(s0);
cbuffer MaterialConstants : register(b0)
{
  float4x4 WorldMatrix;
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 WorldViewProjectionMatrix;
  float4 LightPosition;
  float4 LightColor;
  float4 MaterialDiffuse;
  float4 MaterialSpecular;
  float4 CameraPosition;
  float Time;
  float SpecularPower;
  float2 TextureScale;
}

float4 main(PSInput input) : SV_TARGET
{
  float4 diffuseColor = float4(0.8f, 0.6f, 0.4f, 1.0f);
  float4 specularColor = float4(1.0f, 1.0f, 1.0f, 1.0f);
  float3 emissiveColor = float3(0.1f, 0.05f, 0.0f);
  float2 uv = input.texcoord;
  float2 uvOffset = float2(0.01f, 0.01f);
  float alpha = 1.0f;
  float timeOffset = (Time * 0.5f);
  float sinTime = sin(timeOffset);
  float cosTime = cos(timeOffset);
  float sinZero = 0.0f;
  float cosZero = 1.0f;
  float sinPiHalf = sin(PI / 2.0f);
  float cosPi = cos(PI);
  float expZero = 1.0f;
  float expOne = 2.718281828459045f;
  float logOne = 0.0f;
  float logE = 1.0f;
  float powerTests1 = pow(diffuseColor.r, 2.0f);
  float powerTests2 = pow(specularColor.g, 3.0f);
  float powerTests3 = pow(emissiveColor.b, 0.5f);
  float powerTests4 = rsqrt(alpha);
  float powerTests5 = timeOffset;
  float powerTests6 = 1.0f;
  float powerTests7 = 1.0f / cosTime;
  float powerTests8 = exp2(timeOffset);
  float redundant1 = powerTests1;
  float redundant2 = powerTests2;
  float redundant3 = powerTests3;
  float redundant4 = 0.0f;
  float redundant5 = 0.0f;
  float redundant6 = powerTests6;
  float expr1 = 0.0f;
  float expr2 = 1.0f;
  float expr3 = redundant3;
  float ternary1 = 10.0f;
  float ternary2 = 12.0f;
  float ternary3 = 15.0f;
  bool ternary4 = 1;
  bool ternary5 = false;
  bool logical1 = redundant1 > 0.0f;
  bool logical2 = redundant2 < 1.0f;
  bool logical3 = false;
  bool logical4 = true;
  float2 uvAnimated = uv + (uvOffset * sinTime);
  uvAnimated = (uvAnimated * 1.0f) + 0.0f;
  float4 diffuseSample = DiffuseTexture.Sample(LinearSampler, uvAnimated);
  float4 normalSample = NormalTexture.Sample(LinearSampler, uvAnimated + 0.0f);
  float4 specularSample = SpecularTexture.Sample(LinearSampler, uvAnimated - 0.0f);
  float4 emissiveSample = EmissiveTexture.Sample(LinearSampler, (uvAnimated * 1.0f));
  float3 normalMap = (normalSample.rgb * 2.0f) - 1.0f;
  normalMap = normalize(normalMap + 0.0f);
  float3x3 tangentToWorld = float3x3(normalize(input.tangent), normalize(input.binormal), normalize(input.normal));
  float3 worldNormal = mul(normalMap, tangentToWorld);
  worldNormal = normalize((worldNormal * 1.0f));
  float3 lightDir = normalize(input.lightDir + 0.0f);
  float3 viewDir = normalize(input.viewDir - 0.0f);
  float3 halfDir = normalize(lightDir + viewDir);
  float NdotL = dot(worldNormal, lightDir);
  NdotL = max(0.0f, NdotL);
  float NdotH = dot(worldNormal, halfDir);
  NdotH = max(0.0f, NdotH);
  float specularTerm = pow(NdotH, SpecularPower);
  float VdotN = dot(viewDir, worldNormal);
  float fresnel = pow(1.0f - VdotN, 5.0f);
  float3 reflectDir = reflect((-viewDir), worldNormal);
  float4 envSample = EnvironmentTexture.Sample(LinearSampler, reflectDir);
  float3 finalDiffuse = ((diffuseSample.rgb * diffuseColor.rgb) * MaterialDiffuse.rgb);
  finalDiffuse = ((finalDiffuse * NdotL) * LightColor.rgb);
  finalDiffuse = finalDiffuse + 0.0f;
  float3 finalSpecular = ((specularSample.rgb * specularColor.rgb) * MaterialSpecular.rgb);
  finalSpecular = ((finalSpecular * specularTerm) * LightColor.rgb);
  finalSpecular = (finalSpecular * 1.0f);
  float3 finalEmissive = ((emissiveSample.rgb * emissiveColor) * MaterialDiffuse.rgb);
  finalEmissive = finalEmissive - 0.0f;
  float3 finalEnvironment = (envSample.rgb * fresnel);
  finalEnvironment = (finalEnvironment * 0.3f) + 0.0f;
  float3 finalColor = finalDiffuse + finalSpecular + finalEmissive + finalEnvironment;
  finalColor.r = finalColor.r + sinZero + cosZero + expZero + logOne;
  finalColor.g = finalColor.g + powerTests1 + powerTests2 + redundant1 + expr1;
  finalColor.b = finalColor.b + ternary1 + ternary2 + ternary3;
  finalColor = finalColor / finalColor + 1.0f;
  finalColor = pow(finalColor, 0.454545455f);
  float finalAlpha = ((diffuseSample.a * alpha) * MaterialDiffuse.a);
  finalAlpha = finalAlpha;
  if (logical1 && logical2)
  {
    finalColor = (finalColor * 1.1f);
  }
  else if (logical3 || logical4)
  {
    finalColor = (finalColor * 0.9f);
  }
  {
    {
      finalColor = (finalColor * 1.01f);
    }
    {
      finalColor = (finalColor * 1.01f);
    }
    {
      finalColor = (finalColor * 1.01f);
    }
  }
  float unusedVar1 = 42.0f;
  float unusedVar2 = unusedVar1 * unusedVar1;
  return float4(finalColor, finalAlpha);
}

