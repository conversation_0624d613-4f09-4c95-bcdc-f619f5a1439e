// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float4 Color : TEXCOORD5;
  float Depth : TEXCOORD6;
};

struct PSOutput
{
  float4 Albedo : SV_TARGET0;
  float4 Normal : SV_TARGET1;
  float4 WorldPos : SV_TARGET2;
  float4 Emissive : SV_TARGET3;
};

cbuffer Material : register(b0)
{
  float3 Albedo;
  float Metallic;
  float Roughness;
  float AO;
  float3 EmissiveColor;
  float EmissiveStrength;
  float NormalStrength;
}

Texture2D AlbedoTexture : register(t0);
Texture2D NormalTexture : register(t1);
Texture2D MetallicTexture : register(t2);
Texture2D RoughnessTexture : register(t3);
Texture2D AOTexture : register(t4);
Texture2D EmissiveTexture : register(t5);
SamplerState LinearSampler : register(s0);
float3 getNormalFromMap(float2 texCoord, float3 worldNormal, float3 worldTangent, float3 worldBitangent)
{
  float3 tangentNormal = (NormalTexture.Sample(LinearSampler, texCoord).xyz * 2.0f) - 1.0f;
  tangentNormal.xy *= NormalStrength;
  float3x3 TBN = float3x3(worldTangent, worldBitangent, worldNormal);
  return normalize(mul(tangentNormal, TBN));
}

PSOutput main(PSInput input)
{
  PSOutput output;
  float4 albedoSample = AlbedoTexture.Sample(LinearSampler, input.TexCoord);
  float3 albedo = ((albedoSample.rgb * Albedo) * input.Color.rgb);
  float metallic = (MetallicTexture.Sample(LinearSampler, input.TexCoord).r * Metallic);
  float roughness = (RoughnessTexture.Sample(LinearSampler, input.TexCoord).r * Roughness);
  float ao = (AOTexture.Sample(LinearSampler, input.TexCoord).r * AO);
  float3 emissive = ((EmissiveTexture.Sample(LinearSampler, input.TexCoord).rgb * EmissiveColor) * EmissiveStrength);
  float3 worldNormal = getNormalFromMap(input.TexCoord, input.Normal, input.Tangent, input.Bitangent);
  output.Albedo = float4(albedo, metallic);
  output.Normal = float4((worldNormal * 0.5f) + 0.5f, roughness);
  output.WorldPos = float4(input.WorldPos, ao);
  output.Emissive = float4(emissive, input.Depth);
  return output;
}

