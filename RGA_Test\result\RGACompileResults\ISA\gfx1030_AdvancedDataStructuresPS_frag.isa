_amdgpu_ps_main:
	s_mov_b64 s[4:5], exec                                     // 000000000000: BE84047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s24, s1                                          // 000000000008: BE980301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s25, s1                                          // 000000000014: BE990301
	v_interp_mov_f32_e32 v22, p0, attr10.x                     // 000000000018: C85A2802
	s_load_dwordx4 s[12:15], s[24:25], null                    // 00000000001C: F408030C FA000000
	v_interp_p1_f32_e32 v2, v0, attr4.x                        // 000000000024: C8081000
	v_interp_p1_f32_e32 v3, v0, attr4.y                        // 000000000028: C80C1100
	s_mov_b64 s[0:1], exec                                     // 00000000002C: BE80047E
	v_lshlrev_b32_e32 v17, 6, v22                              // 000000000030: 34222C86
	s_waitcnt lgkmcnt(0)                                       // 000000000034: BF8CC07F
	s_clause 0x2                                               // 000000000038: BFA10002
	buffer_load_dword v18, v17, s[12:15], 0 offen offset:48    // 00000000003C: E0301030 80031211
	buffer_load_dwordx4 v[9:12], v17, s[12:15], 0 offen offset:32// 000000000044: E0381020 80030911
	buffer_load_dwordx4 v[5:8], v17, s[12:15], 0 offen         // 00000000004C: E0381000 80030511
	v_interp_p2_f32_e32 v2, v1, attr4.x                        // 000000000054: C8091001
	v_interp_p2_f32_e32 v3, v1, attr4.y                        // 000000000058: C80D1101
	s_waitcnt vmcnt(2)                                         // 00000000005C: BF8C3F72
	v_and_b32_e32 v13, 1, v18                                  // 000000000060: 361A2481
	s_waitcnt vmcnt(1)                                         // 000000000064: BF8C3F71
	v_fma_f32 v9, v9, v2, v11                                  // 000000000068: D54B0009 042E0509
	v_fmac_f32_e32 v12, v10, v3                                // 000000000070: 5618070A
	v_cmpx_eq_u32_e32 1, v13                                   // 000000000074: 7DA41A81
	s_cbranch_execz _L0                                        // 000000000078: BF88000E
	s_clause 0x1                                               // 00000000007C: BFA10001
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 000000000080: F408020C FA000020
	s_load_dwordx8 s[16:23], s[24:25], 0x40                    // 000000000088: F40C040C FA000040
	v_cvt_f32_u32_e32 v2, v22                                  // 000000000090: 7E040D16
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	image_sample  v[13:15], [v9, v12, v2], s[16:23], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 000000000098: F080072A 00440D09 0000020C
	s_waitcnt vmcnt(0)                                         // 0000000000A4: BF8C3F70
	v_mul_f32_e32 v5, v13, v5                                  // 0000000000A8: 100A0B0D
	v_mul_f32_e32 v6, v14, v6                                  // 0000000000AC: 100C0D0E
	v_mul_f32_e32 v7, v15, v7                                  // 0000000000B0: 100E0F0F
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000B4: 88FE007E
	s_mov_b32 m0, s2                                           // 0000000000B8: BEFC0302
	v_and_b32_e32 v2, 2, v18                                   // 0000000000BC: 36042482
	v_interp_p1_f32_e32 v13, v0, attr1.x                       // 0000000000C0: C8340400
	v_interp_p1_f32_e32 v15, v0, attr1.y                       // 0000000000C4: C83C0500
	v_interp_p1_f32_e32 v14, v0, attr1.z                       // 0000000000C8: C8380600
	s_mov_b64 s[0:1], exec                                     // 0000000000CC: BE80047E
	v_interp_p2_f32_e32 v13, v1, attr1.x                       // 0000000000D0: C8350401
	v_interp_p2_f32_e32 v15, v1, attr1.y                       // 0000000000D4: C83D0501
	v_interp_p2_f32_e32 v14, v1, attr1.z                       // 0000000000D8: C8390601
	v_cmpx_ne_u32_e32 0, v2                                    // 0000000000DC: 7DAA0480
	s_cbranch_execz _L1                                        // 0000000000E0: BF880030
	s_clause 0x1                                               // 0000000000E4: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0x70                    // 0000000000E8: F40C040C FA000070
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000000F0: F408020C FA000020
	v_cvt_f32_u32_e32 v2, v22                                  // 0000000000F8: 7E040D16
	s_mov_b32 m0, s2                                           // 0000000000FC: BEFC0302
	v_interp_p1_f32_e32 v3, v0, attr3.x                        // 000000000100: C80C0C00
	v_interp_p1_f32_e32 v16, v0, attr2.y                       // 000000000104: C8400900
	v_interp_p1_f32_e32 v10, v0, attr3.z                       // 000000000108: C8280E00
	v_interp_p1_f32_e32 v11, v0, attr2.x                       // 00000000010C: C82C0800
	v_interp_p1_f32_e32 v23, v0, attr2.z                       // 000000000110: C85C0A00
	v_interp_p2_f32_e32 v3, v1, attr3.x                        // 000000000114: C80D0C01
	v_interp_p2_f32_e32 v16, v1, attr2.y                       // 000000000118: C8410901
	v_interp_p2_f32_e32 v10, v1, attr3.z                       // 00000000011C: C8290E01
	v_interp_p2_f32_e32 v11, v1, attr2.x                       // 000000000120: C82D0801
	v_interp_p2_f32_e32 v23, v1, attr2.z                       // 000000000124: C85D0A01
	s_waitcnt lgkmcnt(0)                                       // 000000000128: BF8CC07F
	image_sample  v[19:21], [v9, v12, v2], s[16:23], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 00000000012C: F080072A 00441309 0000020C
	v_interp_p1_f32_e32 v2, v0, attr3.y                        // 000000000138: C8080D00
	v_interp_p2_f32_e32 v2, v1, attr3.y                        // 00000000013C: C8090D01
	s_waitcnt vmcnt(0)                                         // 000000000140: BF8C3F70
	v_fma_f32 v20, v20, 2.0, -1.0                              // 000000000144: D54B0014 03CDE914
	v_fma_f32 v19, v19, 2.0, -1.0                              // 00000000014C: D54B0013 03CDE913
	v_fma_f32 v21, v21, 2.0, -1.0                              // 000000000154: D54B0015 03CDE915
	v_mul_f32_e32 v2, v20, v2                                  // 00000000015C: 10040514
	v_mul_f32_e32 v3, v20, v3                                  // 000000000160: 10060714
	v_mul_f32_e32 v10, v20, v10                                // 000000000164: 10141514
	v_fmac_f32_e32 v2, v19, v16                                // 000000000168: 56042113
	v_fmac_f32_e32 v3, v19, v11                                // 00000000016C: 56061713
	v_fmac_f32_e32 v10, v19, v23                               // 000000000170: 56142F13
	v_fmac_f32_e32 v2, v21, v15                                // 000000000174: 56041F15
	v_fmac_f32_e32 v3, v21, v13                                // 000000000178: 56061B15
	v_fmac_f32_e32 v10, v21, v14                               // 00000000017C: 56141D15
	v_mul_f32_e32 v11, v2, v2                                  // 000000000180: 10160502
	v_fmac_f32_e32 v11, v3, v3                                 // 000000000184: 56160703
	v_fmac_f32_e32 v11, v10, v10                               // 000000000188: 5616150A
	v_rsq_f32_e32 v13, v11                                     // 00000000018C: 7E1A5D0B
	v_cmp_neq_f32_e32 vcc_lo, 0, v11                           // 000000000190: 7C1A1680
	v_cndmask_b32_e32 v11, 0, v13, vcc_lo                      // 000000000194: 02161A80
	v_mul_f32_e32 v13, v11, v3                                 // 000000000198: 101A070B
	v_mul_f32_e32 v15, v11, v2                                 // 00000000019C: 101E050B
	v_mul_f32_e32 v14, v11, v10                                // 0000000001A0: 101C150B
_L1:
	s_or_b64 exec, exec, s[0:1]                                // 0000000001A4: 88FE007E
	buffer_load_dwordx2 v[2:3], v17, s[12:15], 0 offen offset:16// 0000000001A8: E0341010 80030211
	v_and_b32_e32 v10, 4, v18                                  // 0000000001B0: 36142484
	s_mov_b64 s[0:1], exec                                     // 0000000001B4: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 0000000001B8: 7DAA1480
	s_cbranch_execz _L2                                        // 0000000001BC: BF88000D
	s_clause 0x1                                               // 0000000001C0: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0x90                    // 0000000001C4: F40C040C FA000090
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000001CC: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v22                                 // 0000000001D4: 7E140D16
	s_waitcnt lgkmcnt(0)                                       // 0000000001D8: BF8CC07F
	image_sample  v[10:11], [v9, v12, v10], s[16:23], s[8:11] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000001DC: F080032A 00440A09 00000A0C
	s_waitcnt vmcnt(0)                                         // 0000000001E8: BF8C3F70
	v_mul_f32_e32 v2, v10, v2                                  // 0000000001EC: 1004050A
	v_mul_f32_e32 v3, v11, v3                                  // 0000000001F0: 1006070B
_L2:
	s_or_b64 exec, exec, s[0:1]                                // 0000000001F4: 88FE007E
	buffer_load_dword v16, v17, s[12:15], 0 offen offset:24    // 0000000001F8: E0301018 80031011
	v_and_b32_e32 v10, 8, v18                                  // 000000000200: 36142488
	s_mov_b64 s[0:1], exec                                     // 000000000204: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 000000000208: 7DAA1480
	s_cbranch_execz _L3                                        // 00000000020C: BF88000C
	s_clause 0x1                                               // 000000000210: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0xb0                    // 000000000214: F40C040C FA0000B0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 00000000021C: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v22                                 // 000000000224: 7E140D16
	s_waitcnt lgkmcnt(0)                                       // 000000000228: BF8CC07F
	image_sample  v10, [v9, v12, v10], s[16:23], s[8:11] dmask:0x1 dim:SQ_RSRC_IMG_2D_ARRAY// 00000000022C: F080012A 00440A09 00000A0C
	s_waitcnt vmcnt(0)                                         // 000000000238: BF8C3F70
	v_mul_f32_e32 v16, v10, v16                                // 00000000023C: 1020210A
_L3:
	s_or_b64 exec, exec, s[0:1]                                // 000000000240: 88FE007E
	buffer_load_dword v17, v17, s[12:15], 0 offen offset:28    // 000000000244: E030101C 80031111
	s_load_dwordx4 s[20:23], s[24:25], 0x30                    // 00000000024C: F408050C FA000030
	v_and_b32_e32 v10, 16, v18                                 // 000000000254: 36142490
	v_mov_b32_e32 v21, 0                                       // 000000000258: 7E2A0280
	v_mov_b32_e32 v18, 0                                       // 00000000025C: 7E240280
	v_mov_b32_e32 v19, 0                                       // 000000000260: 7E260280
	v_mov_b32_e32 v20, 0                                       // 000000000264: 7E280280
	s_mov_b64 s[0:1], exec                                     // 000000000268: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 00000000026C: 7DAA1480
	s_cbranch_execz _L4                                        // 000000000270: BF88000E
	s_clause 0x1                                               // 000000000274: BFA10001
	s_load_dwordx8 s[36:43], s[24:25], 0xd0                    // 000000000278: F40C090C FA0000D0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 000000000280: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v22                                 // 000000000288: 7E140D16
	s_waitcnt lgkmcnt(0)                                       // 00000000028C: BF8CC07F
	image_sample  v[9:11], [v9, v12, v10], s[36:43], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 000000000290: F080072A 00490909 00000A0C
	s_waitcnt vmcnt(0)                                         // 00000000029C: BF8C3F70
	v_mul_f32_e32 v18, v9, v17                                 // 0000000002A0: 10242309
	v_mul_f32_e32 v19, v10, v17                                // 0000000002A4: 1026230A
	v_mul_f32_e32 v20, v11, v17                                // 0000000002A8: 1028230B
_L4:
	s_or_b64 exec, exec, s[0:1]                                // 0000000002AC: 88FE007E
	s_clause 0x1                                               // 0000000002B0: BFA10001
	s_load_dwordx8 s[36:43], s[24:25], 0xf0                    // 0000000002B4: F40C090C FA0000F0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000002BC: F408020C FA000020
	s_mov_b32 m0, s2                                           // 0000000002C4: BEFC0302
	v_interp_p1_f32_e32 v9, v0, attr5.x                        // 0000000002C8: C8241400
	v_interp_p1_f32_e32 v10, v0, attr5.y                       // 0000000002CC: C8281500
	v_interp_p2_f32_e32 v9, v1, attr5.x                        // 0000000002D0: C8251401
	v_interp_p2_f32_e32 v10, v1, attr5.y                       // 0000000002D4: C8291501
	s_and_b64 exec, exec, s[4:5]                               // 0000000002D8: 87FE047E
	s_waitcnt lgkmcnt(0)                                       // 0000000002DC: BF8CC07F
	image_sample v[9:11], v[9:10], s[36:43], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D// 0000000002E0: F0800708 00490909
	s_buffer_load_dword s3, s[20:23], null                     // 0000000002E8: F42000CA FA000000
	v_interp_p1_f32_e32 v12, v0, attr6.x                       // 0000000002F0: C8301800
	v_interp_p1_f32_e32 v22, v0, attr6.y                       // 0000000002F4: C8581900
	v_interp_p1_f32_e32 v23, v0, attr6.z                       // 0000000002F8: C85C1A00
	v_mov_b32_e32 v24, 0                                       // 0000000002FC: 7E300280
	v_mov_b32_e32 v25, 0                                       // 000000000300: 7E320280
	v_interp_p2_f32_e32 v12, v1, attr6.x                       // 000000000304: C8311801
	v_interp_p2_f32_e32 v22, v1, attr6.y                       // 000000000308: C8591901
	v_interp_p2_f32_e32 v23, v1, attr6.z                       // 00000000030C: C85D1A01
	s_waitcnt vmcnt(4)                                         // 000000000310: BF8C3F74
	v_mul_f32_e32 v5, v5, v12                                  // 000000000314: 100A1905
	v_mul_f32_e32 v6, v6, v22                                  // 000000000318: 100C2D06
	v_mul_f32_e32 v7, v7, v23                                  // 00000000031C: 100E2F07
	s_waitcnt lgkmcnt(0)                                       // 000000000320: BF8CC07F
	s_cmp_eq_u32 s3, 0                                         // 000000000324: BF068003
	s_cbranch_scc1 _L5                                         // 000000000328: BF850183
	s_mov_b32 m0, s2                                           // 00000000032C: BEFC0302
	s_buffer_load_dwordx8 s[4:11], s[12:15], 0xa0              // 000000000330: F42C0106 FA0000A0
	v_interp_p1_f32_e32 v21, v0, attr7.y                       // 000000000338: C8541D00
	v_interp_p1_f32_e32 v25, v0, attr7.x                       // 00000000033C: C8641C00
	v_interp_p1_f32_e32 v33, v0, attr7.z                       // 000000000340: C8841E00
	s_buffer_load_dwordx8 s[12:19], s[12:15], 0x80             // 000000000344: F42C0306 FA000080
	v_interp_p1_f32_e32 v12, v0, attr0.x                       // 00000000034C: C8300000
	v_interp_p2_f32_e32 v21, v1, attr7.y                       // 000000000350: C8551D01
	v_interp_p2_f32_e32 v25, v1, attr7.x                       // 000000000354: C8651C01
	v_interp_p2_f32_e32 v33, v1, attr7.z                       // 000000000358: C8851E01
	v_interp_p1_f32_e32 v22, v0, attr0.y                       // 00000000035C: C8580100
	v_interp_p2_f32_e32 v12, v1, attr0.x                       // 000000000360: C8310001
	v_mul_f32_e32 v27, v21, v21                                // 000000000364: 10362B15
	v_interp_p1_f32_e32 v23, v0, attr0.z                       // 000000000368: C85C0200
	v_add_f32_e32 v24, 0xbd23d70a, v5                          // 00000000036C: 06300AFF BD23D70A
	v_interp_p2_f32_e32 v22, v1, attr0.y                       // 000000000374: C8590101
	v_add_f32_e32 v29, 0xbd23d70a, v6                          // 000000000378: 063A0CFF BD23D70A
	v_fmac_f32_e32 v27, v25, v25                               // 000000000380: 56363319
	v_interp_p2_f32_e32 v23, v1, attr0.z                       // 000000000384: C85D0201
	s_waitcnt vmcnt(3)                                         // 000000000388: BF8C3F73
	v_fmaak_f32 v26, v2, v24, 0x3d23d70a                       // 00000000038C: 5A343102 3D23D70A
	v_add_f32_e32 v30, 0xbd23d70a, v7                          // 000000000394: 063C0EFF BD23D70A
	s_waitcnt lgkmcnt(0)                                       // 00000000039C: BF8CC07F
	v_fma_f32 v28, v12, s8, s11                                // 0000000003A0: D54B001C 002C110C
	v_fmac_f32_e32 v27, v33, v33                               // 0000000003A8: 56364321
	v_mul_f32_e32 v34, v3, v3                                  // 0000000003AC: 10440703
	s_mov_b32 s0, 0xbea2f983                                   // 0000000003B0: BE8003FF BEA2F983
	v_fma_f32 v32, v12, s12, s15                               // 0000000003B8: D54B0020 003C190C
	v_fmac_f32_e32 v28, s9, v22                                // 0000000003C0: 56382C09
	v_rsq_f32_e32 v31, v27                                     // 0000000003C4: 7E3E5D1B
	v_cmp_neq_f32_e32 vcc_lo, 0, v27                           // 0000000003C8: 7C1A3680
	v_fma_f32 v35, v12, s16, s19                               // 0000000003CC: D54B0023 004C210C
	v_fmac_f32_e32 v32, s13, v22                               // 0000000003D4: 56402C0D
	v_fmac_f32_e32 v28, s10, v23                               // 0000000003D8: 56382E0A
	v_fmaak_f32 v27, v2, v29, 0x3d23d70a                       // 0000000003DC: 5A363B02 3D23D70A
	v_fmaak_f32 v37, s0, v2, 0x3ea2f983                        // 0000000003E4: 5A4A0400 3EA2F983
	v_fmac_f32_e32 v35, s17, v22                               // 0000000003EC: 56462C11
	v_mul_f32_e32 v34, v34, v34                                // 0000000003F0: 10444522
	v_rcp_f32_e32 v24, v28                                     // 0000000003F4: 7E30551C
	v_cndmask_b32_e32 v36, 0, v31, vcc_lo                      // 0000000003F8: 02483E80
	v_fma_f32 v31, v23, s14, v32 div:2                         // 0000000003FC: D54B001F 1C801D17
	v_fma_f32 v32, v23, s18, v35 div:2                         // 000000000404: D54B0020 1C8C2517
	v_fmaak_f32 v28, v2, v30, 0x3d23d70a                       // 00000000040C: 5A383D02 3D23D70A
	v_mul_f32_e32 v35, v37, v5                                 // 000000000414: 10460B25
	v_mul_f32_e32 v29, v36, v21                                // 000000000418: 103A2B24
	v_fma_f32 v21, v12, s4, s7                                 // 00000000041C: D54B0015 001C090C
	v_mul_f32_e32 v30, v36, v25                                // 000000000424: 103C3324
	v_mul_f32_e32 v33, v36, v33                                // 000000000428: 10424324
	v_mul_f32_e32 v31, v31, v24                                // 00000000042C: 103E311F
	v_mul_f32_e32 v25, v15, v29                                // 000000000430: 10323B0F
	v_mul_f32_e32 v32, v32, v24                                // 000000000434: 10403120
	v_fmac_f32_e32 v21, s5, v22                                // 000000000438: 562A2C05
	s_mov_b32 s7, 0x3d03126f                                   // 00000000043C: BE8703FF 3D03126F
	v_add_f32_e32 v31, 0.5, v31                                // 000000000444: 063E3EF0
	v_fmac_f32_e32 v25, v13, v30                               // 000000000448: 56323D0D
	v_add_f32_e32 v32, 0.5, v32                                // 00000000044C: 064040F0
	v_fma_f32 v21, v23, s6, v21 div:2                          // 000000000450: D54B0015 1C540D17
	s_mov_b32 s6, 0                                            // 000000000458: BE860380
	s_mov_b32 s8, 0                                            // 00000000045C: BE880380
	v_fmac_f32_e32 v25, v14, v33                               // 000000000460: 5632430E
	v_min_f32_e32 v36, v31, v32                                // 000000000464: 1E48411F
	v_max_f32_e32 v38, v31, v32                                // 000000000468: 204C411F
	v_mul_f32_e32 v21, v21, v24                                // 00000000046C: 102A3115
	v_max_f32_e32 v24, 0, v25                                  // 000000000470: 20303280
	v_cmp_ngt_f32_e32 vcc_lo, 0, v36                           // 000000000474: 7C164880
	v_cmp_nlt_f32_e64 s0, 1.0, v38                             // 000000000478: D40E0000 00024CF2
	v_mul_f32_e32 v36, v37, v6                                 // 000000000480: 10480D25
	v_mul_f32_e32 v37, v37, v7                                 // 000000000484: 104A0F25
	v_add_f32_e32 v38, 0.5, v21                                // 000000000488: 064C2AF0
	v_mul_f32_e32 v39, v24, v34                                // 00000000048C: 104E4518
	v_mul_f32_e32 v40, 0x40490fdb, v24                         // 000000000490: 105030FF 40490FDB
	v_mul_f32_e32 v41, 4.0, v24                                // 000000000498: 105230F6
	v_mov_b32_e32 v25, 0                                       // 00000000049C: 7E320280
	v_mov_b32_e32 v24, 0                                       // 0000000004A0: 7E300280
	v_mov_b32_e32 v21, 0                                       // 0000000004A4: 7E2A0280
	s_and_b64 s[0:1], s[0:1], vcc                              // 0000000004A8: 87806A00
	s_branch _L6                                               // 0000000004AC: BF820058
_L12:
	s_or_b64 exec, exec, s[4:5]                                // 0000000004B0: 88FE047E
	v_add_f32_e32 v47, v42, v29                                // 0000000004B4: 065E3B2A
	v_add_f32_e32 v48, v43, v30                                // 0000000004B8: 06603D2B
	v_add_f32_e32 v50, v44, v33                                // 0000000004BC: 0664432C
	s_add_i32 s4, s6, 44                                       // 0000000004C0: 8104AC06
	s_add_i32 s5, s6, 32                                       // 0000000004C4: 8105A006
	v_mul_f32_e32 v49, v47, v47                                // 0000000004C8: 10625F2F
	s_buffer_load_dword s4, s[20:23], s4                       // 0000000004CC: F420010A 08000000
	s_add_i32 s9, s6, 36                                       // 0000000004D4: 8109A406
	s_add_i32 s10, s6, 40                                      // 0000000004D8: 810AA806
	s_clause 0x2                                               // 0000000004DC: BFA10002
	s_buffer_load_dword s5, s[20:23], s5                       // 0000000004E0: F420014A 0A000000
	s_buffer_load_dword s9, s[20:23], s9                       // 0000000004E8: F420024A 12000000
	s_buffer_load_dword s10, s[20:23], s10                     // 0000000004F0: F420028A 14000000
	v_fmac_f32_e32 v49, v48, v48                               // 0000000004F8: 56626130
	s_add_i32 s8, s8, 1                                        // 0000000004FC: 81088108
	s_add_i32 s6, s6, 64                                       // 000000000500: 8106C006
	s_cmp_lt_u32 s8, s3                                        // 000000000504: BF0A0308
	v_fmac_f32_e32 v49, v50, v50                               // 000000000508: 56626532
	v_rsq_f32_e32 v51, v49                                     // 00000000050C: 7E665D31
	v_cmp_neq_f32_e32 vcc_lo, 0, v49                           // 000000000510: 7C1A6280
	s_waitcnt lgkmcnt(0)                                       // 000000000514: BF8CC07F
	v_mul_f32_e32 v45, s4, v45                                 // 000000000518: 105A5A04
	v_cndmask_b32_e32 v49, 0, v51, vcc_lo                      // 00000000051C: 02626680
	v_mul_f32_e32 v45, v45, v46                                // 000000000520: 105A5D2D
	v_mul_f32_e32 v47, v49, v47                                // 000000000524: 105E5F31
	v_mul_f32_e32 v48, v49, v48                                // 000000000528: 10606131
	v_mul_f32_e32 v49, v49, v50                                // 00000000052C: 10626531
	v_mul_f32_e32 v50, v42, v15                                // 000000000530: 10641F2A
	v_mul_f32_e32 v51, v47, v15                                // 000000000534: 10661F2F
	v_mul_f32_e32 v47, v47, v29                                // 000000000538: 105E3B2F
	v_fmac_f32_e32 v50, v43, v13                               // 00000000053C: 56641B2B
	v_fmac_f32_e32 v51, v48, v13                               // 000000000540: 56661B30
	v_fmac_f32_e32 v47, v48, v30                               // 000000000544: 565E3D30
	v_fmac_f32_e32 v50, v44, v14                               // 000000000548: 56641D2C
	v_fmac_f32_e32 v51, v49, v14                               // 00000000054C: 56661D31
	v_fmac_f32_e32 v47, v49, v33                               // 000000000550: 565E4331
	v_max_f32_e32 v49, 0, v50                                  // 000000000554: 20626480
	v_max_f32_e32 v48, 0, v51                                  // 000000000558: 20606680
	v_max_f32_e32 v47, 0, v47                                  // 00000000055C: 205E5E80
	v_mul_f32_e32 v50, v49, v40                                // 000000000560: 10645131
	v_fmaak_f32 v52, v49, v41, 0x38d1b717                      // 000000000564: 5A685331 38D1B717
	v_mul_f32_e32 v48, v48, v48                                // 00000000056C: 10606130
	v_sub_f32_e64 v47, 1.0, v47 clamp                          // 000000000570: D504802F 00025EF2
	v_mul_f32_e32 v48, v48, v34                                // 000000000578: 10604530
	v_mul_f32_e32 v51, v47, v47                                // 00000000057C: 10665F2F
	v_mul_f32_e32 v48, v48, v48                                // 000000000580: 10606130
	v_mul_f32_e32 v51, v51, v51                                // 000000000584: 10666733
	v_mul_f32_e32 v48, v48, v50                                // 000000000588: 10606530
	v_mul_f32_e32 v47, v47, v51                                // 00000000058C: 105E672F
	v_mul_f32_e32 v48, v48, v52                                // 000000000590: 10606930
	v_fma_f32 v50, -v47, v26, 1.0                              // 000000000594: D54B0032 23CA352F
	v_fma_f32 v51, -v47, v27, 1.0                              // 00000000059C: D54B0033 23CA372F
	v_fma_f32 v47, -v47, v28, 1.0                              // 0000000005A4: D54B002F 23CA392F
	v_mul_f32_e32 v52, v49, v39                                // 0000000005AC: 10684F31
	v_rcp_f32_e32 v48, v48                                     // 0000000005B0: 7E605530
	v_add_f32_e32 v50, v50, v26                                // 0000000005B4: 06643532
	v_add_f32_e32 v51, v51, v27                                // 0000000005B8: 06663733
	v_add_f32_e32 v47, v47, v28                                // 0000000005BC: 065E392F
	v_sub_f32_e32 v53, 1.0, v50                                // 0000000005C0: 086A64F2
	v_sub_f32_e32 v46, 1.0, v51                                // 0000000005C4: 085C66F2
	v_mul_f32_e32 v48, v52, v48                                // 0000000005C8: 10606134
	v_sub_f32_e32 v52, 1.0, v47                                // 0000000005CC: 08685EF2
	v_mul_f32_e32 v53, v35, v53                                // 0000000005D0: 106A6B23
	v_mul_f32_e32 v46, v36, v46                                // 0000000005D4: 105C5D24
	v_mul_f32_e32 v54, s5, v48                                 // 0000000005D8: 106C6005
	v_mul_f32_e32 v55, s9, v48                                 // 0000000005DC: 106E6009
	v_mul_f32_e32 v48, s10, v48                                // 0000000005E0: 1060600A
	v_mul_f32_e32 v52, v37, v52                                // 0000000005E4: 10686925
	v_mul_f32_e32 v50, v54, v50                                // 0000000005E8: 10646536
	v_mul_f32_e32 v51, v55, v51                                // 0000000005EC: 10666737
	v_mul_f32_e32 v47, v48, v47                                // 0000000005F0: 105E5F30
	v_fmac_f32_e32 v53, v50, v45                               // 0000000005F4: 566A5B32
	v_fmac_f32_e32 v46, v51, v45                               // 0000000005F8: 565C5B33
	v_fmac_f32_e32 v52, v47, v45                               // 0000000005FC: 56685B2F
	v_fmac_f32_e32 v25, v53, v49                               // 000000000600: 56326335
	v_fmac_f32_e32 v24, v46, v49                               // 000000000604: 5630632E
	v_fmac_f32_e32 v21, v52, v49                               // 000000000608: 562A6334
	s_cbranch_scc0 _L5                                         // 00000000060C: BF8400CA
_L6:
	s_add_i32 s9, s6, 48                                       // 000000000610: 8109B006
	s_add_i32 s4, s6, 16                                       // 000000000614: 81049006
	s_add_i32 s11, s6, 24                                      // 000000000618: 810B9806
	s_add_i32 s5, s6, 20                                       // 00000000061C: 81059406
	s_clause 0x3                                               // 000000000620: BFA10003
	s_buffer_load_dword s15, s[20:23], s9                      // 000000000624: F42003CA 12000000
	s_buffer_load_dword s9, s[20:23], s4                       // 00000000062C: F420024A 08000000
	s_buffer_load_dword s10, s[20:23], s5                      // 000000000634: F420028A 0A000000
	s_buffer_load_dword s11, s[20:23], s11                     // 00000000063C: F42002CA 16000000
	s_waitcnt lgkmcnt(0)                                       // 000000000644: BF8CC07F
	s_cmp_lg_u32 s15, 0                                        // 000000000648: BF07800F
	s_cbranch_scc0 _L7                                         // 00000000064C: BF8400B9
	s_add_i32 s4, s6, 4                                        // 000000000650: 81048406
	s_add_i32 s5, s6, 8                                        // 000000000654: 81058806
	s_clause 0x2                                               // 000000000658: BFA10002
	s_buffer_load_dword s14, s[20:23], s6                      // 00000000065C: F420038A 0C000000
	s_buffer_load_dword s12, s[20:23], s4                      // 000000000664: F420030A 08000000
	s_buffer_load_dword s13, s[20:23], s5                      // 00000000066C: F420034A 0A000000
	s_cmp_lg_u32 s15, 1                                        // 000000000674: BF07810F
	s_mov_b64 s[4:5], -1                                       // 000000000678: BE8404C1
	s_cbranch_scc0 _L8                                         // 00000000067C: BF840035
	v_mov_b32_e32 v45, 1.0                                     // 000000000680: 7E5A02F2
	s_cmp_lg_u32 s15, 2                                        // 000000000684: BF07820F
	s_cbranch_scc1 _L9                                         // 000000000688: BF850031
	s_add_i32 s4, s6, 28                                       // 00000000068C: 81049C06
	s_waitcnt lgkmcnt(0)                                       // 000000000690: BF8CC07F
	v_sub_f32_e32 v42, s12, v22                                // 000000000694: 08542C0C
	s_buffer_load_dword s4, s[20:23], s4                       // 000000000698: F420010A 08000000
	v_mul_f32_e64 v44, s10, s10                                // 0000000006A0: D508002C 0000140A
	v_sub_f32_e32 v43, s14, v12                                // 0000000006A8: 0856180E
	v_sub_f32_e32 v48, s13, v23                                // 0000000006AC: 08602E0D
	v_mul_f32_e32 v45, v42, v42                                // 0000000006B0: 105A552A
	v_fmac_f32_e64 v44, s9, s9                                 // 0000000006B4: D52B002C 00001209
	v_fmac_f32_e32 v45, v43, v43                               // 0000000006BC: 565A572B
	v_fmac_f32_e64 v44, s11, s11                               // 0000000006C0: D52B002C 0000160B
	v_fmac_f32_e32 v45, v48, v48                               // 0000000006C8: 565A6130
	v_rsq_f32_e32 v49, v44                                     // 0000000006CC: 7E625D2C
	v_cmp_neq_f32_e32 vcc_lo, 0, v44                           // 0000000006D0: 7C1A5880
	v_rsq_f32_e32 v50, v45                                     // 0000000006D4: 7E645D2D
	s_waitcnt lgkmcnt(0)                                       // 0000000006D8: BF8CC07F
	v_mul_f32_e64 v46, s4, 0.15915494                          // 0000000006DC: D508002E 0001F004
	v_mul_f32_e64 v47, 0x3e4391d1, s4                          // 0000000006E4: D508002F 000008FF 3E4391D1
	v_cos_f32_e32 v46, v46                                     // 0000000006F0: 7E5C6D2E
	v_cos_f32_e32 v47, v47                                     // 0000000006F4: 7E5E6D2F
	v_cndmask_b32_e32 v44, 0, v49, vcc_lo                      // 0000000006F8: 02586280
	v_cmp_neq_f32_e32 vcc_lo, 0, v45                           // 0000000006FC: 7C1A5A80
	v_cndmask_b32_e32 v49, 0, v50, vcc_lo                      // 000000000700: 02626480
	v_mul_f32_e32 v50, s10, v44                                // 000000000704: 1064580A
	v_sub_f32_e32 v46, v47, v46                                // 000000000708: 085C5D2F
	v_mul_f32_e32 v42, v49, v42                                // 00000000070C: 10545531
	v_mul_f32_e32 v43, v49, v43                                // 000000000710: 10565731
	v_rcp_f32_e32 v46, v46                                     // 000000000714: 7E5C552E
	v_mul_f32_e32 v46, v47, v46                                // 000000000718: 105C5D2F
	v_mul_f32_e32 v47, s9, v44                                 // 00000000071C: 105E5809
	v_fma_f32 v46, -v42, v50, v46                              // 000000000720: D54B002E 24BA652A
	v_sqrt_f32_e32 v50, v45                                    // 000000000728: 7E64672D
	v_mul_f32_e32 v45, s11, v44                                // 00000000072C: 105A580B
	v_mul_f32_e32 v44, v49, v48                                // 000000000730: 10586131
	v_fma_f32 v46, -v43, v47, v46                              // 000000000734: D54B002E 24BA5F2B
	v_fma_f32 v45, -v44, v45, v46 clamp                        // 00000000073C: D54B802D 24BA5B2C
	v_fmaak_f32 v46, s7, v50, 0x3db851ec                       // 000000000744: 5A5C6407 3DB851EC
	v_fmac_f32_e32 v45, v46, v50                               // 00000000074C: 565A652E
_L9:
	s_mov_b64 s[4:5], 0                                        // 000000000750: BE840480
_L8:
	s_andn2_b64 vcc, exec, s[4:5]                              // 000000000754: 8AEA047E
	s_cbranch_vccnz _L10                                       // 000000000758: BF870012
	s_waitcnt lgkmcnt(0)                                       // 00000000075C: BF8CC07F
	v_sub_f32_e32 v44, s12, v22                                // 000000000760: 08582C0C
	v_sub_f32_e32 v42, s14, v12                                // 000000000764: 0854180E
	v_sub_f32_e32 v45, s13, v23                                // 000000000768: 085A2E0D
	v_mul_f32_e32 v43, v44, v44                                // 00000000076C: 1056592C
	v_fmac_f32_e32 v43, v42, v42                               // 000000000770: 5656552A
	v_fmac_f32_e32 v43, v45, v45                               // 000000000774: 56565B2D
	v_rsq_f32_e32 v46, v43                                     // 000000000778: 7E5C5D2B
	v_sqrt_f32_e32 v47, v43                                    // 00000000077C: 7E5E672B
	v_cmp_neq_f32_e32 vcc_lo, 0, v43                           // 000000000780: 7C1A5680
	v_cndmask_b32_e32 v46, 0, v46, vcc_lo                      // 000000000784: 025C5C80
	v_mul_f32_e32 v43, v46, v42                                // 000000000788: 1056552E
	v_mul_f32_e32 v42, v46, v44                                // 00000000078C: 1054592E
	v_mul_f32_e32 v44, v46, v45                                // 000000000790: 10585B2E
	v_fmaak_f32 v45, s7, v47, 0x3db851ec                       // 000000000794: 5A5A5E07 3DB851EC
	v_fma_f32 v45, v45, v47, 1.0                               // 00000000079C: D54B002D 03CA5F2D
_L10:
	s_cbranch_execnz _L11                                      // 0000000007A4: BF890010
_L13:
	v_mul_f32_e64 v42, s10, s10                                // 0000000007A8: D508002A 0000140A
	v_mov_b32_e32 v45, 1.0                                     // 0000000007B0: 7E5A02F2
	v_fmac_f32_e64 v42, s9, s9                                 // 0000000007B4: D52B002A 00001209
	v_fmac_f32_e64 v42, s11, s11                               // 0000000007BC: D52B002A 0000160B
	v_rsq_f32_e32 v43, v42                                     // 0000000007C4: 7E565D2A
	v_cmp_neq_f32_e32 vcc_lo, 0, v42                           // 0000000007C8: 7C1A5480
	v_cndmask_b32_e32 v44, 0, v43, vcc_lo                      // 0000000007CC: 02585680
	v_mul_f32_e64 v43, v44, -s9                                // 0000000007D0: D508002B 4000132C
	v_mul_f32_e64 v42, v44, -s10                               // 0000000007D8: D508002A 4000152C
	v_mul_f32_e64 v44, v44, -s11                               // 0000000007E0: D508002C 4000172C
_L11:
	v_mov_b32_e32 v46, 1.0                                     // 0000000007E8: 7E5C02F2
	s_and_saveexec_b64 s[4:5], s[0:1]                          // 0000000007EC: BE842400
	s_cbranch_execz _L12                                       // 0000000007F0: BF88FF2F
	s_clause 0x1                                               // 0000000007F4: BFA10001
	s_buffer_load_dwordx4 s[28:31], s[20:23], 0x1c             // 0000000007F8: F428070A FA00001C
	s_buffer_load_dword s9, s[20:23], 0x2c                     // 000000000800: F420024A FA00002C
	v_mul_f32_e32 v46, v43, v13                                // 000000000808: 105C1B2B
	s_waitcnt lgkmcnt(0)                                       // 00000000080C: BF8CC07F
	s_clause 0x1                                               // 000000000810: BFA10001
	s_load_dwordx8 s[12:19], s[24:25], 0x110                   // 000000000814: F40C030C FA000110
	s_load_dwordx4 s[36:39], s[24:25], 0x60                    // 00000000081C: F408090C FA000060
	v_fmac_f32_e32 v46, v42, v15                               // 000000000824: 565C1F2A
	v_fmac_f32_e32 v46, v44, v14                               // 000000000828: 565C1D2C
	v_sub_f32_e32 v46, s29, v46                                // 00000000082C: 085C5C1D
	v_rcp_f32_e32 v47, s31                                     // 000000000830: 7E5E541F
	v_rcp_f32_e32 v48, s30                                     // 000000000834: 7E60541E
	v_mul_f32_e64 v49, s9, 0                                   // 000000000838: D5080031 00010009
	v_max_f32_e32 v46, s28, v46                                // 000000000840: 205C5C1C
	v_sub_f32_e32 v57, v38, v46                                // 000000000844: 08725D26
	v_fma_f32 v59, v49, v47, v32                               // 000000000848: D54B003B 04825F31
	v_fma_f32 v58, -s9, v48, v31                               // 000000000850: D54B003A 247E6009
	v_fma_f32 v52, -s9, v47, v32                               // 000000000858: D54B0034 24825E09
	v_fma_f32 v47, s9, v47, v32                                // 000000000860: D54B002F 04825E09
	s_waitcnt lgkmcnt(0)                                       // 000000000868: BF8CC07F
	s_clause 0x2                                               // 00000000086C: BFA10002
	image_sample_c_lz v53, v[57:59], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000870: F0BC0108 01233539
	image_sample_c_lz  v54, [v57, v58, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000878: F0BC010A 01233639 0000343A
	image_sample_c_lz  v51, [v57, v58, v47], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000884: F0BC010A 01233339 00002F3A
	v_fma_f32 v58, v49, v48, v31                               // 000000000890: D54B003A 047E6131
	v_fma_f32 v48, s9, v48, v31                                // 000000000898: D54B0030 047E6009
	s_clause 0x5                                               // 0000000008A0: BFA10005
	image_sample_c_lz  v55, [v57, v58, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008A4: F0BC010A 01233739 0000343A
	image_sample_c_lz v56, v[57:59], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008B0: F0BC0108 01233839
	image_sample_c_lz  v49, [v57, v58, v47], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008B8: F0BC010A 01233139 00002F3A
	image_sample_c_lz  v52, [v57, v48, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008C4: F0BC010A 01233439 00003430
	image_sample_c_lz  v50, [v57, v48, v59], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008D0: F0BC010A 01233239 00003B30
	image_sample_c_lz  v46, [v57, v48, v47], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008DC: F0BC010A 01232E39 00002F30
	s_waitcnt vmcnt(7)                                         // 0000000008E8: BF8C3F77
	v_add_f32_e32 v47, v53, v54                                // 0000000008EC: 065E6D35
	s_waitcnt vmcnt(6)                                         // 0000000008F0: BF8C3F76
	v_add_f32_e32 v47, v51, v47                                // 0000000008F4: 065E5F33
	s_waitcnt vmcnt(5)                                         // 0000000008F8: BF8C3F75
	v_add_f32_e32 v47, v55, v47                                // 0000000008FC: 065E5F37
	s_waitcnt vmcnt(4)                                         // 000000000900: BF8C3F74
	v_add_f32_e32 v47, v56, v47                                // 000000000904: 065E5F38
	s_waitcnt vmcnt(3)                                         // 000000000908: BF8C3F73
	v_add_f32_e32 v47, v49, v47                                // 00000000090C: 065E5F31
	s_waitcnt vmcnt(2)                                         // 000000000910: BF8C3F72
	v_add_f32_e32 v47, v52, v47                                // 000000000914: 065E5F34
	s_waitcnt vmcnt(1)                                         // 000000000918: BF8C3F71
	v_add_f32_e32 v47, v50, v47                                // 00000000091C: 065E5F32
	s_waitcnt vmcnt(0)                                         // 000000000920: BF8C3F70
	v_add_f32_e32 v46, v46, v47                                // 000000000924: 065C5F2E
	v_mul_f32_e32 v46, 0x3de38e39, v46                         // 000000000928: 105C5CFF 3DE38E39
	s_branch _L12                                              // 000000000930: BF82FEDF
_L7:
	s_branch _L13                                              // 000000000934: BF82FF9C
_L5:
	s_mov_b32 m0, s2                                           // 000000000938: BEFC0302
	s_clause 0x2                                               // 00000000093C: BFA10002
	s_buffer_load_dword s2, s[20:23], 0x18                     // 000000000940: F420008A FA000018
	s_buffer_load_dword s3, s[20:23], 0x4                      // 000000000948: F42000CA FA000004
	s_buffer_load_dwordx2 s[0:1], s[20:23], 0x10               // 000000000950: F424000A FA000010
	v_add_f32_e32 v12, v25, v18                                // 000000000958: 06182519
	v_add_f32_e32 v18, v24, v19                                // 00000000095C: 06242718
	v_add_f32_e32 v19, v21, v20                                // 000000000960: 06262915
	v_interp_p1_f32_e32 v20, v0, attr8.w                       // 000000000964: C8502300
	v_interp_p1_f32_e32 v24, v0, attr9.w                       // 000000000968: C8602700
	v_interp_p1_f32_e32 v21, v0, attr8.x                       // 00000000096C: C8542000
	v_interp_p1_f32_e32 v22, v0, attr8.y                       // 000000000970: C8582100
	v_interp_p1_f32_e32 v23, v0, attr9.x                       // 000000000974: C85C2400
	v_interp_p2_f32_e32 v20, v1, attr8.w                       // 000000000978: C8512301
	v_interp_p2_f32_e32 v24, v1, attr9.w                       // 00000000097C: C8612701
	v_interp_p1_f32_e32 v25, v0, attr9.y                       // 000000000980: C8642500
	v_interp_p1_f32_e32 v0, v0, attr11.x                       // 000000000984: C8002C00
	v_interp_p2_f32_e32 v21, v1, attr8.x                       // 000000000988: C8552001
	v_rcp_f32_e64 v20, v20 div:2                               // 00000000098C: D5AA0014 18000114
	v_interp_p2_f32_e32 v22, v1, attr8.y                       // 000000000994: C8592101
	v_rcp_f32_e64 v24, v24 div:2                               // 000000000998: D5AA0018 18000118
	v_interp_p2_f32_e32 v23, v1, attr9.x                       // 0000000009A0: C85D2401
	v_interp_p2_f32_e32 v25, v1, attr9.y                       // 0000000009A4: C8652501
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 0000000009A8: BF8C0072
	v_mul_f32_e32 v26, s3, v16                                 // 0000000009AC: 10342003
	v_interp_p2_f32_e32 v0, v1, attr11.x                       // 0000000009B0: C8012C01
	v_mul_f32_e32 v1, v21, v20                                 // 0000000009B4: 10022915
	s_waitcnt vmcnt(0)                                         // 0000000009B8: BF8C3F70
	v_fma_f32 v9, s0, v26, v9                                  // 0000000009BC: D54B0009 04263400
	v_fma_f32 v10, s1, v26, v10                                // 0000000009C4: D54B000A 042A3401
	v_fmac_f32_e32 v11, s2, v26                                // 0000000009CC: 56163402
	v_fma_f32 v1, v24, v23, -v1                                // 0000000009D0: D54B0001 84062F18
	v_fmac_f32_e32 v12, v9, v5                                 // 0000000009D8: 56180B09
	v_fmac_f32_e32 v18, v10, v6                                // 0000000009DC: 56240D0A
	v_fmac_f32_e32 v19, v11, v7                                // 0000000009E0: 56260F0B
	v_mul_f32_e32 v5, v22, v20                                 // 0000000009E4: 100A2916
	v_fma_f32 v9, v13, 0.5, 0.5                                // 0000000009E8: D54B0009 03C1E10D
	v_mul_f32_e32 v6, v12, v0                                  // 0000000009F0: 100C010C
	v_mul_f32_e32 v7, v18, v0                                  // 0000000009F4: 100E0112
	v_mul_f32_e32 v0, v19, v0                                  // 0000000009F8: 10000113
	v_fma_f32 v5, v24, v25, -v5                                // 0000000009FC: D54B0005 84163318
	v_fma_f32 v10, v15, 0.5, 0.5                               // 000000000A04: D54B000A 03C1E10F
	v_fma_f32 v11, v14, 0.5, 0.5                               // 000000000A0C: D54B000B 03C1E10E
	v_mov_b32_e32 v12, 1.0                                     // 000000000A14: 7E1802F2
	exp mrt0 v6, v7, v0, v8 vm                                 // 000000000A18: F800100F 08000706
	exp mrt1 v9, v10, v11, v3 vm                               // 000000000A20: F800101F 030B0A09
	exp mrt2 v1, v5, v4, v12 vm                                // 000000000A28: F800102F 0C040501
	exp mrt3 v2, v3, v16, v17 done vm                          // 000000000A30: F800183F 11100302
	s_endpgm                                                   // 000000000A38: BF810000
