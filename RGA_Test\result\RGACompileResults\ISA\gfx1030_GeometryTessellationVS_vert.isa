_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v4, s3, 5, v1                                // 000000000040: D76F0004 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v4                            // 000000000048: 7D880801
	s_and_saveexec_b32 s52, vcc_lo                             // 00000000004C: BEB43C6A
	s_cbranch_execz _L1                                        // 000000000050: BF880145
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s5, s3                                           // 000000000060: BE850303
	s_load_dwordx8 s[24:31], s[10:11], 0x30                    // 000000000064: F40C0605 FA000030
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xy v[1:2], v5, s[24:27], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000070: EA012000 80060105
	s_clause 0x1                                               // 000000000078: BFA10001
	s_load_dwordx4 s[48:51], s[4:5], null                      // 00000000007C: F4080C02 FA000000
	s_load_dwordx16 s[12:27], s[4:5], null                     // 000000000084: F4100302 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000008C: BF8CC07F
	s_buffer_load_dwordx4 s[44:47], s[48:51], 0xdc             // 000000000090: F4280B18 FA0000DC
	s_buffer_load_dwordx2 s[0:1], s[24:27], 0xa0               // 000000000098: F424000C FA0000A0
	s_buffer_load_dwordx2 s[34:35], s[48:51], 0xec             // 0000000000A0: F4240898 FA0000EC
	s_load_dwordx8 s[36:43], s[4:5], 0x40                      // 0000000000A8: F40C0902 FA000040
	s_clause 0x1                                               // 0000000000B0: BFA10001
	s_buffer_load_dword s8, s[24:27], 0xa8                     // 0000000000B4: F420020C FA0000A8
	s_buffer_load_dword s53, s[24:27], 0x68                    // 0000000000BC: F4200D4C FA000068
	s_waitcnt lgkmcnt(0)                                       // 0000000000C4: BF8CC07F
	v_rcp_f32_e32 v15, s45                                     // 0000000000C8: 7E1E542D
	v_rcp_f32_e32 v3, s46                                      // 0000000000CC: 7E06542E
	s_waitcnt vmcnt(0)                                         // 0000000000D0: BF8C3F70
	v_mul_f32_e32 v37, s1, v2                                  // 0000000000D4: 104A0401
	v_fma_f32 v10, s0, v1, v15                                 // 0000000000D8: D54B000A 043E0200
	v_mul_f32_e32 v8, s0, v1                                   // 0000000000E0: 10100200
	v_fma_f32 v9, s1, v2, -v3                                  // 0000000000E4: D54B0009 840E0401
	v_fmac_f32_e32 v3, s1, v2                                  // 0000000000EC: 56060401
	v_mul_f32_e32 v35, s34, v37                                // 0000000000F0: 10464A22
	v_mul_f32_e32 v34, s34, v10                                // 0000000000F4: 10441422
	v_fma_f32 v36, s0, v1, -v15                                // 0000000000F8: D54B0024 843E0200
	v_mul_f32_e32 v6, s34, v8                                  // 000000000100: 100C1022
	v_mul_f32_e32 v7, s34, v3                                  // 000000000104: 100E0622
	image_sample_lz v16, v[34:35], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000108: F09C0108 00A91022
	v_mul_f32_e32 v34, s34, v36                                // 000000000110: 10444822
	image_sample_lz v17, v[36:37], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000114: F09C0108 00A31124
	image_sample_lz v18, v[6:7], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000011C: F09C0108 00A91206
	image_sample_lz  v19, [v10, v37], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000124: F09C010A 00A3130A 00000025
	image_sample_lz v20, v[34:35], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000130: F09C0108 00A91422
	image_sample_lz v21, v[8:9], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000138: F09C0108 00A31508
	v_mul_f32_e32 v7, s34, v9                                  // 000000000140: 100E1222
	image_sample_lz  v22, [v8, v3], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000144: F09C010A 00A31608 00000003
	image_sample_lz v23, v[6:7], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000150: F09C0108 00A91706
	s_load_dwordx8 s[0:7], s[10:11], null                      // 000000000158: F40C0005 FA000000
	image_sample_lz  v10, [v6, v35], s[36:43], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000160: F09C010A 00A90A06 00000023
	image_sample_lz  v24, [v8, v37], s[12:19], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000016C: F09C010A 00A31808 00000025
	s_waitcnt lgkmcnt(0)                                       // 000000000178: BF8CC07F
	tbuffer_load_format_xyz v[12:14], v5, s[4:7], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000017C: EA522000 80010C05
	tbuffer_load_format_xyz v[1:3], v5, s[0:3], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000184: EA522000 80000105
	tbuffer_load_format_x v11, v5, s[28:31], 0 format:[BUF_FMT_32_FLOAT] idxen offset:12// 00000000018C: E8B0200C 80070B05
	v_mul_f32_e64 v5, s8, -0.5                                 // 000000000194: D5080005 0001E208
	s_clause 0x4                                               // 00000000019C: BFA10004
	s_buffer_load_dwordx2 s[12:13], s[24:27], 0x40             // 0000000001A0: F424030C FA000040
	s_buffer_load_dwordx2 s[14:15], s[24:27], 0x50             // 0000000001A8: F424038C FA000050
	s_buffer_load_dwordx2 s[16:17], s[24:27], 0x60             // 0000000001B0: F424040C FA000060
	s_buffer_load_dword s20, s[24:27], 0x58                    // 0000000001B8: F420050C FA000058
	s_buffer_load_dword s21, s[24:27], 0x48                    // 0000000001C0: F420054C FA000048
	s_waitcnt vmcnt(11)                                        // 0000000001C8: BF8C3F7B
	v_fma_f32 v17, s47, v17, v5                                // 0000000001CC: D54B0011 0416222F
	v_fma_f32 v25, -0.5, s8, v16                               // 0000000001D4: D54B0019 044010F1
	s_waitcnt vmcnt(10)                                        // 0000000001DC: BF8C3F7A
	v_fmac_f32_e64 v18, s8, -0.5                               // 0000000001E0: D52B0012 0001E208
	s_waitcnt vmcnt(7)                                         // 0000000001E8: BF8C3F77
	v_fmac_f32_e32 v5, s47, v21                                // 0000000001EC: 560A2A2F
	v_add_f32_e32 v17, v17, v20                                // 0000000001F0: 06222911
	v_fmac_f32_e32 v25, s47, v19                               // 0000000001F4: 5632262F
	s_waitcnt vmcnt(6)                                         // 0000000001F8: BF8C3F76
	v_fmac_f32_e32 v18, s47, v22                               // 0000000001FC: 56242C2F
	s_waitcnt vmcnt(5)                                         // 000000000200: BF8C3F75
	v_add_f32_e32 v5, v5, v23                                  // 000000000204: 060A2F05
	s_waitcnt vmcnt(4)                                         // 000000000208: BF8C3F74
	v_fmac_f32_e64 v10, s8, -0.5                               // 00000000020C: D52B000A 0001E208
	v_mul_f32_e32 v20, -0.5, v25                               // 000000000214: 102832F1
	v_mul_f32_e32 v18, -0.5, v18                               // 000000000218: 102424F1
	s_waitcnt vmcnt(3)                                         // 00000000021C: BF8C3F73
	v_fmac_f32_e32 v10, s47, v24                               // 000000000220: 5614302F
	v_fmac_f32_e32 v17, s45, v20                               // 000000000224: 5622282D
	v_fmac_f32_e32 v5, s46, v18                                // 000000000228: 560A242E
	v_fma_f32 v18, v17, v17, 1.0                               // 00000000022C: D54B0012 03CA2311
	v_fmac_f32_e32 v18, v5, v5                                 // 000000000234: 56240B05
	s_waitcnt vmcnt(1)                                         // 000000000238: BF8C3F71
	v_add_f32_e32 v2, v10, v2                                  // 00000000023C: 0604050A
	v_rsq_f32_e32 v20, v18                                     // 000000000240: 7E285D12
	v_cmp_neq_f32_e64 s0, 0, v18                               // 000000000244: D40D0000 00022480
	v_cndmask_b32_e64 v18, 0, v20, s0                          // 00000000024C: D5010012 00022880
	v_fma_f32 v20, 0.5, s8, v10                                // 000000000254: D54B0014 042810F0
	s_buffer_load_dwordx4 s[8:11], s[24:27], 0x20              // 00000000025C: F428020C FA000020
	v_sub_f32_e32 v21, v18, v13                                // 000000000264: 082A1B12
	v_fma_f32 v19, v19, s47, -v20                              // 000000000268: D54B0013 84505F13
	v_fma_f32 v17, v18, v17, -v12                              // 000000000270: D54B0011 84322312
	v_fma_f32 v5, v18, v5, -v14                                // 000000000278: D54B0005 843A0B12
	v_fmamk_f32 v13, v21, 0x3f4ccccd, v13                      // 000000000280: 581A1B15 3F4CCCCD
	v_add_f32_e32 v16, v19, v16                                // 000000000288: 06202113
	v_fmamk_f32 v12, v17, 0x3f4ccccd, v12                      // 00000000028C: 58181911 3F4CCCCD
	v_fmac_f32_e32 v14, 0x3f4ccccd, v5                         // 000000000294: 561C0AFF 3F4CCCCD
	v_mul_f32_e32 v17, v13, v13                                // 00000000029C: 10221B0D
	v_mul_f32_e32 v18, v16, v16                                // 0000000002A0: 10242110
	v_fmac_f32_e32 v17, v12, v12                               // 0000000002A4: 5622190C
	v_fmac_f32_e32 v18, v15, v15                               // 0000000002A8: 56241F0F
	v_fmac_f32_e32 v17, v14, v14                               // 0000000002AC: 56221D0E
	v_rsq_f32_e32 v5, v18                                      // 0000000002B0: 7E0A5D12
	v_cmp_neq_f32_e64 s0, 0, v18                               // 0000000002B4: D40D0000 00022480
	v_rsq_f32_e32 v19, v17                                     // 0000000002BC: 7E265D11
	v_cndmask_b32_e64 v5, 0, v5, s0                            // 0000000002C0: D5010005 00020A80
	v_cmp_neq_f32_e64 s0, 0, v17                               // 0000000002C8: D40D0000 00022280
	v_mul_f32_e32 v16, v5, v16                                 // 0000000002D0: 10202105
	v_cndmask_b32_e64 v17, 0, v19, s0                          // 0000000002D4: D5010011 00022680
	s_buffer_load_dwordx8 s[0:7], s[24:27], null               // 0000000002DC: F42C000C FA000000
	v_mul_f32_e32 v18, v17, v13                                // 0000000002E4: 10241B11
	v_mul_f32_e32 v13, v5, v15                                 // 0000000002E8: 101A1F05
	v_mul_f32_e32 v15, v17, v12                                // 0000000002EC: 101E1911
	v_mul_f32_e32 v5, 0, v5                                    // 0000000002F0: 100A0A80
	v_mul_f32_e32 v17, v17, v14                                // 0000000002F4: 10221D11
	v_mul_f32_e32 v12, v18, v16                                // 0000000002F8: 10182112
	s_waitcnt lgkmcnt(0)                                       // 0000000002FC: BF8CC07F
	v_mul_f32_e32 v22, s14, v15                                // 000000000300: 102C1E0E
	v_mul_f32_e32 v21, s12, v15                                // 000000000304: 102A1E0C
	v_mul_f32_e32 v23, s16, v15                                // 000000000308: 102E1E10
	v_fmac_f32_e32 v12, v15, v13                               // 00000000030C: 56181B0F
	v_fmac_f32_e32 v22, s15, v18                               // 000000000310: 562C240F
	v_fmac_f32_e32 v21, s13, v18                               // 000000000314: 562A240D
	v_fmac_f32_e32 v23, s17, v18                               // 000000000318: 562E2411
	v_fmac_f32_e32 v12, v17, v5                                // 00000000031C: 56180B11
	v_fmac_f32_e32 v22, s20, v17                               // 000000000320: 562C2214
	v_fmac_f32_e32 v21, s21, v17                               // 000000000324: 562A2215
	v_fmac_f32_e32 v23, s53, v17                               // 000000000328: 562E2235
	v_fma_f32 v16, -v12, v18, v16                              // 00000000032C: D54B0010 2442250C
	v_fma_f32 v19, -v12, v15, v13                              // 000000000334: D54B0013 24361F0C
	v_fma_f32 v5, -v12, v17, v5                                // 00000000033C: D54B0005 2416230C
	v_fma_f32 v12, s0, v1, s3                                  // 000000000344: D54B000C 000E0200
	v_mul_f32_e32 v18, v22, v22                                // 00000000034C: 10242D16
	v_mul_f32_e32 v13, v16, v16                                // 000000000350: 101A2110
	s_mov_b32 s3, 0xbf4ccccc                                   // 000000000354: BE8303FF BF4CCCCC
	v_fmac_f32_e32 v12, s1, v2                                 // 00000000035C: 56180401
	v_fmac_f32_e32 v18, v21, v21                               // 000000000360: 56242B15
	v_fmac_f32_e32 v13, v19, v19                               // 000000000364: 561A2713
	s_mov_b32 s1, 0x3f4ccccc                                   // 000000000368: BE8103FF 3F4CCCCC
	v_fmac_f32_e32 v12, s2, v3                                 // 000000000370: 56180602
	v_fmac_f32_e32 v18, v23, v23                               // 000000000374: 56242F17
	v_fmac_f32_e32 v13, v5, v5                                 // 000000000378: 561A0B05
	s_mov_b32 s2, 0xbf19999a                                   // 00000000037C: BE8203FF BF19999A
	v_rsq_f32_e32 v17, v18                                     // 000000000384: 7E225D12
	v_rsq_f32_e32 v14, v13                                     // 000000000388: 7E1C5D0D
	v_cmp_neq_f32_e64 s0, 0, v13                               // 00000000038C: D40D0000 00021A80
	v_fma_f32 v13, s4, v1, s7                                  // 000000000394: D54B000D 001E0204
	v_fmac_f32_e32 v13, s5, v2                                 // 00000000039C: 561A0405
	v_cndmask_b32_e64 v20, 0, v14, s0                          // 0000000003A0: D5010014 00021C80
	s_clause 0x1                                               // 0000000003A8: BFA10001
	s_buffer_load_dwordx2 s[18:19], s[48:51], 0xc0             // 0000000003AC: F4240498 FA0000C0
	s_buffer_load_dword s0, s[48:51], 0xc8                     // 0000000003B4: F4200018 FA0000C8
	v_fma_f32 v14, s8, v1, s11                                 // 0000000003BC: D54B000E 002E0208
	v_fmac_f32_e32 v13, s6, v3                                 // 0000000003C4: 561A0606
	v_mul_f32_e32 v16, v20, v16                                // 0000000003C8: 10202114
	v_mul_f32_e32 v15, v20, v19                                // 0000000003CC: 101E2714
	v_mul_f32_e32 v5, v20, v5                                  // 0000000003D0: 100A0B14
	v_fmac_f32_e32 v14, s9, v2                                 // 0000000003D4: 561C0409
	v_mul_f32_e32 v24, s15, v16                                // 0000000003D8: 1030200F
	v_mul_f32_e32 v19, s13, v16                                // 0000000003DC: 1026200D
	v_mul_f32_e32 v16, s17, v16                                // 0000000003E0: 10202011
	v_fmac_f32_e32 v14, s10, v3                                // 0000000003E4: 561C060A
	v_fmac_f32_e32 v24, s14, v15                               // 0000000003E8: 56301E0E
	v_fmac_f32_e32 v19, s12, v15                               // 0000000003EC: 56261E0C
	v_fmac_f32_e32 v16, s16, v15                               // 0000000003F0: 56201E10
	v_fmac_f32_e32 v24, s20, v5                                // 0000000003F4: 56300A14
	v_fmac_f32_e32 v19, s21, v5                                // 0000000003F8: 56260A15
	v_fmac_f32_e32 v16, s53, v5                                // 0000000003FC: 56200A35
	s_waitcnt lgkmcnt(0)                                       // 000000000400: BF8CC07F
	v_sub_f32_e32 v5, s19, v13                                 // 000000000404: 080A1A13
	v_sub_f32_e32 v26, s0, v14                                 // 000000000408: 08341C00
	v_mul_f32_e32 v15, v24, v24                                // 00000000040C: 101E3118
	v_cmp_neq_f32_e64 s0, 0, v18                               // 000000000410: D40D0000 00022480
	v_sub_f32_e32 v20, s18, v12                                // 000000000418: 08281812
	v_mul_f32_e32 v5, v5, v5                                   // 00000000041C: 100A0B05
	v_fmac_f32_e32 v15, v19, v19                               // 000000000420: 561E2713
	v_cndmask_b32_e64 v18, 0, v17, s0                          // 000000000424: D5010012 00022280
	v_fmac_f32_e32 v5, v20, v20                                // 00000000042C: 560A2914
	v_fmac_f32_e32 v15, v16, v16                               // 000000000430: 561E2110
	v_fmac_f32_e32 v5, v26, v26                                // 000000000434: 560A351A
	v_mul_f32_e64 v26, s35, s44                                // 000000000438: D508001A 00005823
	v_rsq_f32_e32 v25, v15                                     // 000000000440: 7E325D0F
	v_cmp_neq_f32_e64 s0, 0, v15                               // 000000000444: D40D0000 00021E80
	v_mul_f32_e32 v15, v18, v21                                // 00000000044C: 101E2B12
	v_sqrt_f32_e32 v5, v5                                      // 000000000450: 7E0A6705
	v_cndmask_b32_e64 v20, 0, v25, s0                          // 000000000454: D5010014 00023280
	s_mov_b32 s0, 0x3f19999a                                   // 00000000045C: BE8003FF 3F19999A
	v_max_f32_e32 v5, 1.0, v5                                  // 000000000464: 200A0AF2
	v_mul_f32_e32 v17, v20, v16                                // 000000000468: 10222114
	v_mul_f32_e32 v16, v18, v23                                // 00000000046C: 10202F12
	v_mul_f32_e32 v19, v20, v19                                // 000000000470: 10262714
	v_mul_f32_e32 v20, v20, v24                                // 000000000474: 10283114
	v_rcp_f32_e32 v23, s47                                     // 000000000478: 7E2E542F
	v_mul_f32_e32 v21, v17, v15                                // 00000000047C: 102A1F11
	v_mul_f32_e32 v18, v18, v22                                // 000000000480: 10242D12
	v_rcp_f32_e32 v5, v5                                       // 000000000484: 7E0A5505
	v_mul_f32_e32 v22, v20, v16                                // 000000000488: 102C2114
	v_fma_f32 v24, v19, v16, -v21                              // 00000000048C: D54B0018 84562113
	v_mul_f32_e32 v21, v19, v18                                // 000000000494: 102A2513
	v_fma_f32 v22, v17, v18, -v22                              // 000000000498: D54B0016 845A2511
	v_mul_f32_e64 v23, v10, v23 clamp                          // 0000000004A0: D5088017 00022F0A
	v_mul_f32_e32 v25, v24, v24                                // 0000000004A8: 10323118
	v_fma_f32 v27, v20, v15, -v21                              // 0000000004AC: D54B001B 84561F14
	v_sub_f32_e32 v21, 1.0, v18                                // 0000000004B4: 082A24F2
	v_mul_f32_e32 v5, v26, v5                                  // 0000000004B8: 100A0B1A
	v_mul_f32_e32 v28, 0x3ecccccd, v23                         // 0000000004BC: 10382EFF 3ECCCCCD
	v_fmac_f32_e32 v25, v22, v22                               // 0000000004C4: 56322D16
	v_fmaak_f32 v31, s2, v23, 0x3e99999a                       // 0000000004C8: 5A3E2E02 3E99999A
	v_fmaak_f32 v32, s3, v23, 0x3e4cccce                       // 0000000004D0: 5A402E03 3E4CCCCE
	v_fmaak_f32 v29, s0, v23, 0x3e4ccccd                       // 0000000004D8: 5A3A2E00 3E4CCCCD
	v_mul_f32_e32 v26, v18, v28                                // 0000000004E0: 10343912
	v_fmac_f32_e32 v25, v27, v27                               // 0000000004E4: 5632371B
	v_mul_f32_e32 v31, v18, v31                                // 0000000004E8: 103E3F12
	v_cmp_lt_f32_e64 s0, 0.5, v21                              // 0000000004EC: D4010000 00022AF0
	v_mul_f32_e32 v32, v18, v32                                // 0000000004F4: 10404112
	v_fmaak_f32 v30, 0x3ecccccd, v23, 0x3ecccccd               // 0000000004F8: 5A3C2EFF 3ECCCCCD
	v_rsq_f32_e32 v28, v25                                     // 000000000500: 7E385D19
	v_fmaak_f32 v33, s1, v23, 0x3dcccccd                       // 000000000504: 5A422E01 3DCCCCCD
	v_cndmask_b32_e64 v23, 0, v31, s0                          // 00000000050C: D5010017 00023E80
	v_cndmask_b32_e64 v26, 0x80000000, v26, s0                 // 000000000514: D501001A 000234FF 80000000
	v_cndmask_b32_e64 v31, 0, v32, s0                          // 000000000520: D501001F 00024080
	v_cmp_neq_f32_e64 s0, 0, v25                               // 000000000528: D40D0000 00023280
	v_med3_f32 v5, v5, 1.0, 0x42800000                         // 000000000530: D5570005 03FDE505 42800000
	v_fma_f32 v34, v21, 2.0, 1.0                               // 00000000053C: D54B0022 03C9E915
	v_sub_f32_e32 v23, v29, v23                                // 000000000544: 082E2F1D
	v_add_f32_e32 v25, v30, v26                                // 000000000548: 0632351E
	v_cndmask_b32_e64 v32, 0, v28, s0                          // 00000000054C: D5010020 00023880
	v_sub_f32_e32 v28, v33, v31                                // 000000000554: 08383F21
	v_mul_f32_e32 v22, v32, v22                                // 000000000558: 102C2D20
	v_mul_f32_e32 v24, v32, v24                                // 00000000055C: 10303120
	v_mul_f32_e32 v26, v32, v27                                // 000000000560: 10343720
	v_mul_f32_e32 v27, v34, v5                                 // 000000000564: 10360B22
_L1:
	s_or_b32 exec_lo, exec_lo, s52                             // 000000000568: 887E347E
	s_mov_b32 s1, exec_lo                                      // 00000000056C: BE81037E
	v_cmpx_gt_u32_e64 s33, v4                                  // 000000000570: D4D4007E 00020821
	s_cbranch_execz _L2                                        // 000000000578: BF880002
	exp prim v0, off, off, off done                            // 00000000057C: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 000000000584: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000588: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 00000000058C: BE803C6A
	s_cbranch_execz _L3                                        // 000000000590: BF88001B
	v_mov_b32_e32 v0, 1.0                                      // 000000000594: 7E0002F2
	v_mov_b32_e32 v4, 0                                        // 000000000598: 7E080280
	exp pos0 v4, v4, v4, v0 done                               // 00000000059C: F80008CF 00040404
	exp param5 v6, v35, off, off                               // 0000000005A4: F8000253 00002306
	exp param10 v21, off, off, off                             // 0000000005AC: F80002A1 00000015
	exp param3 v22, v24, v26, off                              // 0000000005B4: F8000237 001A1816
	exp param8 v27, off, off, off                              // 0000000005BC: F8000281 0000001B
	exp param1 v15, v18, v16, off                              // 0000000005C4: F8000217 0010120F
	s_waitcnt vmcnt(0)                                         // 0000000005CC: BF8C3F70
	exp param6 v23, v25, v28, v11                              // 0000000005D0: F800026F 0B1C1917
	exp param4 v8, v37, off, off                               // 0000000005D8: F8000243 00002508
	exp param9 v10, off, off, off                              // 0000000005E0: F8000291 0000000A
	exp param2 v19, v20, v17, off                              // 0000000005E8: F8000227 00111413
	exp param7 v12, v13, v14, off                              // 0000000005F0: F8000277 000E0D0C
	exp param0 v1, v2, v3, off                                 // 0000000005F8: F8000207 00030201
_L3:
	s_endpgm                                                   // 000000000600: BF810000
