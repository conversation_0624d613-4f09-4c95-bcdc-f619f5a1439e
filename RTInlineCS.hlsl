
// Unnamed technique, shader RTInlineCS
/*$(ShaderResources)*/

/*$(_compute:main)*/(uint3 DTid : SV_DispatchThreadID)
{
    // Get the dimensions of the render target, so we know what percentage of the screen in X and Y this pixel is at.
    uint2 px = DTid.xy;
    uint w, h;
    Output.GetDimensions(w, h);
    float2 dimensions = float2(w, h);

    // Calculate this pixel's location in screen space
    float2 screenPos = (float2(px) + 0.5f) / dimensions * 2.0 - 1.0;
    screenPos.y = -screenPos.y;

    // Calculate this pixel in world space at a specific depth away from the camera.
    // This gives us the target to shoot the ray at.
    float4 world = mul(float4(screenPos, 0.99f, 1), /*$(Variable:InvViewProjMtx)*/);
    world.xyz /= world.w;

    // Describe the ray to shoot
    RayDesc ray;
    ray.Origin = /*$(Variable:CameraPos)*/;
    ray.TMin = 0;
    ray.TMax = 10000.0f;
    ray.Direction = normalize(world.xyz - ray.Origin);

    // Do a ray query to see if the ray hit anything
    RayQuery<RAY_FLAG_CULL_NON_OPAQUE |
             RAY_FLAG_SKIP_PROCEDURAL_PRIMITIVES |
             RAY_FLAG_ACCEPT_FIRST_HIT_AND_END_SEARCH |
             RAY_FLAG_CULL_BACK_FACING_TRIANGLES> rayQuery;

    rayQuery.TraceRayInline(
        Scene,
        0,
        255,
        ray
    );

    rayQuery.Proceed();

    // If we hit a triangle, show the normal
    if (rayQuery.CommittedStatus() == COMMITTED_TRIANGLE_HIT)
    {
        // Get the barycentric coordinates of where the ray intersected the triangle
        float3 barycentrics;
        barycentrics.yz = rayQuery.CommittedTriangleBarycentrics();
        barycentrics.x = 1.0f - (barycentrics.y + barycentrics.z);

        // Get the normals for each vertex in the triangle we hit
        float3 normal0 = VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 0].Normal;
        float3 normal1 = VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 1].Normal;
        float3 normal2 = VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 2].Normal;
        float2 uv0= VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 0].UV;
        float2 uv1= VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 1].UV;
        float2 uv2= VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 2].UV;
        

        
        // Calculate the normal interpolated across the triangle at that point and normalize it
        float3 normal = normalize(normal0) * barycentrics.x +
                        normalize(normal1) * barycentrics.y +
                        normalize(normal2) * barycentrics.z;
        normal = normalize(normal);

        float2 uv = uv0 * barycentrics.x + uv1 * barycentrics.y + uv2 * barycentrics.z;
        uv = saturate(uv); // 防止uv越界

        // 获取当前三角形的MaterialID
        int materialID = VertexBuffer[rayQuery.CommittedPrimitiveIndex() * 3 + 0].MaterialID;
        // GiGi会根据MaterialID自动切换材质参数和贴图

        // 采样贴图（GiGi自动绑定当前材质的贴图）
        float4 diffuseTex  = DiffuseMap.Sample(samLinear, float3(uv, materialID));
        float4 ambientTex  = AmbientMap.Sample(samLinear, float3(uv, materialID));
        float4 normalTex   = NormalMap.Sample(samLinear, float3(uv, materialID));

        // 法线扰动（如有TBN可做切线空间变换，这里简化直接用normal）
        float3 n = normalize(normal);
        // float3 nMap = normalize(normalTex.xyz * 2.0 - 1.0);
        // n = nMap;

        // 材质参数（全局单值）
        float3 Ka = /*$(Variable:Ka)*/;
        float3 Kd = /*$(Variable:Kd)*/;
        float3 Ks = /*$(Variable:Ks)*/;
        float  Ns = /*$(Variable:Ns)*/;
        float3 Ke = /*$(Variable:Ke)*/;
        float3 Tf = /*$(Variable:Tf)*/;
        float  d  = /*$(Variable:d)*/;
        float  Tr = /*$(Variable:Tr)*/;

        // 光照参数
        float3 lightDir = normalize(float3(0.5, 1, 0.3));
        float3 viewDir = normalize(/*$(Variable:CameraPos)*/ - world.xyz);

        // 材质参数
        float3 Ka_    = Ka;
        float3 Kd_    = Kd;
        float3 Ks_    = Ks;
        float  Ns_    = Ns;
        float3 Ke_    = Ke;
        float3 Tf_    = Tf;
        float  d_     = d;
        float  Tr_    = Tr;

        // 光照计算
        float3 ambient  = Ka_ * ambientTex.rgb;
        float3 diffuse  = Kd_ * diffuseTex.rgb * max(dot(n, lightDir), 0.0);
        float3 halfDir  = normalize(lightDir + viewDir);
        float3 specular = Ks_ * pow(max(dot(n, halfDir), 0.0), Ns_);

        float3 color = ambient + diffuse + specular + Ke_;
        color = color * d_ * Tf_;
        color = max(color, 0.0); // 防止负值

        Output[px] = float4(color,1);

    }
    // Else we missed, show dark grey.
    else
    {
        Output[px] = float4(0.1f, 0.1f, 0.1f, 1.0f);
    }
}

/*
Shader Resources:
	Texture Output (as UAV)
	Buffer Scene (as RTScene)
	Buffer VertexBuffer (as SRV)
Shader Samplers:
	samLinear filter: MinMagMipLinear addressmode: Wrap
*/
