; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 255
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_LightingParams "type.LightingParams"
               OpMemberName %type_LightingParams 0 "CameraPosition"
               OpMemberName %type_LightingParams 1 "NumLights"
               OpMemberName %type_LightingParams 2 "AmbientColor"
               OpMemberName %type_LightingParams 3 "AmbientStrength"
               OpName %LightingParams "LightingParams"
               OpName %type_LightData "type.LightData"
               OpMemberName %type_LightData 0 "LightPositions"
               OpMemberName %type_LightData 1 "LightColors"
               OpMemberName %type_LightData 2 "LightDirections"
               OpMemberName %type_LightData 3 "LightTypes"
               OpName %LightData "LightData"
               OpName %type_2d_image "type.2d.image"
               OpName %GBufferAlbedo "GBufferAlbedo"
               OpName %GBufferNormal "GBufferNormal"
               OpName %GBufferWorldPos "GBufferWorldPos"
               OpName %GBufferEmissive "GBufferEmissive"
               OpName %type_sampler "type.sampler"
               OpName %PointSampler "PointSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %LightingParams DescriptorSet 0
               OpDecorate %LightingParams Binding 0
               OpDecorate %LightData DescriptorSet 0
               OpDecorate %LightData Binding 1
               OpDecorate %GBufferAlbedo DescriptorSet 0
               OpDecorate %GBufferAlbedo Binding 0
               OpDecorate %GBufferNormal DescriptorSet 0
               OpDecorate %GBufferNormal Binding 1
               OpDecorate %GBufferWorldPos DescriptorSet 0
               OpDecorate %GBufferWorldPos Binding 2
               OpDecorate %GBufferEmissive DescriptorSet 0
               OpDecorate %GBufferEmissive Binding 3
               OpDecorate %PointSampler DescriptorSet 0
               OpDecorate %PointSampler Binding 0
               OpMemberDecorate %type_LightingParams 0 Offset 0
               OpMemberDecorate %type_LightingParams 1 Offset 12
               OpMemberDecorate %type_LightingParams 2 Offset 16
               OpMemberDecorate %type_LightingParams 3 Offset 28
               OpDecorate %type_LightingParams Block
               OpDecorate %_arr_v4float_uint_32 ArrayStride 16
               OpDecorate %_arr_v4int_uint_8 ArrayStride 16
               OpMemberDecorate %type_LightData 0 Offset 0
               OpMemberDecorate %type_LightData 1 Offset 512
               OpMemberDecorate %type_LightData 2 Offset 1024
               OpMemberDecorate %type_LightData 3 Offset 1536
               OpDecorate %type_LightData Block
      %float = OpTypeFloat 32
%float_3_14159274 = OpConstant %float 3.14159274
        %int = OpTypeInt 32 1
      %int_1 = OpConstant %int 1
      %int_3 = OpConstant %int 3
    %float_2 = OpConstant %float 2
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
         %27 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_0 = OpConstant %int 0
%float_0_0399999991 = OpConstant %float 0.0399999991
         %30 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
    %float_0 = OpConstant %float 0
         %32 = OpConstantComposite %v3float %float_0 %float_0 %float_0
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
     %int_32 = OpConstant %int 32
      %int_4 = OpConstant %int 4
      %int_2 = OpConstant %int 2
%float_1_20000005 = OpConstant %float 1.20000005
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_0_454545468 = OpConstant %float 0.454545468
         %42 = OpConstantComposite %v3float %float_0_454545468 %float_0_454545468 %float_0_454545468
    %float_5 = OpConstant %float 5
%type_LightingParams = OpTypeStruct %v3float %int %v3float %float
%_ptr_Uniform_type_LightingParams = OpTypePointer Uniform %type_LightingParams
       %uint = OpTypeInt 32 0
    %uint_32 = OpConstant %uint 32
    %v4float = OpTypeVector %float 4
%_arr_v4float_uint_32 = OpTypeArray %v4float %uint_32
     %uint_8 = OpConstant %uint 8
      %v4int = OpTypeVector %int 4
%_arr_v4int_uint_8 = OpTypeArray %v4int %uint_8
%type_LightData = OpTypeStruct %_arr_v4float_uint_32 %_arr_v4float_uint_32 %_arr_v4float_uint_32 %_arr_v4int_uint_8
%_ptr_Uniform_type_LightData = OpTypePointer Uniform %type_LightData
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %57 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%LightingParams = OpVariable %_ptr_Uniform_type_LightingParams Uniform
  %LightData = OpVariable %_ptr_Uniform_type_LightData Uniform
%GBufferAlbedo = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferNormal = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferWorldPos = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%GBufferEmissive = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%PointSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
         %62 = OpUndef %v3float
%float_0_125 = OpConstant %float 0.125
%float_0_318309873 = OpConstant %float 0.318309873
         %65 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
       %main = OpFunction %void None %57
         %66 = OpLabel
         %67 = OpLoad %v2float %in_var_TEXCOORD0
         %68 = OpLoad %type_2d_image %GBufferAlbedo
         %69 = OpLoad %type_sampler %PointSampler
         %70 = OpSampledImage %type_sampled_image %68 %69
         %71 = OpImageSampleImplicitLod %v4float %70 %67 None
         %72 = OpLoad %type_2d_image %GBufferNormal
         %73 = OpLoad %type_sampler %PointSampler
         %74 = OpSampledImage %type_sampled_image %72 %73
         %75 = OpImageSampleImplicitLod %v4float %74 %67 None
         %76 = OpLoad %type_2d_image %GBufferWorldPos
         %77 = OpLoad %type_sampler %PointSampler
         %78 = OpSampledImage %type_sampled_image %76 %77
         %79 = OpImageSampleImplicitLod %v4float %78 %67 None
         %80 = OpLoad %type_2d_image %GBufferEmissive
         %81 = OpLoad %type_sampler %PointSampler
         %82 = OpSampledImage %type_sampled_image %80 %81
         %83 = OpImageSampleImplicitLod %v4float %82 %67 None
         %84 = OpVectorShuffle %v3float %71 %71 0 1 2
         %85 = OpCompositeExtract %float %71 3
         %86 = OpVectorShuffle %v3float %75 %75 0 1 2
         %87 = OpVectorTimesScalar %v3float %86 %float_2
         %88 = OpFSub %v3float %87 %27
         %89 = OpExtInst %v3float %1 Normalize %88
         %90 = OpCompositeExtract %float %75 3
         %91 = OpVectorShuffle %v3float %79 %79 0 1 2
         %92 = OpCompositeExtract %float %79 3
         %93 = OpVectorShuffle %v3float %83 %83 0 1 2
         %94 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_0
         %95 = OpLoad %v3float %94
         %96 = OpFSub %v3float %95 %91
         %97 = OpExtInst %v3float %1 Normalize %96
         %98 = OpCompositeConstruct %v3float %85 %85 %85
         %99 = OpExtInst %v3float %1 FMix %30 %84 %98
               OpBranch %100
        %100 = OpLabel
        %101 = OpPhi %v3float %62 %66 %102 %103
        %104 = OpPhi %v3float %32 %66 %105 %103
        %106 = OpPhi %int %int_0 %66 %107 %103
               OpLoopMerge %108 %103 None
               OpBranch %109
        %109 = OpLabel
        %110 = OpAccessChain %_ptr_Uniform_int %LightingParams %int_1
        %111 = OpLoad %int %110
        %112 = OpSLessThan %bool %106 %111
               OpSelectionMerge %113 None
               OpBranchConditional %112 %114 %113
        %114 = OpLabel
        %115 = OpSLessThan %bool %106 %int_32
               OpBranch %113
        %113 = OpLabel
        %116 = OpPhi %bool %false %109 %115 %114
               OpBranchConditional %116 %117 %108
        %117 = OpLabel
        %118 = OpSDiv %int %106 %int_4
        %119 = OpSRem %int %106 %int_4
        %120 = OpBitcast %uint %119
        %121 = OpAccessChain %_ptr_Uniform_int %LightData %int_3 %118 %120
        %122 = OpLoad %int %121
        %123 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_0 %106
        %124 = OpLoad %v4float %123
        %125 = OpVectorShuffle %v3float %124 %124 0 1 2
        %126 = OpAccessChain %_ptr_Uniform_float %LightData %int_0 %106 %int_3
        %127 = OpLoad %float %126
        %128 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_1 %106
        %129 = OpLoad %v4float %128
        %130 = OpVectorShuffle %v3float %129 %129 0 1 2
        %131 = OpAccessChain %_ptr_Uniform_float %LightData %int_1 %106 %int_3
        %132 = OpLoad %float %131
        %133 = OpIEqual %bool %122 %int_0
               OpSelectionMerge %134 None
               OpBranchConditional %133 %135 %136
        %135 = OpLabel
        %137 = OpFSub %v3float %125 %91
        %138 = OpExtInst %v3float %1 Normalize %137
        %139 = OpExtInst %float %1 Length %137
        %140 = OpFMul %float %139 %139
        %141 = OpFMul %float %127 %127
        %142 = OpFDiv %float %140 %141
        %143 = OpFAdd %float %float_1 %142
        %144 = OpFDiv %float %float_1 %143
               OpBranch %134
        %136 = OpLabel
        %145 = OpIEqual %bool %122 %int_1
               OpSelectionMerge %146 None
               OpBranchConditional %145 %147 %148
        %147 = OpLabel
        %149 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_2 %106
        %150 = OpLoad %v4float %149
        %151 = OpVectorShuffle %v3float %150 %150 0 1 2
        %152 = OpFNegate %v3float %151
        %153 = OpExtInst %v3float %1 Normalize %152
               OpBranch %146
        %148 = OpLabel
        %154 = OpIEqual %bool %122 %int_2
               OpSelectionMerge %155 None
               OpBranchConditional %154 %156 %155
        %156 = OpLabel
        %157 = OpFSub %v3float %125 %91
        %158 = OpExtInst %v3float %1 Normalize %157
        %159 = OpExtInst %float %1 Length %157
        %160 = OpFMul %float %159 %159
        %161 = OpFMul %float %127 %127
        %162 = OpFDiv %float %160 %161
        %163 = OpFAdd %float %float_1 %162
        %164 = OpFDiv %float %float_1 %163
        %165 = OpAccessChain %_ptr_Uniform_v4float %LightData %int_2 %106
        %166 = OpLoad %v4float %165
        %167 = OpVectorShuffle %v3float %166 %166 0 1 2
        %168 = OpExtInst %v3float %1 Normalize %167
        %169 = OpAccessChain %_ptr_Uniform_float %LightData %int_2 %106 %int_3
        %170 = OpLoad %float %169
        %171 = OpFNegate %v3float %168
        %172 = OpDot %float %158 %171
        %173 = OpExtInst %float %1 Cos %170
        %174 = OpFMul %float %170 %float_1_20000005
        %175 = OpExtInst %float %1 Cos %174
        %176 = OpFSub %float %173 %175
        %177 = OpFSub %float %172 %175
        %178 = OpFDiv %float %177 %176
        %179 = OpExtInst %float %1 FClamp %178 %float_0 %float_1
        %180 = OpFMul %float %164 %179
               OpBranch %155
        %155 = OpLabel
        %181 = OpPhi %float %float_1 %148 %180 %156
        %182 = OpPhi %v3float %101 %148 %158 %156
               OpBranch %146
        %146 = OpLabel
        %183 = OpPhi %float %float_1 %147 %181 %155
        %184 = OpPhi %v3float %153 %147 %182 %155
               OpBranch %134
        %134 = OpLabel
        %185 = OpPhi %float %144 %135 %183 %146
        %102 = OpPhi %v3float %138 %135 %184 %146
        %186 = OpFAdd %v3float %97 %102
        %187 = OpExtInst %v3float %1 Normalize %186
        %188 = OpVectorTimesScalar %v3float %130 %132
        %189 = OpVectorTimesScalar %v3float %188 %185
        %190 = OpFMul %float %90 %90
        %191 = OpFMul %float %190 %190
        %192 = OpDot %float %89 %187
        %193 = OpExtInst %float %1 NMax %192 %float_0
        %194 = OpFMul %float %193 %193
        %195 = OpFSub %float %191 %float_1
        %196 = OpFMul %float %194 %195
        %197 = OpFAdd %float %196 %float_1
        %198 = OpFMul %float %float_3_14159274 %197
        %199 = OpFMul %float %198 %197
        %200 = OpFDiv %float %191 %199
        %201 = OpDot %float %89 %97
        %202 = OpExtInst %float %1 NMax %201 %float_0
        %203 = OpDot %float %89 %102
        %204 = OpExtInst %float %1 NMax %203 %float_0
        %205 = OpFAdd %float %90 %float_1
        %206 = OpFMul %float %205 %205
        %207 = OpFMul %float %206 %float_0_125
        %208 = OpFSub %float %float_1 %207
        %209 = OpFMul %float %202 %208
        %210 = OpFAdd %float %209 %207
        %211 = OpFDiv %float %202 %210
        %212 = OpFMul %float %204 %208
        %213 = OpFAdd %float %212 %207
        %214 = OpFDiv %float %204 %213
        %215 = OpFMul %float %214 %211
        %216 = OpDot %float %187 %97
        %217 = OpExtInst %float %1 NMax %216 %float_0
        %218 = OpFSub %v3float %27 %99
        %219 = OpFSub %float %float_1 %217
        %220 = OpExtInst %float %1 FClamp %219 %float_0 %float_1
        %221 = OpExtInst %float %1 Pow %220 %float_5
        %222 = OpVectorTimesScalar %v3float %218 %221
        %223 = OpFAdd %v3float %99 %222
        %224 = OpFSub %v3float %27 %223
        %225 = OpFSub %float %float_1 %85
        %226 = OpVectorTimesScalar %v3float %224 %225
        %227 = OpFMul %float %200 %215
        %228 = OpVectorTimesScalar %v3float %223 %227
        %229 = OpFMul %float %float_4 %202
        %230 = OpFMul %float %229 %204
        %231 = OpFAdd %float %230 %float_9_99999975en05
        %232 = OpCompositeConstruct %v3float %231 %231 %231
        %233 = OpFDiv %v3float %228 %232
        %234 = OpFMul %v3float %226 %84
        %235 = OpFMul %v3float %234 %65
        %236 = OpFAdd %v3float %235 %233
        %237 = OpFMul %v3float %236 %189
        %238 = OpVectorTimesScalar %v3float %237 %204
        %105 = OpFAdd %v3float %104 %238
               OpBranch %103
        %103 = OpLabel
        %107 = OpIAdd %int %106 %int_1
               OpBranch %100
        %108 = OpLabel
        %239 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_2
        %240 = OpLoad %v3float %239
        %241 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_3
        %242 = OpLoad %float %241
        %243 = OpVectorTimesScalar %v3float %240 %242
        %244 = OpFMul %v3float %243 %84
        %245 = OpVectorTimesScalar %v3float %244 %92
        %246 = OpFAdd %v3float %245 %104
        %247 = OpFAdd %v3float %246 %93
        %248 = OpFAdd %v3float %247 %27
        %249 = OpFDiv %v3float %247 %248
        %250 = OpExtInst %v3float %1 Pow %249 %42
        %251 = OpCompositeExtract %float %250 0
        %252 = OpCompositeExtract %float %250 1
        %253 = OpCompositeExtract %float %250 2
        %254 = OpCompositeConstruct %v4float %251 %252 %253 %float_1
               OpStore %out_var_SV_TARGET %254
               OpReturn
               OpFunctionEnd
