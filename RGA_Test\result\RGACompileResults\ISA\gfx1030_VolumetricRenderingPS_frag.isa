_amdgpu_ps_main:
	s_mov_b32 m0, s2                                           // 000000000000: BEFC0302
	s_mov_b32 s8, s1                                           // 000000000004: BE880301
	v_interp_p1_f32_e32 v4, v0, attr0.y                        // 000000000008: C8100100
	v_interp_p1_f32_e32 v3, v0, attr0.x                        // 00000000000C: C80C0000
	v_interp_p1_f32_e32 v5, v0, attr0.z                        // 000000000010: C8140200
	s_getpc_b64 s[0:1]                                         // 000000000014: BE801F00
	s_mov_b64 s[6:7], exec                                     // 000000000018: BE86047E
	v_interp_p2_f32_e32 v4, v1, attr0.y                        // 00000000001C: C8110101
	v_interp_p2_f32_e32 v3, v1, attr0.x                        // 000000000020: C80D0001
	s_mov_b32 s9, s1                                           // 000000000024: BE890301
	v_interp_p2_f32_e32 v5, v1, attr0.z                        // 000000000028: C8150201
	s_load_dwordx4 s[16:19], s[8:9], null                      // 00000000002C: F4080404 FA000000
	v_mul_f32_e32 v2, v4, v4                                   // 000000000034: 10040904
	s_waitcnt lgkmcnt(0)                                       // 000000000038: BF8CC07F
	s_clause 0x2                                               // 00000000003C: BFA10002
	s_buffer_load_dword s30, s[16:19], 0x8                     // 000000000040: F4200788 FA000008
	s_buffer_load_dwordx2 s[2:3], s[16:19], 0x10               // 000000000048: F4240088 FA000010
	s_buffer_load_dword s4, s[16:19], 0x18                     // 000000000050: F4200108 FA000018
	v_fmac_f32_e32 v2, v3, v3                                  // 000000000058: 56040703
	s_buffer_load_dwordx2 s[28:29], s[16:19], null             // 00000000005C: F4240708 FA000000
	v_fmac_f32_e32 v2, v5, v5                                  // 000000000064: 56040B05
	v_rsq_f32_e32 v6, v2                                       // 000000000068: 7E0C5D02
	v_cmp_neq_f32_e32 vcc_lo, 0, v2                            // 00000000006C: 7C1A0480
	v_interp_p1_f32_e32 v2, v0, attr1.x                        // 000000000070: C8080400
	v_interp_p2_f32_e32 v2, v1, attr1.x                        // 000000000074: C8090401
	v_cndmask_b32_e32 v6, 0, v6, vcc_lo                        // 000000000078: 020C0C80
	v_mul_f32_e32 v3, v6, v3                                   // 00000000007C: 10060706
	v_mul_f32_e32 v4, v6, v4                                   // 000000000080: 10080906
	v_mul_f32_e32 v5, v6, v5                                   // 000000000084: 100A0B06
	v_interp_p1_f32_e32 v6, v0, attr1.y                        // 000000000088: C8180500
	v_interp_p1_f32_e32 v0, v0, attr1.z                        // 00000000008C: C8000600
	v_rcp_f32_e32 v7, v3                                       // 000000000090: 7E0E5503
	v_rcp_f32_e32 v8, v4                                       // 000000000094: 7E105504
	v_rcp_f32_e32 v9, v5                                       // 000000000098: 7E125505
	v_interp_p2_f32_e32 v6, v1, attr1.y                        // 00000000009C: C8190501
	v_interp_p2_f32_e32 v0, v1, attr1.z                        // 0000000000A0: C8010601
	s_waitcnt lgkmcnt(0)                                       // 0000000000A4: BF8CC07F
	v_fma_f32 v1, -v2, v7, s28                                 // 0000000000A8: D54B0001 20720F02
	v_fma_f32 v10, -v6, v8, s29                                // 0000000000B0: D54B000A 20761106
	v_fma_f32 v11, -v0, v9, s30                                // 0000000000B8: D54B000B 207A1300
	v_fma_f32 v7, -v2, v7, s2                                  // 0000000000C0: D54B0007 200A0F02
	v_fma_f32 v8, -v6, v8, s3                                  // 0000000000C8: D54B0008 200E1106
	v_fma_f32 v9, -v0, v9, s4                                  // 0000000000D0: D54B0009 20121300
	v_min_f32_e32 v12, v1, v7                                  // 0000000000D8: 1E180F01
	v_min_f32_e32 v13, v10, v8                                 // 0000000000DC: 1E1A110A
	v_min_f32_e32 v14, v11, v9                                 // 0000000000E0: 1E1C130B
	v_max_f32_e32 v1, v1, v7                                   // 0000000000E4: 20020F01
	v_max_f32_e32 v8, v10, v8                                  // 0000000000E8: 2010110A
	v_max_f32_e32 v9, v11, v9                                  // 0000000000EC: 2012130B
	v_max3_f32 v7, v12, v13, v14                               // 0000000000F0: D5540007 043A1B0C
	v_min3_f32 v1, v1, v8, v9                                  // 0000000000F8: D5510001 04261101
	v_cmp_gt_f32_e32 vcc_lo, v1, v7                            // 000000000100: 7C080F01
	v_cmp_lt_f32_e64 s0, 0, v1                                 // 000000000104: D4010000 00020280
	s_and_b64 s[0:1], vcc, s[0:1]                              // 00000000010C: 8780006A
	s_andn2_b64 s[0:1], exec, s[0:1]                           // 000000000110: 8A80007E
	s_andn2_b64 s[6:7], s[6:7], s[0:1]                         // 000000000114: 8A860006
	s_cbranch_scc0 _L0                                         // 000000000118: BF8400D5
	s_and_b64 exec, exec, s[6:7]                               // 00000000011C: 87FE067E
	s_buffer_load_dword s33, s[16:19], 0x1c                    // 000000000120: F4200848 FA00001C
	v_max_f32_e32 v7, 0, v7                                    // 000000000128: 200E0E80
	v_mov_b32_e32 v11, 0                                       // 00000000012C: 7E160280
	v_mov_b32_e32 v8, 1.0                                      // 000000000130: 7E1002F2
	v_mov_b32_e32 v10, 0                                       // 000000000134: 7E140280
	v_mov_b32_e32 v9, 0                                        // 000000000138: 7E120280
	v_cmp_lt_f32_e32 vcc_lo, v7, v1                            // 00000000013C: 7C020307
	s_waitcnt lgkmcnt(0)                                       // 000000000140: BF8CC07F
	s_cmp_gt_i32 s33, 0                                        // 000000000144: BF028021
	s_cselect_b64 s[0:1], -1, 0                                // 000000000148: 858080C1
	s_and_b64 s[0:1], vcc, s[0:1]                              // 00000000014C: 8780006A
	s_and_saveexec_b64 s[24:25], s[0:1]                        // 000000000150: BE982400
	s_cbranch_execz _L1                                        // 000000000154: BF8800C1
	s_clause 0x2                                               // 000000000158: BFA10002
	s_buffer_load_dword s31, s[16:19], 0x58                    // 00000000015C: F42007C8 FA000058
	s_buffer_load_dword s34, s[16:19], 0x2c                    // 000000000164: F4200888 FA00002C
	s_buffer_load_dwordx2 s[26:27], s[16:19], 0x50             // 00000000016C: F4240688 FA000050
	v_rcp_f32_e32 v8, s2                                       // 000000000174: 7E105402
	v_rcp_f32_e32 v10, s3                                      // 000000000178: 7E145403
	v_rcp_f32_e32 v11, s4                                      // 00000000017C: 7E165404
	s_clause 0x2                                               // 000000000180: BFA10002
	s_load_dwordx8 s[0:7], s[8:9], null                        // 000000000184: F40C0004 FA000000
	s_load_dwordx4 s[20:23], s[8:9], 0x20                      // 00000000018C: F4080504 FA000020
	s_load_dwordx8 s[8:15], s[8:9], 0x30                       // 000000000194: F40C0204 FA000030
	v_mov_b32_e32 v9, 0                                        // 00000000019C: 7E120280
	s_mov_b32 s38, 0xbf19999a                                  // 0000000001A0: BEA603FF BF19999A
	v_fma_f32 v12, s28, v8, s28                                // 0000000001A8: D54B000C 0072101C
	v_fma_f32 v13, s29, v10, s29                               // 0000000001B0: D54B000D 0076141D
	v_fma_f32 v11, s30, v11, s30                               // 0000000001B8: D54B000B 007A161E
	v_mov_b32_e32 v8, 1.0                                      // 0000000001C0: 7E1002F2
	v_mov_b32_e32 v10, 0                                       // 0000000001C4: 7E140280
	v_sub_f32_e32 v12, v2, v12                                 // 0000000001C8: 08181902
	v_sub_f32_e32 v13, v6, v13                                 // 0000000001CC: 081A1B06
	v_sub_f32_e32 v14, v0, v11                                 // 0000000001D0: 081C1700
	s_waitcnt lgkmcnt(0)                                       // 0000000001D4: BF8CC07F
	v_mul_f32_e64 v21, s31, s34                                // 0000000001D8: D5080015 0000441F
	v_mul_f32_e64 v15, 0x3dcccccd, s26                         // 0000000001E0: D508000F 000034FF 3DCCCCCD
	v_mul_f32_e64 v16, 0x3d4ccccd, s26                         // 0000000001EC: D5080010 000034FF 3D4CCCCD
	v_mul_f32_e64 v17, 0x3da3d70a, s26                         // 0000000001F8: D5080011 000034FF 3DA3D70A
	v_mov_b32_e32 v11, 0                                       // 000000000204: 7E160280
	v_mul_f32_e32 v18, 0.5, v21                                // 000000000208: 10242AF0
	v_mul_f32_e32 v19, 0x3e800000, v21                         // 00000000020C: 10262AFF 3E800000
	v_mul_f32_e32 v20, 0x3e000000, v21                         // 000000000214: 10282AFF 3E000000
	v_mul_f32_e32 v21, 0x3d800000, v21                         // 00000000021C: 102A2AFF 3D800000
	s_mov_b32 s26, 1                                           // 000000000224: BE9A0381
	s_mov_b64 s[28:29], 0                                      // 000000000228: BE9C0480
	s_branch _L2                                               // 00000000022C: BF820005
_L5:
	s_or_b64 exec, exec, s[34:35]                              // 000000000230: 88FE227E
	s_and_b64 s[30:31], exec, s[30:31]                         // 000000000234: 879E1E7E
	s_or_b64 s[28:29], s[30:31], s[28:29]                      // 000000000238: 889C1C1E
	s_andn2_b64 exec, exec, s[28:29]                           // 00000000023C: 8AFE1C7E
	s_cbranch_execz _L3                                        // 000000000240: BF880085
_L2:
	v_fma_f32 v22, v7, v5, v0                                  // 000000000244: D54B0016 04020B07
	v_fma_f32 v23, v7, v4, v6                                  // 00000000024C: D54B0017 041A0907
	v_fma_f32 v24, v7, v3, v2                                  // 000000000254: D54B0018 040A0707
	v_fma_f32 v25, v7, v3, v12                                 // 00000000025C: D54B0019 04320707
	v_fma_f32 v26, v7, v4, v13                                 // 000000000264: D54B001A 04360907
	v_fma_f32 v22, s27, v22, v17                               // 00000000026C: D54B0016 04462C1B
	v_fma_f32 v23, s27, v23, v16                               // 000000000274: D54B0017 04422E1B
	v_fma_f32 v24, s27, v24, v15                               // 00000000027C: D54B0018 043E301B
	v_fma_f32 v27, v7, v5, v14                                 // 000000000284: D54B001B 043A0B07
	image_sample_lz v25, v[25:27], s[0:7], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 00000000028C: F09C0110 00A01919
	image_sample_lz  v26, [v24, v23, v22], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 000000000294: F09C0112 00A21A18 00001617
	v_add_f32_e32 v27, v24, v24                                // 0000000002A0: 06363118
	v_add_f32_e32 v28, v23, v23                                // 0000000002A4: 06382F17
	v_add_f32_e32 v29, v22, v22                                // 0000000002A8: 063A2D16
	s_mov_b64 s[30:31], -1                                     // 0000000002AC: BE9E04C1
	s_mov_b64 s[36:37], -1                                     // 0000000002B0: BEA404C1
	s_mov_b64 s[34:35], exec                                   // 0000000002B4: BEA2047E
	image_sample_lz v27, v[27:29], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 0000000002B8: F09C0110 00A21B1B
	v_mul_f32_e32 v28, 4.0, v22                                // 0000000002C0: 10382CF6
	s_waitcnt vmcnt(1)                                         // 0000000002C4: BF8C3F71
	v_fmac_f32_e32 v25, v26, v18                               // 0000000002C8: 5632251A
	v_mul_f32_e32 v26, 4.0, v24                                // 0000000002CC: 103430F6
	s_waitcnt vmcnt(0)                                         // 0000000002D0: BF8C3F70
	v_fmac_f32_e32 v25, v27, v19                               // 0000000002D4: 5632271B
	v_mul_f32_e32 v27, 4.0, v23                                // 0000000002D8: 10362EF6
	image_sample_lz v26, v[26:28], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 0000000002DC: F09C0110 00A21A1A
	v_mul_f32_e32 v27, 0x41000000, v23                         // 0000000002E4: 10362EFF 41000000
	v_mul_f32_e32 v28, 0x41000000, v22                         // 0000000002EC: 10382CFF 41000000
	s_waitcnt vmcnt(0)                                         // 0000000002F4: BF8C3F70
	v_fmac_f32_e32 v25, v26, v20                               // 0000000002F8: 5632291A
	v_mul_f32_e32 v26, 0x41000000, v24                         // 0000000002FC: 103430FF 41000000
	image_sample_lz v22, v[26:28], s[8:15], s[20:23] dmask:0x1 dim:SQ_RSRC_IMG_3D// 000000000304: F09C0110 00A2161A
	s_waitcnt vmcnt(0)                                         // 00000000030C: BF8C3F70
	v_fmac_f32_e32 v25, v22, v21                               // 000000000310: 56322B16
	v_max_f32_e32 v22, 0, v25                                  // 000000000314: 202C3280
	v_cmpx_lt_f32_e32 0x3a83126f, v22                          // 000000000318: 7C222CFF 3A83126F
	s_cbranch_execz _L4                                        // 000000000320: BF88003F
	s_clause 0x3                                               // 000000000324: BFA10003
	s_buffer_load_dwordx2 s[36:37], s[16:19], 0x20             // 000000000328: F4240908 FA000020
	s_buffer_load_dword s39, s[16:19], 0x28                    // 000000000330: F42009C8 FA000028
	s_buffer_load_dword s48, s[16:19], 0xc                     // 000000000338: F4200C08 FA00000C
	s_buffer_load_dwordx8 s[40:47], s[16:19], 0x30             // 000000000340: F42C0A08 FA000030
	s_waitcnt lgkmcnt(0)                                       // 000000000348: BF8CC07F
	v_mul_f32_e64 v23, s37, s37                                // 00000000034C: D5080017 00004A25
	v_mul_f32_e64 v26, s44, s40                                // 000000000354: D508001A 0000502C
	v_mul_f32_e64 v27, s46, s42                                // 00000000035C: D508001B 0000542E
	v_fmac_f32_e64 v23, s36, s36                               // 000000000364: D52B0017 00004824
	v_fmac_f32_e64 v23, s39, s39                               // 00000000036C: D52B0017 00004E27
	v_rsq_f32_e32 v24, v23                                     // 000000000374: 7E305D17
	v_cmp_neq_f32_e32 vcc_lo, 0, v23                           // 000000000378: 7C1A2E80
	v_cndmask_b32_e32 v23, 0, v24, vcc_lo                      // 00000000037C: 022E3080
	v_mul_f32_e32 v24, s39, v23                                // 000000000380: 10302E27
	v_mul_f32_e32 v25, s36, v23                                // 000000000384: 10322E24
	v_mul_f32_e32 v23, s37, v23                                // 000000000388: 102E2E25
	v_mul_f32_e32 v24, v5, v24                                 // 00000000038C: 10303105
	v_fmac_f32_e32 v24, v3, v25                                // 000000000390: 56303303
	v_mul_f32_e64 v25, 0xbfb8aa3b, s48                         // 000000000394: D5080019 000060FF BFB8AA3B
	v_fmac_f32_e32 v24, v23, v4                                // 0000000003A0: 56300917
	v_fmaak_f32 v23, s38, v24, 0x3f8b851f                      // 0000000003A4: 5A2E3026 3F8B851F
	v_mul_f32_e32 v24, v23, v23                                // 0000000003AC: 10302F17
	v_mul_f32_e32 v23, v24, v23                                // 0000000003B0: 102E2F18
	v_fma_f32 v24, v22, s43, s47                               // 0000000003B4: D54B0018 00BC5716
	v_mul_f32_e32 v22, v22, v8                                 // 0000000003BC: 102C1116
	v_rsq_f32_e32 v23, v23                                     // 0000000003C0: 7E2E5D17
	v_mul_f32_e32 v25, v25, v24                                // 0000000003C4: 10323119
	v_mul_f32_e32 v22, s47, v22                                // 0000000003C8: 102C2C2F
	v_rcp_f32_e32 v24, v24                                     // 0000000003CC: 7E305518
	v_exp_f32_e32 v25, v25                                     // 0000000003D0: 7E324B19
	v_fma_f32 v23, 0xbbeaaefb, v23, 1.0                        // 0000000003D4: D54B0017 03CA2EFF BBEAAEFB
	v_mul_f32_e32 v22, v22, v23                                // 0000000003E0: 102C2F16
	v_mul_f32_e64 v23, s45, s41                                // 0000000003E4: D5080017 0000522D
	v_mul_f32_e32 v8, v25, v8                                  // 0000000003EC: 10101119
	v_fma_f32 v26, v26, v22, -v25                              // 0000000003F0: D54B001A 84662D1A
	v_fma_f32 v23, v23, v22, -v25                              // 0000000003F8: D54B0017 84662D17
	v_fma_f32 v22, v27, v22, -v25                              // 000000000400: D54B0016 84662D1B
	v_cmp_ngt_f32_e32 vcc_lo, 0x3c23d70a, v8                   // 000000000408: 7C1610FF 3C23D70A
	v_fmac_f32_e32 v9, v26, v24                                // 000000000410: 5612311A
	v_fmac_f32_e32 v10, v23, v24                               // 000000000414: 56143117
	v_fmac_f32_e32 v11, v22, v24                               // 000000000418: 56163116
	s_orn2_b64 s[36:37], vcc, exec                             // 00000000041C: 8BA47E6A
_L4:
	s_or_b64 exec, exec, s[34:35]                              // 000000000420: 88FE227E
	s_and_saveexec_b64 s[34:35], s[36:37]                      // 000000000424: BEA22424
	s_cbranch_execz _L5                                        // 000000000428: BF88FF81
	s_buffer_load_dword s30, s[16:19], 0xc                     // 00000000042C: F4200788 FA00000C
	s_cmp_ge_i32 s26, s33                                      // 000000000434: BF03211A
	s_waitcnt lgkmcnt(0)                                       // 000000000438: BF8CC07F
	v_add_f32_e32 v7, s30, v7                                  // 00000000043C: 060E0E1E
	s_cselect_b64 s[30:31], -1, 0                              // 000000000440: 859E80C1
	s_add_i32 s26, s26, 1                                      // 000000000444: 811A811A
	v_cmp_nlt_f32_e32 vcc_lo, v7, v1                           // 000000000448: 7C1C0307
	s_or_b64 s[30:31], vcc, s[30:31]                           // 00000000044C: 889E1E6A
	s_orn2_b64 s[30:31], s[30:31], exec                        // 000000000450: 8B9E7E1E
	s_branch _L5                                               // 000000000454: BF82FF76
_L3:
	s_or_b64 exec, exec, s[28:29]                              // 000000000458: 88FE1C7E
_L1:
	s_or_b64 exec, exec, s[24:25]                              // 00000000045C: 88FE187E
	v_sub_f32_e32 v0, 1.0, v8                                  // 000000000460: 080010F2
	exp mrt0 v9, v10, v11, v0 done vm                          // 000000000464: F800180F 000B0A09
	s_endpgm                                                   // 00000000046C: BF810000
_L0:
	s_mov_b64 exec, 0                                          // 000000000470: BEFE0480
	exp null off, off, off, off done vm                        // 000000000474: F8001890 00000000
	s_endpgm                                                   // 00000000047C: BF810000
