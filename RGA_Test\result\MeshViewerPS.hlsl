// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct ViewModes
{
  static const int InputPos = 0;
  static const int OutputPos = 1;
  static const int VertexID = 2;
  static const int InstanceID = 3;
  static const int Color = 4;
  static const int Normal = 5;
  static const int Tangent = 6;
  static const int UV = 7;
  static const int MaterialID = 8;
  static const int ShapeID = 9;
  static const int ViewerColor = 10;
  static const int PlasticShaded = 11;
};

struct Struct__MeshViewerPSCB
{
  float3 CameraPos;
  int ViewMode;
};

struct PSInput
{
  float4 Position : SV_POSITION;
  float4 Color : TEXCOORD0;
  float3 Normal : NORMAL;
  float3 WorldPos : POSITION;
};

struct PSOutput
{
  float4 colorTargetF32 : SV_Target0;
};

ConstantBuffer<Struct__MeshViewerPSCB> _MeshViewerPSCB : register(b0);
float3 fresnelSchlick(float cosTheta, float3 F0)
{
  return F0 + (1.0f - F0 * pow(1.0f - cosTheta, 5.0f));
}

float D_GGX(float NoH, float alpha)
{
  float alpha2 = (alpha * alpha);
  float d = ((NoH * NoH) * alpha2 - 1.0f) + 1.0f;
  return (alpha2 * 0.31830988618379067f) / (d * d);
}

float G1_GGX_Schlick(float NoV, float alpha)
{
  float k = alpha / 2.0f;
  return max(NoV, 0.001f) / (NoV * 1.0f - k) + k;
}

float G_Smith(float NoV, float NoL, float alpha)
{
  return (G1_GGX_Schlick(NoL, alpha) * G1_GGX_Schlick(NoV, alpha));
}

float3 microfacetBRDF(float3 L, float3 V, float3 N, float metallic, float roughness, float3 baseColor, float specularlevel)
{
  float3 H = normalize(V + L);
  float NoV = clamp(dot(N, V), 0.0f, 1.0f);
  float NoL = clamp(dot(N, L), 0.0f, 1.0f);
  float NoH = clamp(dot(N, H), 0.0f, 1.0f);
  float VoH = clamp(dot(V, H), 0.0f, 1.0f);
  float3 f0 = (0.16f * (specularlevel * specularlevel));
  f0 = lerp(f0, baseColor, metallic);
  float alpha = (roughness * roughness);
  float3 F = fresnelSchlick(VoH, f0);
  float D = D_GGX(NoH, alpha);
  float G = G_Smith(NoV, NoL, alpha);
  float3 specular = ((F * D) * G) / ((4.0f * max(NoV, 0.001f)) * max(NoL, 0.001f));
  float3 rhoD = baseColor;
  rhoD *= 1.0f - F;
  rhoD *= 1.0f - metallic;
  float3 diffuse = (rhoD * 0.31830988618379067f);
  return diffuse + specular;
}

PSOutput main(PSInput input)
{
  PSOutput ret = (PSOutput)0;
  if (_MeshViewerPSCB.ViewMode == ViewModes::PlasticShaded)
  {
    float3 cameraPosition = _MeshViewerPSCB.CameraPos;
    float3 viewDirection = normalize(cameraPosition - input.WorldPos);
    float3 lightPosition = float3(2.0f, 3.0f, 2.0f);
    float lightRadius = 0.5f;
    float3 lightColor = float3(1.0f, 1.0f, 1.0f);
    float lightIntensity = 2.0f;
    float3 basecolor = float3(0.8f, 0.8f, 0.8f);
    float roughness = 0.4f;
    float specularlevel = 0.5f;
    float3 normal = input.Normal;
    float metallic = 0.0f;
    float3 lightDirection = normalize(lightPosition - input.WorldPos);
    float distanceToLight = length(lightPosition - input.WorldPos);
    float attenuation = 1.0f / 1.0f + (distanceToLight * distanceToLight);
    float3 lightPerpendicularIrradiance = ((lightIntensity * lightColor) * attenuation);
    float3 irradiance = (max(dot(lightDirection, normal), 0.0f) * lightPerpendicularIrradiance);
    float3 brdf = microfacetBRDF(lightDirection, viewDirection, normal, metallic, roughness, basecolor, specularlevel);
    float3 radiance = (irradiance * brdf);
    radiance += float3(0.02f, 0.02f, 0.02f);
    ret.colorTargetF32 = float4(radiance, 1.0f);
  }
  else
  {
    ret.colorTargetF32 = input.Color;
  }
  return ret;
}

