// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct MeshVertex
{
  float3 Position;
  float3 Normal;
  float2 TexCoord;
  float4 Color;
};

cbuffer ComputeParams : register(b0)
{
  uint ParticleCount;
  uint MaxParticles;
  float DeltaTime;
  float Time;
  float3 Gravity;
  float Damping;
  float3 EmitterPosition;
  float EmissionRate;
  float3 EmitterDirection;
  float EmissionSpeed;
  float2 LifetimeRange;
  float2 SizeRange;
  uint FrameCount;
  float NoiseScale;
  float NoiseStrength;
  uint _padding;
}

RWStructuredBuffer<MeshVertex> VertexBuffer : register(u0);
RWStructuredBuffer<uint> IndexBuffer : register(u1);
float Hash(float n)
{
  return frac((sin(n) * 43758.5453f));
}

float Noise(float3 x)
{
  float3 p = floor(x);
  float3 f = frac(x);
  f = ((f * f) * 3.0f - (2.0f * f));
  float n = p.x + (p.y * 57.0f) + (113.0f * p.z);
  return lerp(lerp(lerp(Hash(n + 0.0f), Hash(n + 1.0f), f.x), lerp(Hash(n + 57.0f), Hash(n + 58.0f), f.x), f.y), lerp(lerp(Hash(n + 113.0f), Hash(n + 114.0f), f.x), lerp(Hash(n + 170.0f), Hash(n + 171.0f), f.x), f.y), f.z);
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
  uint width = 64;
  uint height = 64;
  if (id.x >= width || id.y >= height)
    return ;
  uint vertexIndex = (id.y * width) + id.x;
  float2 uv = float2(id.xy) / float2(width - 1, height - 1);
  float3 worldPos = float3((uv.x * 100.0f) - 50.0f, 0, (uv.y * 100.0f) - 50.0f);
  float height1 = (Noise((worldPos * 0.01f) + (Time * 0.1f)) * 10.0f);
  float height2 = (Noise((worldPos * 0.05f) + (Time * 0.05f)) * 2.0f);
  worldPos.y = height1 + height2;
  float3 normal = float3(0, 1, 0);
  if (id.x > 0 && id.x < width - 1 && id.y > 0 && id.y < height - 1)
  {
    float hL = (Noise((worldPos + float3(-1, 0, 0) * 0.01f) + (Time * 0.1f)) * 10.0f);
    float hR = (Noise((worldPos + float3(1, 0, 0) * 0.01f) + (Time * 0.1f)) * 10.0f);
    float hD = (Noise((worldPos + float3(0, 0, -1) * 0.01f) + (Time * 0.1f)) * 10.0f);
    float hU = (Noise((worldPos + float3(0, 0, 1) * 0.01f) + (Time * 0.1f)) * 10.0f);
    normal = normalize(float3(hL - hR, 2.0f, hD - hU));
  }
  MeshVertex vertex;
  vertex.Position = worldPos;
  vertex.Normal = normal;
  vertex.TexCoord = uv;
  vertex.Color = float4(uv, 0.5f, 1.0f);
  VertexBuffer[vertexIndex] = vertex;
  if (id.x < width - 1 && id.y < height - 1)
  {
    uint indexBase = ((id.y * width - 1) + id.x * 6);
    uint v0 = (id.y * width) + id.x;
    uint v1 = (id.y * width) + id.x + 1;
    uint v2 = (id.y + 1 * width) + id.x;
    uint v3 = (id.y + 1 * width) + id.x + 1;
    IndexBuffer[indexBase + 0] = v0;
    IndexBuffer[indexBase + 1] = v2;
    IndexBuffer[indexBase + 2] = v1;
    IndexBuffer[indexBase + 3] = v1;
    IndexBuffer[indexBase + 4] = v2;
    IndexBuffer[indexBase + 5] = v3;
  }
}

