_amdgpu_cs_main:
	s_mov_b32 s12, s1                                          // 000000000000: BE8C0301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v4, s2, 3, v0                               // 000000000008: D7460004 04010602
	s_mov_b32 s13, s1                                          // 000000000010: BE8D0301
	s_mov_b64 s[4:5], exec                                     // 000000000014: BE84047E
	s_load_dwordx4 s[8:11], s[12:13], null                     // 000000000018: F4080206 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s0, s[8:11], null                      // 000000000024: F4200004 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cmpx_gt_u32_e64 s0, v4                                   // 000000000030: D4D4007E 00020800
	s_cbranch_execz _L0                                        // 000000000038: BF88030D
	s_buffer_load_dword s1, s[8:11], 0x4                       // 00000000003C: F4200044 FA000004
	v_lshl_add_u32 v5, s3, 3, v1                               // 000000000044: D7460005 04050603
	s_waitcnt lgkmcnt(0)                                       // 00000000004C: BF8CC07F
	v_cmp_gt_u32_e32 vcc_lo, s1, v5                            // 000000000050: 7D880A01
	s_and_b64 exec, exec, vcc                                  // 000000000054: 87FE6A7E
	s_cbranch_execz _L0                                        // 000000000058: BF880305
	s_buffer_load_dword s16, s[8:11], 0x1c                     // 00000000005C: F4200404 FA00001C
	v_cvt_f32_u32_e32 v8, s0                                   // 000000000064: 7E100C00
	v_cvt_f32_u32_e32 v9, s1                                   // 000000000068: 7E120C01
	s_clause 0x1                                               // 00000000006C: BFA10001
	s_load_dwordx8 s[0:7], s[12:13], null                      // 000000000070: F40C0006 FA000000
	s_load_dwordx4 s[12:15], s[12:13], 0x20                    // 000000000078: F4080306 FA000020
	v_cvt_f32_u32_e32 v2, v4                                   // 000000000080: 7E040D04
	v_cvt_f32_u32_e32 v3, v5                                   // 000000000084: 7E060D05
	v_rcp_iflag_f32_e32 v0, v8                                 // 000000000088: 7E005708
	v_rcp_iflag_f32_e32 v1, v9                                 // 00000000008C: 7E025709
	v_mul_f32_e32 v6, v2, v0                                   // 000000000090: 100C0102
	v_mul_f32_e32 v7, v3, v1                                   // 000000000094: 100E0303
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	s_cmp_lg_u32 s16, 0                                        // 00000000009C: BF078010
	s_cbranch_scc0 _L1                                         // 0000000000A0: BF840061
	s_cmp_lg_u32 s16, 1                                        // 0000000000A4: BF078110
	s_cbranch_scc0 _L2                                         // 0000000000A8: BF840060
	s_cmp_lg_u32 s16, 2                                        // 0000000000AC: BF078210
	s_cbranch_scc0 _L3                                         // 0000000000B0: BF84005F
	image_sample_lz v[0:3], v[6:7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000B4: F09C0F08 00600006
	s_cmp_lg_u32 s16, 3                                        // 0000000000BC: BF078310
	s_cbranch_scc1 _L4                                         // 0000000000C0: BF850057
	v_rcp_f32_e32 v18, v8                                      // 0000000000C4: 7E245508
	v_rcp_f32_e32 v14, v9                                      // 0000000000C8: 7E1C5509
	v_sub_f32_e32 v21, v6, v18                                 // 0000000000CC: 082A2506
	v_fma_f32 v23, 0, v14, v7                                  // 0000000000D0: D54B0017 041E1C80
	v_sub_f32_e32 v22, v7, v14                                 // 0000000000D8: 082C1D07
	v_fma_f32 v24, 0, v18, v6                                  // 0000000000DC: D54B0018 041A2480
	v_add_f32_e32 v25, v14, v7                                 // 0000000000E4: 06320F0E
	s_clause 0x2                                               // 0000000000E8: BFA10002
	image_sample_lz  v[0:3], [v21, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000EC: F09C0F0A 00600015 00000017
	image_sample_lz  v[10:13], [v24, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000000F8: F09C0F0A 00600A18 00000016
	image_sample_lz v[14:17], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000104: F09C0F08 00600E15
	v_add_f32_e32 v26, v18, v6                                 // 00000000010C: 06340D12
	image_sample_lz  v[18:21], [v21, v25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000110: F09C0F0A 00601215 00000019
	s_waitcnt vmcnt(2)                                         // 00000000011C: BF8C3F72
	v_add_f32_e32 v27, v10, v0                                 // 000000000120: 0636010A
	v_add_f32_e32 v28, v11, v1                                 // 000000000124: 0638030B
	v_add_f32_e32 v29, v12, v2                                 // 000000000128: 063A050C
	v_add_f32_e32 v30, v13, v3                                 // 00000000012C: 063C070D
	s_clause 0x1                                               // 000000000130: BFA10001
	image_sample_lz  v[0:3], [v26, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000134: F09C0F0A 0060001A 00000016
	image_sample_lz  v[10:13], [v26, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000140: F09C0F0A 00600A1A 00000017
	s_waitcnt vmcnt(3)                                         // 00000000014C: BF8C3F73
	v_add_f32_e32 v14, v14, v14                                // 000000000150: 061C1D0E
	v_add_f32_e32 v15, v15, v15                                // 000000000154: 061E1F0F
	v_add_f32_e32 v16, v16, v16                                // 000000000158: 06202110
	v_add_f32_e32 v17, v17, v17                                // 00000000015C: 06222311
	s_waitcnt vmcnt(1)                                         // 000000000160: BF8C3F71
	v_fma_f32 v14, v0, 0, -v14                                 // 000000000164: D54B000E 84390100
	v_fma_f32 v15, v1, 0, -v15                                 // 00000000016C: D54B000F 843D0101
	v_fma_f32 v22, v2, 0, -v16                                 // 000000000174: D54B0016 84410102
	v_fma_f32 v31, v3, 0, -v17                                 // 00000000017C: D54B001F 84450103
	image_sample_lz  v[0:3], [v24, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000184: F09C0F0A 00600018 00000017
	v_sub_f32_e32 v27, v14, v27                                // 000000000190: 0836370E
	v_sub_f32_e32 v28, v15, v28                                // 000000000194: 0838390F
	image_sample_lz v[14:17], v[24:25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000198: F09C0F08 00600E18
	v_sub_f32_e32 v29, v22, v29                                // 0000000001A0: 083A3B16
	image_sample_lz  v[22:25], [v26, v25], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000001A4: F09C0F0A 0060161A 00000019
	v_sub_f32_e32 v30, v31, v30                                // 0000000001B0: 083C3D1F
	s_waitcnt vmcnt(2)                                         // 0000000001B4: BF8C3F72
	v_add_f32_e32 v0, v0, v27                                  // 0000000001B8: 06003700
	v_add_f32_e32 v1, v1, v28                                  // 0000000001BC: 06023901
	v_add_f32_e32 v2, v2, v29                                  // 0000000001C0: 06043B02
	v_add_f32_e32 v3, v3, v30                                  // 0000000001C4: 06063D03
	v_add_f32_e32 v0, v10, v0                                  // 0000000001C8: 0600010A
	v_add_f32_e32 v1, v11, v1                                  // 0000000001CC: 0602030B
	v_add_f32_e32 v2, v12, v2                                  // 0000000001D0: 0604050C
	v_add_f32_e32 v3, v13, v3                                  // 0000000001D4: 0606070D
	v_fmac_f32_e32 v0, 0, v18                                  // 0000000001D8: 56002480
	v_fmac_f32_e32 v1, 0, v19                                  // 0000000001DC: 56022680
	v_fmac_f32_e32 v2, 0, v20                                  // 0000000001E0: 56042880
	v_fmac_f32_e32 v3, 0, v21                                  // 0000000001E4: 56062A80
	s_waitcnt vmcnt(1)                                         // 0000000001E8: BF8C3F71
	v_add_f32_e32 v0, v14, v0                                  // 0000000001EC: 0600010E
	v_add_f32_e32 v1, v15, v1                                  // 0000000001F0: 0602030F
	v_add_f32_e32 v2, v16, v2                                  // 0000000001F4: 06040510
	v_add_f32_e32 v3, v17, v3                                  // 0000000001F8: 06060711
	s_waitcnt vmcnt(0)                                         // 0000000001FC: BF8C3F70
	v_fmac_f32_e32 v0, 2.0, v22                                // 000000000200: 56002CF4
	v_fmac_f32_e32 v1, 2.0, v23                                // 000000000204: 56022EF4
	v_fmac_f32_e32 v2, 2.0, v24                                // 000000000208: 560430F4
	v_fmac_f32_e32 v3, 2.0, v25                                // 00000000020C: 560632F4
	v_add_f32_e32 v0, 0.5, v0                                  // 000000000210: 060000F0
	v_add_f32_e32 v1, 0.5, v1                                  // 000000000214: 060202F0
	v_add_f32_e32 v2, 0.5, v2                                  // 000000000218: 060404F0
	v_add_f32_e32 v3, 0.5, v3                                  // 00000000021C: 060606F0
_L4:
	s_mov_b64 s[16:17], 0                                      // 000000000220: BE900480
	s_branch _L5                                               // 000000000224: BF820003
_L1:
	s_branch _L6                                               // 000000000228: BF8200E0
_L2:
	s_branch _L7                                               // 00000000022C: BF82008B
_L3:
	s_mov_b64 s[16:17], -1                                     // 000000000230: BE9004C1
_L5:
	s_andn2_b64 vcc, exec, s[16:17]                            // 000000000234: 8AEA107E
	s_cbranch_vccnz _L8                                        // 000000000238: BF870087
	v_rcp_f32_e32 v14, v8                                      // 00000000023C: 7E1C5508
	v_rcp_f32_e32 v19, v9                                      // 000000000240: 7E265509
	v_sub_f32_e32 v26, v6, v14                                 // 000000000244: 08341D06
	v_sub_f32_e32 v27, v7, v19                                 // 000000000248: 08362707
	v_fma_f32 v28, 0, v14, v6                                  // 00000000024C: D54B001C 041A1C80
	v_add_f32_e32 v25, v14, v6                                 // 000000000254: 06320D0E
	v_add_f32_e32 v29, v19, v7                                 // 000000000258: 063A0F13
	s_clause 0x2                                               // 00000000025C: BFA10002
	image_sample_lz v[0:3], v[26:27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000260: F09C0F08 0060001A
	image_sample_lz  v[10:13], [v28, v27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000268: F09C0F0A 00600A1C 0000001B
	image_sample_lz  v[14:17], [v25, v27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000274: F09C0F0A 00600E19 0000001B
	v_fma_f32 v27, 0, v19, v7                                  // 000000000280: D54B001B 041E2680
	s_waitcnt vmcnt(1)                                         // 000000000288: BF8C3F71
	v_fmamk_f32 v21, v10, 0x80000000, v0                       // 00000000028C: 582A010A 80000000
	v_fmamk_f32 v22, v11, 0x80000000, v1                       // 000000000294: 582C030B 80000000
	v_fmamk_f32 v23, v12, 0x80000000, v2                       // 00000000029C: 582E050C 80000000
	v_fmamk_f32 v24, v13, 0x80000000, v3                       // 0000000002A4: 5830070D 80000000
	s_waitcnt vmcnt(0)                                         // 0000000002AC: BF8C3F70
	v_sub_f32_e32 v21, v14, v21                                // 0000000002B0: 082A2B0E
	v_add_f32_e32 v14, v0, v14                                 // 0000000002B4: 061C1D00
	v_sub_f32_e32 v22, v15, v22                                // 0000000002B8: 082C2D0F
	v_sub_f32_e32 v23, v16, v23                                // 0000000002BC: 082E2F10
	v_sub_f32_e32 v24, v17, v24                                // 0000000002C0: 08303111
	v_fmac_f32_e32 v14, 2.0, v10                               // 0000000002C4: 561C14F4
	v_add_f32_e32 v10, v1, v15                                 // 0000000002C8: 06141F01
	v_fmac_f32_e32 v10, 2.0, v11                               // 0000000002CC: 561416F4
	v_add_f32_e32 v11, v2, v16                                 // 0000000002D0: 06162102
	v_fmac_f32_e32 v11, 2.0, v12                               // 0000000002D4: 561618F4
	v_add_f32_e32 v12, v3, v17                                 // 0000000002D8: 06182303
	image_sample_lz v[0:3], v[26:27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000002DC: F09C0F08 0060001A
	v_fmac_f32_e32 v12, 2.0, v13                               // 0000000002E4: 56181AF4
	s_waitcnt vmcnt(0)                                         // 0000000002E8: BF8C3F70
	v_fmac_f32_e32 v21, -2.0, v0                               // 0000000002EC: 562A00F5
	v_fmac_f32_e32 v22, -2.0, v1                               // 0000000002F0: 562C02F5
	v_fmac_f32_e32 v23, -2.0, v2                               // 0000000002F4: 562E04F5
	v_fmac_f32_e32 v24, -2.0, v3                               // 0000000002F8: 563006F5
	v_fmac_f32_e32 v14, 0x80000000, v0                         // 0000000002FC: 561C00FF 80000000
	v_fmac_f32_e32 v10, 0x80000000, v1                         // 000000000304: 561402FF 80000000
	v_fmac_f32_e32 v11, 0x80000000, v2                         // 00000000030C: 561604FF 80000000
	v_fmac_f32_e32 v12, 0x80000000, v3                         // 000000000314: 561806FF 80000000
	image_sample_lz  v[0:3], [v28, v27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000031C: F09C0F0A 0060001C 0000001B
	s_waitcnt vmcnt(0)                                         // 000000000328: BF8C3F70
	v_fmac_f32_e32 v21, 0, v0                                  // 00000000032C: 562A0080
	v_fmac_f32_e32 v22, 0, v1                                  // 000000000330: 562C0280
	v_fmac_f32_e32 v23, 0, v2                                  // 000000000334: 562E0480
	v_fmac_f32_e32 v24, 0, v3                                  // 000000000338: 56300680
	v_fmac_f32_e32 v14, 0x80000000, v0                         // 00000000033C: 561C00FF 80000000
	v_fmac_f32_e32 v10, 0x80000000, v1                         // 000000000344: 561402FF 80000000
	v_fmac_f32_e32 v11, 0x80000000, v2                         // 00000000034C: 561604FF 80000000
	v_fmac_f32_e32 v12, 0x80000000, v3                         // 000000000354: 561806FF 80000000
	image_sample_lz  v[0:3], [v25, v27], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000035C: F09C0F0A 00600019 0000001B
	s_waitcnt vmcnt(0)                                         // 000000000368: BF8C3F70
	v_fmac_f32_e32 v21, 2.0, v0                                // 00000000036C: 562A00F4
	v_fmac_f32_e32 v22, 2.0, v1                                // 000000000370: 562C02F4
	v_fmac_f32_e32 v23, 2.0, v2                                // 000000000374: 562E04F4
	v_fmac_f32_e32 v24, 2.0, v3                                // 000000000378: 563006F4
	v_fmac_f32_e32 v14, 0x80000000, v0                         // 00000000037C: 561C00FF 80000000
	v_fmac_f32_e32 v10, 0x80000000, v1                         // 000000000384: 561402FF 80000000
	v_fmac_f32_e32 v11, 0x80000000, v2                         // 00000000038C: 561604FF 80000000
	v_fmac_f32_e32 v12, 0x80000000, v3                         // 000000000394: 561806FF 80000000
	image_sample_lz  v[0:3], [v26, v29], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000039C: F09C0F0A 0060001A 0000001D
	s_waitcnt vmcnt(0)                                         // 0000000003A8: BF8C3F70
	v_sub_f32_e32 v15, v21, v0                                 // 0000000003AC: 081E0115
	v_sub_f32_e32 v16, v22, v1                                 // 0000000003B0: 08200316
	v_sub_f32_e32 v17, v23, v2                                 // 0000000003B4: 08220517
	v_sub_f32_e32 v18, v24, v3                                 // 0000000003B8: 08240718
	v_sub_f32_e32 v14, v0, v14                                 // 0000000003BC: 081C1D00
	v_sub_f32_e32 v10, v1, v10                                 // 0000000003C0: 08141501
	v_sub_f32_e32 v11, v2, v11                                 // 0000000003C4: 08161702
	v_sub_f32_e32 v12, v3, v12                                 // 0000000003C8: 08181903
	image_sample_lz v[0:3], v[28:29], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000003CC: F09C0F08 0060001C
	s_waitcnt vmcnt(0)                                         // 0000000003D4: BF8C3F70
	v_fmac_f32_e32 v15, 0, v0                                  // 0000000003D8: 561E0080
	v_fmac_f32_e32 v16, 0, v1                                  // 0000000003DC: 56200280
	v_fmac_f32_e32 v17, 0, v2                                  // 0000000003E0: 56220480
	v_fmac_f32_e32 v18, 0, v3                                  // 0000000003E4: 56240680
	v_fmac_f32_e32 v14, 2.0, v0                                // 0000000003E8: 561C00F4
	v_fmac_f32_e32 v10, 2.0, v1                                // 0000000003EC: 561402F4
	v_fmac_f32_e32 v11, 2.0, v2                                // 0000000003F0: 561604F4
	v_fmac_f32_e32 v12, 2.0, v3                                // 0000000003F4: 561806F4
	image_sample_lz  v[0:3], [v25, v29], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000003F8: F09C0F0A 00600019 0000001D
	s_waitcnt vmcnt(0)                                         // 000000000404: BF8C3F70
	v_add_f32_e32 v13, v0, v15                                 // 000000000408: 061A1F00
	v_add_f32_e32 v15, v1, v16                                 // 00000000040C: 061E2101
	v_add_f32_e32 v16, v2, v17                                 // 000000000410: 06202302
	v_add_f32_e32 v17, v3, v18                                 // 000000000414: 06222503
	v_add_f32_e32 v0, v0, v14                                  // 000000000418: 06001D00
	v_add_f32_e32 v1, v1, v10                                  // 00000000041C: 06021501
	v_add_f32_e32 v2, v2, v11                                  // 000000000420: 06041702
	v_add_f32_e32 v3, v3, v12                                  // 000000000424: 06061903
	v_mul_f32_e32 v0, v0, v0                                   // 000000000428: 10000100
	v_mul_f32_e32 v1, v1, v1                                   // 00000000042C: 10020301
	v_mul_f32_e32 v2, v2, v2                                   // 000000000430: 10040502
	v_mul_f32_e32 v3, v3, v3                                   // 000000000434: 10060703
	v_fmac_f32_e32 v0, v13, v13                                // 000000000438: 56001B0D
	v_fmac_f32_e32 v1, v15, v15                                // 00000000043C: 56021F0F
	v_fmac_f32_e32 v2, v16, v16                                // 000000000440: 56042110
	v_fmac_f32_e32 v3, v17, v17                                // 000000000444: 56062311
	v_sqrt_f32_e32 v0, v0                                      // 000000000448: 7E006700
	v_sqrt_f32_e32 v1, v1                                      // 00000000044C: 7E026701
	v_sqrt_f32_e32 v2, v2                                      // 000000000450: 7E046702
	v_sqrt_f32_e32 v3, v3                                      // 000000000454: 7E066703
_L8:
	s_cbranch_execnz _L9                                       // 000000000458: BF890053
_L7:
	v_rcp_f32_e32 v14, v8                                      // 00000000045C: 7E1C5508
	v_rcp_f32_e32 v23, v9                                      // 000000000460: 7E2E5509
	v_sub_f32_e32 v22, v6, v14                                 // 000000000464: 082C1D06
	v_sub_f32_e32 v28, v7, v23                                 // 000000000468: 08382F07
	v_fma_f32 v27, 0, v14, v6                                  // 00000000046C: D54B001B 041A1C80
	v_add_f32_e32 v25, v14, v6                                 // 000000000474: 06320D0E
	v_fma_f32 v26, 0, v23, v7                                  // 000000000478: D54B001A 041E2E80
	v_add_f32_e32 v23, v23, v7                                 // 000000000480: 062E0F17
	s_clause 0x3                                               // 000000000484: BFA10003
	image_sample_lz  v[0:3], [v22, v28], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000488: F09C0F0A 00600016 0000001C
	image_sample_lz v[10:13], v[27:28], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000494: F09C0F08 00600A1B
	image_sample_lz  v[14:17], [v25, v28], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000049C: F09C0F0A 00600E19 0000001C
	image_sample_lz  v[18:21], [v22, v26], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004A8: F09C0F0A 00601216 0000001A
	s_waitcnt vmcnt(1)                                         // 0000000004B4: BF8C3F71
	v_add_f32_e32 v14, v0, v14                                 // 0000000004B8: 061C1D00
	v_add_f32_e32 v15, v1, v15                                 // 0000000004BC: 061E1F01
	v_add_f32_e32 v16, v2, v16                                 // 0000000004C0: 06202102
	v_add_f32_e32 v17, v3, v17                                 // 0000000004C4: 06222303
	image_sample_lz  v[0:3], [v27, v26], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004C8: F09C0F0A 0060001B 0000001A
	s_waitcnt vmcnt(1)                                         // 0000000004D4: BF8C3F71
	v_add_f32_e32 v10, v10, v18                                // 0000000004D8: 0614250A
	v_add_f32_e32 v11, v11, v19                                // 0000000004DC: 0616270B
	v_add_f32_e32 v12, v12, v20                                // 0000000004E0: 0618290C
	v_add_f32_e32 v13, v13, v21                                // 0000000004E4: 061A2B0D
	s_waitcnt vmcnt(0)                                         // 0000000004E8: BF8C3F70
	v_mul_f32_e32 v18, 0x40a00000, v0                          // 0000000004EC: 102400FF 40A00000
	v_mul_f32_e32 v19, 0x40a00000, v1                          // 0000000004F4: 102602FF 40A00000
	v_mul_f32_e32 v20, 0x40a00000, v2                          // 0000000004FC: 102804FF 40A00000
	v_mul_f32_e32 v21, 0x40a00000, v3                          // 000000000504: 102A06FF 40A00000
	image_sample_lz v[0:3], v[25:26], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000050C: F09C0F08 00600019
	v_fmac_f32_e32 v18, 0, v14                                 // 000000000514: 56241C80
	v_fmac_f32_e32 v19, 0, v15                                 // 000000000518: 56261E80
	v_fmac_f32_e32 v20, 0, v16                                 // 00000000051C: 56282080
	v_fmac_f32_e32 v21, 0, v17                                 // 000000000520: 562A2280
	s_waitcnt vmcnt(0)                                         // 000000000524: BF8C3F70
	v_add_f32_e32 v14, v10, v0                                 // 000000000528: 061C010A
	v_add_f32_e32 v15, v11, v1                                 // 00000000052C: 061E030B
	v_add_f32_e32 v16, v12, v2                                 // 000000000530: 0620050C
	v_add_f32_e32 v17, v13, v3                                 // 000000000534: 0622070D
	s_clause 0x1                                               // 000000000538: BFA10001
	image_sample_lz v[0:3], v[22:23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000053C: F09C0F08 00600016
	image_sample_lz  v[10:13], [v27, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000544: F09C0F0A 00600A1B 00000017
	s_waitcnt vmcnt(1)                                         // 000000000550: BF8C3F71
	v_fmac_f32_e32 v18, 0, v0                                  // 000000000554: 56240080
	s_waitcnt vmcnt(0)                                         // 000000000558: BF8C3F70
	v_add_f32_e32 v0, v14, v10                                 // 00000000055C: 0600150E
	v_fmac_f32_e32 v19, 0, v1                                  // 000000000560: 56260280
	v_add_f32_e32 v1, v15, v11                                 // 000000000564: 0602170F
	v_fmac_f32_e32 v20, 0, v2                                  // 000000000568: 56280480
	v_add_f32_e32 v2, v16, v12                                 // 00000000056C: 06041910
	v_fmac_f32_e32 v21, 0, v3                                  // 000000000570: 562A0680
	v_add_f32_e32 v3, v17, v13                                 // 000000000574: 06061B11
	image_sample_lz  v[10:13], [v25, v23], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000578: F09C0F0A 00600A19 00000017
	v_sub_f32_e32 v0, v18, v0                                  // 000000000584: 08000112
	v_sub_f32_e32 v1, v19, v1                                  // 000000000588: 08020313
	v_sub_f32_e32 v2, v20, v2                                  // 00000000058C: 08040514
	v_sub_f32_e32 v3, v21, v3                                  // 000000000590: 08060715
	s_waitcnt vmcnt(0)                                         // 000000000594: BF8C3F70
	v_fmac_f32_e32 v0, 0, v10                                  // 000000000598: 56001480
	v_fmac_f32_e32 v1, 0, v11                                  // 00000000059C: 56021680
	v_fmac_f32_e32 v2, 0, v12                                  // 0000000005A0: 56041880
	v_fmac_f32_e32 v3, 0, v13                                  // 0000000005A4: 56061A80
_L9:
	s_cbranch_execnz _L10                                      // 0000000005A8: BF890145
_L6:
	s_buffer_load_dword s16, s[8:11], 0x8                      // 0000000005AC: F4200404 FA000008
	v_rcp_f32_e32 v12, v8                                      // 0000000005B4: 7E185508
	v_rcp_f32_e32 v14, v9                                      // 0000000005B8: 7E1C5509
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000005BC: BF8C0070
	v_mul_f32_e64 v0, s16, -2.0                                // 0000000005C0: D5080000 0001EA10
	v_fma_f32 v16, -s16, v12, v6                               // 0000000005C8: D54B0010 241A1810
	v_mul_f32_e64 v17, s16, 0                                  // 0000000005D0: D5080011 00010010
	v_fma_f32 v19, s16, v12, v6                                // 0000000005D8: D54B0013 041A1810
	v_add_f32_e64 v20, s16, s16                                // 0000000005E0: D5030014 00002010
	v_fma_f32 v21, v0, v12, v6                                 // 0000000005E8: D54B0015 041A1900
	v_fma_f32 v22, v0, v14, v7                                 // 0000000005F0: D54B0016 041E1D00
	v_fma_f32 v18, v17, v12, v6                                // 0000000005F8: D54B0012 041A1911
	v_fmac_f32_e32 v6, v20, v12                                // 000000000600: 560C1914
	s_clause 0x1                                               // 000000000604: BFA10001
	image_sample_lz v[0:3], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000608: F09C0F08 00600015
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000610: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(1)                                         // 00000000061C: BF8C3F71
	v_mul_f32_e32 v0, 0x3b76be38, v0                           // 000000000620: 100000FF 3B76BE38
	v_mul_f32_e32 v1, 0x3b76be38, v1                           // 000000000628: 100202FF 3B76BE38
	v_mul_f32_e32 v2, 0x3b76be38, v2                           // 000000000630: 100404FF 3B76BE38
	v_mul_f32_e32 v3, 0x3b76be38, v3                           // 000000000638: 100606FF 3B76BE38
	s_waitcnt vmcnt(0)                                         // 000000000640: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000644: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 00000000064C: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000654: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 00000000065C: 560616FF 3C761240
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000664: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000670: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 000000000674: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 00000000067C: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000684: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 00000000068C: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000694: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 0000000006A0: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000006A4: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 0000000006AC: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 0000000006B4: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 0000000006BC: 560616FF 3C761240
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000006C4: F09C0F0A 00600806 00000016
	v_fma_f32 v22, -s16, v14, v7                               // 0000000006D0: D54B0016 241E1C10
	s_waitcnt vmcnt(0)                                         // 0000000006D8: BF8C3F70
	v_fmac_f32_e32 v0, 0x3b76be38, v8                          // 0000000006DC: 560010FF 3B76BE38
	v_fmac_f32_e32 v1, 0x3b76be38, v9                          // 0000000006E4: 560212FF 3B76BE38
	v_fmac_f32_e32 v2, 0x3b76be38, v10                         // 0000000006EC: 560414FF 3B76BE38
	v_fmac_f32_e32 v3, 0x3b76be38, v11                         // 0000000006F4: 560616FF 3B76BE38
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000006FC: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 000000000704: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000708: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000710: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000718: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000720: 560616FF 3C761240
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000728: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 000000000734: BF8C3F70
	v_fmac_f32_e32 v0, 0x3d756649, v8                          // 000000000738: 560010FF 3D756649
	v_fmac_f32_e32 v1, 0x3d756649, v9                          // 000000000740: 560212FF 3D756649
	v_fmac_f32_e32 v2, 0x3d756649, v10                         // 000000000748: 560414FF 3D756649
	v_fmac_f32_e32 v3, 0x3d756649, v11                         // 000000000750: 560616FF 3D756649
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000758: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000764: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000768: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000770: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000778: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000780: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000788: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000794: BF8C3F70
	v_fmac_f32_e32 v0, 0x3d756649, v8                          // 000000000798: 560010FF 3D756649
	v_fmac_f32_e32 v1, 0x3d756649, v9                          // 0000000007A0: 560212FF 3D756649
	v_fmac_f32_e32 v2, 0x3d756649, v10                         // 0000000007A8: 560414FF 3D756649
	v_fmac_f32_e32 v3, 0x3d756649, v11                         // 0000000007B0: 560616FF 3D756649
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000007B8: F09C0F0A 00600806 00000016
	v_fma_f32 v22, v17, v14, v7                                // 0000000007C4: D54B0016 041E1D11
	s_waitcnt vmcnt(0)                                         // 0000000007CC: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000007D0: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 0000000007D8: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 0000000007E0: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 0000000007E8: 560616FF 3C761240
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000007F0: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 0000000007F8: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 0000000007FC: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000804: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 00000000080C: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000814: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000081C: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 000000000828: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 00000000082C: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000834: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 00000000083C: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 000000000844: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000084C: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000858: BF8C3F70
	v_fmac_f32_e32 v0, 0x3e19f341, v8                          // 00000000085C: 560010FF 3E19F341
	v_fmac_f32_e32 v1, 0x3e19f341, v9                          // 000000000864: 560212FF 3E19F341
	v_fmac_f32_e32 v2, 0x3e19f341, v10                         // 00000000086C: 560414FF 3E19F341
	v_fmac_f32_e32 v3, 0x3e19f341, v11                         // 000000000874: 560616FF 3E19F341
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000087C: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000888: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 00000000088C: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 000000000894: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 00000000089C: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 0000000008A4: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000008AC: F09C0F0A 00600806 00000016
	v_fma_f32 v22, s16, v14, v7                                // 0000000008B8: D54B0016 041E1C10
	v_fmac_f32_e32 v7, v20, v14                                // 0000000008C0: 560E1D14
	s_waitcnt vmcnt(0)                                         // 0000000008C4: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 0000000008C8: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 0000000008D0: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 0000000008D8: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 0000000008E0: 560616FF 3CC2E771
	image_sample_lz v[8:11], v[21:22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000008E8: F09C0F08 00600815
	s_waitcnt vmcnt(0)                                         // 0000000008F0: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000008F4: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 0000000008FC: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000904: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 00000000090C: 560616FF 3C761240
	image_sample_lz  v[8:11], [v16, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000914: F09C0F0A 00600810 00000016
	s_waitcnt vmcnt(0)                                         // 000000000920: BF8C3F70
	v_fmac_f32_e32 v0, 0x3d756649, v8                          // 000000000924: 560010FF 3D756649
	v_fmac_f32_e32 v1, 0x3d756649, v9                          // 00000000092C: 560212FF 3D756649
	v_fmac_f32_e32 v2, 0x3d756649, v10                         // 000000000934: 560414FF 3D756649
	v_fmac_f32_e32 v3, 0x3d756649, v11                         // 00000000093C: 560616FF 3D756649
	image_sample_lz  v[8:11], [v18, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000944: F09C0F0A 00600812 00000016
	s_waitcnt vmcnt(0)                                         // 000000000950: BF8C3F70
	v_fmac_f32_e32 v0, 0x3dc25e9a, v8                          // 000000000954: 560010FF 3DC25E9A
	v_fmac_f32_e32 v1, 0x3dc25e9a, v9                          // 00000000095C: 560212FF 3DC25E9A
	v_fmac_f32_e32 v2, 0x3dc25e9a, v10                         // 000000000964: 560414FF 3DC25E9A
	v_fmac_f32_e32 v3, 0x3dc25e9a, v11                         // 00000000096C: 560616FF 3DC25E9A
	image_sample_lz  v[8:11], [v19, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000974: F09C0F0A 00600813 00000016
	s_waitcnt vmcnt(0)                                         // 000000000980: BF8C3F70
	v_fmac_f32_e32 v0, 0x3d756649, v8                          // 000000000984: 560010FF 3D756649
	v_fmac_f32_e32 v1, 0x3d756649, v9                          // 00000000098C: 560212FF 3D756649
	v_fmac_f32_e32 v2, 0x3d756649, v10                         // 000000000994: 560414FF 3D756649
	v_fmac_f32_e32 v3, 0x3d756649, v11                         // 00000000099C: 560616FF 3D756649
	image_sample_lz  v[8:11], [v6, v22], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000009A4: F09C0F0A 00600806 00000016
	s_waitcnt vmcnt(0)                                         // 0000000009B0: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 0000000009B4: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 0000000009BC: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 0000000009C4: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 0000000009CC: 560616FF 3C761240
	image_sample_lz  v[8:11], [v21, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000009D4: F09C0F0A 00600815 00000007
	s_waitcnt vmcnt(0)                                         // 0000000009E0: BF8C3F70
	v_fmac_f32_e32 v0, 0x3b76be38, v8                          // 0000000009E4: 560010FF 3B76BE38
	v_fmac_f32_e32 v1, 0x3b76be38, v9                          // 0000000009EC: 560212FF 3B76BE38
	v_fmac_f32_e32 v2, 0x3b76be38, v10                         // 0000000009F4: 560414FF 3B76BE38
	v_fmac_f32_e32 v3, 0x3b76be38, v11                         // 0000000009FC: 560616FF 3B76BE38
	image_sample_lz  v[8:11], [v16, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A04: F09C0F0A 00600810 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A10: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000A14: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000A1C: 560212FF 3C761240
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000A24: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000A2C: 560616FF 3C761240
	image_sample_lz  v[8:11], [v18, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A34: F09C0F0A 00600812 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A40: BF8C3F70
	v_fmac_f32_e32 v0, 0x3cc2e771, v8                          // 000000000A44: 560010FF 3CC2E771
	v_fmac_f32_e32 v1, 0x3cc2e771, v9                          // 000000000A4C: 560212FF 3CC2E771
	v_fmac_f32_e32 v2, 0x3cc2e771, v10                         // 000000000A54: 560414FF 3CC2E771
	v_fmac_f32_e32 v3, 0x3cc2e771, v11                         // 000000000A5C: 560616FF 3CC2E771
	image_sample_lz  v[8:11], [v19, v7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A64: F09C0F0A 00600813 00000007
	s_waitcnt vmcnt(0)                                         // 000000000A70: BF8C3F70
	v_fmac_f32_e32 v0, 0x3c761240, v8                          // 000000000A74: 560010FF 3C761240
	v_fmac_f32_e32 v1, 0x3c761240, v9                          // 000000000A7C: 560212FF 3C761240
	image_sample_lz v[6:9], v[6:7], s[0:7], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000A84: F09C0F08 00600606
	v_fmac_f32_e32 v2, 0x3c761240, v10                         // 000000000A8C: 560414FF 3C761240
	v_fmac_f32_e32 v3, 0x3c761240, v11                         // 000000000A94: 560616FF 3C761240
	s_waitcnt vmcnt(0)                                         // 000000000A9C: BF8C3F70
	v_fmac_f32_e32 v0, 0x3b76be38, v6                          // 000000000AA0: 56000CFF 3B76BE38
	v_fmac_f32_e32 v1, 0x3b76be38, v7                          // 000000000AA8: 56020EFF 3B76BE38
	v_fmac_f32_e32 v2, 0x3b76be38, v8                          // 000000000AB0: 560410FF 3B76BE38
	v_fmac_f32_e32 v3, 0x3b76be38, v9                          // 000000000AB8: 560612FF 3B76BE38
_L10:
	s_buffer_load_dwordx4 s[8:11], s[8:11], 0xc                // 000000000AC0: F4280204 FA00000C
	s_waitcnt vmcnt(0)                                         // 000000000AC8: BF8C3F70
	v_max_f32_e64 v3, v3, v3 clamp                             // 000000000ACC: D5108003 00020703
	s_waitcnt lgkmcnt(0)                                       // 000000000AD4: BF8CC07F
	v_fma_f32 v1, v1, s8, -0.5                                 // 000000000AD8: D54B0001 03C41101
	v_fma_f32 v2, v2, s8, -0.5                                 // 000000000AE0: D54B0002 03C41102
	v_fma_f32 v0, v0, s8, -0.5                                 // 000000000AE8: D54B0000 03C41100
	s_mov_b32 s8, 0x40c00000                                   // 000000000AF0: BE8803FF 40C00000
	v_fma_f32 v6, v1, s9, 0.5                                  // 000000000AF8: D54B0006 03C01301
	v_fma_f32 v7, v2, s9, 0.5                                  // 000000000B00: D54B0007 03C01302
	v_mul_f32_e32 v1, s9, v1                                   // 000000000B08: 10020209
	v_fma_f32 v0, v0, s9, 0.5                                  // 000000000B0C: D54B0000 03C01300
	v_cmp_lt_f32_e32 vcc_lo, v6, v7                            // 000000000B14: 7C020F06
	v_fma_f32 v9, -v2, s9, v1                                  // 000000000B18: D54B0009 24041302
	v_fma_f32 v1, v2, s9, -v1                                  // 000000000B20: D54B0001 84041302
	v_cndmask_b32_e64 v8, 1.0, 0, vcc_lo                       // 000000000B28: D5010008 01A900F2
	v_fmac_f32_e32 v7, v8, v9                                  // 000000000B30: 560E1308
	v_fmac_f32_e32 v6, v8, v1                                  // 000000000B34: 560C0308
	v_cmp_lt_f32_e32 vcc_lo, v0, v7                            // 000000000B38: 7C020F00
	v_sub_f32_e32 v9, v7, v0                                   // 000000000B3C: 08120107
	v_sub_f32_e32 v1, v0, v7                                   // 000000000B40: 08020F00
	v_cndmask_b32_e64 v2, 1.0, 0, vcc_lo                       // 000000000B44: D5010002 01A900F2
	v_fmac_f32_e32 v0, v2, v9                                  // 000000000B4C: 56001302
	v_fmac_f32_e32 v7, v2, v1                                  // 000000000B50: 560E0302
	v_add_f32_e32 v9, -1.0, v8                                 // 000000000B54: 061210F3
	v_sub_f32_e32 v8, 0x3f2aaaab, v8                           // 000000000B58: 081010FF 3F2AAAAB
	v_min_f32_e32 v1, v0, v6                                   // 000000000B60: 1E020D00
	v_sub_f32_e32 v0, v0, v6                                   // 000000000B64: 08000D00
	v_sub_f32_e32 v9, v9, v8                                   // 000000000B68: 08121109
	v_sub_f32_e32 v1, v7, v1                                   // 000000000B6C: 08020307
	v_fmac_f32_e32 v8, v2, v9                                  // 000000000B70: 56101302
	v_add_f32_e32 v9, 0x2edbe6ff, v7                           // 000000000B74: 06120EFF 2EDBE6FF
	v_fmaak_f32 v10, s8, v1, 0x2edbe6ff                        // 000000000B7C: 5A140208 2EDBE6FF
	v_mul_f32_e32 v1, s10, v1                                  // 000000000B84: 1002020A
	v_rcp_f32_e32 v9, v9                                       // 000000000B88: 7E125509
	v_rcp_f32_e32 v10, v10                                     // 000000000B8C: 7E14550A
	v_mul_f32_e32 v1, v1, v9                                   // 000000000B90: 10021301
	v_fmac_f32_e32 v8, v0, v10                                 // 000000000B94: 56101500
	v_add_f32_e64 v0, |v8|, 1.0                                // 000000000B98: D5030100 0001E508
	v_add_f32_e64 v2, 0x3f2aaaab, |v8|                         // 000000000BA0: D5030202 000210FF 3F2AAAAB
	v_add_f32_e64 v6, 0x3eaaaaab, |v8|                         // 000000000BAC: D5030206 000210FF 3EAAAAAB
	v_mov_b32_e32 v8, 0xc0400000                               // 000000000BB8: 7E1002FF C0400000
	v_fract_f32_e32 v0, v0                                     // 000000000BC0: 7E004100
	v_fract_f32_e32 v2, v2                                     // 000000000BC4: 7E044102
	v_fract_f32_e32 v6, v6                                     // 000000000BC8: 7E0C4106
	v_fmamk_f32 v0, v0, 0x40c00000, v8                         // 000000000BCC: 58001100 40C00000
	v_fmamk_f32 v2, v2, 0x40c00000, v8                         // 000000000BD4: 58041102 40C00000
	v_fmac_f32_e32 v8, 0x40c00000, v6                          // 000000000BDC: 56100CFF 40C00000
	v_add_f32_e64 v0, |v0|, -1.0 clamp                         // 000000000BE4: D5038100 0001E700
	v_add_f32_e64 v2, |v2|, -1.0 clamp                         // 000000000BEC: D5038102 0001E702
	v_add_f32_e64 v6, |v8|, -1.0 clamp                         // 000000000BF4: D5038106 0001E708
	v_add_f32_e32 v0, -1.0, v0                                 // 000000000BFC: 060000F3
	v_add_f32_e32 v2, -1.0, v2                                 // 000000000C00: 060404F3
	v_add_f32_e32 v6, -1.0, v6                                 // 000000000C04: 060C0CF3
	v_fma_f32 v0, v0, v1, 1.0                                  // 000000000C08: D54B0000 03CA0300
	v_fma_f32 v2, v2, v1, 1.0                                  // 000000000C10: D54B0002 03CA0302
	v_fma_f32 v1, v6, v1, 1.0                                  // 000000000C18: D54B0001 03CA0306
	v_mul_f32_e32 v0, v0, v7                                   // 000000000C20: 10000F00
	v_mul_f32_e32 v2, v2, v7                                   // 000000000C24: 10040F02
	v_mul_f32_e32 v1, v1, v7                                   // 000000000C28: 10020F01
	v_log_f32_e64 v0, |v0|                                     // 000000000C2C: D5A70100 00000100
	v_log_f32_e64 v2, |v2|                                     // 000000000C34: D5A70102 00000102
	v_log_f32_e64 v1, |v1|                                     // 000000000C3C: D5A70101 00000101
	v_mul_legacy_f32_e32 v0, s11, v0                           // 000000000C44: 0E00000B
	v_mul_legacy_f32_e32 v2, s11, v2                           // 000000000C48: 0E04040B
	v_mul_legacy_f32_e32 v6, s11, v1                           // 000000000C4C: 0E0C020B
	v_exp_f32_e64 v0, v0 clamp                                 // 000000000C50: D5A58000 00000100
	v_exp_f32_e64 v1, v2 clamp                                 // 000000000C58: D5A58001 00000102
	v_exp_f32_e64 v2, v6 clamp                                 // 000000000C60: D5A58002 00000106
	image_store v[0:3], v[4:5], s[0:7] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000C68: F0201F08 00000004
_L0:
	s_endpgm                                                   // 000000000C70: BF810000
