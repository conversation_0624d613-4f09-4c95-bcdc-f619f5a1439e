// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct PSInput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
  float2 TexCoordOffset[8] : TEXCOORD1;
};

cbuffer PostProcessParams : register(b0)
{
  float2 TexelSize;
  float BlurRadius;
  float Time;
  float Brightness;
  float Contrast;
  float Saturation;
  float Gamma;
  float VignetteStrength;
  float ChromaticAberration;
  float FilmGrain;
  int EffectType;
}

Texture2D SceneTexture : register(t0);
Texture2D BloomTexture : register(t1);
Texture2D DepthTexture : register(t2);
Texture2D NoiseTexture : register(t3);
SamplerState LinearSampler : register(s0);
SamplerState PointSampler : register(s1);
float3 RGBToHSV(float3 rgb)
{
  float4 K = float4(0.0f, -0.333333333f, 0.666666667f, -1.0f);
  float4 p = lerp(float4(rgb.bg, K.wz), float4(rgb.gb, K.xy), step(rgb.b, rgb.g));
  float4 q = lerp(float4(p.xyw, rgb.r), float4(rgb.r, p.yzx), step(p.x, rgb.r));
  float d = q.x - min(q.w, q.y);
  float e = 1e-10f;
  return float3(abs(q.z + q.w - q.y / (6.0f * d) + e), d / q.x + e, q.x);
}

float3 HSVToRGB(float3 hsv)
{
  float4 K = float4(1.0f, 0.666666667f, 0.333333333f, 3.0f);
  float3 p = abs((frac(hsv.xxx + K.xyz) * 6.0f) - K.www);
  return (hsv.z * lerp(K.xxx, saturate(p - K.xxx), hsv.y));
}

float4 gaussianBlur(PSInput input)
{
  float4 color = float4(0, 0, 0, 0);
  float weights[9] = {0.0625f, 0.125f, 0.0625f, 0.125f, 0.25f, 0.125f, 0.0625f, 0.125f, 0.0625f};
  color += (SceneTexture.Sample(LinearSampler, input.TexCoord) * weights[4]);
  for (int i = 0; i < 8; (i++))
  {
    color += (SceneTexture.Sample(LinearSampler, input.TexCoordOffset[i]) * weights[i]);
  }
  return color;
}

float4 chromaticAberration(float2 texCoord)
{
  float2 center = float2(0.5f, 0.5f);
  float2 direction = texCoord - center;
  float distance = length(direction);
  float2 offsetR = ((direction * ChromaticAberration) * distance);
  float2 offsetG = (((direction * ChromaticAberration) * distance) * 0.5f);
  float2 offsetB = float2(0, 0);
  float r = SceneTexture.Sample(LinearSampler, texCoord + offsetR).r;
  float g = SceneTexture.Sample(LinearSampler, texCoord + offsetG).g;
  float b = SceneTexture.Sample(LinearSampler, texCoord + offsetB).b;
  float a = SceneTexture.Sample(LinearSampler, texCoord).a;
  return float4(r, g, b, a);
}

float4 depthOfField(PSInput input)
{
  float depth = DepthTexture.Sample(PointSampler, input.TexCoord).r;
  float focusDistance = 0.5f;
  float focusRange = 0.2f;
  float blur = abs(depth - focusDistance) / focusRange;
  blur = saturate(blur);
  float4 sharpColor = SceneTexture.Sample(LinearSampler, input.TexCoord);
  float4 blurredColor = gaussianBlur(input);
  return lerp(sharpColor, blurredColor, blur);
}

float4 barrelDistortion(float2 texCoord)
{
  float2 center = float2(0.5f, 0.5f);
  float2 coord = texCoord - center;
  float distortion = 0.2f;
  float r2 = dot(coord, coord);
  float distortionFactor = 1.0f + (distortion * r2);
  float2 distortedCoord = center + (coord * distortionFactor);
  if (distortedCoord.x < 0.0f || distortedCoord.x > 1.0f || distortedCoord.y < 0.0f || distortedCoord.y > 1.0f)
    return float4(0, 0, 0, 1);
  return SceneTexture.Sample(LinearSampler, distortedCoord);
}

float4 main(PSInput input) : SV_TARGET
{
  float4 color;
  if (EffectType == 1)
  {
    color = gaussianBlur(input);
  }
  else if (EffectType == 2)
  {
    float4 sceneColor = SceneTexture.Sample(LinearSampler, input.TexCoord);
    float4 bloomColor = BloomTexture.Sample(LinearSampler, input.TexCoord);
    color = sceneColor + (bloomColor * 0.3f);
  }
  else if (EffectType == 3)
  {
    color = depthOfField(input);
  }
  else if (EffectType == 4)
  {
    color = barrelDistortion(input.TexCoord);
  }
  else
  {
    color = SceneTexture.Sample(LinearSampler, input.TexCoord);
  }
  if (ChromaticAberration > 0.0f)
  {
    color = chromaticAberration(input.TexCoord);
  }
  color.rgb *= Brightness;
  color.rgb = (color.rgb - 0.5f * Contrast) + 0.5f;
  float3 hsv = RGBToHSV(color.rgb);
  hsv.y *= Saturation;
  color.rgb = HSVToRGB(hsv);
  float2 center = float2(0.5f, 0.5f);
  float distance = length(input.TexCoord - center);
  float vignette = 1.0f - smoothstep(0.3f, 0.8f, (distance * VignetteStrength));
  color.rgb *= vignette;
  if (FilmGrain > 0.0f)
  {
    float2 noiseCoord = (input.TexCoord * 10.0f) + (Time * 0.1f);
    float noise = NoiseTexture.Sample(LinearSampler, noiseCoord).r;
    noise = (noise - 0.5f * FilmGrain);
    color.rgb += noise;
  }
  color.rgb = pow(abs(color.rgb), Gamma);
  return saturate(color);
}

