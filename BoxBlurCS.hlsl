// Unnamed technique, shader DoBlurCS
/*$(ShaderResources)*/

/*$(_compute:csmain)*/(uint3 DTid : SV_DispatchThreadID)
{
	static const int c_blurRadius = /*$(Variable:BlurRadius)*/;

	int2 px = int2(DTid.xy);
	float3 color = float3(0.0f, 0.0f, 0.0f);
	float weight = 0.0f;
	for (int iy = -c_blurRadius; iy <= c_blurRadius; ++iy)
	{
		for (int ix = -c_blurRadius; ix <= c_blurRadius; ++ix)
		{
			color += input[px + int2(ix, iy)].rgb;
			weight += 1.0f;
		}
	}
	color /= weight;
	output[px] = float4(pow(color, 1.0f / 2.2f), 1.0f);
}

/*
Shader Resources:
	Texture input (as SRV)
	Texture output (as UAV)
*/