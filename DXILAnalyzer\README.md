# DXIL Analyzer

A Python library for analyzing DXIL (DirectX Intermediate Language) assembly and binary files to extract computational instructions and identify data types.

## Features

- Parse DXIL assembly (.asm) files
- Parse DXIL binary (.dxil) files
- Extract computational instructions (fadd, fmul, fsub, fdiv, etc.)
- Identify data types for each operation (float, int, etc.)
- Generate analysis reports
- Command-line interface for batch processing

## Project Structure

```
DXILAnalyzer/
├── src/
│   ├── __init__.py
│   ├── dxil_parser.py      # DXIL file parser
│   ├── instruction_analyzer.py  # Instruction analysis
│   ├── output_formatter.py # Output formatting
│   └── cli.py              # Command-line interface
├── tests/
│   ├── test_parser.py
│   ├── test_analyzer.py
│   └── test_data/
├── examples/
│   └── sample_analysis.py
├── output/                 # Generated analysis reports
└── README.md
```

## Installation

```bash
cd DXILAnalyzer
pip install -r requirements.txt
```

## Usage

### Command Line

```bash
# Analyze a single DXIL assembly file
python -m src.cli analyze path/to/shader.asm

# Analyze a DXIL binary file
python -m src.cli analyze path/to/shader.dxil

# Batch analyze all files in a directory
python -m src.cli batch path/to/directory/
```

### Python API

```python
from src.dxil_parser import DXILParser
from src.instruction_analyzer import InstructionAnalyzer

# Parse DXIL file
parser = DXILParser()
instructions = parser.parse_file("shader.asm")

# Analyze instructions
analyzer = InstructionAnalyzer()
analysis = analyzer.analyze(instructions)

# Generate report
print(analysis.summary())
```

## Output Format

The analyzer generates reports showing:
- Total number of computational instructions
- Breakdown by operation type (add, multiply, etc.)
- Data type analysis (float32, int32, etc.)
- Instruction details with operand types

Example output:
```
=== DXIL Analysis Report ===
File: SimpleVS.asm
Total Instructions: 15
Computational Instructions: 8

Operation Breakdown:
- fadd: 3 (float + float)
- fmul: 4 (float * float)
- fsub: 1 (float - float)

Data Types:
- float32: 95%
- int32: 5%
```
