; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 478
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %gl_FragCoord %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %in_var_TEXCOORD8 %in_var_TEXCOORD9 %in_var_TEXCOORD10 %in_var_TEXCOORD11 %out_var_SV_Target0 %out_var_SV_Target1 %out_var_SV_Target2 %out_var_SV_Target3
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "DeltaTime"
               OpMemberName %type_PerFrame 6 "FrameCount"
               OpMemberName %type_PerFrame 7 "ScreenResolution"
               OpName %PerFrame "PerFrame"
               OpName %type_LightingParams "type.LightingParams"
               OpMemberName %type_LightingParams 0 "NumLights"
               OpMemberName %type_LightingParams 1 "AmbientIntensity"
               OpMemberName %type_LightingParams 2 "AmbientColor"
               OpMemberName %type_LightingParams 3 "ShadowBias"
               OpMemberName %type_LightingParams 4 "ShadowNormalBias"
               OpMemberName %type_LightingParams 5 "ShadowMapSize"
               OpMemberName %type_LightingParams 6 "PCFRadius"
               OpName %LightingParams "LightingParams"
               OpName %type_StructuredBuffer_MaterialData "type.StructuredBuffer.MaterialData"
               OpName %MaterialData "MaterialData"
               OpMemberName %MaterialData 0 "Albedo"
               OpMemberName %MaterialData 1 "Metallic"
               OpMemberName %MaterialData 2 "Roughness"
               OpMemberName %MaterialData 3 "AO"
               OpMemberName %MaterialData 4 "Emission"
               OpMemberName %MaterialData 5 "TilingOffset"
               OpMemberName %MaterialData 6 "TextureFlags"
               OpMemberName %MaterialData 7 "_padding"
               OpName %Materials "Materials"
               OpName %type_StructuredBuffer_LightData "type.StructuredBuffer.LightData"
               OpName %LightData "LightData"
               OpMemberName %LightData 0 "Position"
               OpMemberName %LightData 1 "Range"
               OpMemberName %LightData 2 "Direction"
               OpMemberName %LightData 3 "SpotAngle"
               OpMemberName %LightData 4 "Color"
               OpMemberName %LightData 5 "Intensity"
               OpMemberName %LightData 6 "Type"
               OpMemberName %LightData 7 "_padding"
               OpName %Lights "Lights"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %AlbedoTextures "AlbedoTextures"
               OpName %NormalTextures "NormalTextures"
               OpName %MetallicRoughnessTextures "MetallicRoughnessTextures"
               OpName %AOTextures "AOTextures"
               OpName %EmissionTextures "EmissionTextures"
               OpName %type_2d_image "type.2d.image"
               OpName %LightmapTexture "LightmapTexture"
               OpName %ShadowMap "ShadowMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %ShadowSampler "ShadowSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %in_var_TEXCOORD8 "in.var.TEXCOORD8"
               OpName %in_var_TEXCOORD9 "in.var.TEXCOORD9"
               OpName %in_var_TEXCOORD10 "in.var.TEXCOORD10"
               OpName %in_var_TEXCOORD11 "in.var.TEXCOORD11"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %out_var_SV_Target1 "out.var.SV_Target1"
               OpName %out_var_SV_Target2 "out.var.SV_Target2"
               OpName %out_var_SV_Target3 "out.var.SV_Target3"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %gl_FragCoord BuiltIn FragCoord
               OpDecorate %in_var_TEXCOORD10 Flat
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %in_var_TEXCOORD7 Location 7
               OpDecorate %in_var_TEXCOORD8 Location 8
               OpDecorate %in_var_TEXCOORD9 Location 9
               OpDecorate %in_var_TEXCOORD10 Location 10
               OpDecorate %in_var_TEXCOORD11 Location 11
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %out_var_SV_Target1 Location 1
               OpDecorate %out_var_SV_Target2 Location 2
               OpDecorate %out_var_SV_Target3 Location 3
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %LightingParams DescriptorSet 0
               OpDecorate %LightingParams Binding 1
               OpDecorate %Materials DescriptorSet 0
               OpDecorate %Materials Binding 0
               OpDecorate %Lights DescriptorSet 0
               OpDecorate %Lights Binding 1
               OpDecorate %AlbedoTextures DescriptorSet 0
               OpDecorate %AlbedoTextures Binding 2
               OpDecorate %NormalTextures DescriptorSet 0
               OpDecorate %NormalTextures Binding 3
               OpDecorate %MetallicRoughnessTextures DescriptorSet 0
               OpDecorate %MetallicRoughnessTextures Binding 4
               OpDecorate %AOTextures DescriptorSet 0
               OpDecorate %AOTextures Binding 5
               OpDecorate %EmissionTextures DescriptorSet 0
               OpDecorate %EmissionTextures Binding 6
               OpDecorate %LightmapTexture DescriptorSet 0
               OpDecorate %LightmapTexture Binding 7
               OpDecorate %ShadowMap DescriptorSet 0
               OpDecorate %ShadowMap Binding 9
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %ShadowSampler DescriptorSet 0
               OpDecorate %ShadowSampler Binding 2
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 212
               OpMemberDecorate %type_PerFrame 7 Offset 216
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_LightingParams 0 Offset 0
               OpMemberDecorate %type_LightingParams 1 Offset 4
               OpMemberDecorate %type_LightingParams 2 Offset 16
               OpMemberDecorate %type_LightingParams 3 Offset 28
               OpMemberDecorate %type_LightingParams 4 Offset 32
               OpMemberDecorate %type_LightingParams 5 Offset 36
               OpMemberDecorate %type_LightingParams 6 Offset 44
               OpDecorate %type_LightingParams Block
               OpMemberDecorate %MaterialData 0 Offset 0
               OpMemberDecorate %MaterialData 1 Offset 16
               OpMemberDecorate %MaterialData 2 Offset 20
               OpMemberDecorate %MaterialData 3 Offset 24
               OpMemberDecorate %MaterialData 4 Offset 28
               OpMemberDecorate %MaterialData 5 Offset 32
               OpMemberDecorate %MaterialData 6 Offset 48
               OpMemberDecorate %MaterialData 7 Offset 52
               OpDecorate %_runtimearr_MaterialData ArrayStride 64
               OpMemberDecorate %type_StructuredBuffer_MaterialData 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_MaterialData 0 NonWritable
               OpDecorate %type_StructuredBuffer_MaterialData BufferBlock
               OpMemberDecorate %LightData 0 Offset 0
               OpMemberDecorate %LightData 1 Offset 12
               OpMemberDecorate %LightData 2 Offset 16
               OpMemberDecorate %LightData 3 Offset 28
               OpMemberDecorate %LightData 4 Offset 32
               OpMemberDecorate %LightData 5 Offset 44
               OpMemberDecorate %LightData 6 Offset 48
               OpMemberDecorate %LightData 7 Offset 52
               OpDecorate %_runtimearr_LightData ArrayStride 64
               OpMemberDecorate %type_StructuredBuffer_LightData 0 Offset 0
               OpMemberDecorate %type_StructuredBuffer_LightData 0 NonWritable
               OpDecorate %type_StructuredBuffer_LightData BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_5 = OpConstant %int 5
      %int_6 = OpConstant %int 6
       %uint = OpTypeInt 32 0
     %uint_1 = OpConstant %uint 1
     %uint_0 = OpConstant %uint 0
      %int_2 = OpConstant %int 2
     %uint_2 = OpConstant %uint 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_1 = OpConstant %int 1
     %uint_4 = OpConstant %uint 4
     %uint_8 = OpConstant %uint 8
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %63 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %uint_16 = OpConstant %uint 16
  %float_0_5 = OpConstant %float 0.5
    %v2float = OpTypeVector %float 2
         %67 = OpConstantComposite %v2float %float_0_5 %float_0_5
         %68 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
    %float_1 = OpConstant %float 1
    %float_2 = OpConstant %float 2
         %71 = OpConstantComposite %v3float %float_1 %float_1 %float_1
%float_0_0399999991 = OpConstant %float 0.0399999991
         %73 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
%float_0_0900000036 = OpConstant %float 0.0900000036
%float_0_0320000015 = OpConstant %float 0.0320000015
%float_1_20000005 = OpConstant %float 1.20000005
    %float_4 = OpConstant %float 4
%float_9_99999975en05 = OpConstant %float 9.99999975e-05
%float_3_14159274 = OpConstant %float 3.14159274
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
         %82 = OpConstantComposite %v2float %float_1 %float_1
     %int_n1 = OpConstant %int -1
    %float_5 = OpConstant %float 5
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %float %uint %v2float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_LightingParams = OpTypeStruct %uint %float %v3float %float %float %v2float %float
%_ptr_Uniform_type_LightingParams = OpTypePointer Uniform %type_LightingParams
%MaterialData = OpTypeStruct %v4float %float %float %float %float %v4float %uint %float
%_runtimearr_MaterialData = OpTypeRuntimeArray %MaterialData
%type_StructuredBuffer_MaterialData = OpTypeStruct %_runtimearr_MaterialData
%_ptr_Uniform_type_StructuredBuffer_MaterialData = OpTypePointer Uniform %type_StructuredBuffer_MaterialData
  %LightData = OpTypeStruct %v3float %float %v3float %float %v3float %float %uint %v3float
%_runtimearr_LightData = OpTypeRuntimeArray %LightData
%type_StructuredBuffer_LightData = OpTypeStruct %_runtimearr_LightData
%_ptr_Uniform_type_StructuredBuffer_LightData = OpTypePointer Uniform %type_StructuredBuffer_LightData
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
        %101 = OpTypeFunction %void
%_ptr_Uniform_MaterialData = OpTypePointer Uniform %MaterialData
%type_sampled_image = OpTypeSampledImage %type_2d_image_array
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Uniform_LightData = OpTypePointer Uniform %LightData
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%LightingParams = OpVariable %_ptr_Uniform_type_LightingParams Uniform
  %Materials = OpVariable %_ptr_Uniform_type_StructuredBuffer_MaterialData Uniform
     %Lights = OpVariable %_ptr_Uniform_type_StructuredBuffer_LightData Uniform
%AlbedoTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NormalTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%MetallicRoughnessTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
 %AOTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%EmissionTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%LightmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
  %ShadowMap = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%ShadowSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_FragCoord = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD8 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD9 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD10 = OpVariable %_ptr_Input_uint Input
%in_var_TEXCOORD11 = OpVariable %_ptr_Input_float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target1 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target2 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target3 = OpVariable %_ptr_Output_v4float Output
        %110 = OpUndef %v3float
%float_0_111111112 = OpConstant %float 0.111111112
%float_0_318309873 = OpConstant %float 0.318309873
        %113 = OpConstantComposite %v3float %float_0_318309873 %float_0_318309873 %float_0_318309873
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
     %uint_3 = OpConstant %uint 3
     %uint_5 = OpConstant %uint 5
     %uint_6 = OpConstant %uint 6
       %main = OpFunction %void None %101
        %118 = OpLabel
        %119 = OpLoad %v4float %gl_FragCoord
        %120 = OpLoad %v3float %in_var_TEXCOORD0
        %121 = OpLoad %v3float %in_var_TEXCOORD1
        %122 = OpLoad %v3float %in_var_TEXCOORD2
        %123 = OpLoad %v3float %in_var_TEXCOORD3
        %124 = OpLoad %v2float %in_var_TEXCOORD4
        %125 = OpLoad %v2float %in_var_TEXCOORD5
        %126 = OpLoad %v4float %in_var_TEXCOORD6
        %127 = OpLoad %v3float %in_var_TEXCOORD7
        %128 = OpLoad %v4float %in_var_TEXCOORD8
        %129 = OpLoad %v4float %in_var_TEXCOORD9
        %130 = OpLoad %uint %in_var_TEXCOORD10
        %131 = OpLoad %float %in_var_TEXCOORD11
        %132 = OpAccessChain %_ptr_Uniform_MaterialData %Materials %int_0 %130
        %133 = OpAccessChain %_ptr_Uniform_v4float %132 %uint_0
        %134 = OpLoad %v4float %133
        %135 = OpAccessChain %_ptr_Uniform_float %132 %uint_1
        %136 = OpLoad %float %135
        %137 = OpAccessChain %_ptr_Uniform_float %132 %uint_2
        %138 = OpLoad %float %137
        %139 = OpAccessChain %_ptr_Uniform_float %132 %uint_3
        %140 = OpLoad %float %139
        %141 = OpAccessChain %_ptr_Uniform_float %132 %uint_4
        %142 = OpLoad %float %141
        %143 = OpAccessChain %_ptr_Uniform_v4float %132 %uint_5
        %144 = OpLoad %v4float %143
        %145 = OpAccessChain %_ptr_Uniform_uint %132 %uint_6
        %146 = OpLoad %uint %145
        %147 = OpVectorShuffle %v2float %144 %144 0 1
        %148 = OpFMul %v2float %124 %147
        %149 = OpVectorShuffle %v2float %144 %144 2 3
        %150 = OpFAdd %v2float %148 %149
        %151 = OpVectorShuffle %v3float %134 %134 0 1 2
        %152 = OpBitwiseAnd %uint %146 %uint_1
        %153 = OpINotEqual %bool %152 %uint_0
               OpSelectionMerge %154 None
               OpBranchConditional %153 %155 %154
        %155 = OpLabel
        %156 = OpLoad %type_2d_image_array %AlbedoTextures
        %157 = OpLoad %type_sampler %LinearSampler
        %158 = OpConvertUToF %float %130
        %159 = OpCompositeExtract %float %150 0
        %160 = OpCompositeExtract %float %150 1
        %161 = OpCompositeConstruct %v3float %159 %160 %158
        %162 = OpSampledImage %type_sampled_image %156 %157
        %163 = OpImageSampleImplicitLod %v4float %162 %161 None
        %164 = OpVectorShuffle %v3float %163 %163 0 1 2
        %165 = OpFMul %v3float %151 %164
               OpBranch %154
        %154 = OpLabel
        %166 = OpPhi %v3float %151 %118 %165 %155
        %167 = OpVectorShuffle %v3float %126 %126 0 1 2
        %168 = OpFMul %v3float %166 %167
        %169 = OpBitwiseAnd %uint %146 %uint_2
        %170 = OpINotEqual %bool %169 %uint_0
               OpSelectionMerge %171 None
               OpBranchConditional %170 %172 %171
        %172 = OpLabel
        %173 = OpLoad %type_2d_image_array %NormalTextures
        %174 = OpLoad %type_sampler %LinearSampler
        %175 = OpConvertUToF %float %130
        %176 = OpCompositeExtract %float %150 0
        %177 = OpCompositeExtract %float %150 1
        %178 = OpCompositeConstruct %v3float %176 %177 %175
        %179 = OpSampledImage %type_sampled_image %173 %174
        %180 = OpImageSampleImplicitLod %v4float %179 %178 None
        %181 = OpVectorShuffle %v3float %180 %180 0 1 2
        %182 = OpVectorTimesScalar %v3float %181 %float_2
        %183 = OpFSub %v3float %182 %71
        %184 = OpCompositeConstruct %mat3v3float %122 %123 %121
        %185 = OpMatrixTimesVector %v3float %184 %183
        %186 = OpExtInst %v3float %1 Normalize %185
               OpBranch %171
        %171 = OpLabel
        %187 = OpPhi %v3float %121 %154 %186 %172
        %188 = OpBitwiseAnd %uint %146 %uint_4
        %189 = OpINotEqual %bool %188 %uint_0
               OpSelectionMerge %190 None
               OpBranchConditional %189 %191 %190
        %191 = OpLabel
        %192 = OpLoad %type_2d_image_array %MetallicRoughnessTextures
        %193 = OpLoad %type_sampler %LinearSampler
        %194 = OpConvertUToF %float %130
        %195 = OpCompositeExtract %float %150 0
        %196 = OpCompositeExtract %float %150 1
        %197 = OpCompositeConstruct %v3float %195 %196 %194
        %198 = OpSampledImage %type_sampled_image %192 %193
        %199 = OpImageSampleImplicitLod %v4float %198 %197 None
        %200 = OpCompositeExtract %float %199 0
        %201 = OpFMul %float %136 %200
        %202 = OpCompositeExtract %float %199 1
        %203 = OpFMul %float %138 %202
               OpBranch %190
        %190 = OpLabel
        %204 = OpPhi %float %138 %171 %203 %191
        %205 = OpPhi %float %136 %171 %201 %191
        %206 = OpBitwiseAnd %uint %146 %uint_8
        %207 = OpINotEqual %bool %206 %uint_0
               OpSelectionMerge %208 None
               OpBranchConditional %207 %209 %208
        %209 = OpLabel
        %210 = OpLoad %type_2d_image_array %AOTextures
        %211 = OpLoad %type_sampler %LinearSampler
        %212 = OpConvertUToF %float %130
        %213 = OpCompositeExtract %float %150 0
        %214 = OpCompositeExtract %float %150 1
        %215 = OpCompositeConstruct %v3float %213 %214 %212
        %216 = OpSampledImage %type_sampled_image %210 %211
        %217 = OpImageSampleImplicitLod %v4float %216 %215 None
        %218 = OpCompositeExtract %float %217 0
        %219 = OpFMul %float %140 %218
               OpBranch %208
        %208 = OpLabel
        %220 = OpPhi %float %140 %190 %219 %209
        %221 = OpBitwiseAnd %uint %146 %uint_16
        %222 = OpINotEqual %bool %221 %uint_0
               OpSelectionMerge %223 None
               OpBranchConditional %222 %224 %223
        %224 = OpLabel
        %225 = OpLoad %type_2d_image_array %EmissionTextures
        %226 = OpLoad %type_sampler %LinearSampler
        %227 = OpConvertUToF %float %130
        %228 = OpCompositeExtract %float %150 0
        %229 = OpCompositeExtract %float %150 1
        %230 = OpCompositeConstruct %v3float %228 %229 %227
        %231 = OpSampledImage %type_sampled_image %225 %226
        %232 = OpImageSampleImplicitLod %v4float %231 %230 None
        %233 = OpVectorShuffle %v3float %232 %232 0 1 2
        %234 = OpVectorTimesScalar %v3float %233 %142
               OpBranch %223
        %223 = OpLabel
        %235 = OpPhi %v3float %63 %208 %234 %224
        %236 = OpLoad %type_2d_image %LightmapTexture
        %237 = OpLoad %type_sampler %LinearSampler
        %238 = OpSampledImage %type_sampled_image_0 %236 %237
        %239 = OpImageSampleImplicitLod %v4float %238 %125 None
        %240 = OpVectorShuffle %v3float %239 %239 0 1 2
        %241 = OpExtInst %v3float %1 Normalize %127
        %242 = OpCompositeConstruct %v3float %205 %205 %205
        %243 = OpExtInst %v3float %1 FMix %73 %168 %242
               OpBranch %244
        %244 = OpLabel
        %245 = OpPhi %v3float %110 %223 %246 %247
        %248 = OpPhi %v3float %63 %223 %249 %247
        %250 = OpPhi %uint %uint_0 %223 %251 %247
        %252 = OpAccessChain %_ptr_Uniform_uint %LightingParams %int_0
        %253 = OpLoad %uint %252
        %254 = OpULessThan %bool %250 %253
               OpLoopMerge %255 %247 None
               OpBranchConditional %254 %256 %255
        %256 = OpLabel
        %257 = OpAccessChain %_ptr_Uniform_LightData %Lights %int_0 %250
        %258 = OpAccessChain %_ptr_Uniform_v3float %257 %uint_0
        %259 = OpLoad %v3float %258
        %260 = OpAccessChain %_ptr_Uniform_v3float %257 %uint_2
        %261 = OpLoad %v3float %260
        %262 = OpAccessChain %_ptr_Uniform_float %257 %uint_3
        %263 = OpLoad %float %262
        %264 = OpAccessChain %_ptr_Uniform_v3float %257 %uint_4
        %265 = OpLoad %v3float %264
        %266 = OpAccessChain %_ptr_Uniform_float %257 %uint_5
        %267 = OpLoad %float %266
        %268 = OpAccessChain %_ptr_Uniform_uint %257 %uint_6
        %269 = OpLoad %uint %268
        %270 = OpIEqual %bool %269 %uint_0
               OpSelectionMerge %271 None
               OpBranchConditional %270 %272 %273
        %272 = OpLabel
        %274 = OpFNegate %v3float %261
        %275 = OpExtInst %v3float %1 Normalize %274
               OpBranch %271
        %273 = OpLabel
        %276 = OpIEqual %bool %269 %uint_1
               OpSelectionMerge %277 None
               OpBranchConditional %276 %278 %279
        %278 = OpLabel
        %280 = OpFSub %v3float %259 %120
        %281 = OpExtInst %v3float %1 Normalize %280
        %282 = OpExtInst %float %1 Length %280
        %283 = OpFMul %float %float_0_0900000036 %282
        %284 = OpFAdd %float %float_1 %283
        %285 = OpFMul %float %float_0_0320000015 %282
        %286 = OpFMul %float %285 %282
        %287 = OpFAdd %float %284 %286
               OpBranch %277
        %279 = OpLabel
        %288 = OpIEqual %bool %269 %uint_2
               OpSelectionMerge %289 None
               OpBranchConditional %288 %290 %289
        %290 = OpLabel
        %291 = OpFSub %v3float %259 %120
        %292 = OpExtInst %v3float %1 Normalize %291
        %293 = OpExtInst %float %1 Length %291
        %294 = OpFNegate %v3float %261
        %295 = OpExtInst %v3float %1 Normalize %294
        %296 = OpDot %float %292 %295
        %297 = OpExtInst %float %1 Cos %263
        %298 = OpFMul %float %263 %float_1_20000005
        %299 = OpExtInst %float %1 Cos %298
        %300 = OpFSub %float %297 %299
        %301 = OpFDiv %float %299 %300
        %302 = OpFSub %float %296 %301
        %303 = OpExtInst %float %1 FClamp %302 %float_0 %float_1
        %304 = OpFMul %float %float_0_0900000036 %293
        %305 = OpFAdd %float %303 %304
        %306 = OpFMul %float %float_0_0320000015 %293
        %307 = OpFMul %float %306 %293
        %308 = OpFAdd %float %305 %307
               OpBranch %289
        %289 = OpLabel
        %309 = OpPhi %float %float_1 %279 %308 %290
        %310 = OpPhi %v3float %245 %279 %292 %290
               OpBranch %277
        %277 = OpLabel
        %311 = OpPhi %float %287 %278 %309 %289
        %312 = OpPhi %v3float %281 %278 %310 %289
               OpBranch %271
        %271 = OpLabel
        %313 = OpPhi %float %float_1 %272 %311 %277
        %246 = OpPhi %v3float %275 %272 %312 %277
        %314 = OpFAdd %v3float %241 %246
        %315 = OpExtInst %v3float %1 Normalize %314
        %316 = OpVectorTimesScalar %v3float %265 %267
        %317 = OpVectorTimesScalar %v3float %316 %313
               OpSelectionMerge %318 None
               OpSwitch %uint_0 %319
        %319 = OpLabel
        %320 = OpCompositeExtract %float %120 0
        %321 = OpCompositeExtract %float %120 1
        %322 = OpCompositeExtract %float %120 2
        %323 = OpCompositeConstruct %v4float %320 %321 %322 %float_1
        %324 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
        %325 = OpLoad %mat4v4float %324
        %326 = OpMatrixTimesVector %v4float %325 %323
        %327 = OpVectorShuffle %v3float %326 %326 0 1 2
        %328 = OpCompositeExtract %float %326 3
        %329 = OpCompositeConstruct %v3float %328 %328 %328
        %330 = OpFDiv %v3float %327 %329
        %331 = OpVectorTimesScalar %v3float %330 %float_0_5
        %332 = OpFAdd %v3float %331 %68
        %333 = OpCompositeExtract %float %332 0
        %334 = OpFOrdLessThan %bool %333 %float_0
        %335 = OpLogicalNot %bool %334
               OpSelectionMerge %336 None
               OpBranchConditional %335 %337 %336
        %337 = OpLabel
        %338 = OpFOrdGreaterThan %bool %333 %float_1
               OpBranch %336
        %336 = OpLabel
        %339 = OpPhi %bool %true %319 %338 %337
        %340 = OpLogicalNot %bool %339
               OpSelectionMerge %341 None
               OpBranchConditional %340 %342 %341
        %342 = OpLabel
        %343 = OpCompositeExtract %float %332 1
        %344 = OpFOrdLessThan %bool %343 %float_0
               OpBranch %341
        %341 = OpLabel
        %345 = OpPhi %bool %true %336 %344 %342
        %346 = OpLogicalNot %bool %345
               OpSelectionMerge %347 None
               OpBranchConditional %346 %348 %347
        %348 = OpLabel
        %349 = OpCompositeExtract %float %332 1
        %350 = OpFOrdGreaterThan %bool %349 %float_1
               OpBranch %347
        %347 = OpLabel
        %351 = OpPhi %bool %true %341 %350 %348
               OpSelectionMerge %352 None
               OpBranchConditional %351 %353 %352
        %353 = OpLabel
               OpBranch %318
        %352 = OpLabel
        %354 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_4
        %355 = OpLoad %float %354
        %356 = OpDot %float %187 %246
        %357 = OpFSub %float %355 %356
        %358 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_3
        %359 = OpLoad %float %358
        %360 = OpExtInst %float %1 NMax %357 %359
        %361 = OpCompositeExtract %float %332 2
        %362 = OpFSub %float %361 %360
        %363 = OpAccessChain %_ptr_Uniform_v2float %LightingParams %int_5
        %364 = OpLoad %v2float %363
        %365 = OpFDiv %v2float %82 %364
               OpBranch %366
        %366 = OpLabel
        %367 = OpPhi %float %float_0 %352 %368 %369
        %370 = OpPhi %int %int_n1 %352 %371 %369
        %372 = OpSLessThanEqual %bool %370 %int_1
               OpLoopMerge %373 %369 None
               OpBranchConditional %372 %374 %373
        %374 = OpLabel
               OpBranch %375
        %375 = OpLabel
        %368 = OpPhi %float %367 %374 %376 %377
        %378 = OpPhi %int %int_n1 %374 %379 %377
        %380 = OpSLessThanEqual %bool %378 %int_1
               OpLoopMerge %381 %377 None
               OpBranchConditional %380 %377 %381
        %377 = OpLabel
        %382 = OpConvertSToF %float %370
        %383 = OpConvertSToF %float %378
        %384 = OpCompositeConstruct %v2float %382 %383
        %385 = OpFMul %v2float %384 %365
        %386 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_6
        %387 = OpLoad %float %386
        %388 = OpVectorTimesScalar %v2float %385 %387
        %389 = OpLoad %type_2d_image %ShadowMap
        %390 = OpLoad %type_sampler %ShadowSampler
        %391 = OpVectorShuffle %v2float %332 %332 0 1
        %392 = OpFAdd %v2float %391 %388
        %393 = OpSampledImage %type_sampled_image_0 %389 %390
        %394 = OpImageSampleDrefExplicitLod %float %393 %392 %362 Lod %float_0
        %376 = OpFAdd %float %368 %394
        %379 = OpIAdd %int %378 %int_1
               OpBranch %375
        %381 = OpLabel
               OpBranch %369
        %369 = OpLabel
        %371 = OpIAdd %int %370 %int_1
               OpBranch %366
        %373 = OpLabel
        %395 = OpFMul %float %367 %float_0_111111112
               OpBranch %318
        %318 = OpLabel
        %396 = OpPhi %float %float_1 %353 %395 %373
        %397 = OpVectorTimesScalar %v3float %317 %396
        %398 = OpFMul %float %204 %204
        %399 = OpFMul %float %398 %398
        %400 = OpDot %float %187 %315
        %401 = OpExtInst %float %1 NMax %400 %float_0
        %402 = OpFMul %float %401 %401
        %403 = OpFMul %float %402 %399
        %404 = OpFMul %float %float_3_14159274 %403
        %405 = OpFMul %float %404 %403
        %406 = OpFDiv %float %399 %405
        %407 = OpDot %float %187 %241
        %408 = OpExtInst %float %1 NMax %407 %float_0
        %409 = OpDot %float %187 %246
        %410 = OpExtInst %float %1 NMax %409 %float_0
        %411 = OpFDiv %float %408 %408
        %412 = OpFDiv %float %410 %410
        %413 = OpFMul %float %412 %411
        %414 = OpDot %float %315 %241
        %415 = OpExtInst %float %1 NMax %414 %float_0
        %416 = OpFSub %float %float_1 %415
        %417 = OpExtInst %float %1 FClamp %416 %float_0 %float_1
        %418 = OpExtInst %float %1 Pow %417 %float_5
        %419 = OpVectorTimesScalar %v3float %243 %418
        %420 = OpFSub %v3float %71 %419
        %421 = OpFAdd %v3float %243 %420
        %422 = OpFSub %v3float %71 %421
        %423 = OpFSub %float %float_1 %205
        %424 = OpVectorTimesScalar %v3float %422 %423
        %425 = OpFMul %float %406 %413
        %426 = OpVectorTimesScalar %v3float %421 %425
        %427 = OpFMul %float %float_4 %408
        %428 = OpFMul %float %427 %410
        %429 = OpFAdd %float %428 %float_9_99999975en05
        %430 = OpCompositeConstruct %v3float %429 %429 %429
        %431 = OpFDiv %v3float %426 %430
        %432 = OpFMul %v3float %424 %168
        %433 = OpFMul %v3float %432 %113
        %434 = OpFMul %v3float %431 %397
        %435 = OpFAdd %v3float %433 %434
        %436 = OpVectorTimesScalar %v3float %435 %410
        %249 = OpFAdd %v3float %248 %436
               OpBranch %247
        %247 = OpLabel
        %251 = OpIAdd %uint %250 %uint_1
               OpBranch %244
        %255 = OpLabel
        %437 = OpAccessChain %_ptr_Uniform_v3float %LightingParams %int_2
        %438 = OpLoad %v3float %437
        %439 = OpAccessChain %_ptr_Uniform_float %LightingParams %int_1
        %440 = OpLoad %float %439
        %441 = OpVectorTimesScalar %v3float %438 %440
        %442 = OpFMul %v3float %441 %168
        %443 = OpVectorTimesScalar %v3float %442 %220
        %444 = OpFAdd %v3float %443 %248
        %445 = OpFMul %v3float %240 %168
        %446 = OpFAdd %v3float %235 %445
        %447 = OpFAdd %v3float %444 %446
        %448 = OpVectorTimesScalar %v3float %447 %131
        %449 = OpVectorShuffle %v2float %129 %129 0 1
        %450 = OpCompositeExtract %float %129 3
        %451 = OpCompositeConstruct %v2float %450 %450
        %452 = OpFDiv %v2float %449 %451
        %453 = OpVectorTimesScalar %v2float %452 %float_0_5
        %454 = OpFAdd %v2float %453 %67
        %455 = OpVectorShuffle %v2float %128 %128 0 1
        %456 = OpCompositeExtract %float %128 3
        %457 = OpCompositeConstruct %v2float %456 %456
        %458 = OpFDiv %v2float %455 %457
        %459 = OpVectorTimesScalar %v2float %458 %float_0_5
        %460 = OpFAdd %v2float %459 %67
        %461 = OpFSub %v2float %454 %460
        %462 = OpCompositeExtract %float %134 3
        %463 = OpCompositeExtract %float %448 0
        %464 = OpCompositeExtract %float %448 1
        %465 = OpCompositeExtract %float %448 2
        %466 = OpCompositeConstruct %v4float %463 %464 %465 %462
        %467 = OpVectorTimesScalar %v3float %187 %float_0_5
        %468 = OpFAdd %v3float %467 %68
        %469 = OpCompositeExtract %float %468 0
        %470 = OpCompositeExtract %float %468 1
        %471 = OpCompositeExtract %float %468 2
        %472 = OpCompositeConstruct %v4float %469 %470 %471 %204
        %473 = OpCompositeExtract %float %119 2
        %474 = OpCompositeExtract %float %461 0
        %475 = OpCompositeExtract %float %461 1
        %476 = OpCompositeConstruct %v4float %474 %475 %473 %float_1
        %477 = OpCompositeConstruct %v4float %205 %204 %220 %142
               OpStore %out_var_SV_Target0 %466
               OpStore %out_var_SV_Target1 %472
               OpStore %out_var_SV_Target2 %476
               OpStore %out_var_SV_Target3 %477
               OpReturn
               OpFunctionEnd
