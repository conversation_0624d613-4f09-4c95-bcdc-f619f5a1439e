; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 190
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %gl_FragCoord %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_WaterMaterial "type.WaterMaterial"
               OpMemberName %type_WaterMaterial 0 "WaterColor"
               OpMemberName %type_WaterMaterial 1 "WaterAlpha"
               OpMemberName %type_WaterMaterial 2 "FresnelStrength"
               OpMemberName %type_WaterMaterial 3 "ReflectionStrength"
               OpMemberName %type_WaterMaterial 4 "RefractionStrength"
               OpMemberName %type_WaterMaterial 5 "NormalStrength"
               OpMemberName %type_WaterMaterial 6 "SpecularPower"
               OpMemberName %type_WaterMaterial 7 "SpecularColor"
               OpMemberName %type_WaterMaterial 8 "FoamThreshold"
               OpMemberName %type_WaterMaterial 9 "FoamColor"
               OpMemberName %type_WaterMaterial 10 "Time"
               OpName %WaterMaterial "WaterMaterial"
               OpName %type_2d_image "type.2d.image"
               OpName %WaterNormalTexture "WaterNormalTexture"
               OpName %ReflectionTexture "ReflectionTexture"
               OpName %RefractionTexture "RefractionTexture"
               OpName %FoamTexture "FoamTexture"
               OpName %DepthTexture "DepthTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %gl_FragCoord BuiltIn FragCoord
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %WaterMaterial DescriptorSet 0
               OpDecorate %WaterMaterial Binding 0
               OpDecorate %WaterNormalTexture DescriptorSet 0
               OpDecorate %WaterNormalTexture Binding 0
               OpDecorate %ReflectionTexture DescriptorSet 0
               OpDecorate %ReflectionTexture Binding 1
               OpDecorate %RefractionTexture DescriptorSet 0
               OpDecorate %RefractionTexture Binding 2
               OpDecorate %FoamTexture DescriptorSet 0
               OpDecorate %FoamTexture Binding 3
               OpDecorate %DepthTexture DescriptorSet 0
               OpDecorate %DepthTexture Binding 4
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_WaterMaterial 0 Offset 0
               OpMemberDecorate %type_WaterMaterial 1 Offset 12
               OpMemberDecorate %type_WaterMaterial 2 Offset 16
               OpMemberDecorate %type_WaterMaterial 3 Offset 20
               OpMemberDecorate %type_WaterMaterial 4 Offset 24
               OpMemberDecorate %type_WaterMaterial 5 Offset 28
               OpMemberDecorate %type_WaterMaterial 6 Offset 32
               OpMemberDecorate %type_WaterMaterial 7 Offset 36
               OpMemberDecorate %type_WaterMaterial 8 Offset 48
               OpMemberDecorate %type_WaterMaterial 9 Offset 52
               OpMemberDecorate %type_WaterMaterial 10 Offset 64
               OpDecorate %type_WaterMaterial Block
        %int = OpTypeInt 32 1
      %int_2 = OpConstant %int 2
      %int_4 = OpConstant %int 4
      %int_3 = OpConstant %int 3
      %float = OpTypeFloat 32
    %float_4 = OpConstant %float 4
      %int_7 = OpConstant %int 7
    %float_2 = OpConstant %float 2
  %float_0_5 = OpConstant %float 0.5
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
         %33 = OpConstantComposite %v3float %float_1 %float_1 %float_1
      %int_5 = OpConstant %int 5
    %v2float = OpTypeVector %float 2
         %36 = OpConstantComposite %v2float %float_0_5 %float_0_5
      %int_6 = OpConstant %int 6
%float_0_0199999996 = OpConstant %float 0.0199999996
%float_0_00999999978 = OpConstant %float 0.00999999978
      %int_0 = OpConstant %int 0
%float_0_300000012 = OpConstant %float 0.300000012
         %42 = OpConstantComposite %v3float %float_0_300000012 %float_0_300000012 %float_0_300000012
         %43 = OpConstantComposite %v3float %float_0_5 %float_1 %float_0_300000012
    %float_0 = OpConstant %float 0
      %int_8 = OpConstant %int 8
    %float_8 = OpConstant %float 8
      %int_9 = OpConstant %int 9
      %int_1 = OpConstant %int 1
%type_WaterMaterial = OpTypeStruct %v3float %float %float %float %float %float %float %v3float %float %v3float %float
%_ptr_Uniform_type_WaterMaterial = OpTypePointer Uniform %type_WaterMaterial
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %58 = OpTypeFunction %void
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%WaterMaterial = OpVariable %_ptr_Uniform_type_WaterMaterial Uniform
%WaterNormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%ReflectionTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%RefractionTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%FoamTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DepthTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_FragCoord = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_v2float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
       %main = OpFunction %void None %58
         %61 = OpLabel
         %62 = OpLoad %v4float %gl_FragCoord
         %63 = OpLoad %v3float %in_var_TEXCOORD1
         %64 = OpLoad %v2float %in_var_TEXCOORD2
         %65 = OpLoad %v3float %in_var_TEXCOORD3
         %66 = OpLoad %v4float %in_var_TEXCOORD4
         %67 = OpLoad %v4float %in_var_TEXCOORD5
         %68 = OpLoad %v2float %in_var_TEXCOORD6
         %69 = OpExtInst %v3float %1 Normalize %63
         %70 = OpExtInst %v3float %1 Normalize %65
         %71 = OpVectorTimesScalar %v2float %64 %float_4
         %72 = OpFAdd %v2float %71 %68
         %73 = OpVectorTimesScalar %v2float %64 %float_2
         %74 = OpVectorTimesScalar %v2float %68 %float_0_5
         %75 = OpFSub %v2float %73 %74
         %76 = OpLoad %type_2d_image %WaterNormalTexture
         %77 = OpLoad %type_sampler %LinearSampler
         %78 = OpSampledImage %type_sampled_image %76 %77
         %79 = OpImageSampleImplicitLod %v4float %78 %72 None
         %80 = OpVectorShuffle %v3float %79 %79 0 1 2
         %81 = OpVectorTimesScalar %v3float %80 %float_2
         %82 = OpFSub %v3float %81 %33
         %83 = OpLoad %type_2d_image %WaterNormalTexture
         %84 = OpLoad %type_sampler %LinearSampler
         %85 = OpSampledImage %type_sampled_image %83 %84
         %86 = OpImageSampleImplicitLod %v4float %85 %75 None
         %87 = OpVectorShuffle %v3float %86 %86 0 1 2
         %88 = OpVectorTimesScalar %v3float %87 %float_2
         %89 = OpFSub %v3float %88 %33
         %90 = OpFAdd %v3float %82 %89
         %91 = OpExtInst %v3float %1 Normalize %90
         %92 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_5
         %93 = OpLoad %float %92
         %94 = OpVectorTimesScalar %v3float %91 %93
         %95 = OpFAdd %v3float %69 %94
         %96 = OpExtInst %v3float %1 Normalize %95
         %97 = OpVectorShuffle %v2float %66 %66 0 1
         %98 = OpCompositeExtract %float %66 3
         %99 = OpCompositeConstruct %v2float %98 %98
        %100 = OpFDiv %v2float %97 %99
        %101 = OpVectorTimesScalar %v2float %100 %float_0_5
        %102 = OpFAdd %v2float %101 %36
        %103 = OpVectorShuffle %v2float %67 %67 0 1
        %104 = OpCompositeExtract %float %67 3
        %105 = OpCompositeConstruct %v2float %104 %104
        %106 = OpFDiv %v2float %103 %105
        %107 = OpVectorTimesScalar %v2float %106 %float_0_5
        %108 = OpFAdd %v2float %107 %36
        %109 = OpVectorShuffle %v2float %96 %96 0 2
        %110 = OpVectorTimesScalar %v2float %109 %float_0_0199999996
        %111 = OpFAdd %v2float %102 %110
        %112 = OpVectorTimesScalar %v2float %109 %float_0_00999999978
        %113 = OpFAdd %v2float %108 %112
        %114 = OpLoad %type_2d_image %ReflectionTexture
        %115 = OpLoad %type_sampler %LinearSampler
        %116 = OpSampledImage %type_sampled_image %114 %115
        %117 = OpImageSampleImplicitLod %v4float %116 %111 None
        %118 = OpVectorShuffle %v3float %117 %117 0 1 2
        %119 = OpLoad %type_2d_image %RefractionTexture
        %120 = OpLoad %type_sampler %LinearSampler
        %121 = OpSampledImage %type_sampled_image %119 %120
        %122 = OpImageSampleImplicitLod %v4float %121 %113 None
        %123 = OpVectorShuffle %v3float %122 %122 0 1 2
        %124 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_2
        %125 = OpLoad %float %124
        %126 = OpDot %float %70 %96
        %127 = OpFSub %float %float_1 %126
        %128 = OpExtInst %float %1 FClamp %127 %float_0 %float_1
        %129 = OpExtInst %float %1 Pow %128 %125
        %130 = OpCompositeConstruct %v3float %129 %129 %129
        %131 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_4
        %132 = OpLoad %float %131
        %133 = OpVectorTimesScalar %v3float %123 %132
        %134 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_3
        %135 = OpLoad %float %134
        %136 = OpVectorTimesScalar %v3float %118 %135
        %137 = OpExtInst %v3float %1 FMix %133 %136 %130
        %138 = OpAccessChain %_ptr_Uniform_v3float %WaterMaterial %int_0
        %139 = OpLoad %v3float %138
        %140 = OpExtInst %v3float %1 FMix %137 %139 %42
        %141 = OpExtInst %v3float %1 Normalize %43
        %142 = OpFAdd %v3float %141 %70
        %143 = OpExtInst %v3float %1 Normalize %142
        %144 = OpDot %float %96 %143
        %145 = OpExtInst %float %1 NMax %float_0 %144
        %146 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_6
        %147 = OpLoad %float %146
        %148 = OpExtInst %float %1 Pow %145 %147
        %149 = OpAccessChain %_ptr_Uniform_v3float %WaterMaterial %int_7
        %150 = OpLoad %v3float %149
        %151 = OpVectorTimesScalar %v3float %150 %148
        %152 = OpFAdd %v3float %140 %151
        %153 = OpLoad %type_2d_image %DepthTexture
        %154 = OpLoad %type_sampler %LinearSampler
        %155 = OpSampledImage %type_sampled_image %153 %154
        %156 = OpImageSampleImplicitLod %v4float %155 %113 None
        %157 = OpCompositeExtract %float %156 0
        %158 = OpCompositeExtract %float %62 2
        %159 = OpCompositeExtract %float %62 3
        %160 = OpFDiv %float %158 %159
        %161 = OpFSub %float %157 %160
        %162 = OpExtInst %float %1 FAbs %161
        %163 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_8
        %164 = OpLoad %float %163
        %165 = OpFDiv %float %162 %164
        %166 = OpExtInst %float %1 FClamp %165 %float_0 %float_1
        %167 = OpFSub %float %float_1 %166
        %168 = OpVectorTimesScalar %v2float %64 %float_8
        %169 = OpVectorTimesScalar %v2float %68 %float_2
        %170 = OpFAdd %v2float %168 %169
        %171 = OpLoad %type_2d_image %FoamTexture
        %172 = OpLoad %type_sampler %LinearSampler
        %173 = OpSampledImage %type_sampled_image %171 %172
        %174 = OpImageSampleImplicitLod %v4float %173 %170 None
        %175 = OpCompositeExtract %float %174 0
        %176 = OpFMul %float %167 %175
        %177 = OpAccessChain %_ptr_Uniform_v3float %WaterMaterial %int_9
        %178 = OpLoad %v3float %177
        %179 = OpCompositeConstruct %v3float %176 %176 %176
        %180 = OpExtInst %v3float %1 FMix %152 %178 %179
        %181 = OpAccessChain %_ptr_Uniform_float %WaterMaterial %int_1
        %182 = OpLoad %float %181
        %183 = OpFMul %float %176 %float_0_5
        %184 = OpFAdd %float %182 %183
        %185 = OpExtInst %float %1 FClamp %184 %float_0 %float_1
        %186 = OpCompositeExtract %float %180 0
        %187 = OpCompositeExtract %float %180 1
        %188 = OpCompositeExtract %float %180 2
        %189 = OpCompositeConstruct %v4float %186 %187 %188 %185
               OpStore %out_var_SV_TARGET %189
               OpReturn
               OpFunctionEnd
