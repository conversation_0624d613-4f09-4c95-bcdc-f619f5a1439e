; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 136
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %gl_VertexIndex %gl_InstanceIndex %in_var_COLOR %in_var_NORMAL %in_var_TANGENT %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %gl_Position %out_var_TEXCOORD0 %out_var_NORMAL %out_var_POSITION
               OpSource HLSL 600
               OpName %type_ConstantBuffer_Struct__MeshViewerVSCB "type.ConstantBuffer.Struct__MeshViewerVSCB"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 0 "RemapRanges"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 1 "ViewMode"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 2 "_padding0"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 3 "ViewProjMtx"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerVSCB 4 "ViewerColor"
               OpName %_MeshViewerVSCB "_MeshViewerVSCB"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_COLOR "in.var.COLOR"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_NORMAL "out.var.NORMAL"
               OpName %out_var_POSITION "out.var.POSITION"
               OpName %main "main"
               OpDecorate %gl_VertexIndex BuiltIn VertexIndex
               OpDecorate %gl_InstanceIndex BuiltIn InstanceIndex
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_COLOR Location 1
               OpDecorate %in_var_NORMAL Location 2
               OpDecorate %in_var_TANGENT Location 3
               OpDecorate %in_var_TEXCOORD0 Location 4
               OpDecorate %in_var_TEXCOORD1 Location 5
               OpDecorate %in_var_TEXCOORD2 Location 6
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_NORMAL Location 1
               OpDecorate %out_var_POSITION Location 2
               OpDecorate %_MeshViewerVSCB DescriptorSet 0
               OpDecorate %_MeshViewerVSCB Binding 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 0 Offset 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 1 Offset 4
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 2 Offset 8
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 Offset 16
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 MatrixStride 16
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 3 RowMajor
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB 4 Offset 80
               OpDecorate %type_ConstantBuffer_Struct__MeshViewerVSCB Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
    %v4float = OpTypeVector %float 4
    %v3float = OpTypeVector %float 3
    %float_1 = OpConstant %float 1
    %float_0 = OpConstant %float 0
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
  %float_0_5 = OpConstant %float 0.5
         %31 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
    %v2float = OpTypeVector %float 2
%mat4v4float = OpTypeMatrix %v4float 4
%type_ConstantBuffer_Struct__MeshViewerVSCB = OpTypeStruct %uint %int %v2float %mat4v4float %v4float
%_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerVSCB = OpTypePointer Uniform %type_ConstantBuffer_Struct__MeshViewerVSCB
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_uint = OpTypePointer Input %uint
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_int = OpTypePointer Input %int
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
       %void = OpTypeVoid
         %43 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
       %bool = OpTypeBool
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
%_MeshViewerVSCB = OpVariable %_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerVSCB Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%gl_VertexIndex = OpVariable %_ptr_Input_uint Input
%gl_InstanceIndex = OpVariable %_ptr_Input_uint Input
%in_var_COLOR = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_int Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_int Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v4float Output
%out_var_NORMAL = OpVariable %_ptr_Output_v3float Output
%out_var_POSITION = OpVariable %_ptr_Output_v3float Output
       %main = OpFunction %void None %43
         %49 = OpLabel
         %50 = OpLoad %v3float %in_var_POSITION
         %51 = OpLoad %uint %gl_VertexIndex
         %52 = OpLoad %uint %gl_InstanceIndex
         %53 = OpLoad %v3float %in_var_COLOR
         %54 = OpLoad %v3float %in_var_NORMAL
         %55 = OpLoad %v4float %in_var_TANGENT
         %56 = OpLoad %v2float %in_var_TEXCOORD0
         %57 = OpLoad %int %in_var_TEXCOORD1
         %58 = OpLoad %int %in_var_TEXCOORD2
         %59 = OpCompositeExtract %float %50 0
         %60 = OpCompositeExtract %float %50 1
         %61 = OpCompositeExtract %float %50 2
         %62 = OpCompositeConstruct %v4float %59 %60 %61 %float_1
         %63 = OpAccessChain %_ptr_Uniform_mat4v4float %_MeshViewerVSCB %int_3
         %64 = OpLoad %mat4v4float %63
         %65 = OpMatrixTimesVector %v4float %64 %62
         %66 = OpAccessChain %_ptr_Uniform_int %_MeshViewerVSCB %int_1
         %67 = OpLoad %int %66
               OpSelectionMerge %68 None
               OpSwitch %67 %69 0 %70 1 %71 2 %72 3 %73 4 %74 5 %75 6 %76 7 %77 8 %78 9 %79 10 %80
         %70 = OpLabel
               OpBranch %68
         %71 = OpLabel
         %81 = OpVectorShuffle %v3float %65 %65 0 1 2
         %82 = OpCompositeExtract %float %65 3
         %83 = OpCompositeConstruct %v3float %82 %82 %82
         %84 = OpFDiv %v3float %81 %83
         %85 = OpCompositeExtract %float %84 0
         %86 = OpCompositeExtract %float %84 1
         %87 = OpCompositeExtract %float %84 2
         %88 = OpCompositeConstruct %v4float %85 %86 %87 %float_1
               OpBranch %68
         %72 = OpLabel
         %89 = OpConvertUToF %float %51
         %90 = OpCompositeConstruct %v4float %89 %float_0 %float_0 %float_1
               OpBranch %68
         %73 = OpLabel
         %91 = OpConvertUToF %float %52
         %92 = OpCompositeConstruct %v4float %91 %float_0 %float_0 %float_1
               OpBranch %68
         %74 = OpLabel
         %93 = OpCompositeExtract %float %53 0
         %94 = OpCompositeExtract %float %53 1
         %95 = OpCompositeExtract %float %53 2
         %96 = OpCompositeConstruct %v4float %93 %94 %95 %float_1
               OpBranch %68
         %75 = OpLabel
         %97 = OpExtInst %v3float %1 Normalize %54
         %98 = OpAccessChain %_ptr_Uniform_uint %_MeshViewerVSCB %int_0
         %99 = OpLoad %uint %98
        %100 = OpINotEqual %bool %99 %uint_0
               OpSelectionMerge %101 None
               OpBranchConditional %100 %102 %103
        %102 = OpLabel
        %104 = OpFAdd %v3float %97 %31
               OpBranch %101
        %103 = OpLabel
               OpBranch %101
        %101 = OpLabel
        %105 = OpPhi %v3float %104 %102 %97 %103
        %106 = OpCompositeExtract %float %105 0
        %107 = OpCompositeExtract %float %105 1
        %108 = OpCompositeExtract %float %105 2
        %109 = OpCompositeConstruct %v4float %106 %107 %108 %float_1
               OpBranch %68
         %76 = OpLabel
        %110 = OpVectorShuffle %v3float %55 %55 0 1 2
        %111 = OpExtInst %v3float %1 Normalize %110
        %112 = OpAccessChain %_ptr_Uniform_uint %_MeshViewerVSCB %int_0
        %113 = OpLoad %uint %112
        %114 = OpINotEqual %bool %113 %uint_0
               OpSelectionMerge %115 None
               OpBranchConditional %114 %116 %117
        %116 = OpLabel
        %118 = OpFAdd %v3float %111 %31
               OpBranch %115
        %117 = OpLabel
               OpBranch %115
        %115 = OpLabel
        %119 = OpPhi %v3float %118 %116 %111 %117
        %120 = OpCompositeExtract %float %119 0
        %121 = OpCompositeExtract %float %119 1
        %122 = OpCompositeExtract %float %119 2
        %123 = OpCompositeConstruct %v4float %120 %121 %122 %float_1
               OpBranch %68
         %77 = OpLabel
        %124 = OpCompositeExtract %float %56 0
        %125 = OpCompositeExtract %float %56 1
        %126 = OpCompositeConstruct %v4float %124 %125 %float_0 %float_1
               OpBranch %68
         %78 = OpLabel
        %127 = OpConvertSToF %float %57
        %128 = OpCompositeConstruct %v4float %127 %float_0 %float_0 %float_1
               OpBranch %68
         %79 = OpLabel
        %129 = OpConvertSToF %float %58
        %130 = OpCompositeConstruct %v4float %129 %float_0 %float_0 %float_1
               OpBranch %68
         %80 = OpLabel
        %131 = OpAccessChain %_ptr_Uniform_v4float %_MeshViewerVSCB %int_4
        %132 = OpLoad %v4float %131
               OpBranch %68
         %69 = OpLabel
        %133 = OpAccessChain %_ptr_Uniform_v4float %_MeshViewerVSCB %int_4
        %134 = OpLoad %v4float %133
               OpBranch %68
         %68 = OpLabel
        %135 = OpPhi %v4float %62 %70 %88 %71 %90 %72 %92 %73 %96 %74 %109 %101 %123 %115 %126 %77 %128 %78 %130 %79 %132 %80 %134 %69
               OpStore %gl_Position %65
               OpStore %out_var_TEXCOORD0 %135
               OpStore %out_var_NORMAL %54
               OpStore %out_var_POSITION %50
               OpReturn
               OpFunctionEnd
