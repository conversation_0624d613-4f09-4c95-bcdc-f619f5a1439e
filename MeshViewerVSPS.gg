{"$schema": "gigischema.json", "version": "1.0", "variables": [{"name": "CameraPos", "type": "Float3"}, {"name": "InvViewProjMtx", "type": "Float4x4"}, {"name": "ViewProjMtx", "type": "Float4x4"}, {"name": "ViewerColor", "type": "Float4", "dflt": "1, 1, 1, 1", "visibility": "User"}, {"name": "InvViewMtx", "type": "Float4x4"}, {"name": "ViewMode", "type": "Int", "dflt": "PlasticShaded", "visibility": "User", "Enum": "ViewModes"}, {"name": "RenderSize", "type": "Uint2", "dflt": "1024,768", "visibility": "User"}, {"name": "RemapRanges", "comment": "Remaps Normals and Tangents to [0-1] range when using their respective ViewMode", "type": "Bool", "dflt": "1", "visibility": "User"}], "shaders": [{"name": "MeshViewerVS", "fileName": "MeshViewerVS.hlsl", "type": "Vertex", "entryPoint": "vs<PERSON>in", "samplers": [{"name": "samLinear"}]}, {"name": "MeshViewerPS", "fileName": "MeshViewerPS.hlsl", "type": "Pixel", "entryPoint": "psmain", "samplers": [{"name": "samLinear"}]}, {"name": "MeshViewerCS", "fileName": "MeshViewerCS.hlsl", "entryPoint": "csmain", "resources": [{"name": "VertexBuffer", "access": "UAV", "type": "<PERSON><PERSON><PERSON>", "buffer": {"typeStruct": {"name": "VertexBuffer"}}}]}], "structs": [{"name": "VertexBuffer", "fields": [{"name": "Position ", "type": "Float3", "semantic": "Position"}, {"name": "Normal", "type": "Float3", "semantic": "Normal"}, {"name": "Color", "type": "Float3", "semantic": "Color"}, {"name": "Tangent", "type": "Float4", "semantic": "Tangent"}, {"name": "UV", "type": "Float2", "semantic": "UV"}, {"name": "MaterialID", "type": "Int", "semantic": "MaterialID"}, {"name": "ShapeID", "type": "Int", "semantic": "ShapeID"}]}], "nodes": [{"resourceTexture": {"name": "De<PERSON><PERSON>", "editorPos": [-53.0, -14.0], "format": {"format": "D32_Float"}, "size": {"variable": {"name": "RenderSize"}}}}, {"resourceTexture": {"name": "ColorU8sRGB", "editorPos": [-58.0, 82.0], "visibility": "Exported", "format": {"format": "RGBA8_Unorm_sRGB"}, "size": {"variable": {"name": "RenderSize"}}}}, {"resourceBuffer": {"name": "<PERSON><PERSON><PERSON><PERSON>", "editorPos": [-197.0, -94.0], "visibility": "Imported"}}, {"actionDrawCall": {"name": "Rasterize", "editorPos": [117.0, -109.0], "linkProperties": [{}, {}, {}, {}, {}, {}, {}, {}, {}], "vertexShader": {"name": "MeshViewerVS"}, "pixelShader": {"name": "MeshViewerPS"}, "depthTargetClear": true, "depthTest": "Greater", "colorTargetSettings": [{"clear": true, "clearColor": [0.20000000298023224, 0.20000000298023224, 0.20000000298023224, 0.20000000298023224], "srcBlend": "SrcAlpha", "destBlend": "InvSrcColor"}, {}, {}, {}, {}, {}, {}, {}], "shadingRateImage": {"node": "<PERSON><PERSON><PERSON><PERSON>"}, "vertexBuffer": {"node": "MeshViewerCS", "pin": "VertexBuffer"}, "indexBuffer": {"node": "<PERSON><PERSON><PERSON><PERSON>"}, "instanceBuffer": {"node": "<PERSON><PERSON><PERSON><PERSON>"}, "colorTargets": [{"node": "ColorF32", "pin": "resource"}, {"node": "ColorU8sRGB", "pin": "resource"}, {"node": "Rasterize"}, {}, {}, {}, {}, {}], "depthTarget": {"node": "De<PERSON><PERSON>", "pin": "resource"}}}, {"actionComputeShader": {"name": "MeshViewerCS", "editorPos": [-72.0, -97.0], "linkProperties": [{}, {}], "connections": [{"srcPin": "VertexBuffer", "dstNode": "<PERSON><PERSON><PERSON><PERSON>", "dstPin": "resource"}], "shader": {"name": "MeshViewerCS"}, "dispatchSize": {"node": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "indirectBuffer": {"node": "MeshViewerCS"}}}}, {"resourceTexture": {"name": "ColorF32", "editorPos": [-53.0, 34.0], "format": {"format": "RGBA32_Float"}, "size": {"variable": {"name": "RenderSize"}}}}], "enums": [{"name": "ViewModes", "items": [{"label": "InputPos"}, {"label": "OutputPos"}, {"label": "VertexID"}, {"label": "InstanceID"}, {"label": "Color"}, {"label": "Normal"}, {"label": "Tangent"}, {"label": "UV"}, {"label": "MaterialID"}, {"label": "ShapeID"}, {"label": "ViewerColor"}, {"label": "PlasticShaded"}]}], "settings": {"dx12": {"shaderModelCs": "cs_6_6"}}}