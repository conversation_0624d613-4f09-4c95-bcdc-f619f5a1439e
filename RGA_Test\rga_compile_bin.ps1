# Compile all shaders for RGA analysis
Write-Host "Compiling all shaders for RGA analysis..." -ForegroundColor Green

$hlslOutputDir = "result\RGACompileResults"
$binaryOutputDir = "result\RGACompileResults\Binary"
$isaOutputDir = "result\RGACompileResults\ISA"
$spvasmOutputDir = "result\RGACompileResults\SPVASM"
$logFile = "result\RGACompileResults\compilation.log"

# Create output directories if they don't exist
if (-not (Test-Path $hlslOutputDir)) {
    New-Item -ItemType Directory -Path $hlslOutputDir -Force | Out-Null
}
if (-not (Test-Path $binaryOutputDir)) {
    New-Item -ItemType Directory -Path $binaryOutputDir -Force | Out-Null
}
if (-not (Test-Path $isaOutputDir)) {
    New-Item -ItemType Directory -Path $isaOutputDir -Force | Out-Null
}
if (-not (Test-Path $spvasmOutputDir)) {
    New-Item -ItemType Directory -Path $spvasmOutputDir -Force | Out-Null
}

# Initialize log file
"=== RGA Compilation Log ===" | Out-File -FilePath $logFile -Encoding UTF8
"Start time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Get all shader files from result directory
$hlslFiles = Get-ChildItem -Path "result" -Filter "*.hlsl"

Write-Host "Found $($hlslFiles.Count) HLSL files" -ForegroundColor Cyan
Write-Host "Will generate files for RGA analysis:" -ForegroundColor Cyan
Write-Host "  1. Code Object binaries (.bin) - for RGA GUI Binary Analysis mode" -ForegroundColor White
Write-Host "  2. ISA disassembly (.isa) - for manual analysis" -ForegroundColor White
Write-Host "  3. SPIR-V assembly (.spvasm) - for SPIR-V analysis" -ForegroundColor White

# Compile HLSL files for RGA
Write-Host "`nCompiling HLSL files..." -ForegroundColor Yellow

# Initialize counters
$totalFiles = $hlslFiles.Count
$successCount = 0
$failureCount = 0

foreach ($file in $hlslFiles) {
    Write-Host "`nProcessing: $($file.Name)" -ForegroundColor White

    # Determine shader profile and entry point based on filename
    $profile = ""
    $entryPoint = "main"
    $shaderType = ""

    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $profile = "vs_6_0"
        $shaderType = "vertex"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $profile = "ps_6_0"
        $shaderType = "fragment"
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $profile = "cs_6_0"
        $shaderType = "compute"
    } else {
        $profile = "vs_6_0"  # Default fallback
        $shaderType = "vertex"
    }

    # Define output files
    $spvFile = Join-Path $isaOutputDir "$($file.BaseName).spv"
    $spvasmFile = Join-Path $spvasmOutputDir "$($file.BaseName).spvasm"

    # Log current file processing
    "Processing: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8

    # Step 1: First compile HLSL to SPIR-V using DXC
    Write-Host "  [1/4] Compiling HLSL to SPIR-V..." -ForegroundColor Cyan

    try {
        if ($shaderType -eq "compute") {
            $dxcResult = & dxc -T $profile -E $entryPoint -spirv $file.FullName -Fo $spvFile
        } elseif ($shaderType -eq "vertex") {
            $dxcResult = & dxc -T $profile -E $entryPoint -spirv $file.FullName -Fo $spvFile
        } elseif ($shaderType -eq "fragment") {
            $dxcResult = & dxc -T $profile -E $entryPoint -spirv $file.FullName -Fo $spvFile
        }

        if ($LASTEXITCODE -eq 0 -and (Test-Path $spvFile)) {
            Write-Host "    [OK] SPIR-V generated" -ForegroundColor Green
            "  [1/4] SPIR-V generation: SUCCESS" | Out-File -FilePath $logFile -Append -Encoding UTF8
        } else {
            Write-Host "    [ERROR] SPIR-V generation failed - Exit code: $LASTEXITCODE" -ForegroundColor Red
            "  [1/4] SPIR-V generation: FAILED (Exit code: $LASTEXITCODE)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "    DXC Output: $dxcResult" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $failureCount++
            continue
        }
    } catch {
        Write-Host "    [ERROR] DXC exception: $($_.Exception.Message)" -ForegroundColor Red
        "  [1/4] SPIR-V generation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        $failureCount++
        continue
    }

    # Step 2: Generate SPIR-V assembly using spirv-dis
    Write-Host "  [2/4] Generating SPIR-V assembly..." -ForegroundColor Cyan
    try {
        $spirvDisResult = & spirv-dis $spvFile -o $spvasmFile 2>&1

        if ($LASTEXITCODE -eq 0 -and (Test-Path $spvasmFile)) {
            Write-Host "    [OK] SPIR-V assembly generated" -ForegroundColor Green
            "  [2/4] SPVASM generation: SUCCESS" | Out-File -FilePath $logFile -Append -Encoding UTF8
        } else {
            Write-Host "    [ERROR] SPIR-V assembly generation failed - Exit code: $LASTEXITCODE" -ForegroundColor Red
            "  [2/4] SPVASM generation: FAILED (Exit code: $LASTEXITCODE)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "    spirv-dis Output: $spirvDisResult" | Out-File -FilePath $logFile -Append -Encoding UTF8
        }
    } catch {
        Write-Host "    [ERROR] SPIR-V assembly exception: $($_.Exception.Message)" -ForegroundColor Red
        "  [2/4] SPVASM generation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
    }

    # Step 3: Generate ISA disassembly using RGA offline mode
    Write-Host "  [3/4] Generating ISA disassembly..." -ForegroundColor Cyan
    try {
        $isaOutputFile = Join-Path $isaOutputDir "$($file.BaseName).isa"

        if ($shaderType -eq "compute") {
            $result1 = & rga -s vk-spv-offline --comp $spvFile -c gfx1030 --isa $isaOutputFile  
        } elseif ($shaderType -eq "vertex") {
            $result1 = & rga -s vk-spv-offline --vert $spvFile -c gfx1030 --isa $isaOutputFile  
        } elseif ($shaderType -eq "fragment") {
            $result1 = & rga -s vk-spv-offline --frag $spvFile -c gfx1030 --isa $isaOutputFile  
        }

        # Check if ISA files were generated (RGA creates files with GPU prefix)
        $isaFiles = Get-ChildItem -Path $isaOutputDir -Filter "gfx1030_$($file.BaseName)_*.isa" -ErrorAction SilentlyContinue
        if ($LASTEXITCODE -eq 0 -and $isaFiles.Count -gt 0) {
            Write-Host "    [OK] ISA disassembly generated: $($isaFiles[0].Name)" -ForegroundColor Green
            "  [3/4] ISA generation: SUCCESS - $($isaFiles[0].Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        } else {
            Write-Host "    [ERROR] ISA generation failed - Exit code: $LASTEXITCODE" -ForegroundColor Red
            "  [3/4] ISA generation: FAILED (Exit code: $LASTEXITCODE)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "    RGA Output: $result1" | Out-File -FilePath $logFile -Append -Encoding UTF8
        }
    } catch {
        Write-Host "    [ERROR] ISA generation exception: $($_.Exception.Message)" -ForegroundColor Red
        "  [3/4] ISA generation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
    }

    # Step 4: Generate Code Object binary using RGA offline mode
    Write-Host "  [4/4] Generating Code Object binary..." -ForegroundColor Cyan
    try {
        $binaryOutputFile = Join-Path $binaryOutputDir "$($file.BaseName).bin"

        if ($shaderType -eq "compute") {
            $result2 = & rga -s vk-spv-offline --comp $spvFile -c gfx1030 -b $binaryOutputFile  
        } elseif ($shaderType -eq "vertex") {
            $result2 = & rga -s vk-spv-offline --vert $spvFile -c gfx1030 -b $binaryOutputFile  
        } elseif ($shaderType -eq "fragment") {
            $result2 = & rga -s vk-spv-offline --frag $spvFile -c gfx1030 -b $binaryOutputFile  
        }

        # Check if binary files were generated (RGA creates files with GPU prefix)
        $binaryFiles = Get-ChildItem -Path $binaryOutputDir -Filter "gfx1030_$($file.BaseName)*" -ErrorAction SilentlyContinue
        if ($LASTEXITCODE -eq 0 -and $binaryFiles.Count -gt 0) {
            Write-Host "    [OK] Code Object binary generated: $($binaryFiles[0].Name)" -ForegroundColor Green
            "  [4/4] Binary generation: SUCCESS - $($binaryFiles[0].Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $successCount++
        } else {
            Write-Host "    [ERROR] Code Object binary generation failed - Exit code: $LASTEXITCODE" -ForegroundColor Red
            "  [4/4] Binary generation: FAILED (Exit code: $LASTEXITCODE)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "    RGA Output: $result2" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $failureCount++
        }
    } catch {
        Write-Host "    [ERROR] Code Object binary exception: $($_.Exception.Message)" -ForegroundColor Red
        "  [4/4] Binary generation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        $failureCount++
    }

    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Final log entry with statistics
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Statistics ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Total files processed: $totalFiles" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Successful compilations: $successCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Failed compilations: $failureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Success rate: $(if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"End time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Complete ===" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Display statistics
Write-Host "`n=== Compilation Statistics ===" -ForegroundColor Yellow
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Successful compilations: $successCount" -ForegroundColor Green
Write-Host "Failed compilations: $failureCount" -ForegroundColor $(if ($failureCount -gt 0) { "Red" } else { "Green" })
$successRate = if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 }
Write-Host "Success rate: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host "`n[DONE] Compilation complete! Files generated for analysis:" -ForegroundColor Green
Write-Host "  - ISA disassembly files (.isa): result\RGACompileResults\ISA\" -ForegroundColor Cyan
Write-Host "  - Code Object binaries (.bin): result\RGACompileResults\Binary\" -ForegroundColor Cyan
Write-Host "  - SPIR-V assembly files (.spvasm): result\RGACompileResults\SPVASM\" -ForegroundColor Cyan
Write-Host "  - Compilation log: result\RGACompileResults\compilation.log" -ForegroundColor Cyan
Write-Host "`nHow to use:" -ForegroundColor Yellow
Write-Host "  1. For RGA GUI Binary Analysis mode: Load .bin files from Binary folder" -ForegroundColor White
Write-Host "  2. For manual analysis: View .isa files from ISA folder (text-based ISA disassembly)" -ForegroundColor White
Write-Host "  3. For SPIR-V analysis: View .spvasm files from SPVASM folder (SPIR-V assembly)" -ForegroundColor White
Write-Host "  4. Check compilation.log for detailed information about any failures" -ForegroundColor White
Write-Host "`nNote: Each shader is compiled independently, so VS and PS will have different results." -ForegroundColor Green
