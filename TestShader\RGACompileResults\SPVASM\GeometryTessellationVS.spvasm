; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 286
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TEXCOORD0 %in_var_COLOR0 %out_var_POSITION %out_var_NORMAL %out_var_TANGENT %out_var_BITANGENT %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_COLOR0 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "LightDirection"
               OpMemberName %type_PerFrame 6 "TessellationLevel"
               OpMemberName %type_PerFrame 7 "HeightmapSize"
               OpMemberName %type_PerFrame 8 "HeightScale"
               OpMemberName %type_PerFrame 9 "DetailScale"
               OpMemberName %type_PerFrame 10 "LODDistance"
               OpName %PerFrame "PerFrame"
               OpName %type_PerObject "type.PerObject"
               OpMemberName %type_PerObject 0 "WorldMatrix"
               OpMemberName %type_PerObject 1 "NormalMatrix"
               OpMemberName %type_PerObject 2 "BoundingBoxMin"
               OpMemberName %type_PerObject 3 "BoundingBoxMax"
               OpMemberName %type_PerObject 4 "TextureTiling"
               OpMemberName %type_PerObject 5 "DisplacementStrength"
               OpMemberName %type_PerObject 6 "_padding"
               OpName %PerObject "PerObject"
               OpName %type_2d_image "type.2d.image"
               OpName %HeightmapTexture "HeightmapTexture"
               OpName %DetailHeightTexture "DetailHeightTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_POSITION "out.var.POSITION"
               OpName %out_var_NORMAL "out.var.NORMAL"
               OpName %out_var_TANGENT "out.var.TANGENT"
               OpName %out_var_BITANGENT "out.var.BITANGENT"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_COLOR0 "out.var.COLOR0"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TEXCOORD0 Location 3
               OpDecorate %in_var_COLOR0 Location 4
               OpDecorate %out_var_POSITION Location 0
               OpDecorate %out_var_NORMAL Location 1
               OpDecorate %out_var_TANGENT Location 2
               OpDecorate %out_var_BITANGENT Location 3
               OpDecorate %out_var_TEXCOORD0 Location 4
               OpDecorate %out_var_TEXCOORD1 Location 5
               OpDecorate %out_var_COLOR0 Location 6
               OpDecorate %out_var_TEXCOORD2 Location 7
               OpDecorate %out_var_TEXCOORD3 Location 8
               OpDecorate %out_var_TEXCOORD4 Location 9
               OpDecorate %out_var_TEXCOORD5 Location 10
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %PerObject DescriptorSet 0
               OpDecorate %PerObject Binding 1
               OpDecorate %HeightmapTexture DescriptorSet 0
               OpDecorate %HeightmapTexture Binding 0
               OpDecorate %DetailHeightTexture DescriptorSet 0
               OpDecorate %DetailHeightTexture Binding 2
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 220
               OpMemberDecorate %type_PerFrame 7 Offset 224
               OpMemberDecorate %type_PerFrame 8 Offset 232
               OpMemberDecorate %type_PerFrame 9 Offset 236
               OpMemberDecorate %type_PerFrame 10 Offset 240
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %type_PerObject 0 Offset 0
               OpMemberDecorate %type_PerObject 0 MatrixStride 16
               OpMemberDecorate %type_PerObject 0 RowMajor
               OpMemberDecorate %type_PerObject 1 Offset 64
               OpMemberDecorate %type_PerObject 1 MatrixStride 16
               OpMemberDecorate %type_PerObject 1 RowMajor
               OpMemberDecorate %type_PerObject 2 Offset 128
               OpMemberDecorate %type_PerObject 3 Offset 144
               OpMemberDecorate %type_PerObject 4 Offset 160
               OpMemberDecorate %type_PerObject 5 Offset 168
               OpMemberDecorate %type_PerObject 6 Offset 172
               OpDecorate %type_PerObject Block
        %int = OpTypeInt 32 1
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_7 = OpConstant %int 7
%float_0_800000012 = OpConstant %float 0.800000012
    %v3float = OpTypeVector %float 3
         %38 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_800000012
      %int_9 = OpConstant %int 9
      %int_5 = OpConstant %int 5
     %int_10 = OpConstant %int 10
      %int_8 = OpConstant %int 8
    %float_0 = OpConstant %float 0
%float_0_200000003 = OpConstant %float 0.200000003
%float_0_400000006 = OpConstant %float 0.400000006
%float_0_100000001 = OpConstant %float 0.100000001
         %47 = OpConstantComposite %v3float %float_0_200000003 %float_0_400000006 %float_0_100000001
%float_0_899999976 = OpConstant %float 0.899999976
         %49 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_899999976
  %float_0_5 = OpConstant %float 0.5
%float_0_300000012 = OpConstant %float 0.300000012
         %52 = OpConstantComposite %v3float %float_0_5 %float_0_400000006 %float_0_300000012
    %float_2 = OpConstant %float 2
      %int_6 = OpConstant %int 6
    %v2float = OpTypeVector %float 2
         %56 = OpConstantComposite %v2float %float_1 %float_1
   %float_64 = OpConstant %float 64
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %v3float %float %v2float %float %float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%type_PerObject = OpTypeStruct %mat4v4float %mat4v4float %v3float %v3float %v2float %float %float
%_ptr_Uniform_type_PerObject = OpTypePointer Uniform %type_PerObject
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %72 = OpTypeFunction %void
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
       %bool = OpTypeBool
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
  %PerObject = OpVariable %_ptr_Uniform_type_PerObject Uniform
%HeightmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DetailHeightTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%out_var_POSITION = OpVariable %_ptr_Output_v3float Output
%out_var_NORMAL = OpVariable %_ptr_Output_v3float Output
%out_var_TANGENT = OpVariable %_ptr_Output_v3float Output
%out_var_BITANGENT = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v2float Output
%out_var_COLOR0 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_float Output
         %79 = OpConstantNull %v4float
       %main = OpFunction %void None %72
         %80 = OpLabel
         %81 = OpLoad %v3float %in_var_POSITION
         %82 = OpLoad %v3float %in_var_NORMAL
         %83 = OpLoad %v2float %in_var_TEXCOORD0
         %84 = OpLoad %v4float %in_var_COLOR0
         %85 = OpAccessChain %_ptr_Uniform_v2float %PerObject %int_4
         %86 = OpLoad %v2float %85
         %87 = OpFMul %v2float %83 %86
         %88 = OpLoad %type_2d_image %HeightmapTexture
         %89 = OpLoad %type_sampler %LinearSampler
         %90 = OpSampledImage %type_sampled_image %88 %89
         %91 = OpImageSampleExplicitLod %v4float %90 %87 Lod %float_0
         %92 = OpCompositeExtract %float %91 0
         %93 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_9
         %94 = OpLoad %float %93
         %95 = OpVectorTimesScalar %v2float %87 %94
         %96 = OpLoad %type_2d_image %DetailHeightTexture
         %97 = OpLoad %type_sampler %LinearSampler
         %98 = OpSampledImage %type_sampled_image %96 %97
         %99 = OpImageSampleExplicitLod %v4float %98 %95 Lod %float_0
        %100 = OpCompositeExtract %float %99 0
        %101 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_8
        %102 = OpLoad %float %101
        %103 = OpFMul %float %92 %102
        %104 = OpFSub %float %100 %float_0_5
        %105 = OpAccessChain %_ptr_Uniform_float %PerObject %int_5
        %106 = OpLoad %float %105
        %107 = OpFMul %float %104 %106
        %108 = OpFAdd %float %103 %107
        %109 = OpCompositeExtract %float %81 1
        %110 = OpFAdd %float %109 %108
        %111 = OpCompositeInsert %v3float %110 %81 1
        %112 = OpCompositeExtract %float %81 0
        %113 = OpCompositeExtract %float %81 2
        %114 = OpCompositeConstruct %v4float %112 %110 %113 %float_1
        %115 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_0
        %116 = OpLoad %mat4v4float %115
        %117 = OpMatrixTimesVector %v4float %116 %114
        %118 = OpVectorShuffle %v3float %117 %117 0 1 2
        %119 = OpAccessChain %_ptr_Uniform_v2float %PerFrame %int_7
        %120 = OpLoad %v2float %119
        %121 = OpFDiv %v2float %56 %120
        %122 = OpCompositeExtract %float %121 0
        %123 = OpFNegate %float %122
        %124 = OpCompositeConstruct %v2float %123 %float_0
        %125 = OpFAdd %v2float %87 %124
        %126 = OpLoad %type_2d_image %HeightmapTexture
        %127 = OpLoad %type_sampler %LinearSampler
        %128 = OpSampledImage %type_sampled_image %126 %127
        %129 = OpImageSampleExplicitLod %v4float %128 %125 Lod %float_0
        %130 = OpCompositeExtract %float %129 0
        %131 = OpVectorTimesScalar %v2float %125 %94
        %132 = OpLoad %type_2d_image %DetailHeightTexture
        %133 = OpLoad %type_sampler %LinearSampler
        %134 = OpSampledImage %type_sampled_image %132 %133
        %135 = OpImageSampleExplicitLod %v4float %134 %131 Lod %float_0
        %136 = OpCompositeExtract %float %135 0
        %137 = OpFMul %float %130 %102
        %138 = OpFSub %float %136 %float_0_5
        %139 = OpFMul %float %138 %106
        %140 = OpFAdd %float %137 %139
        %141 = OpCompositeConstruct %v2float %122 %float_0
        %142 = OpFAdd %v2float %87 %141
        %143 = OpLoad %type_2d_image %HeightmapTexture
        %144 = OpLoad %type_sampler %LinearSampler
        %145 = OpSampledImage %type_sampled_image %143 %144
        %146 = OpImageSampleExplicitLod %v4float %145 %142 Lod %float_0
        %147 = OpCompositeExtract %float %146 0
        %148 = OpVectorTimesScalar %v2float %142 %94
        %149 = OpLoad %type_2d_image %DetailHeightTexture
        %150 = OpLoad %type_sampler %LinearSampler
        %151 = OpSampledImage %type_sampled_image %149 %150
        %152 = OpImageSampleExplicitLod %v4float %151 %148 Lod %float_0
        %153 = OpCompositeExtract %float %152 0
        %154 = OpFMul %float %147 %102
        %155 = OpFSub %float %153 %float_0_5
        %156 = OpFMul %float %155 %106
        %157 = OpFAdd %float %154 %156
        %158 = OpCompositeExtract %float %121 1
        %159 = OpFNegate %float %158
        %160 = OpCompositeConstruct %v2float %float_0 %159
        %161 = OpFAdd %v2float %87 %160
        %162 = OpLoad %type_2d_image %HeightmapTexture
        %163 = OpLoad %type_sampler %LinearSampler
        %164 = OpSampledImage %type_sampled_image %162 %163
        %165 = OpImageSampleExplicitLod %v4float %164 %161 Lod %float_0
        %166 = OpCompositeExtract %float %165 0
        %167 = OpVectorTimesScalar %v2float %161 %94
        %168 = OpLoad %type_2d_image %DetailHeightTexture
        %169 = OpLoad %type_sampler %LinearSampler
        %170 = OpSampledImage %type_sampled_image %168 %169
        %171 = OpImageSampleExplicitLod %v4float %170 %167 Lod %float_0
        %172 = OpCompositeExtract %float %171 0
        %173 = OpFMul %float %166 %102
        %174 = OpFSub %float %172 %float_0_5
        %175 = OpFMul %float %174 %106
        %176 = OpFAdd %float %173 %175
        %177 = OpCompositeConstruct %v2float %float_0 %158
        %178 = OpFAdd %v2float %87 %177
        %179 = OpLoad %type_2d_image %HeightmapTexture
        %180 = OpLoad %type_sampler %LinearSampler
        %181 = OpSampledImage %type_sampled_image %179 %180
        %182 = OpImageSampleExplicitLod %v4float %181 %178 Lod %float_0
        %183 = OpCompositeExtract %float %182 0
        %184 = OpVectorTimesScalar %v2float %178 %94
        %185 = OpLoad %type_2d_image %DetailHeightTexture
        %186 = OpLoad %type_sampler %LinearSampler
        %187 = OpSampledImage %type_sampled_image %185 %186
        %188 = OpImageSampleExplicitLod %v4float %187 %184 Lod %float_0
        %189 = OpCompositeExtract %float %188 0
        %190 = OpFMul %float %183 %102
        %191 = OpFSub %float %189 %float_0_5
        %192 = OpFMul %float %191 %106
        %193 = OpFAdd %float %190 %192
        %194 = OpFSub %float %140 %157
        %195 = OpFMul %float %float_2 %122
        %196 = OpFDiv %float %194 %195
        %197 = OpFSub %float %176 %193
        %198 = OpFMul %float %float_2 %158
        %199 = OpFDiv %float %197 %198
        %200 = OpCompositeConstruct %v3float %196 %float_1 %199
        %201 = OpExtInst %v3float %1 Normalize %200
        %202 = OpExtInst %v3float %1 FMix %82 %201 %38
        %203 = OpExtInst %v3float %1 Normalize %202
        %204 = OpAccessChain %_ptr_Uniform_mat4v4float %PerObject %int_1
        %205 = OpLoad %mat4v4float %204
        %206 = OpCompositeExtract %v4float %205 0
        %207 = OpVectorShuffle %v3float %206 %206 0 1 2
        %208 = OpCompositeExtract %v4float %205 1
        %209 = OpVectorShuffle %v3float %208 %208 0 1 2
        %210 = OpCompositeExtract %v4float %205 2
        %211 = OpVectorShuffle %v3float %210 %210 0 1 2
        %212 = OpCompositeConstruct %mat3v3float %207 %209 %211
        %213 = OpMatrixTimesVector %v3float %212 %203
        %214 = OpExtInst %v3float %1 Normalize %213
        %215 = OpLoad %type_2d_image %HeightmapTexture
        %216 = OpLoad %type_sampler %LinearSampler
        %217 = OpSampledImage %type_sampled_image %215 %216
        %218 = OpImageSampleExplicitLod %v4float %217 %87 Lod %float_0
        %219 = OpCompositeExtract %float %218 0
        %220 = OpLoad %type_2d_image %DetailHeightTexture
        %221 = OpLoad %type_sampler %LinearSampler
        %222 = OpSampledImage %type_sampled_image %220 %221
        %223 = OpImageSampleExplicitLod %v4float %222 %95 Lod %float_0
        %224 = OpCompositeExtract %float %223 0
        %225 = OpFMul %float %219 %102
        %226 = OpFSub %float %224 %float_0_5
        %227 = OpFMul %float %226 %106
        %228 = OpFAdd %float %225 %227
        %229 = OpLoad %type_2d_image %HeightmapTexture
        %230 = OpLoad %type_sampler %LinearSampler
        %231 = OpSampledImage %type_sampled_image %229 %230
        %232 = OpImageSampleExplicitLod %v4float %231 %142 Lod %float_0
        %233 = OpCompositeExtract %float %232 0
        %234 = OpLoad %type_2d_image %DetailHeightTexture
        %235 = OpLoad %type_sampler %LinearSampler
        %236 = OpSampledImage %type_sampled_image %234 %235
        %237 = OpImageSampleExplicitLod %v4float %236 %148 Lod %float_0
        %238 = OpCompositeExtract %float %237 0
        %239 = OpFMul %float %233 %102
        %240 = OpFSub %float %238 %float_0_5
        %241 = OpFMul %float %240 %106
        %242 = OpFAdd %float %239 %241
        %243 = OpFSub %float %242 %228
        %244 = OpCompositeConstruct %v3float %122 %243 %float_0
        %245 = OpExtInst %v3float %1 Normalize %244
        %246 = OpDot %float %245 %203
        %247 = OpVectorTimesScalar %v3float %203 %246
        %248 = OpFSub %v3float %245 %247
        %249 = OpExtInst %v3float %1 Normalize %248
        %250 = OpMatrixTimesVector %v3float %212 %249
        %251 = OpExtInst %v3float %1 Normalize %250
        %252 = OpExtInst %v3float %1 Cross %214 %251
        %253 = OpExtInst %v3float %1 Normalize %252
        %254 = OpCompositeExtract %float %214 1
        %255 = OpFSub %float %float_1 %254
        %256 = OpFDiv %float %108 %102
        %257 = OpExtInst %float %1 FClamp %256 %float_0 %float_1
        %258 = OpCompositeConstruct %v3float %257 %257 %257
        %259 = OpExtInst %v3float %1 FMix %47 %49 %258
        %260 = OpVectorShuffle %v4float %84 %259 4 5 6 3
        %261 = OpFOrdGreaterThan %bool %255 %float_0_5
               OpSelectionMerge %262 None
               OpBranchConditional %261 %263 %262
        %263 = OpLabel
        %264 = OpVectorShuffle %v3float %259 %79 0 1 2
        %265 = OpFSub %float %float_0_5 %254
        %266 = OpFMul %float %265 %float_2
        %267 = OpCompositeConstruct %v3float %266 %266 %266
        %268 = OpExtInst %v3float %1 FMix %264 %52 %267
        %269 = OpVectorShuffle %v4float %84 %268 4 5 6 3
               OpBranch %262
        %262 = OpLabel
        %270 = OpPhi %v4float %260 %80 %269 %263
        %271 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_3
        %272 = OpLoad %v3float %271
        %273 = OpFSub %v3float %272 %118
        %274 = OpExtInst %float %1 Length %273
        %275 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_6
        %276 = OpLoad %float %275
        %277 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_10
        %278 = OpLoad %float %277
        %279 = OpExtInst %float %1 NMax %274 %float_1
        %280 = OpFDiv %float %278 %279
        %281 = OpFMul %float %276 %280
        %282 = OpExtInst %float %1 FClamp %281 %float_1 %float_64
        %283 = OpFMul %float %255 %float_2
        %284 = OpFAdd %float %float_1 %283
        %285 = OpFMul %float %282 %284
               OpStore %out_var_POSITION %111
               OpStore %out_var_NORMAL %214
               OpStore %out_var_TANGENT %251
               OpStore %out_var_BITANGENT %253
               OpStore %out_var_TEXCOORD0 %87
               OpStore %out_var_TEXCOORD1 %95
               OpStore %out_var_COLOR0 %270
               OpStore %out_var_TEXCOORD2 %118
               OpStore %out_var_TEXCOORD3 %285
               OpStore %out_var_TEXCOORD4 %108
               OpStore %out_var_TEXCOORD5 %255
               OpReturn
               OpFunctionEnd
