﻿MALI ANALYSIS SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Geometry  Main shader ===========  Work registers: 64 (100% used at 50% occupancy) Uniform registers: 50 (39% used) Stack use: 320 bytes  - Alloca region: 116 bytes  - Spill region: 204 bytes 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:   15.67  194.00    0.00       LS Shortest path cycles:       27.00  381.00    0.00       LS Longest path cycles:        27.00  381.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: false 
