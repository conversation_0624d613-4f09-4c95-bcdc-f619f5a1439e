﻿MALI ANALYSIS SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Fragment  Main shader ===========  Work registers: 31 (96% used at 100% occupancy) Uniform registers: 30 (23% used) Stack use: false 16-bit arithmetic: 4%                                  A      LS       V       T    Bound Total instruction cycles:    3.03    0.00    1.12    3.00        A Shortest path cycles:        3.03    0.00    1.12    3.00        A Longest path cycles:         3.03    0.00    1.12    3.00        A  A = Arithmetic, LS = Load/Store, V = Varying, T = Texture  Shader properties =================  Has uniform computation: true Has side-effects: false Modifies coverage: false Uses late ZS test: false Uses late ZS update: false Reads color buffer: false  Note: This tool shows only the shader-visible property state. API configuration may also impact the value of some properties. 
