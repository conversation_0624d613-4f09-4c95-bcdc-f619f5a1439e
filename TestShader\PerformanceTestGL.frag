#version 320 es

// Performance Test Fragment Shader - OpenGL ES Version
// Tests fragment shader performance with heavy calculations

precision highp float;

in vec3 vWorldPos;
in vec3 vNormal;
in vec2 vTexCoord;
in float vComplexityResult;

out vec4 fragColor;

uniform sampler2D uTexture;
uniform float uTime;
uniform int uComplexityLevel;
uniform vec3 uCameraPosition;

// Heavy mathematical function for performance testing
vec3 complexColorCalculation(vec2 uv, float time, int iterations)
{
    vec3 color = vec3(0.0);
    vec2 p = uv;
    
    for(int i = 0; i < iterations; i++)
    {
        float t = time + float(i) * 0.05;
        
        // Complex trigonometric calculations
        float r = sin(p.x * 10.0 + t) * cos(p.y * 10.0 + t);
        float g = sin(p.x * 15.0 + t * 1.5) * cos(p.y * 15.0 + t * 1.5);
        float b = sin(p.x * 20.0 + t * 2.0) * cos(p.y * 20.0 + t * 2.0);
        
        // Fractal-like calculations
        vec2 z = p;
        for(int j = 0; j < 5; j++)
        {
            z = vec2(z.x * z.x - z.y * z.y, 2.0 * z.x * z.y) + p * 0.5;
            r += sin(length(z) + t) * 0.1;
            g += cos(length(z) + t * 1.2) * 0.1;
            b += sin(length(z) + t * 1.8) * 0.1;
        }
        
        color += vec3(r, g, b);
        
        // Update UV for next iteration
        p = p * 1.05 + vec2(sin(t), cos(t)) * 0.01;
    }
    
    return color / float(iterations);
}

// Noise function for additional complexity
float noise(vec2 p)
{
    return fract(sin(dot(p, vec2(12.9898, 78.233))) * 43758.5453);
}

float fbm(vec2 p, int octaves)
{
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for(int i = 0; i < octaves; i++)
    {
        value += amplitude * noise(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

void main()
{
    // Sample base texture
    vec4 texColor = texture(uTexture, vTexCoord);
    
    // Perform complex color calculations
    vec3 complexColor = complexColorCalculation(vTexCoord, uTime, uComplexityLevel);
    
    // Calculate noise with multiple octaves
    float noiseValue = fbm(vTexCoord * 10.0 + vec2(uTime * 0.1), 6);
    
    // Lighting calculations
    vec3 normal = normalize(vNormal);
    vec3 viewDir = normalize(uCameraPosition - vWorldPos);
    
    // Multiple light sources for complexity
    vec3 finalColor = vec3(0.0);
    
    for(int i = 0; i < 4; i++)
    {
        float angle = float(i) * 1.57079632679 + uTime; // 90 degrees apart + rotation
        vec3 lightDir = normalize(vec3(cos(angle), 1.0, sin(angle)));
        
        float NdotL = max(0.0, dot(normal, lightDir));
        vec3 lightColor = vec3(
            0.5 + 0.5 * sin(uTime + float(i)),
            0.5 + 0.5 * cos(uTime + float(i) * 1.5),
            0.5 + 0.5 * sin(uTime + float(i) * 2.0)
        );
        
        finalColor += lightColor * NdotL;
    }
    
    // Combine all color contributions
    vec3 result = texColor.rgb * finalColor * complexColor;
    result = mix(result, vec3(noiseValue), 0.2);
    result += vec3(vComplexityResult) * 0.1;
    
    // Apply some post-processing effects
    result = pow(result, vec3(0.8)); // Gamma-like adjustment
    result = mix(result, vec3(dot(result, vec3(0.299, 0.587, 0.114))), -0.2); // Saturation boost
    
    fragColor = vec4(result, texColor.a);
}
