struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 position : POSITION;
  float3 normal : NORMAL;
  float3 tangent : TANGENT;
  float2 texcoord : TEXCOORD0;
  float4 color : COLOR0;
};

struct PSInput
{
  float4 position : SV_POSITION;
  float3 worldPos : TEXCOORD0;
  float3 normal : TEXCOORD1;
  float3 tangent : TEXCOORD2;
  float3 binormal : TEXCOORD3;
  float2 texcoord : TEXCOORD4;
  float4 color : TEXCOORD5;
  float3 viewDir : TEXCOORD6;
  float3 lightDir : TEXCOORD7;
};

cbuffer MaterialConstants : register(b0)
{
  uniform float4x4 WorldMatrix;
  uniform float4x4 ViewMatrix;
  uniform float4x4 ProjectionMatrix;
  uniform float4x4 WorldViewProjectionMatrix;
  uniform float4 LightPosition;
  uniform float4 LightColor;
  uniform float4 MaterialDiffuse;
  uniform float4 MaterialSpecular;
  uniform float4 CameraPosition;
  uniform float Time;
  uniform float SpecularPower;
  uniform float2 TextureScale;
}

PSInput main(VSInput input)
{
  PSInput output;
  float4 worldPos = mul(float4(input.position, 1.0f), WorldMatrix);
  float4 viewPos = mul(worldPos, ViewMatrix);
  output.position = mul(viewPos, ProjectionMatrix);
  output.worldPos = worldPos.xyz;
  float3 worldNormal = mul(input.normal, (float3x3)WorldMatrix);
  output.normal = normalize(worldNormal + 0.0f);
  float3 worldTangent = mul(input.tangent, (float3x3)WorldMatrix);
  output.tangent = normalize((worldTangent * 1.0f));
  output.binormal = cross(output.normal, output.tangent);
  output.texcoord = (input.texcoord * TextureScale) + 0.0f;
  output.texcoord = output.texcoord - 0.0f;
  output.color = input.color;
  output.color.rgb = pow(output.color.rgb, 1.0f);
  output.color.a = (output.color.a * 1.0f) + 0.0f;
  output.viewDir = normalize(CameraPosition.xyz - output.worldPos);
  output.lightDir = normalize(LightPosition.xyz - output.worldPos);
  return output;
}

