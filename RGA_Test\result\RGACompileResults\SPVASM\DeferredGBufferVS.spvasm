; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 77
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_NORMAL %in_var_TANGENT %in_var_BITANGENT %in_var_TEXCOORD0 %in_var_COLOR0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3 %out_var_TEXCOORD4 %out_var_TEXCOORD5 %out_var_TEXCOORD6
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "WorldMatrix"
               OpMemberName %type_PerFrame 1 "ViewMatrix"
               OpMemberName %type_PerFrame 2 "ProjectionMatrix"
               OpMemberName %type_PerFrame 3 "WorldViewProjectionMatrix"
               OpMemberName %type_PerFrame 4 "NormalMatrix"
               OpMemberName %type_PerFrame 5 "Time"
               OpName %PerFrame "PerFrame"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_TANGENT "in.var.TANGENT"
               OpName %in_var_BITANGENT "in.var.BITANGENT"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_COLOR0 "in.var.COLOR0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %out_var_TEXCOORD4 "out.var.TEXCOORD4"
               OpName %out_var_TEXCOORD5 "out.var.TEXCOORD5"
               OpName %out_var_TEXCOORD6 "out.var.TEXCOORD6"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_TANGENT Location 2
               OpDecorate %in_var_BITANGENT Location 3
               OpDecorate %in_var_TEXCOORD0 Location 4
               OpDecorate %in_var_COLOR0 Location 5
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %out_var_TEXCOORD4 Location 4
               OpDecorate %out_var_TEXCOORD5 Location 5
               OpDecorate %out_var_TEXCOORD6 Location 6
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 3 MatrixStride 16
               OpMemberDecorate %type_PerFrame 3 RowMajor
               OpMemberDecorate %type_PerFrame 4 Offset 256
               OpMemberDecorate %type_PerFrame 4 MatrixStride 16
               OpMemberDecorate %type_PerFrame 4 RowMajor
               OpMemberDecorate %type_PerFrame 5 Offset 320
               OpDecorate %type_PerFrame Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %mat4v4float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
    %v3float = OpTypeVector %float 3
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v3float = OpTypePointer Output %v3float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_float = OpTypePointer Output %float
       %void = OpTypeVoid
         %38 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%mat3v3float = OpTypeMatrix %v3float 3
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_TANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_BITANGENT = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%in_var_COLOR0 = OpVariable %_ptr_Input_v4float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD4 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD5 = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD6 = OpVariable %_ptr_Output_float Output
       %main = OpFunction %void None %38
         %41 = OpLabel
         %42 = OpLoad %v3float %in_var_POSITION
         %43 = OpLoad %v3float %in_var_NORMAL
         %44 = OpLoad %v3float %in_var_TANGENT
         %45 = OpLoad %v3float %in_var_BITANGENT
         %46 = OpLoad %v2float %in_var_TEXCOORD0
         %47 = OpLoad %v4float %in_var_COLOR0
         %48 = OpCompositeExtract %float %42 0
         %49 = OpCompositeExtract %float %42 1
         %50 = OpCompositeExtract %float %42 2
         %51 = OpCompositeConstruct %v4float %48 %49 %50 %float_1
         %52 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_0
         %53 = OpLoad %mat4v4float %52
         %54 = OpMatrixTimesVector %v4float %53 %51
         %55 = OpVectorShuffle %v3float %54 %54 0 1 2
         %56 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_3
         %57 = OpLoad %mat4v4float %56
         %58 = OpMatrixTimesVector %v4float %57 %51
         %59 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_4
         %60 = OpLoad %mat4v4float %59
         %61 = OpCompositeExtract %v4float %60 0
         %62 = OpVectorShuffle %v3float %61 %61 0 1 2
         %63 = OpCompositeExtract %v4float %60 1
         %64 = OpVectorShuffle %v3float %63 %63 0 1 2
         %65 = OpCompositeExtract %v4float %60 2
         %66 = OpVectorShuffle %v3float %65 %65 0 1 2
         %67 = OpCompositeConstruct %mat3v3float %62 %64 %66
         %68 = OpMatrixTimesVector %v3float %67 %43
         %69 = OpExtInst %v3float %1 Normalize %68
         %70 = OpMatrixTimesVector %v3float %67 %44
         %71 = OpExtInst %v3float %1 Normalize %70
         %72 = OpMatrixTimesVector %v3float %67 %45
         %73 = OpExtInst %v3float %1 Normalize %72
         %74 = OpCompositeExtract %float %58 2
         %75 = OpCompositeExtract %float %58 3
         %76 = OpFDiv %float %74 %75
               OpStore %gl_Position %58
               OpStore %out_var_TEXCOORD0 %55
               OpStore %out_var_TEXCOORD1 %69
               OpStore %out_var_TEXCOORD2 %71
               OpStore %out_var_TEXCOORD3 %73
               OpStore %out_var_TEXCOORD4 %46
               OpStore %out_var_TEXCOORD5 %47
               OpStore %out_var_TEXCOORD6 %76
               OpReturn
               OpFunctionEnd
