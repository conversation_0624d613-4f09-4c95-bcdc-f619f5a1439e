_amdgpu_ps_main:
	s_mov_b64 s[28:29], exec                                   // 000000000000: BE9C047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_getpc_b64 s[4:5]                                         // 000000000008: BE841F00
	s_mov_b32 s0, s1                                           // 00000000000C: BE800301
	s_mov_b32 s1, s5                                           // 000000000010: BE810305
	s_mov_b32 m0, s2                                           // 000000000014: BEFC0302
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx16 s[4:19], s[0:1], null                      // 00000000001C: F4100100 FA000000
	s_load_dwordx8 s[20:27], s[0:1], 0x30                      // 000000000024: F40C0500 FA000030
	v_interp_p1_f32_e32 v2, v0, attr4.x                        // 00000000002C: C8081000
	v_interp_p1_f32_e32 v3, v0, attr4.y                        // 000000000030: C80C1100
	v_interp_p1_f32_e32 v11, v0, attr3.y                       // 000000000034: C82C0D00
	v_interp_p1_f32_e32 v10, v0, attr3.x                       // 000000000038: C8280C00
	v_interp_p1_f32_e32 v14, v0, attr2.y                       // 00000000003C: C8380900
	v_interp_p2_f32_e32 v2, v1, attr4.x                        // 000000000040: C8091001
	v_interp_p2_f32_e32 v3, v1, attr4.y                        // 000000000044: C80D1101
	v_interp_p2_f32_e32 v11, v1, attr3.y                       // 000000000048: C82D0D01
	v_interp_p1_f32_e32 v12, v0, attr3.z                       // 00000000004C: C8300E00
	v_interp_p1_f32_e32 v13, v0, attr2.x                       // 000000000050: C8340800
	v_interp_p1_f32_e32 v17, v0, attr1.y                       // 000000000054: C8440500
	v_interp_p2_f32_e32 v10, v1, attr3.x                       // 000000000058: C8290C01
	v_interp_p2_f32_e32 v14, v1, attr2.y                       // 00000000005C: C8390901
	v_interp_p1_f32_e32 v15, v0, attr2.z                       // 000000000060: C83C0A00
	v_interp_p1_f32_e32 v16, v0, attr1.x                       // 000000000064: C8400400
	v_interp_p2_f32_e32 v12, v1, attr3.z                       // 000000000068: C8310E01
	v_interp_p2_f32_e32 v13, v1, attr2.x                       // 00000000006C: C8350801
	v_interp_p2_f32_e32 v17, v1, attr1.y                       // 000000000070: C8450501
	v_interp_p1_f32_e32 v18, v0, attr1.z                       // 000000000074: C8480600
	s_waitcnt lgkmcnt(0)                                       // 000000000078: BF8CC07F
	image_sample v[4:6], v[2:3], s[20:27], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000007C: F0800708 00650402
	image_sample v[7:9], v[2:3], s[4:11], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000084: F0800708 00610702
	s_load_dwordx4 s[4:7], s[0:1], null                        // 00000000008C: F4080100 FA000000
	v_interp_p2_f32_e32 v15, v1, attr2.z                       // 000000000094: C83D0A01
	v_interp_p2_f32_e32 v16, v1, attr1.x                       // 000000000098: C8410401
	v_interp_p2_f32_e32 v18, v1, attr1.z                       // 00000000009C: C8490601
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	s_clause 0x1                                               // 0000000000A4: BFA10001
	s_buffer_load_dwordx2 s[8:9], s[4:7], null                 // 0000000000A8: F4240202 FA000000
	s_buffer_load_dword s3, s[4:7], 0x8                        // 0000000000B0: F42000C2 FA000008
	s_load_dwordx16 s[36:51], s[0:1], 0xa0                     // 0000000000B8: F4100900 FA0000A0
	s_waitcnt vmcnt(1)                                         // 0000000000C0: BF8C3F71
	v_fma_f32 v5, v5, 2.0, -1.0                                // 0000000000C4: D54B0005 03CDE905
	v_fma_f32 v4, v4, 2.0, -1.0                                // 0000000000CC: D54B0004 03CDE904
	v_fma_f32 v6, v6, 2.0, -1.0                                // 0000000000D4: D54B0006 03CDE906
	v_mul_f32_e32 v19, v5, v11                                 // 0000000000DC: 10261705
	v_mul_f32_e32 v10, v5, v10                                 // 0000000000E0: 10141505
	v_mov_b32_e32 v11, 0                                       // 0000000000E4: 7E160280
	v_fmac_f32_e32 v19, v4, v14                                // 0000000000E8: 56261D04
	v_mul_f32_e32 v14, v5, v12                                 // 0000000000EC: 101C1905
	v_fmac_f32_e32 v10, v4, v13                                // 0000000000F0: 56141B04
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000000F4: BF8C0070
	v_mul_f32_e32 v5, s8, v7                                   // 0000000000F8: 100A0E08
	v_mul_f32_e32 v7, s3, v9                                   // 0000000000FC: 100E1203
	v_fmac_f32_e32 v19, v6, v17                                // 000000000100: 56262306
	v_fmac_f32_e32 v14, v4, v15                                // 000000000104: 561C1F04
	v_fmac_f32_e32 v10, v6, v16                                // 000000000108: 56142106
	v_log_f32_e32 v5, v5                                       // 00000000010C: 7E0A4F05
	v_log_f32_e32 v7, v7                                       // 000000000110: 7E0E4F07
	v_mul_f32_e32 v4, v19, v19                                 // 000000000114: 10082713
	v_fmac_f32_e32 v14, v6, v18                                // 000000000118: 561C2506
	v_mul_f32_e32 v6, s9, v8                                   // 00000000011C: 100C1009
	s_buffer_load_dword s8, s[16:19], 0xc0                     // 000000000120: F4200208 FA0000C0
	v_mov_b32_e32 v12, 0                                       // 000000000128: 7E180280
	v_fmac_f32_e32 v4, v10, v10                                // 00000000012C: 5608150A
	v_log_f32_e32 v6, v6                                       // 000000000130: 7E0C4F06
	v_mul_legacy_f32_e32 v5, 0x400ccccd, v5                    // 000000000134: 0E0A0AFF 400CCCCD
	v_mul_legacy_f32_e32 v7, 0x400ccccd, v7                    // 00000000013C: 0E0E0EFF 400CCCCD
	v_fmac_f32_e32 v4, v14, v14                                // 000000000144: 56081D0E
	v_rsq_f32_e32 v8, v4                                       // 000000000148: 7E105D04
	v_cmp_neq_f32_e32 vcc_lo, 0, v4                            // 00000000014C: 7C1A0880
	v_mul_legacy_f32_e32 v6, 0x400ccccd, v6                    // 000000000150: 0E0C0CFF 400CCCCD
	v_exp_f32_e32 v4, v5                                       // 000000000158: 7E084B05
	v_exp_f32_e32 v5, v6                                       // 00000000015C: 7E0A4B06
	v_exp_f32_e32 v6, v7                                       // 000000000160: 7E0C4B07
	s_waitcnt lgkmcnt(0)                                       // 000000000164: BF8CC07F
	s_cmp_lt_i32 s8, 1                                         // 000000000168: BF048108
	v_cndmask_b32_e32 v9, 0, v8, vcc_lo                        // 00000000016C: 02121080
	v_mul_f32_e32 v7, v9, v10                                  // 000000000170: 100E1509
	v_mul_f32_e32 v8, v9, v19                                  // 000000000174: 10102709
	v_mul_f32_e32 v9, v9, v14                                  // 000000000178: 10121D09
	v_mov_b32_e32 v10, 0                                       // 00000000017C: 7E140280
	s_cbranch_scc1 _L0                                         // 000000000180: BF8500BC
	s_load_dwordx16 s[52:67], s[0:1], 0x60                     // 000000000184: F4100D00 FA000060
	s_mov_b32 m0, s2                                           // 00000000018C: BEFC0302
	s_buffer_load_dwordx2 s[2:3], s[4:7], 0xc                  // 000000000190: F4240082 FA00000C
	v_interp_p1_f32_e32 v10, v0, attr5.y                       // 000000000198: C8281500
	v_interp_p1_f32_e32 v11, v0, attr5.x                       // 00000000019C: C82C1400
	v_interp_p1_f32_e32 v15, v0, attr5.z                       // 0000000001A0: C83C1600
	v_add_f32_e32 v20, 0xbd23d70a, v4                          // 0000000001A4: 062808FF BD23D70A
	v_add_f32_e32 v21, 0xbd23d70a, v5                          // 0000000001AC: 062A0AFF BD23D70A
	v_interp_p2_f32_e32 v10, v1, attr5.y                       // 0000000001B4: C8291501
	v_interp_p2_f32_e32 v11, v1, attr5.x                       // 0000000001B8: C82D1401
	v_interp_p2_f32_e32 v15, v1, attr5.z                       // 0000000001BC: C83D1601
	s_mov_b32 s9, 0xbea2f983                                   // 0000000001C0: BE8903FF BEA2F983
	v_add_f32_e32 v22, 0xbd23d70a, v6                          // 0000000001C8: 062C0CFF BD23D70A
	v_mul_f32_e32 v13, v10, v10                                // 0000000001D0: 101A150A
	v_interp_p1_f32_e32 v17, v0, attr0.y                       // 0000000001D4: C8440100
	s_mov_b32 s10, 0                                           // 0000000001D8: BE8A0380
	s_waitcnt lgkmcnt(0)                                       // 0000000001DC: BF8CC07F
	image_sample v12, v[2:3], s[60:67], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000001E0: F0800108 006F0C02
	image_sample v18, v[2:3], s[52:59], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000001E8: F0800108 006D1202
	v_fmac_f32_e32 v13, v11, v11                               // 0000000001F0: 561A170B
	v_interp_p2_f32_e32 v17, v1, attr0.y                       // 0000000001F4: C8450101
	v_fmac_f32_e32 v13, v15, v15                               // 0000000001F8: 561A1F0F
	v_rsq_f32_e32 v14, v13                                     // 0000000001FC: 7E1C5D0D
	v_cmp_neq_f32_e32 vcc_lo, 0, v13                           // 000000000200: 7C1A1A80
	v_cndmask_b32_e32 v16, 0, v14, vcc_lo                      // 000000000204: 02201C80
	v_mul_f32_e32 v13, v16, v10                                // 000000000208: 101A1510
	v_mul_f32_e32 v14, v16, v11                                // 00000000020C: 101C1710
	v_mul_f32_e32 v15, v16, v15                                // 000000000210: 101E1F10
	v_interp_p1_f32_e32 v16, v0, attr0.x                       // 000000000214: C8400000
	v_interp_p1_f32_e32 v0, v0, attr0.z                        // 000000000218: C8000200
	v_mul_f32_e32 v19, v8, v13                                 // 00000000021C: 10261B08
	v_mov_b32_e32 v10, 0                                       // 000000000220: 7E140280
	v_mov_b32_e32 v11, 0                                       // 000000000224: 7E160280
	v_interp_p2_f32_e32 v16, v1, attr0.x                       // 000000000228: C8410001
	v_interp_p2_f32_e32 v0, v1, attr0.z                        // 00000000022C: C8010201
	v_fmac_f32_e32 v19, v7, v14                                // 000000000230: 56261D07
	v_fmac_f32_e32 v19, v9, v15                                // 000000000234: 56261F09
	v_max_f32_e32 v26, 0, v19                                  // 000000000238: 20342680
	v_mul_f32_e32 v1, 0x40490fdb, v26                          // 00000000023C: 100234FF 40490FDB
	s_waitcnt vmcnt(1)                                         // 000000000244: BF8C3F71
	v_mul_f32_e32 v12, s3, v12                                 // 000000000248: 10181803
	s_waitcnt vmcnt(0)                                         // 00000000024C: BF8C3F70
	v_mul_f32_e32 v23, s2, v18                                 // 000000000250: 102E2402
	v_mul_f32_e32 v18, 4.0, v26                                // 000000000254: 102434F6
	v_mul_f32_e32 v12, v12, v12                                // 000000000258: 1018190C
	v_fmaak_f32 v25, s9, v23, 0x3ea2f983                       // 00000000025C: 5A322E09 3EA2F983
	v_fmaak_f32 v19, v23, v20, 0x3d23d70a                      // 000000000264: 5A262917 3D23D70A
	v_fmaak_f32 v20, v23, v21, 0x3d23d70a                      // 00000000026C: 5A282B17 3D23D70A
	v_fmaak_f32 v22, v23, v22, 0x3d23d70a                      // 000000000274: 5A2C2D17 3D23D70A
	v_mul_f32_e32 v21, v12, v12                                // 00000000027C: 102A190C
	v_mul_f32_e32 v23, v25, v4                                 // 000000000280: 102E0919
	v_mul_f32_e32 v24, v25, v5                                 // 000000000284: 10300B19
	v_mul_f32_e32 v25, v25, v6                                 // 000000000288: 10320D19
	v_mov_b32_e32 v12, 0                                       // 00000000028C: 7E180280
	v_mul_f32_e32 v26, v26, v21                                // 000000000290: 10342B1A
	s_movk_i32 s9, 0x80                                        // 000000000294: B0090080
_L1:
	s_add_i32 s3, s9, 0xffffff84                               // 000000000298: 8103FF09 FFFFFF84
	s_add_i32 s2, s9, 0xffffff80                               // 0000000002A0: 8102FF09 FFFFFF80
	s_add_i32 s11, s9, 0xffffff88                              // 0000000002A8: 810BFF09 FFFFFF88
	s_clause 0x2                                               // 0000000002B0: BFA10002
	s_buffer_load_dword s24, s[16:19], s3                      // 0000000002B4: F4200608 06000000
	s_buffer_load_dword s25, s[16:19], s2                      // 0000000002BC: F4200648 04000000
	s_buffer_load_dword s11, s[16:19], s11                     // 0000000002C4: F42002C8 16000000
	s_sub_i32 s20, s9, 64                                      // 0000000002CC: 8194C009
	s_sub_i32 s21, s9, 60                                      // 0000000002D0: 8195BC09
	s_sub_i32 s22, s9, 56                                      // 0000000002D4: 8196B809
	s_clause 0x3                                               // 0000000002D8: BFA10003
	s_buffer_load_dword s20, s[16:19], s20                     // 0000000002DC: F4200508 28000000
	s_buffer_load_dword s21, s[16:19], s21                     // 0000000002E4: F4200548 2A000000
	s_buffer_load_dword s22, s[16:19], s22                     // 0000000002EC: F4200588 2C000000
	s_buffer_load_dword s26, s[16:19], s9                      // 0000000002F4: F4200688 12000000
	s_add_i32 s23, s10, 1                                      // 0000000002FC: 8117810A
	s_cmp_lt_u32 s23, s8                                       // 000000000300: BF0A0817
	s_cselect_b64 s[2:3], -1, 0                                // 000000000304: 858280C1
	s_cmp_lt_u32 s10, 3                                        // 000000000308: BF0A830A
	s_mov_b32 s10, s23                                         // 00000000030C: BE8A0317
	s_waitcnt lgkmcnt(0)                                       // 000000000310: BF8CC07F
	v_sub_f32_e32 v27, s24, v17                                // 000000000314: 08362218
	v_sub_f32_e32 v28, s25, v16                                // 000000000318: 08382019
	v_sub_f32_e32 v29, s11, v0                                 // 00000000031C: 083A000B
	v_mul_f32_e32 v30, v27, v27                                // 000000000320: 103C371B
	v_fmac_f32_e32 v30, v28, v28                               // 000000000324: 563C391C
	v_fmac_f32_e32 v30, v29, v29                               // 000000000328: 563C3B1D
	v_rsq_f32_e32 v31, v30                                     // 00000000032C: 7E3E5D1E
	v_cmp_neq_f32_e32 vcc_lo, 0, v30                           // 000000000330: 7C1A3C80
	v_sqrt_f32_e32 v32, v30                                    // 000000000334: 7E40671E
	v_cndmask_b32_e32 v30, 0, v31, vcc_lo                      // 000000000338: 023C3E80
	v_mul_f32_e32 v31, v32, v32                                // 00000000033C: 103E4120
	v_fma_f32 v33, v30, v27, v13                               // 000000000340: D54B0021 0436371E
	v_mul_f32_e32 v32, v30, v28                                // 000000000348: 1040391E
	v_fma_f32 v28, v30, v28, v14                               // 00000000034C: D54B001C 043A391E
	v_mul_f32_e32 v27, v30, v27                                // 000000000354: 1036371E
	v_mul_f32_e32 v34, v30, v29                                // 000000000358: 10443B1E
	v_fma_f32 v29, v30, v29, v15                               // 00000000035C: D54B001D 043E3B1E
	v_mul_f32_e32 v30, v33, v33                                // 000000000364: 103C4321
	v_mul_f32_e32 v27, v27, v8                                 // 000000000368: 1036111B
	v_fmac_f32_e32 v30, v28, v28                               // 00000000036C: 563C391C
	v_fmac_f32_e32 v27, v32, v7                                // 000000000370: 56360F20
	v_fmac_f32_e32 v30, v29, v29                               // 000000000374: 563C3B1D
	v_fmac_f32_e32 v27, v34, v9                                // 000000000378: 56361322
	v_rsq_f32_e32 v32, v30                                     // 00000000037C: 7E405D1E
	v_cmp_neq_f32_e32 vcc_lo, 0, v30                           // 000000000380: 7C1A3C80
	v_max_f32_e32 v27, 0, v27                                  // 000000000384: 20363680
	v_mul_f32_e32 v30, v27, v1                                 // 000000000388: 103C031B
	v_fmaak_f32 v35, v27, v18, 0x38d1b717                      // 00000000038C: 5A46251B 38D1B717
	v_mul_f32_e32 v34, v27, v26                                // 000000000394: 1044351B
	v_cndmask_b32_e32 v32, 0, v32, vcc_lo                      // 000000000398: 02404080
	v_mul_f32_e32 v34, s26, v34                                // 00000000039C: 1044441A
	v_mul_f32_e32 v33, v32, v33                                // 0000000003A0: 10424320
	v_mul_f32_e32 v28, v32, v28                                // 0000000003A4: 10383920
	v_mul_f32_e32 v29, v32, v29                                // 0000000003A8: 103A3B20
	v_mul_f32_e32 v36, v33, v8                                 // 0000000003AC: 10481121
	v_mul_f32_e32 v32, v33, v13                                // 0000000003B0: 10401B21
	v_fmac_f32_e32 v36, v28, v7                                // 0000000003B4: 56480F1C
	v_fmac_f32_e32 v32, v28, v14                               // 0000000003B8: 56401D1C
	v_fmac_f32_e32 v36, v29, v9                                // 0000000003BC: 5648131D
	v_fmac_f32_e32 v32, v29, v15                               // 0000000003C0: 56401F1D
	v_max_f32_e32 v28, 0, v36                                  // 0000000003C4: 20384880
	v_max_f32_e32 v29, 0, v32                                  // 0000000003C8: 203A4080
	v_mul_f32_e32 v28, v28, v28                                // 0000000003CC: 1038391C
	v_sub_f32_e64 v29, 1.0, v29 clamp                          // 0000000003D0: D504801D 00023AF2
	v_mul_f32_e32 v28, v28, v21                                // 0000000003D8: 10382B1C
	v_mul_f32_e32 v32, v29, v29                                // 0000000003DC: 10403B1D
	v_mul_f32_e32 v28, v28, v28                                // 0000000003E0: 1038391C
	v_mul_f32_e32 v28, v28, v30                                // 0000000003E4: 10383D1C
	v_mul_f32_e32 v30, v32, v32                                // 0000000003E8: 103C4120
	v_mul_f32_e32 v28, v28, v31                                // 0000000003EC: 10383F1C
	v_mul_f32_e32 v29, v29, v30                                // 0000000003F0: 103A3D1D
	v_mul_f32_e32 v28, v28, v35                                // 0000000003F4: 1038471C
	v_fma_f32 v30, -v29, v19, 1.0                              // 0000000003F8: D54B001E 23CA271D
	v_fma_f32 v31, -v29, v20, 1.0                              // 000000000400: D54B001F 23CA291D
	v_fma_f32 v29, -v29, v22, 1.0                              // 000000000408: D54B001D 23CA2D1D
	v_rcp_f32_e32 v28, v28                                     // 000000000410: 7E38551C
	v_add_f32_e32 v30, v30, v19                                // 000000000414: 063C271E
	v_add_f32_e32 v31, v31, v20                                // 000000000418: 063E291F
	v_add_f32_e32 v29, v29, v22                                // 00000000041C: 063A2D1D
	v_mul_f32_e32 v28, v34, v28                                // 000000000420: 10383922
	v_fma_f32 v32, v28, s20, -v23                              // 000000000424: D54B0020 845C291C
	v_fma_f32 v33, v28, s21, -v24                              // 00000000042C: D54B0021 84602B1C
	v_fma_f32 v28, v28, s22, -v25                              // 000000000434: D54B001C 84642D1C
	s_cselect_b64 s[20:21], -1, 0                              // 00000000043C: 859480C1
	s_add_i32 s9, s9, 16                                       // 000000000440: 81099009
	v_fma_f32 v30, v30, v32, v23                               // 000000000444: D54B001E 045E411E
	v_fma_f32 v31, v31, v33, v24                               // 00000000044C: D54B001F 0462431F
	v_fma_f32 v28, v29, v28, v25                               // 000000000454: D54B001C 0466391D
	s_and_b64 s[2:3], s[2:3], s[20:21]                         // 00000000045C: 87821402
	v_fmac_f32_e32 v10, v30, v27                               // 000000000460: 5614371E
	v_fmac_f32_e32 v11, v31, v27                               // 000000000464: 5616371F
	v_fmac_f32_e32 v12, v28, v27                               // 000000000468: 5618371C
	s_and_b64 vcc, exec, s[2:3]                                // 00000000046C: 87EA027E
	s_cbranch_vccnz _L1                                        // 000000000470: BF87FF89
_L0:
	image_sample v13, v[2:3], s[36:43], s[12:15] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000474: F0800108 00690D02
	image_sample v[0:2], v[2:3], s[44:51], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000047C: F0800708 006B0002
	s_load_dwordx8 s[16:23], s[0:1], 0xe0                      // 000000000484: F40C0400 FA0000E0
	v_cubema_f32 v3, v7, v8, v9                                // 00000000048C: D5470003 04261107
	v_cubeid_f32 v14, v7, v8, v9                               // 000000000494: D544000E 04261107
	v_cubesc_f32 v15, v7, v8, v9                               // 00000000049C: D545000F 04261107
	v_cubetc_f32 v7, v7, v8, v9                                // 0000000004A4: D5460007 04261107
	v_rcp_f32_e64 v3, |v3|                                     // 0000000004AC: D5AA0103 00000103
	v_rndne_f32_e32 v16, v14                                   // 0000000004B4: 7E20470E
	v_fmaak_f32 v14, v3, v15, 0x3fc00000                       // 0000000004B8: 5A1C1F03 3FC00000
	v_fmaak_f32 v15, v3, v7, 0x3fc00000                        // 0000000004C0: 5A1E0F03 3FC00000
	s_and_b64 exec, exec, s[28:29]                             // 0000000004C8: 87FE1C7E
	s_waitcnt lgkmcnt(0)                                       // 0000000004CC: BF8CC07F
	image_sample v[7:9], v[14:16], s[16:23], s[12:15] dmask:0x7 dim:SQ_RSRC_IMG_CUBE// 0000000004D0: F0800718 0064070E
	s_clause 0x1                                               // 0000000004D8: BFA10001
	s_buffer_load_dword s8, s[4:7], 0x14                       // 0000000004DC: F4200202 FA000014
	s_buffer_load_dwordx4 s[0:3], s[4:7], 0x20                 // 0000000004E4: F4280002 FA000020
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 0000000004EC: BF8C0072
	v_mul_f32_e32 v3, s8, v13                                  // 0000000004F0: 10061A08
	s_waitcnt vmcnt(1)                                         // 0000000004F4: BF8C3F71
	v_mul_f32_e32 v0, s0, v0                                   // 0000000004F8: 10000000
	v_mul_f32_e32 v1, s1, v1                                   // 0000000004FC: 10020201
	v_mul_f32_e32 v2, s2, v2                                   // 000000000500: 10040402
	v_mul_f32_e32 v4, v3, v4                                   // 000000000504: 10080903
	v_mul_f32_e32 v5, v3, v5                                   // 000000000508: 100A0B03
	v_mul_f32_e32 v3, v3, v6                                   // 00000000050C: 10060D03
	v_fmac_f32_e32 v10, s3, v0                                 // 000000000510: 56140003
	v_fmac_f32_e32 v11, s3, v1                                 // 000000000514: 56160203
	v_fmac_f32_e32 v12, s3, v2                                 // 000000000518: 56180403
	s_waitcnt vmcnt(0)                                         // 00000000051C: BF8C3F70
	v_fmac_f32_e32 v10, v4, v7                                 // 000000000520: 56140F04
	v_fmac_f32_e32 v11, v5, v8                                 // 000000000524: 56161105
	v_fmac_f32_e32 v12, v3, v9                                 // 000000000528: 56181303
	v_mov_b32_e32 v3, 1.0                                      // 00000000052C: 7E0602F2
	v_rcp_f32_e32 v0, v10                                      // 000000000530: 7E00550A
	v_rcp_f32_e32 v1, v11                                      // 000000000534: 7E02550B
	v_rcp_f32_e32 v2, v12                                      // 000000000538: 7E04550C
	v_fma_f32 v0, v10, v0, 1.0                                 // 00000000053C: D54B0000 03CA010A
	v_fma_f32 v1, v11, v1, 1.0                                 // 000000000544: D54B0001 03CA030B
	v_fma_f32 v2, v12, v2, 1.0                                 // 00000000054C: D54B0002 03CA050C
	v_log_f32_e32 v0, v0                                       // 000000000554: 7E004F00
	v_log_f32_e32 v1, v1                                       // 000000000558: 7E024F01
	v_log_f32_e32 v2, v2                                       // 00000000055C: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 000000000560: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 000000000568: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 000000000570: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 000000000578: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 00000000057C: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 000000000580: 7E044B02
	exp mrt0 v0, v1, v2, v3 done vm                            // 000000000584: F800180F 03020100
	s_endpgm                                                   // 00000000058C: BF810000
