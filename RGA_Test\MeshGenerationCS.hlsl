// Mesh Generation Compute Shader
// Tests procedural mesh generation and index buffer operations

// Vertex data for mesh generation
struct MeshVertex
{
    float3 Position;
    float3 Normal;
    float2 TexCoord;
    float4 Color;
};

// Compute constants
cbuffer ComputeParams : register(b0)
{
    uint ParticleCount;
    uint MaxParticles;
    float DeltaTime;
    float Time;
    float3 Gravity;
    float Damping;
    float3 EmitterPosition;
    float EmissionRate;
    float3 EmitterDirection;
    float EmissionSpeed;
    float2 LifetimeRange;
    float2 SizeRange;
    uint FrameCount;
    float NoiseScale;
    float NoiseStrength;
    uint _padding;
};

// Buffers
RWStructuredBuffer<MeshVertex> VertexBuffer : register(u0);
RWStructuredBuffer<uint> IndexBuffer : register(u1);

// Noise function
float Hash(float n)
{
    return frac(sin(n) * 43758.5453);
}

float Noise(float3 x)
{
    float3 p = floor(x);
    float3 f = frac(x);
    f = f * f * (3.0 - 2.0 * f);
    
    float n = p.x + p.y * 57.0 + 113.0 * p.z;
    return lerp(lerp(lerp(Hash(n + 0.0), Hash(n + 1.0), f.x),
                     lerp(Hash(n + 57.0), Hash(n + 58.0), f.x), f.y),
                lerp(lerp(Hash(n + 113.0), Hash(n + 114.0), f.x),
                     lerp(Hash(n + 170.0), Hash(n + 171.0), f.x), f.y), f.z);
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
    uint width = 64;
    uint height = 64;
    
    if (id.x >= width || id.y >= height)
        return;
    
    uint vertexIndex = id.y * width + id.x;
    
    // Generate heightmap using noise
    float2 uv = float2(id.xy) / float2(width - 1, height - 1);
    float3 worldPos = float3(uv.x * 100.0 - 50.0, 0, uv.y * 100.0 - 50.0);
    
    float height1 = Noise(worldPos * 0.01 + Time * 0.1) * 10.0;
    float height2 = Noise(worldPos * 0.05 + Time * 0.05) * 2.0;
    worldPos.y = height1 + height2;
    
    // Calculate normal using finite differences
    float3 normal = float3(0, 1, 0);
    if (id.x > 0 && id.x < width - 1 && id.y > 0 && id.y < height - 1)
    {
        float hL = Noise((worldPos + float3(-1, 0, 0)) * 0.01 + Time * 0.1) * 10.0;
        float hR = Noise((worldPos + float3(1, 0, 0)) * 0.01 + Time * 0.1) * 10.0;
        float hD = Noise((worldPos + float3(0, 0, -1)) * 0.01 + Time * 0.1) * 10.0;
        float hU = Noise((worldPos + float3(0, 0, 1)) * 0.01 + Time * 0.1) * 10.0;
        
        normal = normalize(float3(hL - hR, 2.0, hD - hU));
    }
    
    MeshVertex vertex;
    vertex.Position = worldPos;
    vertex.Normal = normal;
    vertex.TexCoord = uv;
    vertex.Color = float4(uv, 0.5, 1.0);
    
    VertexBuffer[vertexIndex] = vertex;
    
    // Generate indices for triangles
    if (id.x < width - 1 && id.y < height - 1)
    {
        uint indexBase = (id.y * (width - 1) + id.x) * 6;
        
        uint v0 = id.y * width + id.x;
        uint v1 = id.y * width + id.x + 1;
        uint v2 = (id.y + 1) * width + id.x;
        uint v3 = (id.y + 1) * width + id.x + 1;
        
        // First triangle
        IndexBuffer[indexBase + 0] = v0;
        IndexBuffer[indexBase + 1] = v2;
        IndexBuffer[indexBase + 2] = v1;
        
        // Second triangle
        IndexBuffer[indexBase + 3] = v1;
        IndexBuffer[indexBase + 4] = v2;
        IndexBuffer[indexBase + 5] = v3;
    }
}
