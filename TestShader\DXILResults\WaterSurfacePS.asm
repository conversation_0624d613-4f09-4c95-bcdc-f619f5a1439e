;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float     zw
; TEXCOORD                 0   xyz         1     NONE   float       
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 6     zw        3     NONE   float     zw
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xyzw        5     NONE   float   xy w
; TEXCOORD                 5   xyzw        6     NONE   float   xy w
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 7f6403fa3e4e47bd12a847f375aa6e0a
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 8
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 7
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer WaterMaterial
; {
;
;   struct WaterMaterial
;   {
;
;       float3 WaterColor;                            ; Offset:    0
;       float WaterAlpha;                             ; Offset:   12
;       float FresnelStrength;                        ; Offset:   16
;       float ReflectionStrength;                     ; Offset:   20
;       float RefractionStrength;                     ; Offset:   24
;       float NormalStrength;                         ; Offset:   28
;       float SpecularPower;                          ; Offset:   32
;       float3 SpecularColor;                         ; Offset:   36
;       float FoamThreshold;                          ; Offset:   48
;       float3 FoamColor;                             ; Offset:   52
;       float Time;                                   ; Offset:   64
;   
;   } WaterMaterial;                                  ; Offset:    0 Size:    68
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; WaterMaterial                     cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; WaterNormalTexture                texture     f32          2d      T0             t0     1
; ReflectionTexture                 texture     f32          2d      T1             t1     1
; RefractionTexture                 texture     f32          2d      T2             t2     1
; FoamTexture                       texture     f32          2d      T3             t3     1
; DepthTexture                      texture     f32          2d      T4             t4     1
;
;
; ViewId state:
;
; Number of inputs: 28, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 2, 3, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 23, 24, 25, 27 }
;   output 1 depends on inputs: { 2, 3, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 23, 24, 25, 27 }
;   output 2 depends on inputs: { 2, 3, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 20, 21, 23, 24, 25, 27 }
;   output 3 depends on inputs: { 2, 3, 8, 9, 10, 12, 13, 14, 15, 24, 25, 27 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%WaterMaterial = type { <3 x float>, float, float, float, float, float, float, <3 x float>, float, <3 x float>, float }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 4, i32 4, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.dot3.f32(i32 55, float %21, float %22, float %23, float %21, float %22, float %23)  ; Dot3(ax,ay,az,bx,by,bz)
  %27 = call float @dx.op.unary.f32(i32 25, float %26)  ; Rsqrt(value)
  %28 = fmul fast float %27, %21
  %29 = fmul fast float %27, %22
  %30 = fmul fast float %27, %23
  %31 = call float @dx.op.dot3.f32(i32 55, float %16, float %17, float %18, float %16, float %17, float %18)  ; Dot3(ax,ay,az,bx,by,bz)
  %32 = call float @dx.op.unary.f32(i32 25, float %31)  ; Rsqrt(value)
  %33 = fmul fast float %32, %16
  %34 = fmul fast float %32, %17
  %35 = fmul fast float %32, %18
  %36 = fmul fast float %19, 4.000000e+00
  %37 = fmul fast float %20, 4.000000e+00
  %38 = fadd fast float %36, %8
  %39 = fadd fast float %37, %9
  %40 = fmul fast float %19, 2.000000e+00
  %41 = fmul fast float %20, 2.000000e+00
  %42 = fmul fast float %8, 5.000000e-01
  %43 = fmul fast float %9, 5.000000e-01
  %44 = fsub fast float %40, %42
  %45 = fsub fast float %41, %43
  %46 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %6, float %38, float %39, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %47 = extractvalue %dx.types.ResRet.f32 %46, 0
  %48 = extractvalue %dx.types.ResRet.f32 %46, 1
  %49 = extractvalue %dx.types.ResRet.f32 %46, 2
  %50 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %5, %dx.types.Handle %6, float %44, float %45, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %51 = extractvalue %dx.types.ResRet.f32 %50, 0
  %52 = extractvalue %dx.types.ResRet.f32 %50, 1
  %53 = extractvalue %dx.types.ResRet.f32 %50, 2
  %54 = fadd fast float %51, %47
  %55 = fmul fast float %54, 2.000000e+00
  %56 = fadd fast float %55, -2.000000e+00
  %57 = fadd fast float %52, %48
  %58 = fmul fast float %57, 2.000000e+00
  %59 = fadd fast float %58, -2.000000e+00
  %60 = fadd fast float %53, %49
  %61 = fmul fast float %60, 2.000000e+00
  %62 = fadd fast float %61, -2.000000e+00
  %63 = call float @dx.op.dot3.f32(i32 55, float %56, float %59, float %62, float %56, float %59, float %62)  ; Dot3(ax,ay,az,bx,by,bz)
  %64 = call float @dx.op.unary.f32(i32 25, float %63)  ; Rsqrt(value)
  %65 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %66 = extractvalue %dx.types.CBufRet.f32 %65, 3
  %67 = fmul fast float %66, %64
  %68 = fmul fast float %67, %56
  %69 = fmul fast float %67, %59
  %70 = fmul fast float %67, %62
  %71 = fadd fast float %68, %28
  %72 = fadd fast float %69, %29
  %73 = fadd fast float %70, %30
  %74 = call float @dx.op.dot3.f32(i32 55, float %71, float %72, float %73, float %71, float %72, float %73)  ; Dot3(ax,ay,az,bx,by,bz)
  %75 = call float @dx.op.unary.f32(i32 25, float %74)  ; Rsqrt(value)
  %76 = fmul fast float %71, %75
  %77 = fmul fast float %72, %75
  %78 = fmul fast float %73, %75
  %79 = fdiv fast float %13, %15
  %80 = fdiv fast float %14, %15
  %81 = fmul fast float %79, 5.000000e-01
  %82 = fmul fast float %80, 5.000000e-01
  %83 = fadd fast float %81, 5.000000e-01
  %84 = fadd fast float %82, 5.000000e-01
  %85 = fdiv fast float %10, %12
  %86 = fdiv fast float %11, %12
  %87 = fmul fast float %85, 5.000000e-01
  %88 = fmul fast float %86, 5.000000e-01
  %89 = fadd fast float %87, 5.000000e-01
  %90 = fmul fast float %76, 0x3F947AE140000000
  %91 = fmul fast float %78, 0x3F947AE140000000
  %92 = fadd fast float %83, %90
  %93 = fadd fast float %84, %91
  %94 = fmul fast float %76, 0x3F847AE140000000
  %95 = fmul fast float %78, 0x3F847AE140000000
  %96 = fadd fast float %89, %94
  %97 = fadd fast float %95, 5.000000e-01
  %98 = fadd fast float %97, %88
  %99 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %92, float %93, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %100 = extractvalue %dx.types.ResRet.f32 %99, 0
  %101 = extractvalue %dx.types.ResRet.f32 %99, 1
  %102 = extractvalue %dx.types.ResRet.f32 %99, 2
  %103 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %6, float %96, float %98, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %104 = extractvalue %dx.types.ResRet.f32 %103, 0
  %105 = extractvalue %dx.types.ResRet.f32 %103, 1
  %106 = extractvalue %dx.types.ResRet.f32 %103, 2
  %107 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %108 = extractvalue %dx.types.CBufRet.f32 %107, 0
  %109 = call float @dx.op.dot3.f32(i32 55, float %33, float %34, float %35, float %76, float %77, float %78)  ; Dot3(ax,ay,az,bx,by,bz)
  %110 = fsub fast float 1.000000e+00, %109
  %111 = call float @dx.op.unary.f32(i32 7, float %110)  ; Saturate(value)
  %112 = call float @dx.op.unary.f32(i32 23, float %111)  ; Log(value)
  %113 = fmul fast float %112, %108
  %114 = call float @dx.op.unary.f32(i32 21, float %113)  ; Exp(value)
  %115 = extractvalue %dx.types.CBufRet.f32 %107, 1
  %116 = fmul fast float %115, %100
  %117 = fmul fast float %115, %101
  %118 = fmul fast float %115, %102
  %119 = extractvalue %dx.types.CBufRet.f32 %107, 2
  %120 = fmul fast float %119, %104
  %121 = fmul fast float %119, %105
  %122 = fmul fast float %119, %106
  %123 = fsub fast float %116, %120
  %124 = fsub fast float %117, %121
  %125 = fsub fast float %118, %122
  %126 = fmul fast float %114, %123
  %127 = fmul fast float %114, %124
  %128 = fmul fast float %114, %125
  %129 = fadd fast float %126, %120
  %130 = fadd fast float %127, %121
  %131 = fadd fast float %128, %122
  %132 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %133 = extractvalue %dx.types.CBufRet.f32 %132, 0
  %134 = extractvalue %dx.types.CBufRet.f32 %132, 1
  %135 = extractvalue %dx.types.CBufRet.f32 %132, 2
  %136 = fsub fast float %133, %129
  %137 = fsub fast float %134, %130
  %138 = fsub fast float %135, %131
  %139 = fmul fast float %136, 0x3FD3333340000000
  %140 = fmul fast float %137, 0x3FD3333340000000
  %141 = fmul fast float %138, 0x3FD3333340000000
  %142 = fadd fast float %139, %129
  %143 = fadd fast float %140, %130
  %144 = fadd fast float %141, %131
  %145 = fadd fast float %33, 0x3FDBA4CF60000000
  %146 = fadd fast float %34, 0x3FEBA4CF60000000
  %147 = fadd fast float %35, 0x3FD0961620000000
  %148 = call float @dx.op.dot3.f32(i32 55, float %145, float %146, float %147, float %145, float %146, float %147)  ; Dot3(ax,ay,az,bx,by,bz)
  %149 = call float @dx.op.unary.f32(i32 25, float %148)  ; Rsqrt(value)
  %150 = fmul fast float %149, %145
  %151 = fmul fast float %149, %146
  %152 = fmul fast float %149, %147
  %153 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %154 = extractvalue %dx.types.CBufRet.f32 %153, 0
  %155 = call float @dx.op.dot3.f32(i32 55, float %76, float %77, float %78, float %150, float %151, float %152)  ; Dot3(ax,ay,az,bx,by,bz)
  %156 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %155)  ; FMax(a,b)
  %157 = call float @dx.op.unary.f32(i32 23, float %156)  ; Log(value)
  %158 = fmul fast float %157, %154
  %159 = call float @dx.op.unary.f32(i32 21, float %158)  ; Exp(value)
  %160 = extractvalue %dx.types.CBufRet.f32 %153, 1
  %161 = extractvalue %dx.types.CBufRet.f32 %153, 2
  %162 = extractvalue %dx.types.CBufRet.f32 %153, 3
  %163 = fmul fast float %159, %160
  %164 = fmul fast float %159, %161
  %165 = fmul fast float %159, %162
  %166 = fadd fast float %142, %163
  %167 = fadd fast float %143, %164
  %168 = fadd fast float %144, %165
  %169 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %6, float %96, float %98, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %170 = extractvalue %dx.types.ResRet.f32 %169, 0
  %171 = fdiv fast float %24, %25
  %172 = fsub fast float %170, %171
  %173 = call float @dx.op.unary.f32(i32 6, float %172)  ; FAbs(value)
  %174 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %175 = extractvalue %dx.types.CBufRet.f32 %174, 0
  %176 = fdiv fast float %173, %175
  %177 = call float @dx.op.unary.f32(i32 7, float %176)  ; Saturate(value)
  %178 = fsub fast float 1.000000e+00, %177
  %179 = fmul fast float %19, 8.000000e+00
  %180 = fmul fast float %20, 8.000000e+00
  %181 = fmul fast float %8, 2.000000e+00
  %182 = fmul fast float %9, 2.000000e+00
  %183 = fadd fast float %179, %181
  %184 = fadd fast float %180, %182
  %185 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %6, float %183, float %184, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %186 = extractvalue %dx.types.ResRet.f32 %185, 0
  %187 = fmul fast float %186, %178
  %188 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %189 = extractvalue %dx.types.CBufRet.f32 %188, 1
  %190 = extractvalue %dx.types.CBufRet.f32 %188, 2
  %191 = extractvalue %dx.types.CBufRet.f32 %188, 3
  %192 = fsub fast float %189, %166
  %193 = fsub fast float %190, %167
  %194 = fsub fast float %191, %168
  %195 = fmul fast float %192, %187
  %196 = fmul fast float %193, %187
  %197 = fmul fast float %194, %187
  %198 = fadd fast float %195, %166
  %199 = fadd fast float %196, %167
  %200 = fadd fast float %197, %168
  %201 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %202 = extractvalue %dx.types.CBufRet.f32 %201, 3
  %203 = fmul fast float %187, 5.000000e-01
  %204 = fadd fast float %202, %203
  %205 = call float @dx.op.unary.f32(i32 7, float %204)  ; Saturate(value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %198)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %199)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %200)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %205)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!16}
!dx.entryPoints = !{!17}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !12, !14}
!5 = !{!6, !8, !9, !10, !11}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{i32 2, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 2, i32 0, !7}
!11 = !{i32 4, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 4, i32 1, i32 2, i32 0, !7}
!12 = !{!13}
!13 = !{i32 0, %WaterMaterial* undef, !"", i32 0, i32 0, i32 1, i32 68, null}
!14 = !{!15}
!15 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!16 = !{[30 x i32] [i32 28, i32 4, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 0, i32 0, i32 15, i32 15, i32 15, i32 0, i32 15, i32 15, i32 15, i32 15, i32 7, i32 7, i32 7, i32 0, i32 7, i32 7, i32 0, i32 7, i32 15, i32 15, i32 0, i32 15]}
!17 = !{void ()* @main, !"main", !18, !4, null}
!18 = !{!19, !39, null}
!19 = !{!20, !23, !24, !27, !30, !32, !35, !37}
!20 = !{i32 0, !"SV_Position", i8 9, i8 3, !21, i8 4, i32 1, i8 4, i32 0, i8 0, !22}
!21 = !{i32 0}
!22 = !{i32 3, i32 12}
!23 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 1, i8 3, i32 1, i8 0, null}
!24 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !25, i8 2, i32 1, i8 3, i32 2, i8 0, !26}
!25 = !{i32 1}
!26 = !{i32 3, i32 7}
!27 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !28, i8 2, i32 1, i8 2, i32 3, i8 0, !29}
!28 = !{i32 2}
!29 = !{i32 3, i32 3}
!30 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !31, i8 2, i32 1, i8 3, i32 4, i8 0, !26}
!31 = !{i32 3}
!32 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !33, i8 2, i32 1, i8 4, i32 5, i8 0, !34}
!33 = !{i32 4}
!34 = !{i32 3, i32 11}
!35 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !36, i8 2, i32 1, i8 4, i32 6, i8 0, !34}
!36 = !{i32 5}
!37 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !38, i8 2, i32 1, i8 2, i32 3, i8 2, !29}
!38 = !{i32 6}
!39 = !{!40}
!40 = !{i32 0, !"SV_Target", i8 9, i8 16, !21, i8 0, i32 1, i8 4, i32 0, i8 0, !41}
!41 = !{i32 3, i32 15}
