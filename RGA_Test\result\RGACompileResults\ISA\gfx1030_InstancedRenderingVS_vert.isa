_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s8, s9                                           // 000000000020: BE880309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v9, s3, 5, v1                                // 000000000040: D76F0009 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v9                            // 000000000048: 7D881201
	s_and_saveexec_b32 s34, vcc_lo                             // 00000000004C: BEA23C6A
	s_cbranch_execz _L1                                        // 000000000050: BF8800DE
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v7, s0, v5                                // 000000000058: 4A0E0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s9, s3                                           // 000000000060: BE890303
	s_clause 0x2                                               // 000000000064: BFA10002
	s_load_dwordx4 s[4:7], s[10:11], 0x70                      // 000000000068: F4080105 FA000070
	s_load_dwordx4 s[24:27], s[10:11], 0x40                    // 000000000070: F4080605 FA000040
	s_load_dwordx4 s[12:15], s[10:11], null                    // 000000000078: F4080305 FA000000
	s_mov_b32 s1, exec_lo                                      // 000000000080: BE81037E
	s_waitcnt lgkmcnt(0)                                       // 000000000084: BF8CC07F
	tbuffer_load_format_xyz v[1:3], v7, s[4:7], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000088: EA522000 80010107
	tbuffer_load_format_x v10, v7, s[24:27], 0 format:[BUF_FMT_32_FLOAT] idxen// 000000000090: E8B02000 80060A07
	tbuffer_load_format_xyz v[4:6], v7, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000098: EA522000 80030407
	s_load_dwordx4 s[20:23], s[8:9], null                      // 0000000000A0: F4080504 FA000000
	s_waitcnt vmcnt(2)                                         // 0000000000A8: BF8C3F72
	v_cmpx_ngt_f32_e32 1.0, v3                                 // 0000000000AC: 7C3606F2
	s_xor_b32 s1, exec_lo, s1                                  // 0000000000B0: 8901017E
	s_cbranch_execz _L2                                        // 0000000000B4: BF88000F
	s_mov_b32 s2, exec_lo                                      // 0000000000B8: BE82037E
	v_cmpx_ngt_f32_e32 2.0, v3                                 // 0000000000BC: 7C3606F4
	s_cbranch_execz _L3                                        // 0000000000C0: BF88000B
	s_waitcnt lgkmcnt(0)                                       // 0000000000C4: BF8CC07F
	s_buffer_load_dword s0, s[20:23], 0x4c                     // 0000000000C8: F420000A FA00004C
	s_waitcnt lgkmcnt(0)                                       // 0000000000D0: BF8CC07F
	v_fmamk_f32 v8, s0, 0x40400000, v1                         // 0000000000D4: 58100200 40400000
	v_mul_f32_e32 v8, 0.15915494, v8                           // 0000000000DC: 101010F8
	v_sin_f32_e32 v8, v8                                       // 0000000000E0: 7E106B08
	s_waitcnt vmcnt(0)                                         // 0000000000E4: BF8C3F70
	v_fmamk_f32 v5, v8, 0x3dcccccd, v5                         // 0000000000E8: 580A0B08 3DCCCCCD
_L3:
	s_or_b32 exec_lo, exec_lo, s2                              // 0000000000F0: 887E027E
_L2:
	s_andn2_saveexec_b32 s0, s1                                // 0000000000F4: BE803F01
	s_cbranch_execz _L4                                        // 0000000000F8: BF880023
	tbuffer_load_format_x v8, v7, s[4:7], 0 format:[BUF_FMT_32_FLOAT] idxen offset:12// 0000000000FC: E8B0200C 80010807
	s_waitcnt lgkmcnt(0)                                       // 000000000104: BF8CC07F
	s_clause 0x1                                               // 000000000108: BFA10001
	s_buffer_load_dwordx4 s[4:7], s[20:23], 0x4c               // 00000000010C: F428010A FA00004C
	s_buffer_load_dword s1, s[20:23], 0x5c                     // 000000000114: F420004A FA00005C
	s_waitcnt vmcnt(2)                                         // 00000000011C: BF8C3F72
	v_fmamk_f32 v1, v10, 0x3dcccccd, v1                        // 000000000120: 5802030A 3DCCCCCD
	s_waitcnt vmcnt(1)                                         // 000000000128: BF8C3F71
	v_mul_f32_e32 v11, 0x3dcccccd, v5                          // 00000000012C: 10160AFF 3DCCCCCD
	s_waitcnt lgkmcnt(0)                                       // 000000000134: BF8CC07F
	v_fmac_f32_e64 v1, s4, 2.0                                 // 000000000138: D52B0001 0001E804
	v_mul_f32_e32 v12, s5, v11                                 // 000000000140: 10181605
	v_mul_f32_e32 v13, s6, v11                                 // 000000000144: 101A1606
	v_mul_f32_e32 v11, s7, v11                                 // 000000000148: 10161607
	v_mul_f32_e32 v1, 0.15915494, v1                           // 00000000014C: 100202F8
	v_sin_f32_e32 v1, v1                                       // 000000000150: 7E026B01
	v_mul_f32_e32 v1, s1, v1                                   // 000000000154: 10020201
	v_fma_f32 v4, v12, v1, v4                                  // 000000000158: D54B0004 0412030C
	v_fmac_f32_e32 v5, v13, v1                                 // 000000000160: 560A030D
	v_fmac_f32_e32 v6, v11, v1                                 // 000000000164: 560C030B
	s_waitcnt vmcnt(0)                                         // 000000000168: BF8C3F70
	v_mul_f32_e64 v8, 0x3dcccccd, v8 clamp                     // 00000000016C: D5088008 000210FF 3DCCCCCD
	v_mul_f32_e32 v8, v8, v2                                   // 000000000178: 10100508
	v_mul_f32_e32 v4, v4, v8                                   // 00000000017C: 10081104
	v_mul_f32_e32 v5, v5, v8                                   // 000000000180: 100A1105
	v_mul_f32_e32 v6, v6, v8                                   // 000000000184: 100C1106
_L4:
	s_or_b32 exec_lo, exec_lo, s0                              // 000000000188: 887E007E
	s_clause 0x2                                               // 00000000018C: BFA10002
	s_load_dwordx4 s[28:31], s[10:11], 0x30                    // 000000000190: F4080705 FA000030
	s_load_dwordx8 s[12:19], s[10:11], 0x50                    // 000000000198: F40C0305 FA000050
	s_load_dwordx8 s[0:7], s[10:11], 0x10                      // 0000000001A0: F40C0005 FA000010
	v_add_f32_e32 v29, v2, v2                                  // 0000000001A8: 063A0502
	tbuffer_load_format_xyz v[23:25], v7, s[24:27], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen offset:4// 0000000001AC: EA522004 80061707
	s_waitcnt lgkmcnt(0)                                       // 0000000001B4: BF8CC07F
	tbuffer_load_format_xyzw v[11:14], v7, s[28:31], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 0000000001B8: EA6B2000 80070B07
	tbuffer_load_format_xyzw v[15:18], v7, s[16:19], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 0000000001C0: EA6B2000 80040F07
	tbuffer_load_format_xyzw v[19:22], v7, s[12:15], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 0000000001C8: EA6B2000 80031307
	tbuffer_load_format_xyz v[26:28], v7, s[0:3], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 0000000001D0: EA522000 80001A07
	tbuffer_load_format_xy v[7:8], v7, s[4:7], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 0000000001D8: EA012000 80010707
	s_clause 0x3                                               // 0000000001E0: BFA10003
	s_buffer_load_dwordx8 s[12:19], s[20:23], null             // 0000000001E4: F42C030A FA000000
	s_buffer_load_dwordx8 s[4:11], s[20:23], 0x20              // 0000000001EC: F42C010A FA000020
	s_buffer_load_dwordx2 s[2:3], s[20:23], 0x40               // 0000000001F4: F424008A FA000040
	s_buffer_load_dword s1, s[20:23], 0x48                     // 0000000001FC: F420004A FA000048
	v_cmp_gt_f32_e64 s0, 0.5, v2                               // 000000000204: D4040000 000204F0
	s_waitcnt vmcnt(5)                                         // 00000000020C: BF8C3F75
	v_mul_f32_e32 v32, v6, v25                                 // 000000000210: 10403306
	v_mul_f32_e32 v30, v4, v25                                 // 000000000214: 103C3304
	v_mul_f32_e32 v31, v5, v25                                 // 000000000218: 103E3305
	v_fmac_f32_e32 v10, v4, v25                                // 00000000021C: 56143304
	v_fma_f32 v4, v5, v25, v23                                 // 000000000220: D54B0004 045E3305
	v_fma_f32 v5, v6, v25, v24                                 // 000000000228: D54B0005 04623306
	s_waitcnt vmcnt(3)                                         // 000000000230: BF8C3F73
	v_mul_f32_e32 v6, v15, v11                                 // 000000000234: 100C170F
	v_mul_f32_e32 v23, v16, v12                                // 000000000238: 102E1910
	v_mul_f32_e32 v24, v17, v13                                // 00000000023C: 10301B11
	v_mul_f32_e32 v1, v18, v14                                 // 000000000240: 10021D12
	v_fma_f32 v11, v15, v11, -0.5                              // 000000000244: D54B000B 03C6170F
	v_fmaak_f32 v12, v16, v12, 0xbe99999a                      // 00000000024C: 5A181910 BE99999A
	v_fmaak_f32 v13, v17, v13, 0xbdcccccd                      // 000000000254: 5A1A1B11 BDCCCCCD
	s_waitcnt vmcnt(1)                                         // 00000000025C: BF8C3F71
	v_mul_f32_e32 v14, v27, v21                                // 000000000260: 101C2B1B
	v_mul_f32_e32 v15, v19, v28                                // 000000000264: 101E3913
	v_mul_f32_e32 v16, v26, v20                                // 000000000268: 1020291A
	v_add_f32_e32 v17, v22, v22                                // 00000000026C: 06222D16
	v_mul_f32_e32 v22, v32, v19                                // 000000000270: 102C2720
	v_mul_f32_e32 v18, v31, v21                                // 000000000274: 10242B1F
	v_mul_f32_e32 v25, v30, v20                                // 000000000278: 1032291E
	v_fma_f32 v11, v11, v29, 0.5                               // 00000000027C: D54B000B 03C23B0B
	v_fmaak_f32 v33, v12, v29, 0x3e99999a                      // 000000000284: 5A423B0C 3E99999A
	v_fmaak_f32 v29, v13, v29, 0x3dcccccd                      // 00000000028C: 5A3A3B0D 3DCCCCCD
	v_fma_f32 v13, v28, v20, -v14                              // 000000000294: D54B000D 843A291C
	v_fma_f32 v14, v26, v21, -v15                              // 00000000029C: D54B000E 843E2B1A
	v_fma_f32 v15, v27, v19, -v16                              // 0000000002A4: D54B000F 8442271B
	v_fma_f32 v16, v30, v21, -v22                              // 0000000002AC: D54B0010 845A2B1E
	v_fma_f32 v18, v32, v20, -v18                              // 0000000002B4: D54B0012 844A2920
	v_fma_f32 v22, v31, v19, -v25                              // 0000000002BC: D54B0016 8466271F
	v_cndmask_b32_e64 v12, v6, v11, s0                         // 0000000002C4: D501000C 00021706
	v_cndmask_b32_e64 v11, v23, v33, s0                        // 0000000002CC: D501000B 00024317
	v_fmac_f32_e32 v4, v17, v16                                // 0000000002D4: 56082111
	v_fma_f32 v6, v17, v13, v26                                // 0000000002D8: D54B0006 046A1B11
	v_fma_f32 v23, v17, v14, v27                               // 0000000002E0: D54B0017 046E1D11
	v_fmac_f32_e32 v10, v17, v18                               // 0000000002E8: 56142511
	v_fmac_f32_e32 v28, v17, v15                               // 0000000002EC: 56381F11
	v_fma_f32 v4, -v22, v19, v4                                // 0000000002F0: D54B0004 24122716
	v_fmac_f32_e32 v5, v17, v22                                // 0000000002F8: 560A2D11
	v_fma_f32 v17, -v15, v19, v23                              // 0000000002FC: D54B0011 245E270F
	v_fma_f32 v23, -v14, v21, v6                               // 000000000304: D54B0017 241A2B0E
	v_fma_f32 v6, -v16, v21, v10                               // 00000000030C: D54B0006 242A2B10
	v_fmac_f32_e32 v4, v18, v21                                // 000000000314: 56082B12
	v_fma_f32 v10, -v18, v20, v5                               // 000000000318: D54B000A 24162912
	v_fmac_f32_e32 v17, v13, v21                               // 000000000320: 56222B0D
	v_fma_f32 v25, -v13, v20, v28                              // 000000000324: D54B0019 2472290D
	v_fmac_f32_e32 v6, v22, v20                                // 00000000032C: 560C2916
	s_waitcnt lgkmcnt(0)                                       // 000000000330: BF8CC07F
	v_sub_f32_e32 v5, s3, v4                                   // 000000000334: 080A0803
	v_fmac_f32_e32 v23, v15, v20                               // 000000000338: 562E290F
	v_mul_f32_e32 v18, v17, v17                                // 00000000033C: 10242311
	v_fmac_f32_e32 v10, v16, v19                               // 000000000340: 56142710
	v_sub_f32_e32 v26, s2, v6                                  // 000000000344: 08340C02
	v_mul_f32_e32 v20, v5, v5                                  // 000000000348: 10280B05
	v_fmac_f32_e32 v25, v14, v19                               // 00000000034C: 5632270E
	v_fmac_f32_e32 v18, v23, v23                               // 000000000350: 56242F17
	v_sub_f32_e32 v27, s1, v10                                 // 000000000354: 08361401
	v_fma_f32 v13, v4, s13, s15                                // 000000000358: D54B000D 003C1B04
	v_fmac_f32_e32 v20, v26, v26                               // 000000000360: 5628351A
	v_fma_f32 v14, v4, s17, s19                                // 000000000364: D54B000E 004C2304
	v_fmac_f32_e32 v18, v25, v25                               // 00000000036C: 56243319
	v_fma_f32 v16, v4, s5, s7                                  // 000000000370: D54B0010 001C0B04
	v_fma_f32 v15, v4, s9, s11                                 // 000000000378: D54B000F 002C1304
	v_fmac_f32_e32 v20, v27, v27                               // 000000000380: 5628371B
	v_fmac_f32_e32 v13, s12, v6                                // 000000000384: 561A0C0C
	v_rsq_f32_e32 v18, v18                                     // 000000000388: 7E245D12
	v_fmac_f32_e32 v14, s16, v6                                // 00000000038C: 561C0C10
	v_fmac_f32_e32 v16, s4, v6                                 // 000000000390: 56200C04
	v_rsq_f32_e32 v28, v20                                     // 000000000394: 7E385D14
	v_fmac_f32_e32 v15, s8, v6                                 // 000000000398: 561E0C08
	v_cndmask_b32_e64 v20, v24, v29, s0                        // 00000000039C: D5010014 00023B18
	v_fmac_f32_e32 v13, s14, v10                               // 0000000003A4: 561A140E
	v_fmac_f32_e32 v14, s18, v10                               // 0000000003A8: 561C1412
	v_fmac_f32_e32 v16, s6, v10                                // 0000000003AC: 56201406
	v_fmac_f32_e32 v15, s10, v10                               // 0000000003B0: 561E140A
	v_mul_legacy_f32_e32 v23, v23, v18                         // 0000000003B4: 0E2E2517
	v_mul_legacy_f32_e32 v22, v17, v18                         // 0000000003B8: 0E2C2511
	v_mul_legacy_f32_e32 v21, v25, v18                         // 0000000003BC: 0E2A2519
	v_mul_legacy_f32_e32 v19, v26, v28                         // 0000000003C0: 0E26391A
	v_mul_legacy_f32_e32 v18, v5, v28                          // 0000000003C4: 0E243905
	v_mul_legacy_f32_e32 v17, v27, v28                         // 0000000003C8: 0E22391B
_L1:
	s_or_b32 exec_lo, exec_lo, s34                             // 0000000003CC: 887E227E
	s_mov_b32 s1, exec_lo                                      // 0000000003D0: BE81037E
	v_cmpx_gt_u32_e64 s33, v9                                  // 0000000003D4: D4D4007E 00021221
	s_cbranch_execz _L5                                        // 0000000003DC: BF880002
	exp prim v0, off, off, off done                            // 0000000003E0: F8000941 00000000
_L5:
	s_waitcnt expcnt(0)                                        // 0000000003E8: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000003EC: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000003F0: BE803C6A
	s_cbranch_execz _L6                                        // 0000000003F4: BF880011
	exp pos0 v13, v14, v16, v15 done                           // 0000000003F8: F80008CF 0F100E0D
	exp param5 v3, off, off, off                               // 000000000400: F8000251 00000003
	exp param3 v12, v11, v20, v1                               // 000000000408: F800023F 01140B0C
	exp param1 v23, v22, v21, off                              // 000000000410: F8000217 00151617
	exp param6 v2, off, off, off                               // 000000000418: F8000261 00000002
	exp param4 v19, v18, v17, off                              // 000000000420: F8000247 00111213
	s_waitcnt vmcnt(0)                                         // 000000000428: BF8C3F70
	exp param2 v7, v8, off, off                                // 00000000042C: F8000223 00000807
	exp param0 v6, v4, v10, off                                // 000000000434: F8000207 000A0406
_L6:
	s_endpgm                                                   // 00000000043C: BF810000
