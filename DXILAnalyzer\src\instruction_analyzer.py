"""
Instruction Analyzer - Analyzes DXIL instructions to extract computational patterns and data types.
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from collections import defaultdict, Counter
from .dxil_parser import D<PERSON><PERSON>Instruction, DXILFunction


@dataclass
class OperationAnalysis:
    """Analysis result for a specific operation type."""
    operation_type: str
    count: int
    data_types: Dict[str, int]
    examples: List[DXILInstruction]
    operand_patterns: List[str]


@dataclass
class InstructionAnalysisResult:
    """Complete analysis result for a set of instructions."""
    total_instructions: int
    computational_instructions: int
    operation_breakdown: Dict[str, OperationAnalysis]
    data_type_distribution: Dict[str, int]
    complexity_metrics: Dict[str, float]
    instruction_details: List[Dict[str, Any]]


class InstructionAnalyzer:
    """Analyzes DXIL instructions to extract computational patterns."""
    
    # Operation categories for analysis
    OPERATION_CATEGORIES = {
        'arithmetic': ['float_add', 'float_sub', 'float_mul', 'float_div', 'int_add', 'int_sub', 'int_mul', 'int_div'],
        'vector': ['dot2_float', 'dot3_float', 'dot4_float'],
        'mathematical': ['unary_float', 'binary_float', 'tertiary_float'],
        'memory': ['load_input_float', 'store_output_float', 'cbuffer_load_float'],
        'texture': ['sample_float'],
        'bitwise': ['bitwise_and', 'bitwise_or', 'bitwise_xor', 'shift_left', 'logical_shift_right', 'arithmetic_shift_right']
    }
    
    # Data type mappings
    DATA_TYPE_MAPPINGS = {
        'f32': 'float32',
        'f64': 'float64', 
        'i32': 'int32',
        'i64': 'int64',
        'i16': 'int16',
        'i8': 'int8',
        'i1': 'bool',
        'float': 'float32',
        'double': 'float64'
    }
    
    def __init__(self):
        self.analysis_cache: Dict[str, InstructionAnalysisResult] = {}
    
    def analyze(self, instructions: List[DXILInstruction]) -> InstructionAnalysisResult:
        """Analyze a list of DXIL instructions."""
        if not instructions:
            return self._create_empty_result()
            
        # Filter computational instructions
        computational_instructions = [
            inst for inst in instructions 
            if inst.operation and self._is_computational(inst.operation)
        ]
        
        # Analyze operations
        operation_breakdown = self._analyze_operations(computational_instructions)
        
        # Analyze data types
        data_type_distribution = self._analyze_data_types(computational_instructions)
        
        # Calculate complexity metrics
        complexity_metrics = self._calculate_complexity_metrics(computational_instructions)
        
        # Create detailed instruction analysis
        instruction_details = self._create_instruction_details(computational_instructions)
        
        return InstructionAnalysisResult(
            total_instructions=len(instructions),
            computational_instructions=len(computational_instructions),
            operation_breakdown=operation_breakdown,
            data_type_distribution=data_type_distribution,
            complexity_metrics=complexity_metrics,
            instruction_details=instruction_details
        )
    
    def analyze_functions(self, functions: List[DXILFunction]) -> Dict[str, InstructionAnalysisResult]:
        """Analyze instructions for each function separately."""
        results = {}
        
        for function in functions:
            results[function.name] = self.analyze(function.instructions)
            
        return results
    
    def _is_computational(self, operation: str) -> bool:
        """Check if an operation is computational (not just data movement)."""
        non_computational = ['load_input', 'store_output', 'cbuffer_load']
        return not any(nc in operation for nc in non_computational)
    
    def _analyze_operations(self, instructions: List[DXILInstruction]) -> Dict[str, OperationAnalysis]:
        """Analyze operation types and patterns."""
        operation_counts = Counter()
        operation_data_types = defaultdict(lambda: defaultdict(int))
        operation_examples = defaultdict(list)
        operation_patterns = defaultdict(set)
        
        for instruction in instructions:
            if not instruction.operation:
                continue
                
            op = instruction.operation
            operation_counts[op] += 1
            
            # Track data types for this operation
            if instruction.data_type:
                normalized_type = self.DATA_TYPE_MAPPINGS.get(instruction.data_type, instruction.data_type)
                operation_data_types[op][normalized_type] += 1
            
            # Keep examples (limit to 3 per operation)
            if len(operation_examples[op]) < 3:
                operation_examples[op].append(instruction)
            
            # Track operand patterns
            if instruction.operands:
                pattern = self._extract_operand_pattern(instruction)
                if pattern:
                    operation_patterns[op].add(pattern)
        
        # Create OperationAnalysis objects
        breakdown = {}
        for op, count in operation_counts.items():
            breakdown[op] = OperationAnalysis(
                operation_type=op,
                count=count,
                data_types=dict(operation_data_types[op]),
                examples=operation_examples[op],
                operand_patterns=list(operation_patterns[op])
            )
            
        return breakdown
    
    def _analyze_data_types(self, instructions: List[DXILInstruction]) -> Dict[str, int]:
        """Analyze data type distribution."""
        type_counts = defaultdict(int)
        
        for instruction in instructions:
            if instruction.data_type:
                normalized_type = self.DATA_TYPE_MAPPINGS.get(instruction.data_type, instruction.data_type)
                type_counts[normalized_type] += 1
        
        return dict(type_counts)
    
    def _calculate_complexity_metrics(self, instructions: List[DXILInstruction]) -> Dict[str, float]:
        """Calculate complexity metrics for the shader."""
        if not instructions:
            return {}
            
        total_ops = len(instructions)
        
        # Count different operation categories
        category_counts = defaultdict(int)
        for instruction in instructions:
            if instruction.operation:
                for category, ops in self.OPERATION_CATEGORIES.items():
                    if instruction.operation in ops:
                        category_counts[category] += 1
                        break
        
        # Calculate metrics
        metrics = {
            'arithmetic_intensity': category_counts['arithmetic'] / total_ops if total_ops > 0 else 0,
            'vector_operations_ratio': category_counts['vector'] / total_ops if total_ops > 0 else 0,
            'memory_operations_ratio': category_counts['memory'] / total_ops if total_ops > 0 else 0,
            'texture_operations_ratio': category_counts['texture'] / total_ops if total_ops > 0 else 0,
            'total_computational_ops': total_ops,
            'unique_operations': len(set(inst.operation for inst in instructions if inst.operation))
        }
        
        return metrics
    
    def _create_instruction_details(self, instructions: List[DXILInstruction]) -> List[Dict[str, Any]]:
        """Create detailed analysis for each instruction."""
        details = []
        
        for instruction in instructions:
            detail = {
                'line_number': instruction.line_number,
                'operation': instruction.operation,
                'data_type': instruction.data_type,
                'operand_count': len(instruction.operands),
                'has_result': instruction.result_variable is not None,
                'raw_instruction': instruction.raw_line.strip()
            }
            
            # Add operand analysis
            if instruction.operands:
                detail['operand_pattern'] = self._extract_operand_pattern(instruction)
                
            details.append(detail)
            
        return details
    
    def _extract_operand_pattern(self, instruction: DXILInstruction) -> Optional[str]:
        """Extract operand pattern from instruction."""
        if not instruction.operands:
            return None
            
        # Simplify operand types for pattern matching
        pattern_parts = []
        for operand in instruction.operands:
            if operand.startswith('%'):
                pattern_parts.append('var')
            elif operand.replace('.', '').replace('-', '').replace('e+', '').replace('e-', '').isdigit():
                pattern_parts.append('const')
            elif operand in ['float', 'i32', 'f32', 'i64']:
                pattern_parts.append('type')
            else:
                pattern_parts.append('other')
                
        return ' '.join(pattern_parts)
    
    def _create_empty_result(self) -> InstructionAnalysisResult:
        """Create an empty analysis result."""
        return InstructionAnalysisResult(
            total_instructions=0,
            computational_instructions=0,
            operation_breakdown={},
            data_type_distribution={},
            complexity_metrics={},
            instruction_details=[]
        )
    
    def get_summary_statistics(self, result: InstructionAnalysisResult) -> Dict[str, Any]:
        """Get summary statistics from analysis result."""
        if result.computational_instructions == 0:
            return {'message': 'No computational instructions found'}
            
        # Top operations
        top_operations = sorted(
            result.operation_breakdown.items(),
            key=lambda x: x[1].count,
            reverse=True
        )[:5]
        
        # Most common data type
        most_common_type = max(result.data_type_distribution.items(), key=lambda x: x[1]) if result.data_type_distribution else ('unknown', 0)
        
        return {
            'total_computational_instructions': result.computational_instructions,
            'unique_operation_types': len(result.operation_breakdown),
            'top_operations': [(op, analysis.count) for op, analysis in top_operations],
            'most_common_data_type': most_common_type,
            'arithmetic_intensity': result.complexity_metrics.get('arithmetic_intensity', 0),
            'vector_operations_ratio': result.complexity_metrics.get('vector_operations_ratio', 0)
        }
