;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xy          1     NONE   float   xy  
; TEXCOORD                 1     zw        1     NONE   float     zw
; TEXCOORD                 2     zw        2     NONE   float     zw
; TEXCOORD                 3     zw        3     NONE   float     zw
; TEXCOORD                 4     zw        4     NONE   float     zw
; TEXCOORD                 5     zw        5     NONE   float     zw
; TEXCOORD                 6     zw        6     NONE   float     zw
; TEXCOORD                 7     zw        7     NONE   float     zw
; TEXCOORD                 8     zw        8     NONE   float     zw
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: ad8ec347a808486d87192f392b1a2db4
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 3
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 9
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer PostProcessParams
; {
;
;   struct PostProcessParams
;   {
;
;       float2 TexelSize;                             ; Offset:    0
;       float BlurRadius;                             ; Offset:    8
;       float Time;                                   ; Offset:   12
;       float Brightness;                             ; Offset:   16
;       float Contrast;                               ; Offset:   20
;       float Saturation;                             ; Offset:   24
;       float Gamma;                                  ; Offset:   28
;       float VignetteStrength;                       ; Offset:   32
;       float ChromaticAberration;                    ; Offset:   36
;       float FilmGrain;                              ; Offset:   40
;       int EffectType;                               ; Offset:   44
;   
;   } PostProcessParams;                              ; Offset:    0 Size:    48
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PostProcessParams                 cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; PointSampler                      sampler      NA          NA      S1             s1     1
; SceneTexture                      texture     f32          2d      T0             t0     1
; BloomTexture                      texture     f32          2d      T1             t1     1
; DepthTexture                      texture     f32          2d      T2             t2     1
; NoiseTexture                      texture     f32          2d      T3             t3     1
;
;
; ViewId state:
;
; Number of inputs: 36, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 5, 6, 7, 10, 11, 14, 15, 18, 19, 22, 23, 26, 27, 30, 31, 34, 35 }
;   output 1 depends on inputs: { 4, 5, 6, 7, 10, 11, 14, 15, 18, 19, 22, 23, 26, 27, 30, 31, 34, 35 }
;   output 2 depends on inputs: { 4, 5, 6, 7, 10, 11, 14, 15, 18, 19, 22, 23, 26, 27, 30, 31, 34, 35 }
;   output 3 depends on inputs: { 4, 5, 6, 7, 10, 11, 14, 15, 18, 19, 22, 23, 26, 27, 30, 31, 34, 35 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%PostProcessParams = type { <2 x float>, float, float, float, float, float, float, float, float, float, i32 }
%struct.SamplerState = type { i32 }

@weights.i.hca = internal unnamed_addr constant [9 x float] [float 6.250000e-02, float 1.250000e-01, float 6.250000e-02, float 1.250000e-01, float 2.500000e-01, float 1.250000e-01, float 6.250000e-02, float 1.250000e-01, float 6.250000e-02]

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = alloca [8 x float], align 4
  %11 = alloca [8 x float], align 4
  %12 = alloca [8 x float], align 4
  %13 = alloca [8 x float], align 4
  %14 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %7, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %15 = extractvalue %dx.types.CBufRet.i32 %14, 3
  %16 = icmp eq i32 %15, 1
  br i1 %16, label %17, label %90

; <label>:17                                      ; preds = %0
  %18 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 0
  %19 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 0
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %20, float* %18, align 4
  store float %21, float* %19, align 4
  %22 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 1
  %23 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 1
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 1, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 1, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %24, float* %22, align 4
  store float %25, float* %23, align 4
  %26 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 2
  %27 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 2
  %28 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 2, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %29 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 2, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %28, float* %26, align 4
  store float %29, float* %27, align 4
  %30 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 3
  %31 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 3
  %32 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 3, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %33 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 3, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %32, float* %30, align 4
  store float %33, float* %31, align 4
  %34 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 4
  %35 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 4
  %36 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 4, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %37 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 4, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %36, float* %34, align 4
  store float %37, float* %35, align 4
  %38 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 5
  %39 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 5
  %40 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 5, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %41 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 5, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %40, float* %38, align 4
  store float %41, float* %39, align 4
  %42 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 6
  %43 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 6
  %44 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 6, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %45 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 6, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %44, float* %42, align 4
  store float %45, float* %43, align 4
  %46 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 7
  %47 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 7
  %48 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 7, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %49 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 7, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %48, float* %46, align 4
  store float %49, float* %47, align 4
  %50 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %51 = extractvalue %dx.types.ResRet.f32 %50, 0
  %52 = extractvalue %dx.types.ResRet.f32 %50, 1
  %53 = extractvalue %dx.types.ResRet.f32 %50, 2
  %54 = extractvalue %dx.types.ResRet.f32 %50, 3
  %55 = fmul fast float %51, 2.500000e-01
  %56 = fmul fast float %52, 2.500000e-01
  %57 = fmul fast float %53, 2.500000e-01
  %58 = fmul fast float %54, 2.500000e-01
  br label %59

; <label>:59                                      ; preds = %83, %17
  %60 = phi float [ 6.250000e-02, %17 ], [ %89, %83 ]
  %61 = phi float [ %21, %17 ], [ %87, %83 ]
  %62 = phi float [ %20, %17 ], [ %85, %83 ]
  %63 = phi float [ %55, %17 ], [ %77, %83 ]
  %64 = phi float [ %56, %17 ], [ %78, %83 ]
  %65 = phi float [ %57, %17 ], [ %79, %83 ]
  %66 = phi float [ %58, %17 ], [ %80, %83 ]
  %67 = phi i32 [ 0, %17 ], [ %81, %83 ]
  %68 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %62, float %61, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %69 = extractvalue %dx.types.ResRet.f32 %68, 0
  %70 = extractvalue %dx.types.ResRet.f32 %68, 1
  %71 = extractvalue %dx.types.ResRet.f32 %68, 2
  %72 = extractvalue %dx.types.ResRet.f32 %68, 3
  %73 = fmul fast float %69, %60
  %74 = fmul fast float %70, %60
  %75 = fmul fast float %71, %60
  %76 = fmul fast float %72, %60
  %77 = fadd fast float %73, %63
  %78 = fadd fast float %74, %64
  %79 = fadd fast float %75, %65
  %80 = fadd fast float %76, %66
  %81 = add nuw nsw i32 %67, 1
  %82 = icmp eq i32 %81, 8
  br i1 %82, label %236, label %83

; <label>:83                                      ; preds = %59
  %84 = getelementptr [8 x float], [8 x float]* %10, i32 0, i32 %81
  %85 = load float, float* %84, align 4
  %86 = getelementptr [8 x float], [8 x float]* %11, i32 0, i32 %81
  %87 = load float, float* %86, align 4
  %88 = getelementptr inbounds [9 x float], [9 x float]* @weights.i.hca, i32 0, i32 %81
  %89 = load float, float* %88, align 4, !tbaa !29
  br label %59

; <label>:90                                      ; preds = %0
  %91 = icmp eq i32 %15, 2
  br i1 %91, label %92, label %111

; <label>:92                                      ; preds = %90
  %93 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %94 = extractvalue %dx.types.ResRet.f32 %93, 0
  %95 = extractvalue %dx.types.ResRet.f32 %93, 1
  %96 = extractvalue %dx.types.ResRet.f32 %93, 2
  %97 = extractvalue %dx.types.ResRet.f32 %93, 3
  %98 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %99 = extractvalue %dx.types.ResRet.f32 %98, 0
  %100 = extractvalue %dx.types.ResRet.f32 %98, 1
  %101 = extractvalue %dx.types.ResRet.f32 %98, 2
  %102 = extractvalue %dx.types.ResRet.f32 %98, 3
  %103 = fmul fast float %99, 0x3FD3333340000000
  %104 = fmul fast float %100, 0x3FD3333340000000
  %105 = fmul fast float %101, 0x3FD3333340000000
  %106 = fmul fast float %102, 0x3FD3333340000000
  %107 = fadd fast float %103, %94
  %108 = fadd fast float %104, %95
  %109 = fadd fast float %105, %96
  %110 = fadd fast float %106, %97
  br label %237

; <label>:111                                     ; preds = %90
  %112 = icmp eq i32 %15, 3
  br i1 %112, label %113, label %205

; <label>:113                                     ; preds = %111
  %114 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 0
  %115 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 0
  %116 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %117 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %116, float* %114, align 4
  store float %117, float* %115, align 4
  %118 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 1
  %119 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 1
  %120 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 1, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %121 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 1, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %120, float* %118, align 4
  store float %121, float* %119, align 4
  %122 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 2
  %123 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 2
  %124 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 2, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %125 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 2, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %124, float* %122, align 4
  store float %125, float* %123, align 4
  %126 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 3
  %127 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 3
  %128 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 3, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %129 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 3, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %128, float* %126, align 4
  store float %129, float* %127, align 4
  %130 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 4
  %131 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 4
  %132 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 4, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %133 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 4, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %132, float* %130, align 4
  store float %133, float* %131, align 4
  %134 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 5
  %135 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 5
  %136 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 5, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %137 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 5, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %136, float* %134, align 4
  store float %137, float* %135, align 4
  %138 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 6
  %139 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 6
  %140 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 6, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %141 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 6, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %140, float* %138, align 4
  store float %141, float* %139, align 4
  %142 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 7
  %143 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 7
  %144 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 7, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %145 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 7, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  store float %144, float* %142, align 4
  store float %145, float* %143, align 4
  %146 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %5, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %147 = extractvalue %dx.types.ResRet.f32 %146, 0
  %148 = fadd fast float %147, -5.000000e-01
  %149 = call float @dx.op.unary.f32(i32 6, float %148)  ; FAbs(value)
  %150 = fmul fast float %149, 5.000000e+00
  %151 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %152 = extractvalue %dx.types.ResRet.f32 %151, 0
  %153 = extractvalue %dx.types.ResRet.f32 %151, 2
  %154 = extractvalue %dx.types.ResRet.f32 %151, 1
  %155 = extractvalue %dx.types.ResRet.f32 %151, 3
  %156 = fmul fast float %152, 2.500000e-01
  %157 = fmul fast float %154, 2.500000e-01
  %158 = fmul fast float %153, 2.500000e-01
  %159 = fmul fast float %155, 2.500000e-01
  br label %160

; <label>:160                                     ; preds = %184, %113
  %161 = phi float [ 6.250000e-02, %113 ], [ %190, %184 ]
  %162 = phi float [ %117, %113 ], [ %188, %184 ]
  %163 = phi float [ %116, %113 ], [ %186, %184 ]
  %164 = phi float [ %156, %113 ], [ %178, %184 ]
  %165 = phi float [ %157, %113 ], [ %179, %184 ]
  %166 = phi float [ %158, %113 ], [ %180, %184 ]
  %167 = phi float [ %159, %113 ], [ %181, %184 ]
  %168 = phi i32 [ 0, %113 ], [ %182, %184 ]
  %169 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %163, float %162, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %170 = extractvalue %dx.types.ResRet.f32 %169, 0
  %171 = extractvalue %dx.types.ResRet.f32 %169, 1
  %172 = extractvalue %dx.types.ResRet.f32 %169, 2
  %173 = extractvalue %dx.types.ResRet.f32 %169, 3
  %174 = fmul fast float %170, %161
  %175 = fmul fast float %171, %161
  %176 = fmul fast float %172, %161
  %177 = fmul fast float %173, %161
  %178 = fadd fast float %174, %164
  %179 = fadd fast float %175, %165
  %180 = fadd fast float %176, %166
  %181 = fadd fast float %177, %167
  %182 = add nuw nsw i32 %168, 1
  %183 = icmp eq i32 %182, 8
  br i1 %183, label %191, label %184

; <label>:184                                     ; preds = %160
  %185 = getelementptr [8 x float], [8 x float]* %12, i32 0, i32 %182
  %186 = load float, float* %185, align 4
  %187 = getelementptr [8 x float], [8 x float]* %13, i32 0, i32 %182
  %188 = load float, float* %187, align 4
  %189 = getelementptr inbounds [9 x float], [9 x float]* @weights.i.hca, i32 0, i32 %182
  %190 = load float, float* %189, align 4, !tbaa !29
  br label %160

; <label>:191                                     ; preds = %160
  %192 = call float @dx.op.unary.f32(i32 7, float %150)  ; Saturate(value)
  %193 = fsub fast float %178, %152
  %194 = fsub fast float %179, %154
  %195 = fsub fast float %180, %153
  %196 = fsub fast float %181, %155
  %197 = fmul fast float %192, %193
  %198 = fmul fast float %192, %194
  %199 = fmul fast float %192, %195
  %200 = fmul fast float %192, %196
  %201 = fadd fast float %197, %152
  %202 = fadd fast float %198, %154
  %203 = fadd fast float %199, %153
  %204 = fadd fast float %200, %155
  br label %237

; <label>:205                                     ; preds = %111
  %206 = icmp eq i32 %15, 4
  br i1 %206, label %207, label %230

; <label>:207                                     ; preds = %205
  %208 = fadd fast float %8, -5.000000e-01
  %209 = fadd fast float %9, -5.000000e-01
  %210 = call float @dx.op.dot2.f32(i32 54, float %208, float %209, float %208, float %209)  ; Dot2(ax,ay,bx,by)
  %211 = fmul fast float %210, 0x3FC99999A0000000
  %212 = fadd fast float %211, 1.000000e+00
  %213 = fmul fast float %212, %208
  %214 = fmul fast float %212, %209
  %215 = fadd fast float %213, 5.000000e-01
  %216 = fadd fast float %214, 5.000000e-01
  %217 = fcmp fast olt float %215, 0.000000e+00
  %218 = fcmp fast ogt float %215, 1.000000e+00
  %219 = or i1 %217, %218
  %220 = fcmp fast olt float %216, 0.000000e+00
  %221 = or i1 %220, %219
  %222 = fcmp fast ogt float %216, 1.000000e+00
  %223 = or i1 %222, %221
  br i1 %223, label %237, label %224

; <label>:224                                     ; preds = %207
  %225 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %215, float %216, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %226 = extractvalue %dx.types.ResRet.f32 %225, 0
  %227 = extractvalue %dx.types.ResRet.f32 %225, 1
  %228 = extractvalue %dx.types.ResRet.f32 %225, 2
  %229 = extractvalue %dx.types.ResRet.f32 %225, 3
  br label %237

; <label>:230                                     ; preds = %205
  %231 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %232 = extractvalue %dx.types.ResRet.f32 %231, 0
  %233 = extractvalue %dx.types.ResRet.f32 %231, 1
  %234 = extractvalue %dx.types.ResRet.f32 %231, 2
  %235 = extractvalue %dx.types.ResRet.f32 %231, 3
  br label %237

; <label>:236                                     ; preds = %59
  br label %237

; <label>:237                                     ; preds = %236, %230, %224, %207, %191, %92
  %238 = phi float [ %107, %92 ], [ %201, %191 ], [ %232, %230 ], [ %226, %224 ], [ 0.000000e+00, %207 ], [ %77, %236 ]
  %239 = phi float [ %108, %92 ], [ %202, %191 ], [ %233, %230 ], [ %227, %224 ], [ 0.000000e+00, %207 ], [ %78, %236 ]
  %240 = phi float [ %109, %92 ], [ %203, %191 ], [ %234, %230 ], [ %228, %224 ], [ 0.000000e+00, %207 ], [ %79, %236 ]
  %241 = phi float [ %110, %92 ], [ %204, %191 ], [ %235, %230 ], [ %229, %224 ], [ 1.000000e+00, %207 ], [ %80, %236 ]
  %242 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %243 = extractvalue %dx.types.CBufRet.f32 %242, 1
  %244 = fcmp fast ogt float %243, 0.000000e+00
  %245 = fadd fast float %8, -5.000000e-01
  %246 = fadd fast float %9, -5.000000e-01
  %247 = fmul fast float %245, %245
  %248 = fmul fast float %246, %246
  %249 = fadd fast float %248, %247
  %250 = call float @dx.op.unary.f32(i32 24, float %249)  ; Sqrt(value)
  br i1 %244, label %251, label %270

; <label>:251                                     ; preds = %237
  %252 = fmul fast float %250, %245
  %253 = fmul fast float %252, %243
  %254 = fmul fast float %250, %246
  %255 = fmul fast float %254, %243
  %256 = fmul fast float %243, 5.000000e-01
  %257 = fmul fast float %256, %252
  %258 = fmul fast float %256, %254
  %259 = fadd fast float %253, %8
  %260 = fadd fast float %255, %9
  %261 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %259, float %260, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %262 = extractvalue %dx.types.ResRet.f32 %261, 0
  %263 = fadd fast float %257, %8
  %264 = fadd fast float %258, %9
  %265 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %263, float %264, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %266 = extractvalue %dx.types.ResRet.f32 %265, 1
  %267 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %6, float %8, float %9, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %268 = extractvalue %dx.types.ResRet.f32 %267, 2
  %269 = extractvalue %dx.types.ResRet.f32 %267, 3
  br label %270

; <label>:270                                     ; preds = %251, %237
  %271 = phi float [ %262, %251 ], [ %238, %237 ]
  %272 = phi float [ %266, %251 ], [ %239, %237 ]
  %273 = phi float [ %268, %251 ], [ %240, %237 ]
  %274 = phi float [ %269, %251 ], [ %241, %237 ]
  %275 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %276 = extractvalue %dx.types.CBufRet.f32 %275, 0
  %277 = fmul fast float %276, %271
  %278 = fmul fast float %276, %272
  %279 = fmul fast float %276, %273
  %280 = fadd fast float %277, -5.000000e-01
  %281 = fadd fast float %278, -5.000000e-01
  %282 = fadd fast float %279, -5.000000e-01
  %283 = extractvalue %dx.types.CBufRet.f32 %275, 1
  %284 = fmul fast float %280, %283
  %285 = fmul fast float %281, %283
  %286 = fmul fast float %282, %283
  %287 = fadd fast float %284, 5.000000e-01
  %288 = fadd fast float %285, 5.000000e-01
  %289 = fadd fast float %286, 5.000000e-01
  %290 = fcmp fast olt float %288, %289
  %291 = select i1 %290, float 0.000000e+00, float 1.000000e+00
  %292 = fsub fast float %288, %289
  %293 = fsub fast float %289, %288
  %294 = fmul fast float %291, %292
  %295 = fmul fast float %291, %293
  %296 = fadd fast float %294, %289
  %297 = fadd fast float %295, %288
  %298 = fadd fast float %291, -1.000000e+00
  %299 = fsub fast float 0x3FE5555560000000, %291
  %300 = fcmp fast olt float %287, %296
  %301 = select i1 %300, float 0.000000e+00, float 1.000000e+00
  %302 = fsub fast float %287, %296
  %303 = fsub fast float %298, %299
  %304 = fsub fast float %296, %287
  %305 = fmul fast float %301, %302
  %306 = fmul fast float %301, %303
  %307 = fmul fast float %301, %304
  %308 = fadd fast float %305, %296
  %309 = fadd fast float %307, %287
  %310 = call float @dx.op.binary.f32(i32 36, float %309, float %297)  ; FMin(a,b)
  %311 = fsub fast float %308, %310
  %312 = fsub fast float %309, %297
  %313 = fmul fast float %311, 6.000000e+00
  %314 = fadd fast float %313, 0x3DDB7CDFE0000000
  %315 = fdiv fast float %312, %314
  %316 = fadd fast float %299, %315
  %317 = fadd fast float %316, %306
  %318 = call float @dx.op.unary.f32(i32 6, float %317)  ; FAbs(value)
  %319 = fadd fast float %308, 0x3DDB7CDFE0000000
  %320 = fdiv fast float %311, %319
  %321 = extractvalue %dx.types.CBufRet.f32 %275, 2
  %322 = fmul fast float %320, %321
  %323 = fadd fast float %318, 1.000000e+00
  %324 = fadd fast float %318, 0x3FE5555560000000
  %325 = fadd fast float %318, 0x3FD5555560000000
  %326 = call float @dx.op.unary.f32(i32 22, float %323)  ; Frc(value)
  %327 = call float @dx.op.unary.f32(i32 22, float %324)  ; Frc(value)
  %328 = call float @dx.op.unary.f32(i32 22, float %325)  ; Frc(value)
  %329 = fmul fast float %326, 6.000000e+00
  %330 = fmul fast float %327, 6.000000e+00
  %331 = fmul fast float %328, 6.000000e+00
  %332 = fadd fast float %329, -3.000000e+00
  %333 = fadd fast float %330, -3.000000e+00
  %334 = fadd fast float %331, -3.000000e+00
  %335 = call float @dx.op.unary.f32(i32 6, float %332)  ; FAbs(value)
  %336 = call float @dx.op.unary.f32(i32 6, float %333)  ; FAbs(value)
  %337 = call float @dx.op.unary.f32(i32 6, float %334)  ; FAbs(value)
  %338 = fadd fast float %335, -1.000000e+00
  %339 = fadd fast float %336, -1.000000e+00
  %340 = fadd fast float %337, -1.000000e+00
  %341 = call float @dx.op.unary.f32(i32 7, float %338)  ; Saturate(value)
  %342 = call float @dx.op.unary.f32(i32 7, float %339)  ; Saturate(value)
  %343 = call float @dx.op.unary.f32(i32 7, float %340)  ; Saturate(value)
  %344 = fadd fast float %341, -1.000000e+00
  %345 = fadd fast float %342, -1.000000e+00
  %346 = fadd fast float %343, -1.000000e+00
  %347 = fmul fast float %344, %322
  %348 = fmul fast float %345, %322
  %349 = fmul fast float %346, %322
  %350 = fadd fast float %347, 1.000000e+00
  %351 = fadd fast float %348, 1.000000e+00
  %352 = fadd fast float %349, 1.000000e+00
  %353 = extractvalue %dx.types.CBufRet.f32 %242, 0
  %354 = fmul fast float %250, %353
  %355 = fadd fast float %354, 0xBFD3333340000000
  %356 = fmul fast float %355, 2.000000e+00
  %357 = call float @dx.op.unary.f32(i32 7, float %356)  ; Saturate(value)
  %358 = fmul fast float %357, 2.000000e+00
  %359 = fsub fast float 3.000000e+00, %358
  %360 = fmul fast float %357, %357
  %361 = fmul fast float %360, %359
  %362 = fsub fast float 1.000000e+00, %361
  %363 = fmul fast float %362, %308
  %364 = fmul fast float %363, %350
  %365 = fmul fast float %363, %351
  %366 = fmul fast float %363, %352
  %367 = extractvalue %dx.types.CBufRet.f32 %242, 2
  %368 = fcmp fast ogt float %367, 0.000000e+00
  br i1 %368, label %369, label %384

; <label>:369                                     ; preds = %270
  %370 = fmul fast float %8, 1.000000e+01
  %371 = fmul fast float %9, 1.000000e+01
  %372 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %373 = extractvalue %dx.types.CBufRet.f32 %372, 3
  %374 = fmul fast float %373, 0x3FB99999A0000000
  %375 = fadd fast float %374, %370
  %376 = fadd fast float %374, %371
  %377 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %6, float %375, float %376, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %378 = extractvalue %dx.types.ResRet.f32 %377, 0
  %379 = fadd fast float %378, -5.000000e-01
  %380 = fmul fast float %379, %367
  %381 = fadd fast float %380, %364
  %382 = fadd fast float %380, %365
  %383 = fadd fast float %380, %366
  br label %384

; <label>:384                                     ; preds = %369, %270
  %385 = phi float [ %381, %369 ], [ %364, %270 ]
  %386 = phi float [ %382, %369 ], [ %365, %270 ]
  %387 = phi float [ %383, %369 ], [ %366, %270 ]
  %388 = extractvalue %dx.types.CBufRet.f32 %275, 3
  %389 = call float @dx.op.unary.f32(i32 6, float %385)  ; FAbs(value)
  %390 = call float @dx.op.unary.f32(i32 6, float %386)  ; FAbs(value)
  %391 = call float @dx.op.unary.f32(i32 6, float %387)  ; FAbs(value)
  %392 = call float @dx.op.unary.f32(i32 23, float %389)  ; Log(value)
  %393 = call float @dx.op.unary.f32(i32 23, float %390)  ; Log(value)
  %394 = call float @dx.op.unary.f32(i32 23, float %391)  ; Log(value)
  %395 = fmul fast float %392, %388
  %396 = fmul fast float %393, %388
  %397 = fmul fast float %394, %388
  %398 = call float @dx.op.unary.f32(i32 21, float %395)  ; Exp(value)
  %399 = call float @dx.op.unary.f32(i32 21, float %396)  ; Exp(value)
  %400 = call float @dx.op.unary.f32(i32 21, float %397)  ; Exp(value)
  %401 = call float @dx.op.unary.f32(i32 7, float %398)  ; Saturate(value)
  %402 = call float @dx.op.unary.f32(i32 7, float %399)  ; Saturate(value)
  %403 = call float @dx.op.unary.f32(i32 7, float %400)  ; Saturate(value)
  %404 = call float @dx.op.unary.f32(i32 7, float %274)  ; Saturate(value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %401)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %402)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %403)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %404)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot2.f32(i32, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!16}
!dx.entryPoints = !{!17}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !11, !13}
!5 = !{!6, !8, !9, !10}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{i32 2, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 2, i32 0, !7}
!11 = !{!12}
!12 = !{i32 0, %PostProcessParams* undef, !"", i32 0, i32 0, i32 1, i32 48, null}
!13 = !{!14, !15}
!14 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!15 = !{i32 1, %struct.SamplerState* undef, !"", i32 0, i32 1, i32 1, i32 0, null}
!16 = !{[38 x i32] [i32 36, i32 4, i32 0, i32 0, i32 0, i32 0, i32 15, i32 15, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15, i32 0, i32 0, i32 15, i32 15]}
!17 = !{void ()* @main, !"main", !18, !4, null}
!18 = !{!19, !26, null}
!19 = !{!20, !22, !24}
!20 = !{i32 0, !"SV_Position", i8 9, i8 3, !21, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!21 = !{i32 0}
!22 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 1, i8 2, i32 1, i8 0, !23}
!23 = !{i32 3, i32 3}
!24 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !25, i8 2, i32 8, i8 2, i32 1, i8 2, !23}
!25 = !{i32 1, i32 2, i32 3, i32 4, i32 5, i32 6, i32 7, i32 8}
!26 = !{!27}
!27 = !{i32 0, !"SV_Target", i8 9, i8 16, !21, i8 0, i32 1, i8 4, i32 0, i8 0, !28}
!28 = !{i32 3, i32 15}
!29 = !{!30, !30, i64 0}
!30 = !{!"float", !31, i64 0}
!31 = !{!"omnipotent char", !32, i64 0}
!32 = !{!"Simple C/C++ TBAA"}
