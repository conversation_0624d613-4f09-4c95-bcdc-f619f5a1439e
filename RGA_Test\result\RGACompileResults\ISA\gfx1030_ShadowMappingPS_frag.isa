_amdgpu_ps_main:
	s_mov_b64 s[18:19], exec                                   // 000000000000: BE92047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s16, s1                                          // 000000000008: BE900301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s17, s1                                          // 000000000014: BE910301
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx4 s[12:15], s[16:17], 0x20                    // 00000000001C: F4080308 FA000020
	s_load_dwordx8 s[4:11], s[16:17], null                     // 000000000024: F40C0108 FA000000
	v_interp_p1_f32_e32 v2, v0, attr1.x                        // 00000000002C: C8080400
	v_interp_p1_f32_e32 v3, v0, attr1.y                        // 000000000030: C80C0500
	v_interp_p2_f32_e32 v2, v1, attr1.x                        // 000000000034: C8090401
	v_interp_p2_f32_e32 v3, v1, attr1.y                        // 000000000038: C80D0501
	s_and_b64 exec, exec, s[18:19]                             // 00000000003C: 87FE127E
	s_waitcnt lgkmcnt(0)                                       // 000000000040: BF8CC07F
	image_sample v[2:5], v[2:3], s[4:11], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000044: F0800F08 00610202
	v_interp_p1_f32_e32 v6, v0, attr2.w                        // 00000000004C: C8180B00
	v_interp_p1_f32_e32 v7, v0, attr2.x                        // 000000000050: C81C0800
	s_load_dwordx4 s[12:15], s[16:17], null                    // 000000000054: F4080308 FA000000
	v_interp_p2_f32_e32 v6, v1, attr2.w                        // 00000000005C: C8190B01
	v_interp_p2_f32_e32 v7, v1, attr2.x                        // 000000000060: C81D0801
	v_rcp_f32_e32 v9, v6                                       // 000000000064: 7E125506
	v_interp_p1_f32_e32 v6, v0, attr2.y                        // 000000000068: C8180900
	v_interp_p2_f32_e32 v6, v1, attr2.y                        // 00000000006C: C8190901
	v_mul_f32_e32 v7, v7, v9                                   // 000000000070: 100E1307
	v_mul_f32_e32 v6, v6, v9                                   // 000000000074: 100C1306
	v_fma_f32 v7, v7, 0.5, 0.5                                 // 000000000078: D54B0007 03C1E107
	v_fma_f32 v8, v6, -0.5, 0.5                                // 000000000080: D54B0008 03C1E306
	v_min_f32_e32 v6, v7, v8                                   // 000000000088: 1E0C1107
	v_max_f32_e32 v10, v7, v8                                  // 00000000008C: 20141107
	v_cmp_ngt_f32_e32 vcc_lo, 0, v6                            // 000000000090: 7C160C80
	v_cmp_nlt_f32_e64 s0, 1.0, v10                             // 000000000094: D40E0000 000214F2
	v_mov_b32_e32 v6, 0                                        // 00000000009C: 7E0C0280
	s_and_b64 s[4:5], s[0:1], vcc                              // 0000000000A0: 87846A00
	s_and_saveexec_b64 s[0:1], s[4:5]                          // 0000000000A4: BE802404
	s_cbranch_execz _L0                                        // 0000000000A8: BF880030
	s_waitcnt lgkmcnt(0)                                       // 0000000000AC: BF8CC07F
	s_buffer_load_dword s3, s[12:15], 0x38                     // 0000000000B0: F42000C6 FA000038
	v_mov_b32_e32 v6, 0                                        // 0000000000B8: 7E0C0280
	s_waitcnt lgkmcnt(0)                                       // 0000000000BC: BF8CC07F
	s_lshr_b32 s4, s3, 31                                      // 0000000000C0: 90049F03
	s_add_i32 s4, s3, s4                                       // 0000000000C4: 81040403
	s_ashr_i32 s20, s4, 1                                      // 0000000000C8: 91148104
	s_sub_i32 s21, 0, s20                                      // 0000000000CC: 81951480
	s_cmp_gt_i32 s21, s20                                      // 0000000000D0: BF021415
	s_cbranch_scc1 _L1                                         // 0000000000D4: BF850020
	s_buffer_load_dword s22, s[12:15], 0x30                    // 0000000000D8: F4200586 FA000030
	s_clause 0x1                                               // 0000000000E0: BFA10001
	s_load_dwordx8 s[4:11], s[16:17], 0x30                     // 0000000000E4: F40C0108 FA000030
	s_load_dwordx4 s[16:19], s[16:17], 0x50                    // 0000000000EC: F4080408 FA000050
	s_mov_b32 m0, s2                                           // 0000000000F4: BEFC0302
	v_interp_p1_f32_e32 v6, v0, attr2.z                        // 0000000000F8: C8180A00
	v_interp_p2_f32_e32 v6, v1, attr2.z                        // 0000000000FC: C8190A01
	s_waitcnt lgkmcnt(0)                                       // 000000000100: BF8CC07F
	v_fma_f32 v9, v6, v9, -s22                                 // 000000000104: D54B0009 805A1306
	v_mov_b32_e32 v6, 0                                        // 00000000010C: 7E0C0280
	s_mov_b32 s22, s21                                         // 000000000110: BE960315
_L3:
	v_cvt_f32_i32_e32 v10, s22                                 // 000000000114: 7E140A16
	s_mov_b32 s23, s21                                         // 000000000118: BE970315
	v_fmamk_f32 v10, v10, 0x3a000000, v7                       // 00000000011C: 58140F0A 3A000000
_L2:
	v_cvt_f32_i32_e32 v11, s23                                 // 000000000124: 7E160A17
	s_add_i32 s23, s23, 1                                      // 000000000128: 81178117
	s_cmp_le_i32 s23, s20                                      // 00000000012C: BF051417
	v_fmamk_f32 v11, v11, 0x3a000000, v8                       // 000000000130: 5816110B 3A000000
	image_sample_c_lz v11, v[9:11], s[4:11], s[16:19] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000138: F0BC0108 00810B09
	s_waitcnt vmcnt(0)                                         // 000000000140: BF8C3F70
	v_add_f32_e32 v6, v11, v6                                  // 000000000144: 060C0D0B
	s_cbranch_scc1 _L2                                         // 000000000148: BF85FFF6
	s_add_i32 s22, s22, 1                                      // 00000000014C: 81168116
	s_cmp_le_i32 s22, s20                                      // 000000000150: BF051416
	s_cbranch_scc1 _L3                                         // 000000000154: BF85FFEF
_L1:
	s_lshl_b32 s3, s3, 1                                       // 000000000158: 8F038103
	s_or_b32 s3, s3, 1                                         // 00000000015C: 88038103
	v_cvt_f32_i32_e32 v7, s3                                   // 000000000160: 7E0E0A03
	v_rcp_iflag_f32_e32 v7, v7                                 // 000000000164: 7E0E5707
	v_mul_f32_e32 v6, v6, v7                                   // 000000000168: 100C0F06
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 00000000016C: 88FE007E
	s_mov_b32 m0, s2                                           // 000000000170: BEFC0302
	s_waitcnt lgkmcnt(0)                                       // 000000000174: BF8CC07F
	s_clause 0x1                                               // 000000000178: BFA10001
	s_buffer_load_dword s16, s[12:15], 0x34                    // 00000000017C: F4200406 FA000034
	s_buffer_load_dwordx8 s[0:7], s[12:15], 0x10               // 000000000184: F42C0006 FA000010
	v_interp_p1_f32_e32 v9, v0, attr3.y                        // 00000000018C: C8240D00
	v_interp_p1_f32_e32 v10, v0, attr3.x                       // 000000000190: C8280C00
	v_interp_p1_f32_e32 v11, v0, attr0.y                       // 000000000194: C82C0100
	v_interp_p1_f32_e32 v8, v0, attr4.y                        // 000000000198: C8201100
	v_interp_p1_f32_e32 v12, v0, attr0.x                       // 00000000019C: C8300000
	v_interp_p2_f32_e32 v9, v1, attr3.y                        // 0000000001A0: C8250D01
	v_interp_p2_f32_e32 v10, v1, attr3.x                       // 0000000001A4: C8290C01
	v_interp_p2_f32_e32 v11, v1, attr0.y                       // 0000000001A8: C82D0101
	v_interp_p1_f32_e32 v13, v0, attr3.z                       // 0000000001AC: C8340E00
	v_interp_p1_f32_e32 v7, v0, attr4.x                        // 0000000001B0: C81C1000
	v_mul_f32_e32 v14, v9, v9                                  // 0000000001B4: 101C1309
	v_interp_p2_f32_e32 v8, v1, attr4.y                        // 0000000001B8: C8211101
	v_interp_p2_f32_e32 v12, v1, attr0.x                       // 0000000001BC: C8310001
	v_interp_p1_f32_e32 v15, v0, attr0.z                       // 0000000001C0: C83C0200
	v_mul_f32_e32 v16, v11, v11                                // 0000000001C4: 1020170B
	v_interp_p2_f32_e32 v13, v1, attr3.z                       // 0000000001C8: C8350E01
	v_fmac_f32_e32 v14, v10, v10                               // 0000000001CC: 561C150A
	v_interp_p2_f32_e32 v7, v1, attr4.x                        // 0000000001D0: C81D1001
	v_interp_p1_f32_e32 v0, v0, attr4.z                        // 0000000001D4: C8001200
	v_interp_p2_f32_e32 v15, v1, attr0.z                       // 0000000001D8: C83D0201
	v_fmac_f32_e32 v16, v12, v12                               // 0000000001DC: 5620190C
	v_mul_f32_e32 v17, v8, v8                                  // 0000000001E0: 10221108
	v_fmac_f32_e32 v14, v13, v13                               // 0000000001E4: 561C1B0D
	v_interp_p2_f32_e32 v0, v1, attr4.z                        // 0000000001E8: C8011201
	s_buffer_load_dwordx4 s[8:11], s[12:15], null              // 0000000001EC: F4280206 FA000000
	s_waitcnt lgkmcnt(0)                                       // 0000000001F4: BF8CC07F
	s_buffer_load_dword s3, s[12:15], 0xc                      // 0000000001F8: F42000C6 FA00000C
	v_fmac_f32_e32 v16, v15, v15                               // 000000000200: 56201F0F
	v_fmac_f32_e32 v17, v7, v7                                 // 000000000204: 56220F07
	v_rsq_f32_e32 v1, v14                                      // 000000000208: 7E025D0E
	v_cmp_neq_f32_e32 vcc_lo, 0, v14                           // 00000000020C: 7C1A1C80
	v_rsq_f32_e32 v18, v16                                     // 000000000210: 7E245D10
	v_fmac_f32_e32 v17, v0, v0                                 // 000000000214: 56220100
	v_rsq_f32_e32 v19, v17                                     // 000000000218: 7E265D11
	v_cndmask_b32_e32 v1, 0, v1, vcc_lo                        // 00000000021C: 02020280
	v_cmp_neq_f32_e32 vcc_lo, 0, v16                           // 000000000220: 7C1A2080
	v_mul_f32_e32 v9, v1, v9                                   // 000000000224: 10121301
	v_cndmask_b32_e32 v14, 0, v18, vcc_lo                      // 000000000228: 021C2480
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 00000000022C: 7C1A2280
	v_mul_f32_e32 v10, v1, v10                                 // 000000000230: 10141501
	v_mul_f32_e32 v1, v1, v13                                  // 000000000234: 10021B01
	v_mul_f32_e32 v11, v14, v11                                // 000000000238: 1016170E
	v_cndmask_b32_e32 v16, 0, v19, vcc_lo                      // 00000000023C: 02202680
	v_mul_f32_e32 v12, v14, v12                                // 000000000240: 1018190E
	v_mul_f32_e32 v17, v11, v9                                 // 000000000244: 1022130B
	v_fmac_f32_e32 v9, v16, v8                                 // 000000000248: 56121110
	v_mul_f32_e32 v8, v14, v15                                 // 00000000024C: 10101F0E
	v_fmac_f32_e32 v17, v12, v10                               // 000000000250: 5622150C
	v_fmac_f32_e32 v10, v16, v7                                // 000000000254: 56140F10
	v_mul_f32_e32 v7, v9, v9                                   // 000000000258: 100E1309
	v_fmac_f32_e32 v17, v8, v1                                 // 00000000025C: 56220308
	v_fmac_f32_e32 v1, v16, v0                                 // 000000000260: 56020110
	v_fmac_f32_e32 v7, v10, v10                                // 000000000264: 560E150A
	v_fmac_f32_e32 v7, v1, v1                                  // 000000000268: 560E0301
	v_rsq_f32_e32 v0, v7                                       // 00000000026C: 7E005D07
	v_cmp_neq_f32_e32 vcc_lo, 0, v7                            // 000000000270: 7C1A0E80
	v_cndmask_b32_e32 v0, 0, v0, vcc_lo                        // 000000000274: 02000080
	v_mul_f32_e32 v7, v0, v9                                   // 000000000278: 100E1300
	v_mul_f32_e32 v9, v0, v10                                  // 00000000027C: 10121500
	v_mul_f32_e32 v0, v0, v1                                   // 000000000280: 10000300
	v_sub_f32_e64 v1, 1.0, s16                                 // 000000000284: D5040001 000020F2
	v_mul_f32_e32 v7, v7, v11                                  // 00000000028C: 100E1707
	v_fma_f32 v1, v1, v6, s16                                  // 000000000290: D54B0001 00420D01
	v_max_f32_e32 v6, 0, v17                                   // 000000000298: 200C2280
	v_fmac_f32_e32 v7, v9, v12                                 // 00000000029C: 560E1909
	s_waitcnt vmcnt(0)                                         // 0000000002A0: BF8C3F70
	v_mul_f32_e32 v9, s7, v4                                   // 0000000002A4: 10120807
	v_mul_f32_e32 v4, s10, v4                                  // 0000000002A8: 1008080A
	v_mul_f32_e32 v6, v1, v6                                   // 0000000002AC: 100C0D01
	v_fmac_f32_e32 v7, v0, v8                                  // 0000000002B0: 560E1100
	v_mul_f32_e32 v8, s6, v3                                   // 0000000002B4: 10100606
	v_mul_f32_e32 v3, s9, v3                                   // 0000000002B8: 10060609
	v_fmac_f32_e32 v9, v6, v4                                  // 0000000002BC: 56120906
	v_max_f32_e32 v0, 0, v7                                    // 0000000002C0: 20000E80
	v_mul_f32_e32 v7, s5, v2                                   // 0000000002C4: 100E0405
	v_mul_f32_e32 v2, s8, v2                                   // 0000000002C8: 10040408
	v_fmac_f32_e32 v8, v6, v3                                  // 0000000002CC: 56100706
	v_log_f32_e32 v0, v0                                       // 0000000002D0: 7E004F00
	v_fmac_f32_e32 v7, v6, v2                                  // 0000000002D4: 560E0506
	v_mul_legacy_f32_e32 v0, s4, v0                            // 0000000002D8: 0E000004
	v_exp_f32_e32 v0, v0                                       // 0000000002DC: 7E004B00
	v_mul_f32_e32 v0, v0, v1                                   // 0000000002E0: 10000300
	v_fmac_f32_e32 v7, s0, v0                                  // 0000000002E4: 560E0000
	v_fmac_f32_e32 v8, s1, v0                                  // 0000000002E8: 56100001
	v_fmac_f32_e32 v9, s2, v0                                  // 0000000002EC: 56120002
	s_waitcnt lgkmcnt(0)                                       // 0000000002F0: BF8CC07F
	v_mul_f32_e32 v0, s3, v5                                   // 0000000002F4: 10000A03
	exp mrt0 v7, v8, v9, v0 done vm                            // 0000000002F8: F800180F 00090807
	s_endpgm                                                   // 000000000300: BF810000
