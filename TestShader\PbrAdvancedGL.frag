#version 320 es

// Advanced PBR Fragment Shader - OpenGL ES Version
// Tests advanced PBR features and complex lighting

precision highp float;
precision highp samplerCube;

in vec3 vWorldPos;
in vec3 vNormal;
in vec3 vTangent;
in vec3 vBitangent;
in vec2 vTexCoord;
in vec4 vColor;
in vec3 vViewDir;
in float vVertexAO;

out vec4 fragColor;

// Material uniforms
uniform vec3 uAlbedo;
uniform float uMetallic;
uniform float uRoughness;
uniform float uAO;
uniform vec3 uEmissive;
uniform float uNormalStrength;
uniform float uHeightScale;
uniform float uSubsurfaceStrength;

// Lighting uniforms
uniform vec3 uLightPositions[8];
uniform vec3 uLightColors[8];
uniform float uLightIntensities[8];
uniform int uNumLights;

// Environment uniforms
uniform samplerCube uEnvironmentMap;
uniform samplerCube uIrradianceMap;
uniform sampler2D uBrdfLUT;

// Material textures
uniform sampler2D uAlbedoTexture;
uniform sampler2D uNormalTexture;
uniform sampler2D uMetallicTexture;
uniform sampler2D uRoughnessTexture;
uniform sampler2D uAOTexture;
uniform sampler2D uEmissiveTexture;
uniform sampler2D uHeightTexture;
uniform sampler2D uSubsurfaceTexture;

const float PI = 3.14159265359;

vec3 getNormalFromMap()
{
    vec3 tangentNormal = texture(uNormalTexture, vTexCoord).xyz * 2.0 - 1.0;
    tangentNormal.xy *= uNormalStrength;
    
    vec3 N = normalize(vNormal);
    vec3 T = normalize(vTangent);
    vec3 B = normalize(vBitangent);
    mat3 TBN = mat3(T, B, N);
    
    return normalize(TBN * tangentNormal);
}

float distributionGGX(vec3 N, vec3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;
    
    return num / denom;
}

float geometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float geometrySmith(vec3 N, vec3 V, vec3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = geometrySchlickGGX(NdotV, roughness);
    float ggx1 = geometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

vec3 fresnelSchlick(float cosTheta, vec3 F0)
{
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

vec3 fresnelSchlickRoughness(float cosTheta, vec3 F0, float roughness)
{
    return F0 + (max(vec3(1.0 - roughness), F0) - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

// Subsurface scattering approximation
vec3 subsurfaceScattering(vec3 N, vec3 L, vec3 V, vec3 albedo, float strength)
{
    vec3 H = normalize(L + N * 0.5); // Offset normal for subsurface effect
    float VdotH = max(0.0, dot(V, -H));
    float scattering = pow(VdotH, 4.0) * strength;
    return albedo * scattering;
}

void main()
{
    // Sample material textures
    vec3 albedo = pow(texture(uAlbedoTexture, vTexCoord).rgb * uAlbedo * vColor.rgb, vec3(2.2));
    float metallic = texture(uMetallicTexture, vTexCoord).r * uMetallic;
    float roughness = texture(uRoughnessTexture, vTexCoord).r * uRoughness;
    float ao = texture(uAOTexture, vTexCoord).r * uAO * vVertexAO;
    vec3 emissive = texture(uEmissiveTexture, vTexCoord).rgb * uEmissive;
    float subsurface = texture(uSubsurfaceTexture, vTexCoord).r * uSubsurfaceStrength;
    
    // Get normal from normal map
    vec3 N = getNormalFromMap();
    vec3 V = normalize(vViewDir);
    
    // Calculate reflectance at normal incidence
    vec3 F0 = vec3(0.04);
    F0 = mix(F0, albedo, metallic);
    
    // Reflectance equation
    vec3 Lo = vec3(0.0);
    
    // Calculate lighting for each light source
    for(int i = 0; i < uNumLights && i < 8; ++i)
    {
        vec3 L = normalize(uLightPositions[i] - vWorldPos);
        vec3 H = normalize(V + L);
        float distance = length(uLightPositions[i] - vWorldPos);
        float attenuation = 1.0 / (distance * distance);
        vec3 radiance = uLightColors[i] * uLightIntensities[i] * attenuation;
        
        // Cook-Torrance BRDF
        float NDF = distributionGGX(N, H, roughness);
        float G = geometrySmith(N, V, L, roughness);
        vec3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
        
        vec3 kS = F;
        vec3 kD = vec3(1.0) - kS;
        kD *= 1.0 - metallic;
        
        vec3 numerator = NDF * G * F;
        float denominator = 4.0 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.0001;
        vec3 specular = numerator / denominator;
        
        float NdotL = max(dot(N, L), 0.0);
        Lo += (kD * albedo / PI + specular) * radiance * NdotL;
        
        // Add subsurface scattering
        if (subsurface > 0.0)
        {
            vec3 sss = subsurfaceScattering(N, L, V, albedo, subsurface);
            Lo += sss * radiance * 0.5;
        }
    }
    
    // Ambient lighting (IBL)
    vec3 F = fresnelSchlickRoughness(max(dot(N, V), 0.0), F0, roughness);
    vec3 kS = F;
    vec3 kD = 1.0 - kS;
    kD *= 1.0 - metallic;
    
    vec3 irradiance = texture(uIrradianceMap, N).rgb;
    vec3 diffuse = irradiance * albedo;
    
    vec3 R = reflect(-V, N);
    const float MAX_REFLECTION_LOD = 4.0;
    vec3 prefilteredColor = textureLod(uEnvironmentMap, R, roughness * MAX_REFLECTION_LOD).rgb;
    vec2 brdf = texture(uBrdfLUT, vec2(max(dot(N, V), 0.0), roughness)).rg;
    vec3 specular = prefilteredColor * (F * brdf.x + brdf.y);
    
    vec3 ambient = (kD * diffuse + specular) * ao;
    
    vec3 color = ambient + Lo + emissive;
    
    // HDR tonemapping
    color = color / (color + vec3(1.0));
    
    // Gamma correction
    color = pow(color, vec3(1.0/2.2));
    
    fragColor = vec4(color, 1.0);
}
