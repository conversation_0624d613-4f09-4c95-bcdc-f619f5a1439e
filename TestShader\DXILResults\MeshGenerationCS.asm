;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 3bf58769d49ba525aa69105947ee235d
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(8,8,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer ComputeParams
; {
;
;   struct ComputeParams
;   {
;
;       uint ParticleCount;                           ; Offset:    0
;       uint MaxParticles;                            ; Offset:    4
;       float DeltaTime;                              ; Offset:    8
;       float Time;                                   ; Offset:   12
;       float3 Gravity;                               ; Offset:   16
;       float Damping;                                ; Offset:   28
;       float3 EmitterPosition;                       ; Offset:   32
;       float EmissionRate;                           ; Offset:   44
;       float3 EmitterDirection;                      ; Offset:   48
;       float EmissionSpeed;                          ; Offset:   60
;       float2 LifetimeRange;                         ; Offset:   64
;       float2 SizeRange;                             ; Offset:   72
;       uint FrameCount;                              ; Offset:   80
;       float NoiseScale;                             ; Offset:   84
;       float NoiseStrength;                          ; Offset:   88
;       uint _padding;                                ; Offset:   92
;   
;   } ComputeParams;                                  ; Offset:    0 Size:    96
;
; }
;
; Resource bind info for VertexBuffer
; {
;
;   struct struct.MeshVertex
;   {
;
;       float3 Position;                              ; Offset:    0
;       float3 Normal;                                ; Offset:   12
;       float2 TexCoord;                              ; Offset:   24
;       float4 Color;                                 ; Offset:   32
;   
;   } $Element;                                       ; Offset:    0 Size:    48
;
; }
;
; Resource bind info for IndexBuffer
; {
;
;   uint $Element;                                    ; Offset:    0 Size:     4
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; ComputeParams                     cbuffer      NA          NA     CB0            cb0     1
; VertexBuffer                          UAV  struct         r/w      U0             u0     1
; IndexBuffer                           UAV  struct         r/w      U1             u1     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.RWStructuredBuffer<MeshVertex>" = type { %struct.MeshVertex }
%struct.MeshVertex = type { <3 x float>, <3 x float>, <2 x float>, <4 x float> }
%"class.RWStructuredBuffer<unsigned int>" = type { i32 }
%ComputeParams = type { i32, i32, float, float, <3 x float>, float, <3 x float>, float, <3 x float>, float, <2 x float>, <2 x float>, i32, float, float, i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %5 = call i32 @dx.op.threadId.i32(i32 93, i32 1)  ; ThreadId(component)
  %6 = or i32 %5, %4
  %7 = icmp ugt i32 %6, 63
  br i1 %7, label %486, label %8

; <label>:8                                       ; preds = %0
  %9 = shl i32 %5, 6
  %10 = add i32 %9, %4
  %11 = uitofp i32 %4 to float
  %12 = uitofp i32 %5 to float
  %13 = fmul fast float %11, 0x3F90410420000000
  %14 = fmul fast float %12, 0x3F90410420000000
  %15 = fmul fast float %11, 0x3FF9659680000000
  %16 = fadd fast float %15, -5.000000e+01
  %17 = fmul fast float %12, 0x3FF9659680000000
  %18 = fadd fast float %17, -5.000000e+01
  %19 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %20 = extractvalue %dx.types.CBufRet.f32 %19, 3
  %21 = fmul fast float %20, 0x3FB99999A0000000
  %22 = fadd fast float %21, -5.000000e-01
  %23 = fadd fast float %22, %13
  %24 = fadd fast float %22, %14
  %25 = call float @dx.op.unary.f32(i32 27, float %23)  ; Round_ni(value)
  %26 = call float @dx.op.unary.f32(i32 27, float %21)  ; Round_ni(value)
  %27 = call float @dx.op.unary.f32(i32 27, float %24)  ; Round_ni(value)
  %28 = call float @dx.op.unary.f32(i32 22, float %23)  ; Frc(value)
  %29 = call float @dx.op.unary.f32(i32 22, float %21)  ; Frc(value)
  %30 = call float @dx.op.unary.f32(i32 22, float %24)  ; Frc(value)
  %31 = fmul fast float %28, %28
  %32 = fmul fast float %29, %29
  %33 = fmul fast float %30, %30
  %34 = fmul fast float %28, 2.000000e+00
  %35 = fmul fast float %29, 2.000000e+00
  %36 = fmul fast float %30, 2.000000e+00
  %37 = fsub fast float 3.000000e+00, %34
  %38 = fsub fast float 3.000000e+00, %35
  %39 = fsub fast float 3.000000e+00, %36
  %40 = fmul fast float %31, %37
  %41 = fmul fast float %32, %38
  %42 = fmul fast float %33, %39
  %43 = fmul fast float %26, 5.700000e+01
  %44 = fadd fast float %43, %25
  %45 = fmul fast float %27, 1.130000e+02
  %46 = fadd fast float %44, %45
  %47 = fadd fast float %46, 1.710000e+02
  %48 = call float @dx.op.unary.f32(i32 13, float %47)  ; Sin(value)
  %49 = fmul fast float %48, 0x40E55DD180000000
  %50 = call float @dx.op.unary.f32(i32 22, float %49)  ; Frc(value)
  %51 = fadd fast float %46, 1.700000e+02
  %52 = call float @dx.op.unary.f32(i32 13, float %51)  ; Sin(value)
  %53 = fmul fast float %52, 0x40E55DD180000000
  %54 = call float @dx.op.unary.f32(i32 22, float %53)  ; Frc(value)
  %55 = fsub fast float %50, %54
  %56 = fmul fast float %55, %40
  %57 = fadd fast float %56, %54
  %58 = fadd fast float %46, 1.140000e+02
  %59 = call float @dx.op.unary.f32(i32 13, float %58)  ; Sin(value)
  %60 = fmul fast float %59, 0x40E55DD180000000
  %61 = call float @dx.op.unary.f32(i32 22, float %60)  ; Frc(value)
  %62 = fadd fast float %46, 1.130000e+02
  %63 = call float @dx.op.unary.f32(i32 13, float %62)  ; Sin(value)
  %64 = fmul fast float %63, 0x40E55DD180000000
  %65 = call float @dx.op.unary.f32(i32 22, float %64)  ; Frc(value)
  %66 = fsub fast float %61, %65
  %67 = fmul fast float %66, %40
  %68 = fadd fast float %67, %65
  %69 = fsub fast float %57, %68
  %70 = fmul fast float %69, %41
  %71 = fadd fast float %70, %68
  %72 = fadd fast float %46, 5.800000e+01
  %73 = call float @dx.op.unary.f32(i32 13, float %72)  ; Sin(value)
  %74 = fmul fast float %73, 0x40E55DD180000000
  %75 = call float @dx.op.unary.f32(i32 22, float %74)  ; Frc(value)
  %76 = fadd fast float %46, 5.700000e+01
  %77 = call float @dx.op.unary.f32(i32 13, float %76)  ; Sin(value)
  %78 = fmul fast float %77, 0x40E55DD180000000
  %79 = call float @dx.op.unary.f32(i32 22, float %78)  ; Frc(value)
  %80 = fsub fast float %75, %79
  %81 = fmul fast float %80, %40
  %82 = fadd fast float %81, %79
  %83 = fadd fast float %46, 1.000000e+00
  %84 = call float @dx.op.unary.f32(i32 13, float %83)  ; Sin(value)
  %85 = fmul fast float %84, 0x40E55DD180000000
  %86 = call float @dx.op.unary.f32(i32 22, float %85)  ; Frc(value)
  %87 = call float @dx.op.unary.f32(i32 13, float %46)  ; Sin(value)
  %88 = fmul fast float %87, 0x40E55DD180000000
  %89 = call float @dx.op.unary.f32(i32 22, float %88)  ; Frc(value)
  %90 = fsub fast float %86, %89
  %91 = fmul fast float %90, %40
  %92 = fadd fast float %91, %89
  %93 = fsub fast float %82, %92
  %94 = fmul fast float %93, %41
  %95 = fadd fast float %94, %92
  %96 = fsub fast float %71, %95
  %97 = fmul fast float %42, %96
  %98 = fadd fast float %97, %95
  %99 = fmul fast float %98, 1.000000e+01
  %100 = fmul fast float %11, 0x3FB4514540000000
  %101 = fmul fast float %12, 0x3FB4514540000000
  %102 = fmul fast float %20, 0x3FA99999A0000000
  %103 = fadd fast float %102, -2.500000e+00
  %104 = fadd fast float %103, %100
  %105 = fadd fast float %103, %101
  %106 = call float @dx.op.unary.f32(i32 27, float %104)  ; Round_ni(value)
  %107 = call float @dx.op.unary.f32(i32 27, float %102)  ; Round_ni(value)
  %108 = call float @dx.op.unary.f32(i32 27, float %105)  ; Round_ni(value)
  %109 = call float @dx.op.unary.f32(i32 22, float %104)  ; Frc(value)
  %110 = call float @dx.op.unary.f32(i32 22, float %102)  ; Frc(value)
  %111 = call float @dx.op.unary.f32(i32 22, float %105)  ; Frc(value)
  %112 = fmul fast float %109, %109
  %113 = fmul fast float %110, %110
  %114 = fmul fast float %111, %111
  %115 = fmul fast float %109, 2.000000e+00
  %116 = fmul fast float %110, 2.000000e+00
  %117 = fmul fast float %111, 2.000000e+00
  %118 = fsub fast float 3.000000e+00, %115
  %119 = fsub fast float 3.000000e+00, %116
  %120 = fsub fast float 3.000000e+00, %117
  %121 = fmul fast float %112, %118
  %122 = fmul fast float %113, %119
  %123 = fmul fast float %114, %120
  %124 = fmul fast float %107, 5.700000e+01
  %125 = fadd fast float %124, %106
  %126 = fmul fast float %108, 1.130000e+02
  %127 = fadd fast float %125, %126
  %128 = fadd fast float %127, 1.710000e+02
  %129 = call float @dx.op.unary.f32(i32 13, float %128)  ; Sin(value)
  %130 = fmul fast float %129, 0x40E55DD180000000
  %131 = call float @dx.op.unary.f32(i32 22, float %130)  ; Frc(value)
  %132 = fadd fast float %127, 1.700000e+02
  %133 = call float @dx.op.unary.f32(i32 13, float %132)  ; Sin(value)
  %134 = fmul fast float %133, 0x40E55DD180000000
  %135 = call float @dx.op.unary.f32(i32 22, float %134)  ; Frc(value)
  %136 = fsub fast float %131, %135
  %137 = fmul fast float %136, %121
  %138 = fadd fast float %137, %135
  %139 = fadd fast float %127, 1.140000e+02
  %140 = call float @dx.op.unary.f32(i32 13, float %139)  ; Sin(value)
  %141 = fmul fast float %140, 0x40E55DD180000000
  %142 = call float @dx.op.unary.f32(i32 22, float %141)  ; Frc(value)
  %143 = fadd fast float %127, 1.130000e+02
  %144 = call float @dx.op.unary.f32(i32 13, float %143)  ; Sin(value)
  %145 = fmul fast float %144, 0x40E55DD180000000
  %146 = call float @dx.op.unary.f32(i32 22, float %145)  ; Frc(value)
  %147 = fsub fast float %142, %146
  %148 = fmul fast float %147, %121
  %149 = fadd fast float %148, %146
  %150 = fsub fast float %138, %149
  %151 = fmul fast float %150, %122
  %152 = fadd fast float %151, %149
  %153 = fadd fast float %127, 5.800000e+01
  %154 = call float @dx.op.unary.f32(i32 13, float %153)  ; Sin(value)
  %155 = fmul fast float %154, 0x40E55DD180000000
  %156 = call float @dx.op.unary.f32(i32 22, float %155)  ; Frc(value)
  %157 = fadd fast float %127, 5.700000e+01
  %158 = call float @dx.op.unary.f32(i32 13, float %157)  ; Sin(value)
  %159 = fmul fast float %158, 0x40E55DD180000000
  %160 = call float @dx.op.unary.f32(i32 22, float %159)  ; Frc(value)
  %161 = fsub fast float %156, %160
  %162 = fmul fast float %161, %121
  %163 = fadd fast float %162, %160
  %164 = fadd fast float %127, 1.000000e+00
  %165 = call float @dx.op.unary.f32(i32 13, float %164)  ; Sin(value)
  %166 = fmul fast float %165, 0x40E55DD180000000
  %167 = call float @dx.op.unary.f32(i32 22, float %166)  ; Frc(value)
  %168 = call float @dx.op.unary.f32(i32 13, float %127)  ; Sin(value)
  %169 = fmul fast float %168, 0x40E55DD180000000
  %170 = call float @dx.op.unary.f32(i32 22, float %169)  ; Frc(value)
  %171 = fsub fast float %167, %170
  %172 = fmul fast float %171, %121
  %173 = fadd fast float %172, %170
  %174 = fsub fast float %163, %173
  %175 = fmul fast float %174, %122
  %176 = fadd fast float %175, %173
  %177 = fsub fast float %152, %176
  %178 = fmul fast float %123, %177
  %179 = fadd fast float %178, %176
  %180 = fmul fast float %179, 2.000000e+00
  %181 = fadd fast float %180, %99
  %182 = add i32 %4, -1
  %183 = icmp ult i32 %182, 62
  %184 = add i32 %5, -1
  %185 = icmp ult i32 %184, 62
  %186 = and i1 %183, %185
  br i1 %186, label %187, label %467

; <label>:187                                     ; preds = %8
  %188 = fmul fast float %181, 0x3F847AE140000000
  %189 = fadd fast float %21, %13
  %190 = fadd fast float %189, 0xBFE051EB80000000
  %191 = fadd fast float %188, %21
  %192 = fadd fast float %21, %14
  %193 = fadd fast float %192, -5.000000e-01
  %194 = call float @dx.op.unary.f32(i32 27, float %190)  ; Round_ni(value)
  %195 = call float @dx.op.unary.f32(i32 27, float %191)  ; Round_ni(value)
  %196 = call float @dx.op.unary.f32(i32 27, float %193)  ; Round_ni(value)
  %197 = call float @dx.op.unary.f32(i32 22, float %190)  ; Frc(value)
  %198 = call float @dx.op.unary.f32(i32 22, float %191)  ; Frc(value)
  %199 = call float @dx.op.unary.f32(i32 22, float %193)  ; Frc(value)
  %200 = fmul fast float %197, %197
  %201 = fmul fast float %198, %198
  %202 = fmul fast float %199, %199
  %203 = fmul fast float %197, 2.000000e+00
  %204 = fmul fast float %198, 2.000000e+00
  %205 = fmul fast float %199, 2.000000e+00
  %206 = fsub fast float 3.000000e+00, %203
  %207 = fsub fast float 3.000000e+00, %204
  %208 = fsub fast float 3.000000e+00, %205
  %209 = fmul fast float %200, %206
  %210 = fmul fast float %201, %207
  %211 = fmul fast float %202, %208
  %212 = fmul fast float %195, 5.700000e+01
  %213 = fmul fast float %196, 1.130000e+02
  %214 = fadd fast float %213, %212
  %215 = fadd fast float %214, %194
  %216 = fadd fast float %215, 1.710000e+02
  %217 = call float @dx.op.unary.f32(i32 13, float %216)  ; Sin(value)
  %218 = fmul fast float %217, 0x40E55DD180000000
  %219 = call float @dx.op.unary.f32(i32 22, float %218)  ; Frc(value)
  %220 = fadd fast float %215, 1.700000e+02
  %221 = call float @dx.op.unary.f32(i32 13, float %220)  ; Sin(value)
  %222 = fmul fast float %221, 0x40E55DD180000000
  %223 = call float @dx.op.unary.f32(i32 22, float %222)  ; Frc(value)
  %224 = fsub fast float %219, %223
  %225 = fmul fast float %224, %209
  %226 = fadd fast float %225, %223
  %227 = fadd fast float %215, 1.140000e+02
  %228 = call float @dx.op.unary.f32(i32 13, float %227)  ; Sin(value)
  %229 = fmul fast float %228, 0x40E55DD180000000
  %230 = call float @dx.op.unary.f32(i32 22, float %229)  ; Frc(value)
  %231 = fadd fast float %215, 1.130000e+02
  %232 = call float @dx.op.unary.f32(i32 13, float %231)  ; Sin(value)
  %233 = fmul fast float %232, 0x40E55DD180000000
  %234 = call float @dx.op.unary.f32(i32 22, float %233)  ; Frc(value)
  %235 = fsub fast float %230, %234
  %236 = fmul fast float %235, %209
  %237 = fadd fast float %236, %234
  %238 = fsub fast float %226, %237
  %239 = fmul fast float %238, %210
  %240 = fadd fast float %239, %237
  %241 = fadd fast float %215, 5.800000e+01
  %242 = call float @dx.op.unary.f32(i32 13, float %241)  ; Sin(value)
  %243 = fmul fast float %242, 0x40E55DD180000000
  %244 = call float @dx.op.unary.f32(i32 22, float %243)  ; Frc(value)
  %245 = fadd fast float %215, 5.700000e+01
  %246 = call float @dx.op.unary.f32(i32 13, float %245)  ; Sin(value)
  %247 = fmul fast float %246, 0x40E55DD180000000
  %248 = call float @dx.op.unary.f32(i32 22, float %247)  ; Frc(value)
  %249 = fsub fast float %244, %248
  %250 = fmul fast float %249, %209
  %251 = fadd fast float %250, %248
  %252 = fadd fast float %215, 1.000000e+00
  %253 = call float @dx.op.unary.f32(i32 13, float %252)  ; Sin(value)
  %254 = fmul fast float %253, 0x40E55DD180000000
  %255 = call float @dx.op.unary.f32(i32 22, float %254)  ; Frc(value)
  %256 = call float @dx.op.unary.f32(i32 13, float %215)  ; Sin(value)
  %257 = fmul fast float %256, 0x40E55DD180000000
  %258 = call float @dx.op.unary.f32(i32 22, float %257)  ; Frc(value)
  %259 = fsub fast float %255, %258
  %260 = fmul fast float %259, %209
  %261 = fadd fast float %260, %258
  %262 = fsub fast float %251, %261
  %263 = fmul fast float %262, %210
  %264 = fadd fast float %263, %261
  %265 = fsub fast float %240, %264
  %266 = fmul fast float %265, %211
  %267 = fadd fast float %189, 0xBFDF5C28E0000000
  %268 = call float @dx.op.unary.f32(i32 27, float %267)  ; Round_ni(value)
  %269 = call float @dx.op.unary.f32(i32 22, float %267)  ; Frc(value)
  %270 = fmul fast float %269, %269
  %271 = fmul fast float %269, 2.000000e+00
  %272 = fsub fast float 3.000000e+00, %271
  %273 = fmul fast float %270, %272
  %274 = fadd fast float %214, %268
  %275 = fadd fast float %274, 1.710000e+02
  %276 = call float @dx.op.unary.f32(i32 13, float %275)  ; Sin(value)
  %277 = fmul fast float %276, 0x40E55DD180000000
  %278 = call float @dx.op.unary.f32(i32 22, float %277)  ; Frc(value)
  %279 = fadd fast float %274, 1.700000e+02
  %280 = call float @dx.op.unary.f32(i32 13, float %279)  ; Sin(value)
  %281 = fmul fast float %280, 0x40E55DD180000000
  %282 = call float @dx.op.unary.f32(i32 22, float %281)  ; Frc(value)
  %283 = fsub fast float %278, %282
  %284 = fmul fast float %283, %273
  %285 = fadd fast float %284, %282
  %286 = fadd fast float %274, 1.140000e+02
  %287 = call float @dx.op.unary.f32(i32 13, float %286)  ; Sin(value)
  %288 = fmul fast float %287, 0x40E55DD180000000
  %289 = call float @dx.op.unary.f32(i32 22, float %288)  ; Frc(value)
  %290 = fadd fast float %274, 1.130000e+02
  %291 = call float @dx.op.unary.f32(i32 13, float %290)  ; Sin(value)
  %292 = fmul fast float %291, 0x40E55DD180000000
  %293 = call float @dx.op.unary.f32(i32 22, float %292)  ; Frc(value)
  %294 = fsub fast float %289, %293
  %295 = fmul fast float %294, %273
  %296 = fadd fast float %295, %293
  %297 = fsub fast float %285, %296
  %298 = fmul fast float %297, %210
  %299 = fadd fast float %298, %296
  %300 = fadd fast float %274, 5.800000e+01
  %301 = call float @dx.op.unary.f32(i32 13, float %300)  ; Sin(value)
  %302 = fmul fast float %301, 0x40E55DD180000000
  %303 = call float @dx.op.unary.f32(i32 22, float %302)  ; Frc(value)
  %304 = fadd fast float %274, 5.700000e+01
  %305 = call float @dx.op.unary.f32(i32 13, float %304)  ; Sin(value)
  %306 = fmul fast float %305, 0x40E55DD180000000
  %307 = call float @dx.op.unary.f32(i32 22, float %306)  ; Frc(value)
  %308 = fsub fast float %303, %307
  %309 = fmul fast float %308, %273
  %310 = fadd fast float %309, %307
  %311 = fadd fast float %274, 1.000000e+00
  %312 = call float @dx.op.unary.f32(i32 13, float %311)  ; Sin(value)
  %313 = fmul fast float %312, 0x40E55DD180000000
  %314 = call float @dx.op.unary.f32(i32 22, float %313)  ; Frc(value)
  %315 = call float @dx.op.unary.f32(i32 13, float %274)  ; Sin(value)
  %316 = fmul fast float %315, 0x40E55DD180000000
  %317 = call float @dx.op.unary.f32(i32 22, float %316)  ; Frc(value)
  %318 = fsub fast float %314, %317
  %319 = fmul fast float %318, %273
  %320 = fadd fast float %319, %317
  %321 = fsub fast float %310, %320
  %322 = fmul fast float %321, %210
  %323 = fadd fast float %322, %320
  %324 = fsub fast float %299, %323
  %325 = fmul fast float %324, %211
  %326 = fadd fast float %189, -5.000000e-01
  %327 = fadd fast float %192, 0xBFE051EB80000000
  %328 = call float @dx.op.unary.f32(i32 27, float %326)  ; Round_ni(value)
  %329 = call float @dx.op.unary.f32(i32 27, float %327)  ; Round_ni(value)
  %330 = call float @dx.op.unary.f32(i32 22, float %326)  ; Frc(value)
  %331 = call float @dx.op.unary.f32(i32 22, float %327)  ; Frc(value)
  %332 = fmul fast float %330, %330
  %333 = fmul fast float %331, %331
  %334 = fmul fast float %330, 2.000000e+00
  %335 = fmul fast float %331, 2.000000e+00
  %336 = fsub fast float 3.000000e+00, %334
  %337 = fsub fast float 3.000000e+00, %335
  %338 = fmul fast float %332, %336
  %339 = fmul fast float %333, %337
  %340 = fadd fast float %328, %212
  %341 = fmul fast float %329, 1.130000e+02
  %342 = fadd fast float %341, %340
  %343 = fadd fast float %342, 1.710000e+02
  %344 = call float @dx.op.unary.f32(i32 13, float %343)  ; Sin(value)
  %345 = fmul fast float %344, 0x40E55DD180000000
  %346 = call float @dx.op.unary.f32(i32 22, float %345)  ; Frc(value)
  %347 = fadd fast float %342, 1.700000e+02
  %348 = call float @dx.op.unary.f32(i32 13, float %347)  ; Sin(value)
  %349 = fmul fast float %348, 0x40E55DD180000000
  %350 = call float @dx.op.unary.f32(i32 22, float %349)  ; Frc(value)
  %351 = fsub fast float %346, %350
  %352 = fmul fast float %351, %338
  %353 = fadd fast float %352, %350
  %354 = fadd fast float %342, 1.140000e+02
  %355 = call float @dx.op.unary.f32(i32 13, float %354)  ; Sin(value)
  %356 = fmul fast float %355, 0x40E55DD180000000
  %357 = call float @dx.op.unary.f32(i32 22, float %356)  ; Frc(value)
  %358 = fadd fast float %342, 1.130000e+02
  %359 = call float @dx.op.unary.f32(i32 13, float %358)  ; Sin(value)
  %360 = fmul fast float %359, 0x40E55DD180000000
  %361 = call float @dx.op.unary.f32(i32 22, float %360)  ; Frc(value)
  %362 = fsub fast float %357, %361
  %363 = fmul fast float %362, %338
  %364 = fadd fast float %363, %361
  %365 = fsub fast float %353, %364
  %366 = fmul fast float %365, %210
  %367 = fadd fast float %366, %364
  %368 = fadd fast float %342, 5.800000e+01
  %369 = call float @dx.op.unary.f32(i32 13, float %368)  ; Sin(value)
  %370 = fmul fast float %369, 0x40E55DD180000000
  %371 = call float @dx.op.unary.f32(i32 22, float %370)  ; Frc(value)
  %372 = fadd fast float %342, 5.700000e+01
  %373 = call float @dx.op.unary.f32(i32 13, float %372)  ; Sin(value)
  %374 = fmul fast float %373, 0x40E55DD180000000
  %375 = call float @dx.op.unary.f32(i32 22, float %374)  ; Frc(value)
  %376 = fsub fast float %371, %375
  %377 = fmul fast float %376, %338
  %378 = fadd fast float %377, %375
  %379 = fadd fast float %342, 1.000000e+00
  %380 = call float @dx.op.unary.f32(i32 13, float %379)  ; Sin(value)
  %381 = fmul fast float %380, 0x40E55DD180000000
  %382 = call float @dx.op.unary.f32(i32 22, float %381)  ; Frc(value)
  %383 = call float @dx.op.unary.f32(i32 13, float %342)  ; Sin(value)
  %384 = fmul fast float %383, 0x40E55DD180000000
  %385 = call float @dx.op.unary.f32(i32 22, float %384)  ; Frc(value)
  %386 = fsub fast float %382, %385
  %387 = fmul fast float %386, %338
  %388 = fadd fast float %387, %385
  %389 = fsub fast float %378, %388
  %390 = fmul fast float %389, %210
  %391 = fadd fast float %390, %388
  %392 = fsub fast float %367, %391
  %393 = fmul fast float %339, %392
  %394 = fadd fast float %192, 0xBFDF5C28E0000000
  %395 = call float @dx.op.unary.f32(i32 27, float %394)  ; Round_ni(value)
  %396 = call float @dx.op.unary.f32(i32 22, float %394)  ; Frc(value)
  %397 = fmul fast float %396, %396
  %398 = fmul fast float %396, 2.000000e+00
  %399 = fsub fast float 3.000000e+00, %398
  %400 = fmul fast float %397, %399
  %401 = fmul fast float %395, 1.130000e+02
  %402 = fadd fast float %401, %340
  %403 = fadd fast float %402, 1.710000e+02
  %404 = call float @dx.op.unary.f32(i32 13, float %403)  ; Sin(value)
  %405 = fmul fast float %404, 0x40E55DD180000000
  %406 = call float @dx.op.unary.f32(i32 22, float %405)  ; Frc(value)
  %407 = fadd fast float %402, 1.700000e+02
  %408 = call float @dx.op.unary.f32(i32 13, float %407)  ; Sin(value)
  %409 = fmul fast float %408, 0x40E55DD180000000
  %410 = call float @dx.op.unary.f32(i32 22, float %409)  ; Frc(value)
  %411 = fsub fast float %406, %410
  %412 = fmul fast float %411, %338
  %413 = fadd fast float %412, %410
  %414 = fadd fast float %402, 1.140000e+02
  %415 = call float @dx.op.unary.f32(i32 13, float %414)  ; Sin(value)
  %416 = fmul fast float %415, 0x40E55DD180000000
  %417 = call float @dx.op.unary.f32(i32 22, float %416)  ; Frc(value)
  %418 = fadd fast float %402, 1.130000e+02
  %419 = call float @dx.op.unary.f32(i32 13, float %418)  ; Sin(value)
  %420 = fmul fast float %419, 0x40E55DD180000000
  %421 = call float @dx.op.unary.f32(i32 22, float %420)  ; Frc(value)
  %422 = fsub fast float %417, %421
  %423 = fmul fast float %422, %338
  %424 = fadd fast float %423, %421
  %425 = fsub fast float %413, %424
  %426 = fmul fast float %425, %210
  %427 = fadd fast float %426, %424
  %428 = fadd fast float %402, 5.800000e+01
  %429 = call float @dx.op.unary.f32(i32 13, float %428)  ; Sin(value)
  %430 = fmul fast float %429, 0x40E55DD180000000
  %431 = call float @dx.op.unary.f32(i32 22, float %430)  ; Frc(value)
  %432 = fadd fast float %402, 5.700000e+01
  %433 = call float @dx.op.unary.f32(i32 13, float %432)  ; Sin(value)
  %434 = fmul fast float %433, 0x40E55DD180000000
  %435 = call float @dx.op.unary.f32(i32 22, float %434)  ; Frc(value)
  %436 = fsub fast float %431, %435
  %437 = fmul fast float %436, %338
  %438 = fadd fast float %437, %435
  %439 = fadd fast float %402, 1.000000e+00
  %440 = call float @dx.op.unary.f32(i32 13, float %439)  ; Sin(value)
  %441 = fmul fast float %440, 0x40E55DD180000000
  %442 = call float @dx.op.unary.f32(i32 22, float %441)  ; Frc(value)
  %443 = call float @dx.op.unary.f32(i32 13, float %402)  ; Sin(value)
  %444 = fmul fast float %443, 0x40E55DD180000000
  %445 = call float @dx.op.unary.f32(i32 22, float %444)  ; Frc(value)
  %446 = fsub fast float %442, %445
  %447 = fmul fast float %446, %338
  %448 = fadd fast float %447, %445
  %449 = fsub fast float %438, %448
  %450 = fmul fast float %449, %210
  %451 = fadd fast float %450, %448
  %452 = fsub fast float %427, %451
  %453 = fmul fast float %400, %452
  %454 = fadd fast float %266, %264
  %455 = fsub fast float %454, %323
  %456 = fsub fast float %455, %325
  %457 = fmul fast float %456, 1.000000e+01
  %458 = fadd fast float %393, %391
  %459 = fsub fast float %458, %451
  %460 = fsub fast float %459, %453
  %461 = fmul fast float %460, 1.000000e+01
  %462 = call float @dx.op.dot3.f32(i32 55, float %457, float 2.000000e+00, float %461, float %457, float 2.000000e+00, float %461)  ; Dot3(ax,ay,az,bx,by,bz)
  %463 = call float @dx.op.unary.f32(i32 25, float %462)  ; Rsqrt(value)
  %464 = fmul fast float %463, %457
  %465 = fmul fast float %463, 2.000000e+00
  %466 = fmul fast float %461, %463
  br label %467

; <label>:467                                     ; preds = %187, %8
  %468 = phi float [ %464, %187 ], [ 0.000000e+00, %8 ]
  %469 = phi float [ %465, %187 ], [ 1.000000e+00, %8 ]
  %470 = phi float [ %466, %187 ], [ 0.000000e+00, %8 ]
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %10, i32 0, float %16, float %181, float %18, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %10, i32 12, float %468, float %469, float %470, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %10, i32 24, float %13, float %14, float undef, float undef, i8 3)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %10, i32 32, float %13, float %14, float 5.000000e-01, float 1.000000e+00, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %471 = icmp ult i32 %4, 63
  %472 = icmp ult i32 %5, 63
  %473 = and i1 %471, %472
  br i1 %473, label %474, label %486

; <label>:474                                     ; preds = %467
  %475 = mul i32 %5, 63
  %476 = add i32 %475, %4
  %477 = mul i32 %476, 6
  %478 = add i32 %10, 1
  %479 = add i32 %10, 64
  %480 = add i32 %10, 65
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %477, i32 0, i32 %10, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %481 = or i32 %477, 1
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %481, i32 0, i32 %479, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %482 = add i32 %477, 2
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %482, i32 0, i32 %478, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %483 = add i32 %477, 3
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %483, i32 0, i32 %478, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %484 = add i32 %477, 4
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %484, i32 0, i32 %479, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %485 = add i32 %477, 5
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %485, i32 0, i32 %480, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  br label %486

; <label>:486                                     ; preds = %474, %467, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #1

; Function Attrs: nounwind
declare void @dx.op.bufferStore.f32(i32, %dx.types.Handle, i32, i32, float, float, float, float, i8) #2

; Function Attrs: nounwind
declare void @dx.op.bufferStore.i32(i32, %dx.types.Handle, i32, i32, i32, i32, i32, i32, i8) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind readonly }
attributes #2 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!12}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{null, !5, !10, null}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.RWStructuredBuffer<MeshVertex>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i1 false, i1 false, i1 false, !7}
!7 = !{i32 1, i32 48}
!8 = !{i32 1, %"class.RWStructuredBuffer<unsigned int>"* undef, !"", i32 0, i32 1, i32 1, i32 12, i1 false, i1 false, i1 false, !9}
!9 = !{i32 1, i32 4}
!10 = !{!11}
!11 = !{i32 0, %ComputeParams* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!12 = !{void ()* @main, !"main", null, !4, !13}
!13 = !{i32 0, i64 16, i32 4, !14}
!14 = !{i32 8, i32 8, i32 1}
