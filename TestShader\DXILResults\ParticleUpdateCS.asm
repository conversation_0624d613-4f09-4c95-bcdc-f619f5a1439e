;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 3e1230046f5aa002a477dd1db1a9000e
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(64,1,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer ComputeParams
; {
;
;   struct ComputeParams
;   {
;
;       uint ParticleCount;                           ; Offset:    0
;       uint MaxParticles;                            ; Offset:    4
;       float DeltaTime;                              ; Offset:    8
;       float Time;                                   ; Offset:   12
;       float3 Gravity;                               ; Offset:   16
;       float Damping;                                ; Offset:   28
;       float3 EmitterPosition;                       ; Offset:   32
;       float EmissionRate;                           ; Offset:   44
;       float3 EmitterDirection;                      ; Offset:   48
;       float EmissionSpeed;                          ; Offset:   60
;       float2 LifetimeRange;                         ; Offset:   64
;       float2 SizeRange;                             ; Offset:   72
;       uint FrameCount;                              ; Offset:   80
;       float NoiseScale;                             ; Offset:   84
;       float NoiseStrength;                          ; Offset:   88
;       uint _padding;                                ; Offset:   92
;   
;   } ComputeParams;                                  ; Offset:    0 Size:    96
;
; }
;
; Resource bind info for ParticleBuffer
; {
;
;   struct struct.Particle
;   {
;
;       float3 Position;                              ; Offset:    0
;       float Life;                                   ; Offset:   12
;       float3 Velocity;                              ; Offset:   16
;       float Size;                                   ; Offset:   28
;       float4 Color;                                 ; Offset:   32
;       float3 Acceleration;                          ; Offset:   48
;       float Mass;                                   ; Offset:   60
;       uint Type;                                    ; Offset:   64
;       float3 _padding;                              ; Offset:   68
;   
;   } $Element;                                       ; Offset:    0 Size:    80
;
; }
;
; Resource bind info for DeadParticleBuffer
; {
;
;   struct struct.Particle
;   {
;
;       float3 Position;                              ; Offset:    0
;       float Life;                                   ; Offset:   12
;       float3 Velocity;                              ; Offset:   16
;       float Size;                                   ; Offset:   28
;       float4 Color;                                 ; Offset:   32
;       float3 Acceleration;                          ; Offset:   48
;       float Mass;                                   ; Offset:   60
;       uint Type;                                    ; Offset:   64
;       float3 _padding;                              ; Offset:   68
;   
;   } $Element;                                       ; Offset:    0 Size:    80
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; ComputeParams                     cbuffer      NA          NA     CB0            cb0     1
; ParticleBuffer                        UAV  struct         r/w      U0             u0     1
; DeadParticleBuffer                    UAV  struct     r/w+cnt      U1             u1     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.ResRet.i32 = type { i32, i32, i32, i32, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%"class.RWStructuredBuffer<Particle>" = type { %struct.Particle }
%struct.Particle = type { <3 x float>, float, <3 x float>, float, <4 x float>, <3 x float>, float, i32, <3 x float> }
%"class.AppendStructuredBuffer<Particle>" = type { %struct.Particle }
%ComputeParams = type { i32, i32, float, float, <3 x float>, float, <3 x float>, float, <3 x float>, float, <2 x float>, <2 x float>, i32, float, float, i32 }

@"\01?SharedDistances@@3PAMA" = external addrspace(3) global [64 x float], align 4
@"\01?SharedIndices@@3PAIA" = external addrspace(3) global [64 x i32], align 4
@"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim" = addrspace(3) global [192 x float] undef, align 4

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %5 = call i32 @dx.op.threadIdInGroup.i32(i32 95, i32 0)  ; ThreadIdInGroup(component)
  %6 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %7 = extractvalue %dx.types.CBufRet.i32 %6, 0
  %8 = icmp ult i32 %4, %7
  br i1 %8, label %9, label %611

; <label>:9                                       ; preds = %0
  %10 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 0)  ; BufferLoad(srv,index,wot)
  %11 = extractvalue %dx.types.ResRet.f32 %10, 0
  %12 = extractvalue %dx.types.ResRet.f32 %10, 1
  %13 = extractvalue %dx.types.ResRet.f32 %10, 2
  %14 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 12)  ; BufferLoad(srv,index,wot)
  %15 = extractvalue %dx.types.ResRet.f32 %14, 0
  %16 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 16)  ; BufferLoad(srv,index,wot)
  %17 = extractvalue %dx.types.ResRet.f32 %16, 0
  %18 = extractvalue %dx.types.ResRet.f32 %16, 1
  %19 = extractvalue %dx.types.ResRet.f32 %16, 2
  %20 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 28)  ; BufferLoad(srv,index,wot)
  %21 = extractvalue %dx.types.ResRet.f32 %20, 0
  %22 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 32)  ; BufferLoad(srv,index,wot)
  %23 = extractvalue %dx.types.ResRet.f32 %22, 0
  %24 = extractvalue %dx.types.ResRet.f32 %22, 1
  %25 = extractvalue %dx.types.ResRet.f32 %22, 2
  %26 = extractvalue %dx.types.ResRet.f32 %22, 3
  %27 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 48)  ; BufferLoad(srv,index,wot)
  %28 = extractvalue %dx.types.ResRet.f32 %27, 0
  %29 = extractvalue %dx.types.ResRet.f32 %27, 1
  %30 = extractvalue %dx.types.ResRet.f32 %27, 2
  %31 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 60)  ; BufferLoad(srv,index,wot)
  %32 = extractvalue %dx.types.ResRet.f32 %31, 0
  %33 = call %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32 68, %dx.types.Handle %2, i32 %4, i32 64)  ; BufferLoad(srv,index,wot)
  %34 = extractvalue %dx.types.ResRet.i32 %33, 0
  %35 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %2, i32 %4, i32 68)  ; BufferLoad(srv,index,wot)
  %36 = extractvalue %dx.types.ResRet.f32 %35, 0
  %37 = extractvalue %dx.types.ResRet.f32 %35, 1
  %38 = extractvalue %dx.types.ResRet.f32 %35, 2
  %39 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %40 = extractvalue %dx.types.CBufRet.f32 %39, 2
  %41 = fsub fast float %15, %40
  %42 = fcmp fast ugt float %41, 0.000000e+00
  br i1 %42, label %45, label %43

; <label>:43                                      ; preds = %9
  %44 = call i32 @dx.op.bufferUpdateCounter(i32 70, %dx.types.Handle %1, i8 1)  ; BufferUpdateCounter(uav,inc)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 0, float %11, float %12, float %13, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 12, float %41, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 16, float %17, float %18, float %19, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 28, float %21, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 32, float %23, float %24, float %25, float %26, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 48, float %28, float %29, float %30, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 60, float %32, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %1, i32 %44, i32 64, i32 %34, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %1, i32 %44, i32 68, float %36, float %37, float %38, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 0, float %11, float %12, float %13, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 12, float %41, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 16, float %17, float %18, float %19, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 28, float %21, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 32, float %23, float %24, float %25, float %26, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 48, float %28, float %29, float %30, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 60, float %32, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %2, i32 %4, i32 64, i32 0, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 68, float %36, float %37, float %38, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  br label %611

; <label>:45                                      ; preds = %9
  %46 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %47 = extractvalue %dx.types.CBufRet.f32 %46, 0
  %48 = extractvalue %dx.types.CBufRet.f32 %46, 1
  %49 = extractvalue %dx.types.CBufRet.f32 %46, 2
  %50 = fmul fast float %47, %32
  %51 = fmul fast float %48, %32
  %52 = fmul fast float %49, %32
  %53 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %54 = extractvalue %dx.types.CBufRet.f32 %53, 1
  %55 = fmul fast float %54, %11
  %56 = fmul fast float %54, %12
  %57 = fmul fast float %54, %13
  %58 = extractvalue %dx.types.CBufRet.f32 %39, 3
  %59 = fmul fast float %58, 0x3FB99999A0000000
  %60 = fadd fast float %55, %59
  %61 = fadd fast float %56, %59
  %62 = fadd fast float %57, %59
  %63 = fadd fast float %60, 0xBFB99999A0000000
  %64 = call float @dx.op.unary.f32(i32 27, float %63)  ; Round_ni(value)
  %65 = call float @dx.op.unary.f32(i32 27, float %61)  ; Round_ni(value)
  %66 = call float @dx.op.unary.f32(i32 27, float %62)  ; Round_ni(value)
  %67 = call float @dx.op.unary.f32(i32 22, float %63)  ; Frc(value)
  %68 = call float @dx.op.unary.f32(i32 22, float %61)  ; Frc(value)
  %69 = call float @dx.op.unary.f32(i32 22, float %62)  ; Frc(value)
  %70 = fmul fast float %67, %67
  %71 = fmul fast float %68, %68
  %72 = fmul fast float %69, %69
  %73 = fmul fast float %67, 2.000000e+00
  %74 = fmul fast float %68, 2.000000e+00
  %75 = fmul fast float %69, 2.000000e+00
  %76 = fsub fast float 3.000000e+00, %73
  %77 = fsub fast float 3.000000e+00, %74
  %78 = fsub fast float 3.000000e+00, %75
  %79 = fmul fast float %70, %76
  %80 = fmul fast float %71, %77
  %81 = fmul fast float %72, %78
  %82 = fmul fast float %65, 5.700000e+01
  %83 = fmul fast float %66, 1.130000e+02
  %84 = fadd fast float %83, %82
  %85 = fadd fast float %84, %64
  %86 = fadd fast float %85, 1.710000e+02
  %87 = call float @dx.op.unary.f32(i32 13, float %86)  ; Sin(value)
  %88 = fmul fast float %87, 0x40E55DD180000000
  %89 = call float @dx.op.unary.f32(i32 22, float %88)  ; Frc(value)
  %90 = fadd fast float %85, 1.700000e+02
  %91 = call float @dx.op.unary.f32(i32 13, float %90)  ; Sin(value)
  %92 = fmul fast float %91, 0x40E55DD180000000
  %93 = call float @dx.op.unary.f32(i32 22, float %92)  ; Frc(value)
  %94 = fsub fast float %89, %93
  %95 = fmul fast float %94, %79
  %96 = fadd fast float %95, %93
  %97 = fadd fast float %85, 1.140000e+02
  %98 = call float @dx.op.unary.f32(i32 13, float %97)  ; Sin(value)
  %99 = fmul fast float %98, 0x40E55DD180000000
  %100 = call float @dx.op.unary.f32(i32 22, float %99)  ; Frc(value)
  %101 = fadd fast float %85, 1.130000e+02
  %102 = call float @dx.op.unary.f32(i32 13, float %101)  ; Sin(value)
  %103 = fmul fast float %102, 0x40E55DD180000000
  %104 = call float @dx.op.unary.f32(i32 22, float %103)  ; Frc(value)
  %105 = fsub fast float %100, %104
  %106 = fmul fast float %105, %79
  %107 = fadd fast float %106, %104
  %108 = fsub fast float %96, %107
  %109 = fmul fast float %108, %80
  %110 = fadd fast float %109, %107
  %111 = fadd fast float %85, 5.800000e+01
  %112 = call float @dx.op.unary.f32(i32 13, float %111)  ; Sin(value)
  %113 = fmul fast float %112, 0x40E55DD180000000
  %114 = call float @dx.op.unary.f32(i32 22, float %113)  ; Frc(value)
  %115 = fadd fast float %85, 5.700000e+01
  %116 = call float @dx.op.unary.f32(i32 13, float %115)  ; Sin(value)
  %117 = fmul fast float %116, 0x40E55DD180000000
  %118 = call float @dx.op.unary.f32(i32 22, float %117)  ; Frc(value)
  %119 = fsub fast float %114, %118
  %120 = fmul fast float %119, %79
  %121 = fadd fast float %120, %118
  %122 = fadd fast float %85, 1.000000e+00
  %123 = call float @dx.op.unary.f32(i32 13, float %122)  ; Sin(value)
  %124 = fmul fast float %123, 0x40E55DD180000000
  %125 = call float @dx.op.unary.f32(i32 22, float %124)  ; Frc(value)
  %126 = call float @dx.op.unary.f32(i32 13, float %85)  ; Sin(value)
  %127 = fmul fast float %126, 0x40E55DD180000000
  %128 = call float @dx.op.unary.f32(i32 22, float %127)  ; Frc(value)
  %129 = fsub fast float %125, %128
  %130 = fmul fast float %129, %79
  %131 = fadd fast float %130, %128
  %132 = fsub fast float %121, %131
  %133 = fmul fast float %132, %80
  %134 = fadd fast float %133, %131
  %135 = fsub fast float %110, %134
  %136 = fmul fast float %135, %81
  %137 = fadd fast float %136, %134
  %138 = fadd fast float %60, 0x3FB99999A0000000
  %139 = call float @dx.op.unary.f32(i32 27, float %138)  ; Round_ni(value)
  %140 = call float @dx.op.unary.f32(i32 22, float %138)  ; Frc(value)
  %141 = fmul fast float %140, %140
  %142 = fmul fast float %140, 2.000000e+00
  %143 = fsub fast float 3.000000e+00, %142
  %144 = fmul fast float %141, %143
  %145 = fadd fast float %84, %139
  %146 = fadd fast float %145, 1.710000e+02
  %147 = call float @dx.op.unary.f32(i32 13, float %146)  ; Sin(value)
  %148 = fmul fast float %147, 0x40E55DD180000000
  %149 = call float @dx.op.unary.f32(i32 22, float %148)  ; Frc(value)
  %150 = fadd fast float %145, 1.700000e+02
  %151 = call float @dx.op.unary.f32(i32 13, float %150)  ; Sin(value)
  %152 = fmul fast float %151, 0x40E55DD180000000
  %153 = call float @dx.op.unary.f32(i32 22, float %152)  ; Frc(value)
  %154 = fsub fast float %149, %153
  %155 = fmul fast float %154, %144
  %156 = fadd fast float %155, %153
  %157 = fadd fast float %145, 1.140000e+02
  %158 = call float @dx.op.unary.f32(i32 13, float %157)  ; Sin(value)
  %159 = fmul fast float %158, 0x40E55DD180000000
  %160 = call float @dx.op.unary.f32(i32 22, float %159)  ; Frc(value)
  %161 = fadd fast float %145, 1.130000e+02
  %162 = call float @dx.op.unary.f32(i32 13, float %161)  ; Sin(value)
  %163 = fmul fast float %162, 0x40E55DD180000000
  %164 = call float @dx.op.unary.f32(i32 22, float %163)  ; Frc(value)
  %165 = fsub fast float %160, %164
  %166 = fmul fast float %165, %144
  %167 = fadd fast float %166, %164
  %168 = fsub fast float %156, %167
  %169 = fmul fast float %168, %80
  %170 = fadd fast float %169, %167
  %171 = fadd fast float %145, 5.800000e+01
  %172 = call float @dx.op.unary.f32(i32 13, float %171)  ; Sin(value)
  %173 = fmul fast float %172, 0x40E55DD180000000
  %174 = call float @dx.op.unary.f32(i32 22, float %173)  ; Frc(value)
  %175 = fadd fast float %145, 5.700000e+01
  %176 = call float @dx.op.unary.f32(i32 13, float %175)  ; Sin(value)
  %177 = fmul fast float %176, 0x40E55DD180000000
  %178 = call float @dx.op.unary.f32(i32 22, float %177)  ; Frc(value)
  %179 = fsub fast float %174, %178
  %180 = fmul fast float %179, %144
  %181 = fadd fast float %180, %178
  %182 = fadd fast float %145, 1.000000e+00
  %183 = call float @dx.op.unary.f32(i32 13, float %182)  ; Sin(value)
  %184 = fmul fast float %183, 0x40E55DD180000000
  %185 = call float @dx.op.unary.f32(i32 22, float %184)  ; Frc(value)
  %186 = call float @dx.op.unary.f32(i32 13, float %145)  ; Sin(value)
  %187 = fmul fast float %186, 0x40E55DD180000000
  %188 = call float @dx.op.unary.f32(i32 22, float %187)  ; Frc(value)
  %189 = fsub fast float %185, %188
  %190 = fmul fast float %189, %144
  %191 = fadd fast float %190, %188
  %192 = fsub fast float %181, %191
  %193 = fmul fast float %192, %80
  %194 = fadd fast float %193, %191
  %195 = fsub fast float %170, %194
  %196 = fmul fast float %195, %81
  %197 = fadd fast float %196, %194
  %198 = fadd fast float %61, 0xBFB99999A0000000
  %199 = call float @dx.op.unary.f32(i32 27, float %60)  ; Round_ni(value)
  %200 = call float @dx.op.unary.f32(i32 27, float %198)  ; Round_ni(value)
  %201 = call float @dx.op.unary.f32(i32 22, float %60)  ; Frc(value)
  %202 = call float @dx.op.unary.f32(i32 22, float %198)  ; Frc(value)
  %203 = fmul fast float %201, %201
  %204 = fmul fast float %202, %202
  %205 = fmul fast float %201, 2.000000e+00
  %206 = fmul fast float %202, 2.000000e+00
  %207 = fsub fast float 3.000000e+00, %205
  %208 = fsub fast float 3.000000e+00, %206
  %209 = fmul fast float %203, %207
  %210 = fmul fast float %204, %208
  %211 = fmul fast float %200, 5.700000e+01
  %212 = fadd fast float %199, %83
  %213 = fadd fast float %212, %211
  %214 = fadd fast float %213, 1.710000e+02
  %215 = call float @dx.op.unary.f32(i32 13, float %214)  ; Sin(value)
  %216 = fmul fast float %215, 0x40E55DD180000000
  %217 = call float @dx.op.unary.f32(i32 22, float %216)  ; Frc(value)
  %218 = fadd fast float %213, 1.700000e+02
  %219 = call float @dx.op.unary.f32(i32 13, float %218)  ; Sin(value)
  %220 = fmul fast float %219, 0x40E55DD180000000
  %221 = call float @dx.op.unary.f32(i32 22, float %220)  ; Frc(value)
  %222 = fsub fast float %217, %221
  %223 = fmul fast float %222, %209
  %224 = fadd fast float %223, %221
  %225 = fadd fast float %213, 1.140000e+02
  %226 = call float @dx.op.unary.f32(i32 13, float %225)  ; Sin(value)
  %227 = fmul fast float %226, 0x40E55DD180000000
  %228 = call float @dx.op.unary.f32(i32 22, float %227)  ; Frc(value)
  %229 = fadd fast float %213, 1.130000e+02
  %230 = call float @dx.op.unary.f32(i32 13, float %229)  ; Sin(value)
  %231 = fmul fast float %230, 0x40E55DD180000000
  %232 = call float @dx.op.unary.f32(i32 22, float %231)  ; Frc(value)
  %233 = fsub fast float %228, %232
  %234 = fmul fast float %233, %209
  %235 = fadd fast float %234, %232
  %236 = fsub fast float %224, %235
  %237 = fmul fast float %236, %210
  %238 = fadd fast float %237, %235
  %239 = fadd fast float %213, 5.800000e+01
  %240 = call float @dx.op.unary.f32(i32 13, float %239)  ; Sin(value)
  %241 = fmul fast float %240, 0x40E55DD180000000
  %242 = call float @dx.op.unary.f32(i32 22, float %241)  ; Frc(value)
  %243 = fadd fast float %213, 5.700000e+01
  %244 = call float @dx.op.unary.f32(i32 13, float %243)  ; Sin(value)
  %245 = fmul fast float %244, 0x40E55DD180000000
  %246 = call float @dx.op.unary.f32(i32 22, float %245)  ; Frc(value)
  %247 = fsub fast float %242, %246
  %248 = fmul fast float %247, %209
  %249 = fadd fast float %248, %246
  %250 = fadd fast float %213, 1.000000e+00
  %251 = call float @dx.op.unary.f32(i32 13, float %250)  ; Sin(value)
  %252 = fmul fast float %251, 0x40E55DD180000000
  %253 = call float @dx.op.unary.f32(i32 22, float %252)  ; Frc(value)
  %254 = call float @dx.op.unary.f32(i32 13, float %213)  ; Sin(value)
  %255 = fmul fast float %254, 0x40E55DD180000000
  %256 = call float @dx.op.unary.f32(i32 22, float %255)  ; Frc(value)
  %257 = fsub fast float %253, %256
  %258 = fmul fast float %257, %209
  %259 = fadd fast float %258, %256
  %260 = fsub fast float %249, %259
  %261 = fmul fast float %260, %210
  %262 = fadd fast float %261, %259
  %263 = fsub fast float %238, %262
  %264 = fmul fast float %263, %81
  %265 = fadd fast float %264, %262
  %266 = fadd fast float %61, 0x3FB99999A0000000
  %267 = call float @dx.op.unary.f32(i32 27, float %266)  ; Round_ni(value)
  %268 = call float @dx.op.unary.f32(i32 22, float %266)  ; Frc(value)
  %269 = fmul fast float %268, %268
  %270 = fmul fast float %268, 2.000000e+00
  %271 = fsub fast float 3.000000e+00, %270
  %272 = fmul fast float %269, %271
  %273 = fmul fast float %267, 5.700000e+01
  %274 = fadd fast float %212, %273
  %275 = fadd fast float %274, 1.710000e+02
  %276 = call float @dx.op.unary.f32(i32 13, float %275)  ; Sin(value)
  %277 = fmul fast float %276, 0x40E55DD180000000
  %278 = call float @dx.op.unary.f32(i32 22, float %277)  ; Frc(value)
  %279 = fadd fast float %274, 1.700000e+02
  %280 = call float @dx.op.unary.f32(i32 13, float %279)  ; Sin(value)
  %281 = fmul fast float %280, 0x40E55DD180000000
  %282 = call float @dx.op.unary.f32(i32 22, float %281)  ; Frc(value)
  %283 = fsub fast float %278, %282
  %284 = fmul fast float %283, %209
  %285 = fadd fast float %284, %282
  %286 = fadd fast float %274, 1.140000e+02
  %287 = call float @dx.op.unary.f32(i32 13, float %286)  ; Sin(value)
  %288 = fmul fast float %287, 0x40E55DD180000000
  %289 = call float @dx.op.unary.f32(i32 22, float %288)  ; Frc(value)
  %290 = fadd fast float %274, 1.130000e+02
  %291 = call float @dx.op.unary.f32(i32 13, float %290)  ; Sin(value)
  %292 = fmul fast float %291, 0x40E55DD180000000
  %293 = call float @dx.op.unary.f32(i32 22, float %292)  ; Frc(value)
  %294 = fsub fast float %289, %293
  %295 = fmul fast float %294, %209
  %296 = fadd fast float %295, %293
  %297 = fsub fast float %285, %296
  %298 = fmul fast float %297, %272
  %299 = fadd fast float %298, %296
  %300 = fadd fast float %274, 5.800000e+01
  %301 = call float @dx.op.unary.f32(i32 13, float %300)  ; Sin(value)
  %302 = fmul fast float %301, 0x40E55DD180000000
  %303 = call float @dx.op.unary.f32(i32 22, float %302)  ; Frc(value)
  %304 = fadd fast float %274, 5.700000e+01
  %305 = call float @dx.op.unary.f32(i32 13, float %304)  ; Sin(value)
  %306 = fmul fast float %305, 0x40E55DD180000000
  %307 = call float @dx.op.unary.f32(i32 22, float %306)  ; Frc(value)
  %308 = fsub fast float %303, %307
  %309 = fmul fast float %308, %209
  %310 = fadd fast float %309, %307
  %311 = fadd fast float %274, 1.000000e+00
  %312 = call float @dx.op.unary.f32(i32 13, float %311)  ; Sin(value)
  %313 = fmul fast float %312, 0x40E55DD180000000
  %314 = call float @dx.op.unary.f32(i32 22, float %313)  ; Frc(value)
  %315 = call float @dx.op.unary.f32(i32 13, float %274)  ; Sin(value)
  %316 = fmul fast float %315, 0x40E55DD180000000
  %317 = call float @dx.op.unary.f32(i32 22, float %316)  ; Frc(value)
  %318 = fsub fast float %314, %317
  %319 = fmul fast float %318, %209
  %320 = fadd fast float %319, %317
  %321 = fsub fast float %310, %320
  %322 = fmul fast float %321, %272
  %323 = fadd fast float %322, %320
  %324 = fsub fast float %299, %323
  %325 = fmul fast float %324, %81
  %326 = fadd fast float %325, %323
  %327 = fadd fast float %62, 0xBFB99999A0000000
  %328 = call float @dx.op.unary.f32(i32 27, float %327)  ; Round_ni(value)
  %329 = call float @dx.op.unary.f32(i32 22, float %327)  ; Frc(value)
  %330 = fmul fast float %329, %329
  %331 = fmul fast float %329, 2.000000e+00
  %332 = fsub fast float 3.000000e+00, %331
  %333 = fmul fast float %330, %332
  %334 = fadd fast float %199, %82
  %335 = fmul fast float %328, 1.130000e+02
  %336 = fadd fast float %335, %334
  %337 = fadd fast float %336, 1.710000e+02
  %338 = call float @dx.op.unary.f32(i32 13, float %337)  ; Sin(value)
  %339 = fmul fast float %338, 0x40E55DD180000000
  %340 = call float @dx.op.unary.f32(i32 22, float %339)  ; Frc(value)
  %341 = fadd fast float %336, 1.700000e+02
  %342 = call float @dx.op.unary.f32(i32 13, float %341)  ; Sin(value)
  %343 = fmul fast float %342, 0x40E55DD180000000
  %344 = call float @dx.op.unary.f32(i32 22, float %343)  ; Frc(value)
  %345 = fsub fast float %340, %344
  %346 = fmul fast float %345, %209
  %347 = fadd fast float %346, %344
  %348 = fadd fast float %336, 1.140000e+02
  %349 = call float @dx.op.unary.f32(i32 13, float %348)  ; Sin(value)
  %350 = fmul fast float %349, 0x40E55DD180000000
  %351 = call float @dx.op.unary.f32(i32 22, float %350)  ; Frc(value)
  %352 = fadd fast float %336, 1.130000e+02
  %353 = call float @dx.op.unary.f32(i32 13, float %352)  ; Sin(value)
  %354 = fmul fast float %353, 0x40E55DD180000000
  %355 = call float @dx.op.unary.f32(i32 22, float %354)  ; Frc(value)
  %356 = fsub fast float %351, %355
  %357 = fmul fast float %356, %209
  %358 = fadd fast float %357, %355
  %359 = fsub fast float %347, %358
  %360 = fmul fast float %359, %80
  %361 = fadd fast float %360, %358
  %362 = fadd fast float %336, 5.800000e+01
  %363 = call float @dx.op.unary.f32(i32 13, float %362)  ; Sin(value)
  %364 = fmul fast float %363, 0x40E55DD180000000
  %365 = call float @dx.op.unary.f32(i32 22, float %364)  ; Frc(value)
  %366 = fadd fast float %336, 5.700000e+01
  %367 = call float @dx.op.unary.f32(i32 13, float %366)  ; Sin(value)
  %368 = fmul fast float %367, 0x40E55DD180000000
  %369 = call float @dx.op.unary.f32(i32 22, float %368)  ; Frc(value)
  %370 = fsub fast float %365, %369
  %371 = fmul fast float %370, %209
  %372 = fadd fast float %371, %369
  %373 = fadd fast float %336, 1.000000e+00
  %374 = call float @dx.op.unary.f32(i32 13, float %373)  ; Sin(value)
  %375 = fmul fast float %374, 0x40E55DD180000000
  %376 = call float @dx.op.unary.f32(i32 22, float %375)  ; Frc(value)
  %377 = call float @dx.op.unary.f32(i32 13, float %336)  ; Sin(value)
  %378 = fmul fast float %377, 0x40E55DD180000000
  %379 = call float @dx.op.unary.f32(i32 22, float %378)  ; Frc(value)
  %380 = fsub fast float %376, %379
  %381 = fmul fast float %380, %209
  %382 = fadd fast float %381, %379
  %383 = fsub fast float %372, %382
  %384 = fmul fast float %383, %80
  %385 = fadd fast float %384, %382
  %386 = fsub fast float %361, %385
  %387 = fmul fast float %333, %386
  %388 = fadd fast float %387, %385
  %389 = fadd fast float %62, 0x3FB99999A0000000
  %390 = call float @dx.op.unary.f32(i32 27, float %389)  ; Round_ni(value)
  %391 = call float @dx.op.unary.f32(i32 22, float %389)  ; Frc(value)
  %392 = fmul fast float %391, %391
  %393 = fmul fast float %391, 2.000000e+00
  %394 = fsub fast float 3.000000e+00, %393
  %395 = fmul fast float %392, %394
  %396 = fmul fast float %390, 1.130000e+02
  %397 = fadd fast float %396, %334
  %398 = fadd fast float %397, 1.710000e+02
  %399 = call float @dx.op.unary.f32(i32 13, float %398)  ; Sin(value)
  %400 = fmul fast float %399, 0x40E55DD180000000
  %401 = call float @dx.op.unary.f32(i32 22, float %400)  ; Frc(value)
  %402 = fadd fast float %397, 1.700000e+02
  %403 = call float @dx.op.unary.f32(i32 13, float %402)  ; Sin(value)
  %404 = fmul fast float %403, 0x40E55DD180000000
  %405 = call float @dx.op.unary.f32(i32 22, float %404)  ; Frc(value)
  %406 = fsub fast float %401, %405
  %407 = fmul fast float %406, %209
  %408 = fadd fast float %407, %405
  %409 = fadd fast float %397, 1.140000e+02
  %410 = call float @dx.op.unary.f32(i32 13, float %409)  ; Sin(value)
  %411 = fmul fast float %410, 0x40E55DD180000000
  %412 = call float @dx.op.unary.f32(i32 22, float %411)  ; Frc(value)
  %413 = fadd fast float %397, 1.130000e+02
  %414 = call float @dx.op.unary.f32(i32 13, float %413)  ; Sin(value)
  %415 = fmul fast float %414, 0x40E55DD180000000
  %416 = call float @dx.op.unary.f32(i32 22, float %415)  ; Frc(value)
  %417 = fsub fast float %412, %416
  %418 = fmul fast float %417, %209
  %419 = fadd fast float %418, %416
  %420 = fsub fast float %408, %419
  %421 = fmul fast float %420, %80
  %422 = fadd fast float %421, %419
  %423 = fadd fast float %397, 5.800000e+01
  %424 = call float @dx.op.unary.f32(i32 13, float %423)  ; Sin(value)
  %425 = fmul fast float %424, 0x40E55DD180000000
  %426 = call float @dx.op.unary.f32(i32 22, float %425)  ; Frc(value)
  %427 = fadd fast float %397, 5.700000e+01
  %428 = call float @dx.op.unary.f32(i32 13, float %427)  ; Sin(value)
  %429 = fmul fast float %428, 0x40E55DD180000000
  %430 = call float @dx.op.unary.f32(i32 22, float %429)  ; Frc(value)
  %431 = fsub fast float %426, %430
  %432 = fmul fast float %431, %209
  %433 = fadd fast float %432, %430
  %434 = fadd fast float %397, 1.000000e+00
  %435 = call float @dx.op.unary.f32(i32 13, float %434)  ; Sin(value)
  %436 = fmul fast float %435, 0x40E55DD180000000
  %437 = call float @dx.op.unary.f32(i32 22, float %436)  ; Frc(value)
  %438 = call float @dx.op.unary.f32(i32 13, float %397)  ; Sin(value)
  %439 = fmul fast float %438, 0x40E55DD180000000
  %440 = call float @dx.op.unary.f32(i32 22, float %439)  ; Frc(value)
  %441 = fsub fast float %437, %440
  %442 = fmul fast float %441, %209
  %443 = fadd fast float %442, %440
  %444 = fsub fast float %433, %443
  %445 = fmul fast float %444, %80
  %446 = fadd fast float %445, %443
  %447 = fsub fast float %422, %446
  %448 = fmul fast float %395, %447
  %449 = fadd fast float %448, %446
  %450 = fsub fast float %326, %265
  %451 = fadd fast float %450, %388
  %452 = fsub fast float %451, %449
  %453 = fsub fast float %137, %197
  %454 = fsub fast float %453, %388
  %455 = fadd fast float %454, %449
  %456 = fsub fast float %197, %137
  %457 = fadd fast float %456, %265
  %458 = fsub fast float %457, %326
  %459 = extractvalue %dx.types.CBufRet.f32 %53, 2
  %460 = fmul fast float %459, 5.000000e+00
  %461 = fmul fast float %452, %460
  %462 = fmul fast float %455, %460
  %463 = fmul fast float %458, %460
  %464 = fadd fast float %461, %50
  %465 = fadd fast float %462, %51
  %466 = fadd fast float %463, %52
  %467 = fdiv fast float %464, %32
  %468 = fdiv fast float %465, %32
  %469 = fdiv fast float %466, %32
  %470 = fmul fast float %467, %40
  %471 = fmul fast float %468, %40
  %472 = fmul fast float %469, %40
  %473 = fadd fast float %470, %17
  %474 = fadd fast float %471, %18
  %475 = fadd fast float %472, %19
  %476 = extractvalue %dx.types.CBufRet.f32 %46, 3
  %477 = fmul fast float %473, %476
  %478 = fmul fast float %474, %476
  %479 = fmul fast float %475, %476
  %480 = fmul fast float %477, %40
  %481 = fmul fast float %478, %40
  %482 = fmul fast float %479, %40
  %483 = fadd fast float %480, %11
  %484 = fadd fast float %481, %12
  %485 = fadd fast float %482, %13
  %486 = fmul fast float %41, 5.000000e-01
  %487 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %488 = extractvalue %dx.types.CBufRet.f32 %487, 3
  %489 = extractvalue %dx.types.CBufRet.f32 %487, 2
  %490 = fsub fast float %488, %489
  %491 = fmul fast float %490, %486
  %492 = mul i32 %5, 3
  %493 = add i32 0, %492
  %494 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %493
  store float %483, float addrspace(3)* %494, align 4
  %495 = mul i32 %5, 3
  %496 = add i32 1, %495
  %497 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %496
  store float %484, float addrspace(3)* %497, align 4
  %498 = mul i32 %5, 3
  %499 = add i32 2, %498
  %500 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %499
  store float %485, float addrspace(3)* %500, align 4
  %501 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %502 = extractvalue %dx.types.CBufRet.f32 %501, 0
  %503 = extractvalue %dx.types.CBufRet.f32 %501, 1
  %504 = extractvalue %dx.types.CBufRet.f32 %501, 2
  %505 = fsub fast float %483, %502
  %506 = fsub fast float %484, %503
  %507 = fsub fast float %485, %504
  %508 = fmul fast float %505, %505
  %509 = fmul fast float %506, %506
  %510 = fadd fast float %509, %508
  %511 = fmul fast float %507, %507
  %512 = fadd fast float %510, %511
  %513 = call float @dx.op.unary.f32(i32 24, float %512)  ; Sqrt(value)
  %514 = getelementptr [64 x float], [64 x float] addrspace(3)* @"\01?SharedDistances@@3PAMA", i32 0, i32 %5
  store float %513, float addrspace(3)* %514, align 4, !tbaa !14
  %515 = getelementptr [64 x i32], [64 x i32] addrspace(3)* @"\01?SharedIndices@@3PAIA", i32 0, i32 %5
  store i32 %4, i32 addrspace(3)* %515, align 4, !tbaa !18
  call void @dx.op.barrier(i32 80, i32 9)  ; Barrier(barrierMode)
  br label %516

; <label>:516                                     ; preds = %567, %45
  %517 = phi float [ 0.000000e+00, %45 ], [ %568, %567 ]
  %518 = phi float [ 0.000000e+00, %45 ], [ %569, %567 ]
  %519 = phi float [ 0.000000e+00, %45 ], [ %570, %567 ]
  %520 = phi float [ 0.000000e+00, %45 ], [ %571, %567 ]
  %521 = phi float [ 0.000000e+00, %45 ], [ %572, %567 ]
  %522 = phi float [ 0.000000e+00, %45 ], [ %573, %567 ]
  %523 = phi i32 [ 0, %45 ], [ %574, %567 ]
  %524 = phi i32 [ 0, %45 ], [ %575, %567 ]
  %525 = icmp eq i32 %524, %5
  br i1 %525, label %567, label %526

; <label>:526                                     ; preds = %516
  %527 = mul i32 %524, 3
  %528 = add i32 0, %527
  %529 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %528
  %530 = load float, float addrspace(3)* %529, align 4
  %531 = mul i32 %524, 3
  %532 = add i32 1, %531
  %533 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %532
  %534 = load float, float addrspace(3)* %533, align 4
  %535 = mul i32 %524, 3
  %536 = add i32 2, %535
  %537 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedPositions@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %536
  %538 = load float, float addrspace(3)* %537, align 4
  %539 = fsub fast float %483, %530
  %540 = fsub fast float %484, %534
  %541 = fsub fast float %485, %538
  %542 = fmul fast float %539, %539
  %543 = fmul fast float %540, %540
  %544 = fadd fast float %543, %542
  %545 = fmul fast float %541, %541
  %546 = fadd fast float %544, %545
  %547 = call float @dx.op.unary.f32(i32 24, float %546)  ; Sqrt(value)
  %548 = fcmp fast olt float %547, 5.000000e+00
  %549 = fcmp fast ogt float %547, 0.000000e+00
  %550 = and i1 %548, %549
  br i1 %550, label %551, label %567

; <label>:551                                     ; preds = %526
  %552 = call float @dx.op.dot3.f32(i32 55, float %539, float %540, float %541, float %539, float %540, float %541)  ; Dot3(ax,ay,az,bx,by,bz)
  %553 = call float @dx.op.unary.f32(i32 25, float %552)  ; Rsqrt(value)
  %554 = fmul fast float %553, %539
  %555 = fmul fast float %553, %540
  %556 = fmul fast float %553, %541
  %557 = fdiv fast float %554, %547
  %558 = fdiv fast float %555, %547
  %559 = fdiv fast float %556, %547
  %560 = fadd fast float %557, %517
  %561 = fadd fast float %558, %518
  %562 = fadd fast float %559, %519
  %563 = fadd fast float %530, %520
  %564 = fadd fast float %534, %521
  %565 = fadd fast float %538, %522
  %566 = add i32 %523, 1
  br label %567

; <label>:567                                     ; preds = %551, %526, %516
  %568 = phi float [ %517, %516 ], [ %560, %551 ], [ %517, %526 ]
  %569 = phi float [ %518, %516 ], [ %561, %551 ], [ %518, %526 ]
  %570 = phi float [ %519, %516 ], [ %562, %551 ], [ %519, %526 ]
  %571 = phi float [ %520, %516 ], [ %563, %551 ], [ %520, %526 ]
  %572 = phi float [ %521, %516 ], [ %564, %551 ], [ %521, %526 ]
  %573 = phi float [ %522, %516 ], [ %565, %551 ], [ %522, %526 ]
  %574 = phi i32 [ %523, %516 ], [ %566, %551 ], [ %523, %526 ]
  %575 = add nuw nsw i32 %524, 1
  %576 = icmp eq i32 %575, 64
  br i1 %576, label %577, label %516

; <label>:577                                     ; preds = %567
  %578 = fadd fast float %491, %489
  %579 = icmp eq i32 %574, 0
  br i1 %579, label %607, label %580

; <label>:580                                     ; preds = %577
  %581 = uitofp i32 %574 to float
  %582 = fdiv fast float %571, %581
  %583 = fdiv fast float %572, %581
  %584 = fdiv fast float %573, %581
  %585 = fsub fast float %582, %483
  %586 = fsub fast float %583, %484
  %587 = fsub fast float %584, %485
  %588 = call float @dx.op.dot3.f32(i32 55, float %585, float %586, float %587, float %585, float %586, float %587)  ; Dot3(ax,ay,az,bx,by,bz)
  %589 = call float @dx.op.unary.f32(i32 25, float %588)  ; Rsqrt(value)
  %590 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %3, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %591 = extractvalue %dx.types.CBufRet.f32 %590, 2
  %592 = fmul fast float %591, 0x3FB99999A0000000
  %593 = fmul fast float %592, %568
  %594 = fmul fast float %592, %569
  %595 = fmul fast float %592, %570
  %596 = fmul fast float %589, 0x3FA99999A0000000
  %597 = fmul fast float %591, %596
  %598 = fmul fast float %597, %585
  %599 = fmul fast float %597, %586
  %600 = fmul fast float %597, %587
  %601 = fadd fast float %598, %477
  %602 = fadd fast float %601, %593
  %603 = fadd fast float %599, %478
  %604 = fadd fast float %603, %594
  %605 = fadd fast float %600, %479
  %606 = fadd fast float %605, %595
  br label %607

; <label>:607                                     ; preds = %580, %577
  %608 = phi float [ %602, %580 ], [ %477, %577 ]
  %609 = phi float [ %604, %580 ], [ %478, %577 ]
  %610 = phi float [ %606, %580 ], [ %479, %577 ]
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 0, float %483, float %484, float %485, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 12, float %41, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 16, float %608, float %609, float %610, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 28, float %578, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 32, float %23, float %24, float %25, float %486, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 48, float %467, float %468, float %469, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 60, float %32, float undef, float undef, float undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.i32(i32 69, %dx.types.Handle %2, i32 %4, i32 64, i32 %34, i32 undef, i32 undef, i32 undef, i8 1)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %4, i32 68, float %36, float %37, float %38, float undef, i8 7)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  br label %611

; <label>:611                                     ; preds = %607, %43, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadIdInGroup.i32(i32, i32) #0

; Function Attrs: nounwind
declare i32 @dx.op.bufferUpdateCounter(i32, %dx.types.Handle, i8) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: noduplicate nounwind
declare void @dx.op.barrier(i32, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #3

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #3

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #3

; Function Attrs: nounwind
declare void @dx.op.bufferStore.f32(i32, %dx.types.Handle, i32, i32, float, float, float, float, i8) #1

; Function Attrs: nounwind
declare void @dx.op.bufferStore.i32(i32, %dx.types.Handle, i32, i32, i32, i32, i32, i32, i8) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #3

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32, %dx.types.Handle, i32, i32) #3

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { noduplicate nounwind }
attributes #3 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!11}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{null, !5, !9, null}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.RWStructuredBuffer<Particle>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i1 false, i1 false, i1 false, !7}
!7 = !{i32 1, i32 80}
!8 = !{i32 1, %"class.AppendStructuredBuffer<Particle>"* undef, !"", i32 0, i32 1, i32 1, i32 12, i1 false, i1 true, i1 false, !7}
!9 = !{!10}
!10 = !{i32 0, %ComputeParams* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!11 = !{void ()* @main, !"main", null, !4, !12}
!12 = !{i32 0, i64 16, i32 4, !13}
!13 = !{i32 64, i32 1, i32 1}
!14 = !{!15, !15, i64 0}
!15 = !{!"float", !16, i64 0}
!16 = !{!"omnipotent char", !17, i64 0}
!17 = !{!"Simple C/C++ TBAA"}
!18 = !{!19, !19, i64 0}
!19 = !{!"int", !16, i64 0}
