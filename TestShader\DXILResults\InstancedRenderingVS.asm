;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TEXCOORD                 0   xy          2     NONE   float   xy  
; COLOR                    0   xyzw        3     NONE   float   xyzw
; TEXCOORD                 1   xyzw        4     NONE   float   xyzw
; TEXCOORD                 2   xyzw        5     NONE   float   xyzw
; TEXCOORD                 3   xyzw        6     NONE   float   xyzw
; TEXCOORD                 4   xyzw        7     NONE   float   xyzw
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 5      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 6      w        2     NONE   float      w
; TEXCOORD                 2   xy          3     NONE   float   xy  
; TEXCOORD                 3   xyzw        4     NONE   float   xyzw
; TEXCOORD                 4   xyz         5     NONE   float   xyz 
;
; shader hash: 0a8c81f03ad25a65b0f690f32f6be2fd
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 8
; SigOutputElements: 8
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 8
; SigOutputVectors[0]: 6
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TEXCOORD                 0                              
; COLOR                    0                              
; TEXCOORD                 1                              
; TEXCOORD                 2                              
; TEXCOORD                 3                              
; TEXCOORD                 4                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 ViewProjectionMatrix;   ; Offset:    0
;       float3 CameraPosition;                        ; Offset:   64
;       float Time;                                   ; Offset:   76
;       float3 WindDirection;                         ; Offset:   80
;       float WindStrength;                           ; Offset:   92
;   
;   } PerFrame;                                       ; Offset:    0 Size:    96
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 32, outputs: 23
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 1 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 2 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 3 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 4 depends on inputs: { 0, 1, 2, 16, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 5 depends on inputs: { 0, 1, 2, 16, 17, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 6 depends on inputs: { 0, 1, 2, 16, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 7 depends on inputs: { 30 }
;   output 8 depends on inputs: { 4, 5, 6, 20, 21, 22, 23 }
;   output 9 depends on inputs: { 4, 5, 6, 20, 21, 22, 23 }
;   output 10 depends on inputs: { 4, 5, 6, 20, 21, 22, 23 }
;   output 11 depends on inputs: { 29 }
;   output 12 depends on inputs: { 8 }
;   output 13 depends on inputs: { 9 }
;   output 16 depends on inputs: { 12, 24, 29 }
;   output 17 depends on inputs: { 13, 25, 29 }
;   output 18 depends on inputs: { 14, 26, 29 }
;   output 19 depends on inputs: { 15, 27 }
;   output 20 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 21 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;   output 22 depends on inputs: { 0, 1, 2, 16, 17, 18, 19, 20, 21, 22, 23, 28, 29, 30, 31 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%hostlayout.PerFrame = type { [4 x <4 x float>], <3 x float>, float, <3 x float>, float }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %21 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %22 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %23 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %24 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %25 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %26 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %27 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %28 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %29 = fcmp fast olt float %4, 1.000000e+00
  br i1 %29, label %30, label %59

; <label>:30                                      ; preds = %0
  %31 = call float @dx.op.loadInput.f32(i32 4, i32 7, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %32 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %33 = extractvalue %dx.types.CBufRet.f32 %32, 3
  %34 = fmul fast float %33, 2.000000e+00
  %35 = fmul fast float %13, 0x3FB99999A0000000
  %36 = fadd fast float %35, %2
  %37 = fadd fast float %36, %34
  %38 = call float @dx.op.unary.f32(i32 13, float %37)  ; Sin(value)
  %39 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %40 = extractvalue %dx.types.CBufRet.f32 %39, 3
  %41 = extractvalue %dx.types.CBufRet.f32 %39, 0
  %42 = extractvalue %dx.types.CBufRet.f32 %39, 1
  %43 = extractvalue %dx.types.CBufRet.f32 %39, 2
  %44 = fmul fast float %27, 0x3FB99999A0000000
  %45 = fmul fast float %44, %38
  %46 = fmul fast float %45, %40
  %47 = fmul fast float %46, %41
  %48 = fmul fast float %46, %42
  %49 = fmul fast float %46, %43
  %50 = fadd fast float %47, %26
  %51 = fadd fast float %48, %27
  %52 = fadd fast float %49, %28
  %53 = fmul fast float %31, 0x3FB99999A0000000
  %54 = call float @dx.op.unary.f32(i32 7, float %53)  ; Saturate(value)
  %55 = fmul fast float %54, %3
  %56 = fmul fast float %50, %55
  %57 = fmul fast float %51, %55
  %58 = fmul fast float %52, %55
  br label %69

; <label>:59                                      ; preds = %0
  %60 = fcmp fast olt float %4, 2.000000e+00
  br i1 %60, label %69, label %61

; <label>:61                                      ; preds = %59
  %62 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %63 = extractvalue %dx.types.CBufRet.f32 %62, 3
  %64 = fmul fast float %63, 3.000000e+00
  %65 = fadd fast float %64, %2
  %66 = call float @dx.op.unary.f32(i32 13, float %65)  ; Sin(value)
  %67 = fmul fast float %66, 0x3FB99999A0000000
  %68 = fadd fast float %67, %27
  br label %69

; <label>:69                                      ; preds = %61, %59, %30
  %70 = phi float [ %56, %30 ], [ %26, %59 ], [ %26, %61 ]
  %71 = phi float [ %57, %30 ], [ %27, %59 ], [ %68, %61 ]
  %72 = phi float [ %58, %30 ], [ %28, %59 ], [ %28, %61 ]
  %73 = fmul fast float %70, %16
  %74 = fmul fast float %71, %16
  %75 = fmul fast float %72, %16
  %76 = fmul fast float %75, %10
  %77 = fmul fast float %74, %11
  %78 = fsub fast float %76, %77
  %79 = fmul fast float %73, %11
  %80 = fmul fast float %75, %9
  %81 = fsub fast float %79, %80
  %82 = fmul fast float %74, %9
  %83 = fmul fast float %73, %10
  %84 = fsub fast float %82, %83
  %85 = fmul fast float %84, %10
  %86 = fmul fast float %81, %11
  %87 = fsub fast float %85, %86
  %88 = fmul fast float %78, %11
  %89 = fmul fast float %84, %9
  %90 = fsub fast float %88, %89
  %91 = fmul fast float %81, %9
  %92 = fmul fast float %78, %10
  %93 = fmul fast float %78, %12
  %94 = fmul fast float %81, %12
  %95 = fmul fast float %84, %12
  %96 = fadd fast float %87, %93
  %97 = fadd fast float %90, %94
  %98 = fsub fast float %95, %92
  %99 = fadd fast float %98, %91
  %100 = fmul fast float %96, 2.000000e+00
  %101 = fmul fast float %97, 2.000000e+00
  %102 = fmul fast float %99, 2.000000e+00
  %103 = fmul fast float %25, %10
  %104 = fmul fast float %24, %11
  %105 = fsub fast float %103, %104
  %106 = fmul fast float %23, %11
  %107 = fmul fast float %25, %9
  %108 = fsub fast float %106, %107
  %109 = fmul fast float %24, %9
  %110 = fmul fast float %23, %10
  %111 = fsub fast float %109, %110
  %112 = fmul fast float %111, %10
  %113 = fmul fast float %108, %11
  %114 = fsub fast float %112, %113
  %115 = fmul fast float %105, %11
  %116 = fmul fast float %111, %9
  %117 = fsub fast float %115, %116
  %118 = fmul fast float %108, %9
  %119 = fmul fast float %105, %10
  %120 = fmul fast float %105, %12
  %121 = fmul fast float %108, %12
  %122 = fmul fast float %111, %12
  %123 = fadd fast float %114, %120
  %124 = fadd fast float %117, %121
  %125 = fsub fast float %122, %119
  %126 = fadd fast float %125, %118
  %127 = fmul fast float %123, 2.000000e+00
  %128 = fmul fast float %124, 2.000000e+00
  %129 = fmul fast float %126, 2.000000e+00
  %130 = fadd fast float %127, %23
  %131 = fadd fast float %128, %24
  %132 = fadd fast float %129, %25
  %133 = fadd fast float %73, %13
  %134 = fadd fast float %133, %100
  %135 = fadd fast float %74, %14
  %136 = fadd fast float %135, %101
  %137 = fadd fast float %75, %15
  %138 = fadd fast float %137, %102
  %139 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %140 = extractvalue %dx.types.CBufRet.f32 %139, 0
  %141 = extractvalue %dx.types.CBufRet.f32 %139, 1
  %142 = extractvalue %dx.types.CBufRet.f32 %139, 2
  %143 = extractvalue %dx.types.CBufRet.f32 %139, 3
  %144 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %145 = extractvalue %dx.types.CBufRet.f32 %144, 0
  %146 = extractvalue %dx.types.CBufRet.f32 %144, 1
  %147 = extractvalue %dx.types.CBufRet.f32 %144, 2
  %148 = extractvalue %dx.types.CBufRet.f32 %144, 3
  %149 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %150 = extractvalue %dx.types.CBufRet.f32 %149, 0
  %151 = extractvalue %dx.types.CBufRet.f32 %149, 1
  %152 = extractvalue %dx.types.CBufRet.f32 %149, 2
  %153 = extractvalue %dx.types.CBufRet.f32 %149, 3
  %154 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %155 = extractvalue %dx.types.CBufRet.f32 %154, 0
  %156 = extractvalue %dx.types.CBufRet.f32 %154, 1
  %157 = extractvalue %dx.types.CBufRet.f32 %154, 2
  %158 = extractvalue %dx.types.CBufRet.f32 %154, 3
  %159 = fmul fast float %134, %140
  %160 = call float @dx.op.tertiary.f32(i32 46, float %136, float %141, float %159)  ; FMad(a,b,c)
  %161 = call float @dx.op.tertiary.f32(i32 46, float %138, float %142, float %160)  ; FMad(a,b,c)
  %162 = fadd fast float %161, %143
  %163 = fmul fast float %134, %145
  %164 = call float @dx.op.tertiary.f32(i32 46, float %136, float %146, float %163)  ; FMad(a,b,c)
  %165 = call float @dx.op.tertiary.f32(i32 46, float %138, float %147, float %164)  ; FMad(a,b,c)
  %166 = fadd fast float %165, %148
  %167 = fmul fast float %134, %150
  %168 = call float @dx.op.tertiary.f32(i32 46, float %136, float %151, float %167)  ; FMad(a,b,c)
  %169 = call float @dx.op.tertiary.f32(i32 46, float %138, float %152, float %168)  ; FMad(a,b,c)
  %170 = fadd fast float %169, %153
  %171 = fmul fast float %134, %155
  %172 = call float @dx.op.tertiary.f32(i32 46, float %136, float %156, float %171)  ; FMad(a,b,c)
  %173 = call float @dx.op.tertiary.f32(i32 46, float %138, float %157, float %172)  ; FMad(a,b,c)
  %174 = fadd fast float %173, %158
  %175 = call float @dx.op.dot3.f32(i32 55, float %130, float %131, float %132, float %130, float %131, float %132)  ; Dot3(ax,ay,az,bx,by,bz)
  %176 = call float @dx.op.unary.f32(i32 25, float %175)  ; Rsqrt(value)
  %177 = fmul fast float %176, %130
  %178 = fmul fast float %176, %131
  %179 = fmul fast float %176, %132
  %180 = fmul fast float %17, %5
  %181 = fmul fast float %18, %6
  %182 = fmul fast float %19, %7
  %183 = fmul fast float %20, %8
  %184 = fcmp fast olt float %3, 5.000000e-01
  br i1 %184, label %185, label %196

; <label>:185                                     ; preds = %69
  %186 = fmul fast float %3, 2.000000e+00
  %187 = fadd fast float %180, -5.000000e-01
  %188 = fadd fast float %181, 0xBFD3333340000000
  %189 = fadd fast float %182, 0xBFB99999A0000000
  %190 = fmul fast float %187, %186
  %191 = fmul fast float %188, %186
  %192 = fmul fast float %189, %186
  %193 = fadd fast float %190, 5.000000e-01
  %194 = fadd fast float %191, 0x3FD3333340000000
  %195 = fadd fast float %192, 0x3FB99999A0000000
  br label %196

; <label>:196                                     ; preds = %185, %69
  %197 = phi float [ %193, %185 ], [ %180, %69 ]
  %198 = phi float [ %194, %185 ], [ %181, %69 ]
  %199 = phi float [ %195, %185 ], [ %182, %69 ]
  %200 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %201 = extractvalue %dx.types.CBufRet.f32 %200, 0
  %202 = extractvalue %dx.types.CBufRet.f32 %200, 1
  %203 = extractvalue %dx.types.CBufRet.f32 %200, 2
  %204 = fsub fast float %201, %134
  %205 = fsub fast float %202, %136
  %206 = fsub fast float %203, %138
  %207 = call float @dx.op.dot3.f32(i32 55, float %204, float %205, float %206, float %204, float %205, float %206)  ; Dot3(ax,ay,az,bx,by,bz)
  %208 = call float @dx.op.unary.f32(i32 25, float %207)  ; Rsqrt(value)
  %209 = fmul fast float %204, %208
  %210 = fmul fast float %205, %208
  %211 = fmul fast float %206, %208
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %162)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %166)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %170)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %174)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %134)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %136)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %138)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %177)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %178)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %179)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %21)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %22)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %197)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %198)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 2, float %199)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 3, float %183)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %209)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %210)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 2, float %211)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %4)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %3)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!7 = !{[34 x i32] [i32 32, i32 23, i32 7340159, i32 7340159, i32 7340159, i32 0, i32 1792, i32 1792, i32 1792, i32 0, i32 4096, i32 8192, i32 0, i32 0, i32 65536, i32 131072, i32 262144, i32 524288, i32 7340159, i32 7340079, i32 7340111, i32 7340159, i32 7341951, i32 7341951, i32 7341951, i32 7341951, i32 65536, i32 131072, i32 262144, i32 524288, i32 7340159, i32 7800959, i32 7340287, i32 7340159]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !27, null}
!10 = !{!11, !14, !15, !17, !19, !21, !23, !25}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"NORMAL", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 1, i8 0, !13}
!15 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 2, i8 0, !16}
!16 = !{i32 3, i32 3}
!17 = !{i32 3, !"COLOR", i8 9, i8 0, !12, i8 0, i32 1, i8 4, i32 3, i8 0, !18}
!18 = !{i32 3, i32 15}
!19 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !20, i8 0, i32 1, i8 4, i32 4, i8 0, !18}
!20 = !{i32 1}
!21 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !22, i8 0, i32 1, i8 4, i32 5, i8 0, !18}
!22 = !{i32 2}
!23 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !24, i8 0, i32 1, i8 4, i32 6, i8 0, !18}
!24 = !{i32 3}
!25 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !26, i8 0, i32 1, i8 4, i32 7, i8 0, !18}
!26 = !{i32 4}
!27 = !{!28, !29, !30, !31, !32, !33, !34, !37}
!28 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !18}
!29 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 1, i8 0, !13}
!30 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !20, i8 2, i32 1, i8 3, i32 2, i8 0, !13}
!31 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !22, i8 2, i32 1, i8 2, i32 3, i8 0, !16}
!32 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !24, i8 2, i32 1, i8 4, i32 4, i8 0, !18}
!33 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 5, i8 0, !13}
!34 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 1, i32 1, i8 3, !36}
!35 = !{i32 5}
!36 = !{i32 3, i32 1}
!37 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !38, i8 2, i32 1, i8 1, i32 2, i8 3, !36}
!38 = !{i32 6}
