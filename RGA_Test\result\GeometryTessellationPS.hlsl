// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct MaterialLayer
{
  float4 Albedo;
  float Metallic;
  float Roughness;
  float NormalStrength;
  float TilingScale;
  float2 Offset;
  float BlendSharpness;
  float _padding;
};

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float2 DetailUV : TEXCOORD5;
  float4 Color : COLOR0;
  float Height : TEXCOORD6;
  float Slope : TEXCOORD7;
  float3 ViewDir : TEXCOORD8;
  float4 ShadowCoord : TEXCOORD9;
  float FogFactor : TEXCOORD10;
};

struct PSOutput
{
  float4 Albedo : SV_Target0;
  float4 Normal : SV_Target1;
  float4 Material : SV_Target2;
  float4 Motion : SV_Target3;
};

cbuffer PerFrame : register(b0)
{
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 ViewProjectionMatrix;
  float3 CameraPosition;
  float Time;
  float3 LightDirection;
  float TessellationLevel;
  float2 HeightmapSize;
  float HeightScale;
  float DetailScale;
  float LODDistance;
}

cbuffer MaterialParams : register(b1)
{
  MaterialLayer GrassLayer;
  MaterialLayer RockLayer;
  MaterialLayer SnowLayer;
  MaterialLayer SandLayer;
  float3 FogColor;
  float FogDensity;
  float FogStart;
  float FogEnd;
  float2 WindDirection;
  float WindStrength;
  float _padding2;
}

Texture2DArray AlbedoTextures : register(t0);
Texture2DArray NormalTextures : register(t1);
Texture2DArray RoughnessTextures : register(t2);
Texture2D SplatmapTexture : register(t5);
Texture2D DetailNormalTexture : register(t6);
TextureCube SkyboxTexture : register(t7);
Texture2D ShadowMap : register(t8);
SamplerState LinearSampler : register(s0);
SamplerState TrilinearSampler : register(s1);
SamplerComparisonState ShadowSampler : register(s2);
float3 UnpackNormal(float3 packedNormal)
{
  return (packedNormal * 2.0f) - 1.0f;
}

float3 BlendNormals(float3 normal1, float3 normal2, float strength)
{
  float3 t = normal1 + float3(0, 0, 1);
  float3 u = (normal2 * float3(-1, -1, 1));
  float3 r = (t / t.z * dot(t, u)) - u;
  return normalize(lerp(normal1, r, strength));
}

float4 SampleMaterialLayer(MaterialLayer layer, uint textureIndex, float2 uv)
{
  float2 tiledUV = (uv * layer.TilingScale) + layer.Offset;
  if (textureIndex == 0)
  {
    float2 windOffset = (((WindDirection * WindStrength) * sin(Time + (tiledUV.x * 10.0f))) * 0.01f);
    tiledUV += windOffset;
  }
  float4 albedo = AlbedoTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex));
  return (albedo * layer.Albedo);
}

float3 SampleNormalLayer(MaterialLayer layer, uint textureIndex, float2 uv)
{
  float2 tiledUV = (uv * layer.TilingScale) + layer.Offset;
  float3 normal = NormalTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex)).rgb;
  normal = UnpackNormal(normal);
  return (normal * layer.NormalStrength);
}

float2 SampleMaterialProperties(MaterialLayer layer, uint textureIndex, float2 uv)
{
  float2 tiledUV = (uv * layer.TilingScale) + layer.Offset;
  float2 roughnessAO = RoughnessTextures.Sample(TrilinearSampler, float3(tiledUV, textureIndex)).rg;
  return float2((layer.Roughness * roughnessAO.r), roughnessAO.g);
}

float CalculateBlendWeight(float splatValue, float height, float slope, uint materialType)
{
  float weight = splatValue;
  if (materialType == 2)
  {
    weight *= saturate(height - (HeightScale * 0.7f) / (HeightScale * 0.3f));
  }
  else if (materialType == 3)
  {
    weight *= saturate(1.0f - height / (HeightScale * 0.3f));
  }
  if (materialType == 1)
  {
    weight *= saturate(slope - 0.3f / 0.4f);
  }
  else if (materialType == 0)
  {
    weight *= saturate(1.0f - slope / 0.5f);
  }
  return weight;
}

float SampleShadow(float4 shadowCoord)
{
  float3 projCoords = shadowCoord.xyz / shadowCoord.w;
  projCoords = (projCoords * 0.5f) + 0.5f;
  if (projCoords.x < 0.0f || projCoords.x > 1.0f || projCoords.y < 0.0f || projCoords.y > 1.0f)
    return 1.0f;
  float shadow = 0.0f;
  float2 texelSize = 1.0f / float2(2048, 2048);
  for (int x = -1; x <= 1; (++x))
  {
    for (int y = -1; y <= 1; (++y))
    {
      float2 offset = (float2(x, y) * texelSize);
      shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, projCoords.xy + offset, projCoords.z);
    }
  }
  return shadow / 9.0f;
}

float3 CalculateEnvironmentReflection(float3 normal, float3 viewDir, float roughness)
{
  float3 reflectDir = reflect((-viewDir), normal);
  float mipLevel = (roughness * 7.0f);
  return SkyboxTexture.SampleLevel(TrilinearSampler, reflectDir, mipLevel).rgb;
}

PSOutput main(PSInput input)
{
  PSOutput output;
  float3 N = normalize(input.Normal);
  float3 T = normalize(input.Tangent);
  float3 B = normalize(input.Bitangent);
  float3 V = normalize(input.ViewDir);
  float4 splatWeights = SplatmapTexture.Sample(LinearSampler, input.TexCoord);
  float4 blendWeights;
  blendWeights.r = CalculateBlendWeight(splatWeights.r, input.Height, input.Slope, 0);
  blendWeights.g = CalculateBlendWeight(splatWeights.g, input.Height, input.Slope, 1);
  blendWeights.b = CalculateBlendWeight(splatWeights.b, input.Height, input.Slope, 2);
  blendWeights.a = CalculateBlendWeight(splatWeights.a, input.Height, input.Slope, 3);
  float totalWeight = dot(blendWeights, 1.0f);
  if (totalWeight > 0.0f)
    blendWeights /= totalWeight;
  else
    blendWeights = float4(1, 0, 0, 0);
  float4 finalAlbedo = float4(0, 0, 0, 0);
  float3 finalNormal = float3(0, 0, 0);
  float finalRoughness = 0.0f;
  float finalAO = 0.0f;
  float finalMetallic = 0.0f;
  if (blendWeights.r > 0.0f)
  {
    float4 grassAlbedo = SampleMaterialLayer(GrassLayer, 0, input.TexCoord);
    float3 grassNormal = SampleNormalLayer(GrassLayer, 0, input.TexCoord);
    float2 grassProps = SampleMaterialProperties(GrassLayer, 0, input.TexCoord);
    finalAlbedo += (grassAlbedo * blendWeights.r);
    finalNormal += (grassNormal * blendWeights.r);
    finalRoughness += (grassProps.r * blendWeights.r);
    finalAO += (grassProps.g * blendWeights.r);
    finalMetallic += (GrassLayer.Metallic * blendWeights.r);
  }
  if (blendWeights.g > 0.0f)
  {
    float4 rockAlbedo = SampleMaterialLayer(RockLayer, 1, input.TexCoord);
    float3 rockNormal = SampleNormalLayer(RockLayer, 1, input.TexCoord);
    float2 rockProps = SampleMaterialProperties(RockLayer, 1, input.TexCoord);
    finalAlbedo += (rockAlbedo * blendWeights.g);
    finalNormal += (rockNormal * blendWeights.g);
    finalRoughness += (rockProps.r * blendWeights.g);
    finalAO += (rockProps.g * blendWeights.g);
    finalMetallic += (RockLayer.Metallic * blendWeights.g);
  }
  if (blendWeights.b > 0.0f)
  {
    float4 snowAlbedo = SampleMaterialLayer(SnowLayer, 2, input.TexCoord);
    float3 snowNormal = SampleNormalLayer(SnowLayer, 2, input.TexCoord);
    float2 snowProps = SampleMaterialProperties(SnowLayer, 2, input.TexCoord);
    finalAlbedo += (snowAlbedo * blendWeights.b);
    finalNormal += (snowNormal * blendWeights.b);
    finalRoughness += (snowProps.r * blendWeights.b);
    finalAO += (snowProps.g * blendWeights.b);
    finalMetallic += (SnowLayer.Metallic * blendWeights.b);
  }
  if (blendWeights.a > 0.0f)
  {
    float4 sandAlbedo = SampleMaterialLayer(SandLayer, 3, input.TexCoord);
    float3 sandNormal = SampleNormalLayer(SandLayer, 3, input.TexCoord);
    float2 sandProps = SampleMaterialProperties(SandLayer, 3, input.TexCoord);
    finalAlbedo += (sandAlbedo * blendWeights.a);
    finalNormal += (sandNormal * blendWeights.a);
    finalRoughness += (sandProps.r * blendWeights.a);
    finalAO += (sandProps.g * blendWeights.a);
    finalMetallic += (SandLayer.Metallic * blendWeights.a);
  }
  float3 detailNormal = DetailNormalTexture.Sample(TrilinearSampler, input.DetailUV).rgb;
  detailNormal = UnpackNormal(detailNormal);
  finalNormal = BlendNormals(finalNormal, detailNormal, 0.5f);
  float3x3 TBN = float3x3(T, B, N);
  float3 worldNormal = normalize(mul(finalNormal, TBN));
  float3 L = normalize((-LightDirection));
  float NdotL = max(dot(worldNormal, L), 0.0f);
  float shadow = SampleShadow(input.ShadowCoord);
  float3 envReflection = CalculateEnvironmentReflection(worldNormal, V, finalRoughness);
  float3 ambient = (float3(0.1f, 0.1f, 0.15f) * finalAO);
  float3 diffuse = ((finalAlbedo.rgb * NdotL) * shadow);
  float3 specular = ((envReflection * 1.0f - finalRoughness) * finalMetallic);
  float3 finalColor = ambient + diffuse + specular;
  float fogFactor = saturate(input.FogFactor - FogStart / FogEnd - FogStart);
  finalColor = lerp(finalColor, FogColor, (fogFactor * FogDensity));
  output.Albedo = float4(finalColor, finalAlbedo.a);
  output.Normal = float4((worldNormal * 0.5f) + 0.5f, finalRoughness);
  output.Material = float4(finalMetallic, finalRoughness, finalAO, input.Height / HeightScale);
  output.Motion = float4(0, 0, input.Position.z / input.Position.w, 1.0f);
  return output;
}

