// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

cbuffer StressTestParams : register(b0)
{
  uint WorkGroupSize;
  uint TotalWorkItems;
  uint ComplexityLevel;
  uint TestType;
  float Time;
  float3 Padding;
}

RWStructuredBuffer<float4> InputBuffer : register(u0);
RWStructuredBuffer<float4> OutputBuffer : register(u1);
RWTexture2D<float4> OutputTexture : register(u2);
Texture2D<float4> InputTexture : register(t0);
SamplerState LinearSampler : register(s0);
[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
  uint index = id.x;
  if (index >= TotalWorkItems)
    return ;
  float4 result = float4(0, 0, 0, 0);
  if (TestType == 0)
  {
    float4 data = InputBuffer[index];
    for (uint i = 0; i < ComplexityLevel; (i++))
    {
      float t = Time + (float(i) * 0.01f);
      result.x += (sin((data.x * t)) * cos((data.y * t)));
      result.y += (tan((data.z * t)) * atan((data.w * t)));
      result.z += (exp((data.x * 0.1f)) * log(abs(data.y) + 1.0f));
      result.w += (pow(abs(data.z), 2.5f) * sqrt(abs(data.w) + 1.0f));
      float complex1 = ((sin(data.x + t) * cos(data.y + t)) * exp(((-data.z) * 0.1f)));
      float complex2 = (pow(abs(sin(data.w + t)), 3.0f) * log(abs((data.x * data.y)) + 1.0f));
      result += float4(complex1, complex2, (complex1 * complex2), complex1 + complex2);
      data = (data * 1.01f) + (result * 0.01f);
    }
  }
  else if (TestType == 1)
  {
    for (uint i = 0; i < ComplexityLevel; (i++))
    {
      uint randomIndex = ((index * 1664525) + 1013904223 + i % TotalWorkItems);
      float4 data = InputBuffer[randomIndex];
      result += (data * sin(float(i) + Time));
      uint writeIndex = ((randomIndex * 1103515245) + 12345 % TotalWorkItems);
      OutputBuffer[writeIndex] = (result * 0.1f);
    }
  }
  else if (TestType == 2)
  {
    uint2 texSize;
    InputTexture.GetDimensions(texSize.x, texSize.y);
    for (uint i = 0; i < ComplexityLevel; (i++))
    {
      float2 uv = float2(float(index + i) / float(TotalWorkItems), (sin(Time + float(i)) * 0.5f) + 0.5f);
      result += InputTexture.SampleLevel(LinearSampler, uv, 0);
      result += InputTexture.SampleLevel(LinearSampler, (uv * 2.0f), 1);
      result += InputTexture.SampleLevel(LinearSampler, (uv * 4.0f), 2);
      result += InputTexture.SampleLevel(LinearSampler, (uv * 8.0f), 3);
      float2 ddx = float2(1.0f / texSize.x, 0);
      float2 ddy = float2(0, 1.0f / texSize.y);
      result += InputTexture.SampleGrad(LinearSampler, uv, ddx, ddy);
    }
    uint2 outputCoord = uint2((index % texSize.x), index / texSize.x);
    if (outputCoord.y < texSize.y)
    {
      OutputTexture[outputCoord] = result / float((ComplexityLevel * 5));
    }
  }
  else if (TestType == 3)
  {
    float4 data = InputBuffer[index];
    for (uint i = 0; i < ComplexityLevel; (i++))
    {
      float mathResult = (sin(data.x + Time) * cos(data.y + Time));
      mathResult += (pow(abs(data.z), 2.0f) * exp(((-data.w) * 0.1f)));
      uint memIndex = (index + (i * 17) % TotalWorkItems);
      float4 memData = InputBuffer[memIndex];
      float2 uv = float2((mathResult * 0.5f) + 0.5f, float(i) / float(ComplexityLevel));
      float4 texData = InputTexture.SampleLevel(LinearSampler, uv, 0);
      result += float4(mathResult, 0, 0, 0) + (memData * 0.1f) + (texData * 0.1f);
      data = lerp(data, result, 0.1f);
    }
    result = (normalize(result) * length(data));
    result += float4(sin((result.x * 10.0f)), cos((result.y * 10.0f)), tan((result.z * 5.0f)), atan((result.w * 5.0f)));
  }
  OutputBuffer[index] = result;
}

