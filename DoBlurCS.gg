{"$schema": "gigischema.json", "version": "1.0", "variables": [{"name": "BlurRadius", "type": "Int", "dflt": "1", "visibility": "User"}], "shaders": [{"name": "DoBlurCS", "fileName": "BoxBlurCS.hlsl", "entryPoint": "csmain", "resources": [{"name": "input", "access": "SRV", "type": "Texture"}, {"name": "output", "access": "UAV", "type": "Texture"}]}], "nodes": [{"resourceTexture": {"name": "Input", "editorPos": [-5.0, -14.0], "visibility": "Imported"}}, {"resourceTexture": {"name": "Output", "editorPos": [-5.0, 50.0], "visibility": "Exported", "format": {"node": {"name": "Input"}}, "size": {"node": {"name": "Input"}}}}, {"actionComputeShader": {"name": "Do<PERSON>lur", "editorPos": [123.0, -14.0], "linkProperties": [{}, {}, {}], "connections": [{"srcPin": "input", "dstNode": "Input", "dstPin": "resource"}, {"srcPin": "output", "dstNode": "Output", "dstPin": "resource"}], "shader": {"name": "DoBlurCS"}, "dispatchSize": {"node": {"name": "Input"}, "indirectBuffer": {"node": "Do<PERSON>lur"}}}}]}