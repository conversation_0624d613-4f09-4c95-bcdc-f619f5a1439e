// Unnamed technique, shader ModelViewerPS
struct ViewModes
{
    static const int InputPos = 0;
    static const int OutputPos = 1;
    static const int VertexID = 2;
    static const int InstanceID = 3;
    static const int Color = 4;
    static const int Normal = 5;
    static const int Tangent = 6;
    static const int UV = 7;
    static const int MaterialID = 8;
    static const int ShapeID = 9;
    static const int ViewerColor = 10;
    static const int PlasticShaded = 11;
};

struct Struct__MeshViewerPSCB
{
    float3 CameraPos;
    int ViewMode;
};

ConstantBuffer<Struct__MeshViewerPSCB> _MeshViewerPSCB : register(b0);


// Define some constants
#define M_PI 3.1415926535897932384626433832795
#define M_INV_PI 0.31830988618379067153776752674503

struct PSInput // AKA VSOutput
{
	float4 Position   : SV_POSITION;
	float4 Color      : TEXCOORD0;
	float3 Normal     : NORMAL;
	float3 WorldPos   : POSITION;
};

struct PSOutput
{
	float4 colorTargetF32 : SV_Target0;
};

float3 LinearToSRGB(float3 linearCol)
{
    float3 sRGBLo = linearCol * 12.92;
    float3 sRGBHi = (pow(abs(linearCol), float3(1.0 / 2.4, 1.0 / 2.4, 1.0 / 2.4)) * 1.055) - 0.055;
    float3 sRGB;
    sRGB.r = linearCol.r <= 0.0031308 ? sRGBLo.r : sRGBHi.r;
    sRGB.g = linearCol.g <= 0.0031308 ? sRGBLo.g : sRGBHi.g;
    sRGB.b = linearCol.b <= 0.0031308 ? sRGBLo.b : sRGBHi.b;
    return sRGB;
}

float3 fresnelSchlick(float cosTheta, float3 F0)
{
	return F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
}

float D_GGX(float NoH, float alpha)
{
	float alpha2 = alpha * alpha;
	float d = (NoH * NoH) * (alpha2 - 1.0) + 1.0; // d : temporary denominator 
	return alpha2 * M_INV_PI / (d * d);
}

float G1_GGX_Schlick(float NoV, float alpha) {
  float k = alpha / 2.0;
  return max(NoV, 0.001) / (NoV * (1.0 - k) + k);
}

float G_Smith(float NoV, float NoL, float alpha) {
  return G1_GGX_Schlick(NoL, alpha) * G1_GGX_Schlick(NoV, alpha);
}

float3 microfacetBRDF(float3 L, float3 V, float3 N, float metallic, float roughness, float3 baseColor, float specularlevel) 
{
  float3 H = normalize(V + L); // half vector
  
  float NoV = clamp(dot(N, V), 0.0, 1.0);
  float NoL = clamp(dot(N, L), 0.0, 1.0);
  float NoH = clamp(dot(N, H), 0.0, 1.0);
  float VoH = clamp(dot(V, H), 0.0, 1.0);
  
  float3 f0 = 0.16 * (specularlevel * specularlevel); // Disney's specualr reflectance parameterization for dialectric materials
  f0 = lerp(f0, baseColor, metallic); 

  float alpha = roughness * roughness; // alpha a = roughness^2 : From Disney parameterization to be perceptually linear
  
  float3 F = fresnelSchlick(VoH, f0); // fresnel term
  float D = D_GGX(NoH, alpha); // normal distribution function
  float G = G_Smith(NoV, NoL, alpha); // geometric shadowing function matching GGX NDF
  
  float3 specular = (F * D * G) / (4.0 * max(NoV, 0.001) * max(NoL, 0.001)); 
  
  float3 rhoD = baseColor; 
  rhoD *= 1.0 - F; // Reduce diffuse based on energy lost to fresnel specular increase but no F0 adjustemnt 
  rhoD *= (1.0 - metallic);

  float3 diffuse = rhoD * M_INV_PI;
  
  return diffuse + specular;

}
PSOutput main(PSInput input)
{
	PSOutput ret = (PSOutput)0;
	if (_MeshViewerPSCB.ViewMode == ViewModes::PlasticShaded)
	{
		float3 cameraPosition = _MeshViewerPSCB.CameraPos;
		float3 viewDirection = normalize(cameraPosition - input.WorldPos);

		// 固定球形光源参数 (与光线追踪版本一致)
		float3 lightPosition = float3(2.0f, 3.0f, 2.0f);  // 光源位置
		float lightRadius = 0.5f;  // 光源半径
		float3 lightColor = float3(1.0f, 1.0f, 1.0f);  // 光源颜色
		float lightIntensity = 2.0f;  // 光源强度

		// 材质参数 (与光线追踪版本一致)
		float3 basecolor = float3(0.8f, 0.8f, 0.8f);  // 基础颜色
		float roughness = 0.4f;  // 粗糙度
		float specularlevel = 0.5f;  // 镜面反射级别
		float3 normal = input.Normal;
		float metallic = 0.0f;  // 金属度

		// 计算到光源的距离和方向
		float3 lightDirection = normalize(lightPosition - input.WorldPos);
		float distanceToLight = length(lightPosition - input.WorldPos);
		
		// 球形光源衰减
		float attenuation = 1.0f / (1.0f + distanceToLight * distanceToLight);
		float3 lightPerpendicularIrradiance = lightIntensity * lightColor * attenuation;

		// PBR光照计算
		float3 irradiance = max(dot(lightDirection, normal), 0.0) * lightPerpendicularIrradiance; 
		float3 brdf = microfacetBRDF(lightDirection, viewDirection, normal, metallic, roughness, basecolor, specularlevel);
		float3 radiance = irradiance * brdf;

		// 环境光照
		radiance += float3(0.02f, 0.02f, 0.02f);

		ret.colorTargetF32 = float4(radiance,1.0f);
	}
	else
	{
		ret.colorTargetF32 = input.Color;
	}
	return ret;
}
