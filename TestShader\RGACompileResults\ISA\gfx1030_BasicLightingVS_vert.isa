_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s22, s2, 0x90016                                 // 00000000000C: 9396FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s20, s9                                          // 000000000020: BE940309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s22, 12                                     // 00000000002C: 8F028C16
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v8, s3, 5, v1                                // 000000000040: D76F0008 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v8                            // 000000000048: 7D881001
	s_and_saveexec_b32 s23, vcc_lo                             // 00000000004C: BE973C6A
	s_cbranch_execz _L1                                        // 000000000050: BF880079
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s21, s3                                          // 000000000060: BE950303
	s_load_dwordx16 s[4:19], s[10:11], null                    // 000000000064: F4100105 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[13:15], v5, s[4:7], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80010D05
	tbuffer_load_format_xyz v[16:18], v5, s[8:11], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80021005
	tbuffer_load_format_xyzw v[1:4], v5, s[16:19], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 000000000080: EA6B2000 80040105
	tbuffer_load_format_xy v[6:7], v5, s[12:15], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000088: EA012000 80030605
	s_load_dwordx4 s[16:19], s[20:21], null                    // 000000000090: F408040A FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	s_clause 0xd                                               // 00000000009C: BFA1000D
	s_buffer_load_dwordx2 s[20:21], s[16:19], 0xc0             // 0000000000A0: F4240508 FA0000C0
	s_buffer_load_dword s33, s[16:19], 0xc8                    // 0000000000A8: F4200848 FA0000C8
	s_buffer_load_dwordx8 s[24:31], s[16:19], 0x40             // 0000000000B0: F42C0608 FA000040
	s_buffer_load_dwordx8 s[36:43], s[16:19], 0x60             // 0000000000B8: F42C0908 FA000060
	s_buffer_load_dwordx2 s[34:35], s[16:19], 0x80             // 0000000000C0: F4240888 FA000080
	s_buffer_load_dwordx2 s[44:45], s[16:19], 0x90             // 0000000000C8: F4240B08 FA000090
	s_buffer_load_dwordx2 s[46:47], s[16:19], 0xa0             // 0000000000D0: F4240B88 FA0000A0
	s_buffer_load_dword s50, s[16:19], 0xa8                    // 0000000000D8: F4200C88 FA0000A8
	s_buffer_load_dwordx8 s[8:15], s[16:19], null              // 0000000000E0: F42C0208 FA000000
	s_buffer_load_dwordx8 s[0:7], s[16:19], 0x20               // 0000000000E8: F42C0008 FA000020
	s_buffer_load_dword s51, s[16:19], 0x98                    // 0000000000F0: F4200CC8 FA000098
	s_buffer_load_dword s52, s[16:19], 0x88                    // 0000000000F8: F4200D08 FA000088
	s_buffer_load_dwordx2 s[48:49], s[16:19], 0xd0             // 000000000100: F4240C08 FA0000D0
	s_buffer_load_dword s16, s[16:19], 0xd8                    // 000000000108: F4200408 FA0000D8
	s_waitcnt lgkmcnt(0)                                       // 000000000110: BF8CC07F
	v_mul_f32_e64 v5, s21, s21                                 // 000000000114: D5080005 00002A15
	v_fmac_f32_e64 v5, s20, s20                                // 00000000011C: D52B0005 00002814
	v_fmac_f32_e64 v5, s33, s33                                // 000000000124: D52B0005 00004221
	v_rsq_f32_e32 v5, v5                                       // 00000000012C: 7E0A5D05
	v_mul_legacy_f32_e64 v9, -s20, v5                          // 000000000130: D5070009 20020A14
	s_waitcnt vmcnt(3)                                         // 000000000138: BF8C3F73
	v_fma_f32 v11, s28, v13, s31                               // 00000000013C: D54B000B 007E1A1C
	v_fma_f32 v10, s24, v13, s27                               // 000000000144: D54B000A 006E1A18
	s_waitcnt vmcnt(2)                                         // 00000000014C: BF8C3F72
	v_mul_f32_e32 v21, s44, v16                                // 000000000150: 102A202C
	v_fma_f32 v12, s36, v13, s39                               // 000000000154: D54B000C 009E1A24
	v_mul_f32_e32 v20, s34, v16                                // 00000000015C: 10282022
	v_fmac_f32_e32 v11, s29, v14                               // 000000000160: 56161C1D
	v_mul_f32_e32 v22, s46, v16                                // 000000000164: 102C202E
	v_fmac_f32_e32 v10, s25, v14                               // 000000000168: 56141C19
	v_fmac_f32_e32 v21, s45, v17                               // 00000000016C: 562A222D
	v_fmac_f32_e32 v12, s37, v14                               // 000000000170: 56181C25
	v_fmac_f32_e32 v11, s30, v15                               // 000000000174: 56161E1E
	v_fmac_f32_e32 v20, s35, v17                               // 000000000178: 56282223
	v_fmac_f32_e32 v22, s47, v17                               // 00000000017C: 562C222F
	v_fmac_f32_e32 v10, s26, v15                               // 000000000180: 56141E1A
	v_fmac_f32_e32 v21, s51, v18                               // 000000000184: 562A2433
	v_sub_f32_e32 v23, s49, v11                                // 000000000188: 082E1631
	v_fma_f32 v19, s40, v13, s43                               // 00000000018C: D54B0013 00AE1A28
	v_fmac_f32_e32 v12, s38, v15                               // 000000000194: 56181E26
	v_fmac_f32_e32 v20, s52, v18                               // 000000000198: 56282434
	v_fmac_f32_e32 v22, s50, v18                               // 00000000019C: 562C2432
	v_mul_f32_e32 v17, v21, v21                                // 0000000001A0: 10222B15
	v_sub_f32_e32 v24, s48, v10                                // 0000000001A4: 08301430
	v_mul_f32_e32 v18, v23, v23                                // 0000000001A8: 10242F17
	v_fmac_f32_e32 v19, s41, v14                               // 0000000001AC: 56261C29
	v_sub_f32_e32 v25, s16, v12                                // 0000000001B0: 08321810
	v_fmac_f32_e32 v17, v20, v20                               // 0000000001B4: 56222914
	v_mul_f32_e32 v13, s8, v10                                 // 0000000001B8: 101A1408
	v_fmac_f32_e32 v18, v24, v24                               // 0000000001BC: 56243118
	v_fmac_f32_e32 v19, s42, v15                               // 0000000001C0: 56261E2A
	v_mul_f32_e32 v14, s12, v10                                // 0000000001C4: 101C140C
	v_mul_f32_e32 v15, s0, v10                                 // 0000000001C8: 101E1400
	v_mul_f32_e32 v16, s4, v10                                 // 0000000001CC: 10201404
	v_fmac_f32_e32 v17, v22, v22                               // 0000000001D0: 56222D16
	v_fmac_f32_e32 v18, v25, v25                               // 0000000001D4: 56243319
	v_fmac_f32_e32 v13, s9, v11                                // 0000000001D8: 561A1609
	v_fmac_f32_e32 v14, s13, v11                               // 0000000001DC: 561C160D
	v_fmac_f32_e32 v15, s1, v11                                // 0000000001E0: 561E1601
	v_fmac_f32_e32 v16, s5, v11                                // 0000000001E4: 56201605
	v_rsq_f32_e32 v26, v17                                     // 0000000001E8: 7E345D11
	v_rsq_f32_e32 v27, v18                                     // 0000000001EC: 7E365D12
	v_fmac_f32_e32 v13, s10, v12                               // 0000000001F0: 561A180A
	v_fmac_f32_e32 v14, s14, v12                               // 0000000001F4: 561C180E
	v_fmac_f32_e32 v15, s2, v12                                // 0000000001F8: 561E1802
	v_fmac_f32_e32 v16, s6, v12                                // 0000000001FC: 56201806
	v_mul_legacy_f32_e64 v17, -s21, v5                         // 000000000200: D5070011 20020A15
	v_fmac_f32_e32 v13, s11, v19                               // 000000000208: 561A260B
	v_fmac_f32_e32 v14, s15, v19                               // 00000000020C: 561C260F
	v_fmac_f32_e32 v15, s3, v19                                // 000000000210: 561E2603
	v_fmac_f32_e32 v16, s7, v19                                // 000000000214: 56202607
	v_mul_legacy_f32_e32 v18, v20, v26                         // 000000000218: 0E243514
	v_mul_legacy_f32_e32 v19, v21, v26                         // 00000000021C: 0E263515
	v_mul_legacy_f32_e32 v21, v22, v26                         // 000000000220: 0E2A3516
	v_mul_legacy_f32_e32 v22, v24, v27                         // 000000000224: 0E2C3718
	v_mul_legacy_f32_e32 v23, v23, v27                         // 000000000228: 0E2E3717
	v_mul_legacy_f32_e32 v24, v25, v27                         // 00000000022C: 0E303719
	v_mul_legacy_f32_e64 v20, -s33, v5                         // 000000000230: D5070014 20020A21
_L1:
	s_or_b32 exec_lo, exec_lo, s23                             // 000000000238: 887E177E
	s_mov_b32 s1, exec_lo                                      // 00000000023C: BE81037E
	v_cmpx_gt_u32_e64 s22, v8                                  // 000000000240: D4D4007E 00021016
	s_cbranch_execz _L2                                        // 000000000248: BF880002
	exp prim v0, off, off, off done                            // 00000000024C: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 000000000254: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000258: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 00000000025C: BE803C6A
	s_cbranch_execz _L3                                        // 000000000260: BF880010
	exp pos0 v13, v14, v15, v16 done                           // 000000000264: F80008CF 100F0E0D
	exp param5 v9, v17, v20, off                               // 00000000026C: F8000257 00141109
	s_waitcnt vmcnt(1)                                         // 000000000274: BF8C3F71
	exp param3 v1, v2, v3, v4                                  // 000000000278: F800023F 04030201
	exp param1 v18, v19, v21, off                              // 000000000280: F8000217 00151312
	exp param4 v22, v23, v24, off                              // 000000000288: F8000247 00181716
	s_waitcnt vmcnt(0)                                         // 000000000290: BF8C3F70
	exp param2 v6, v7, off, off                                // 000000000294: F8000223 00000706
	exp param0 v10, v11, v12, off                              // 00000000029C: F8000207 000C0B0A
_L3:
	s_endpgm                                                   // 0000000002A4: BF810000
