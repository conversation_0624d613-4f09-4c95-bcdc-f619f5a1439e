// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

cbuffer ComputeParams : register(b0)
{
  uint ParticleCount;
  uint MaxParticles;
  float DeltaTime;
  float Time;
  float3 Gravity;
  float Damping;
  float3 EmitterPosition;
  float EmissionRate;
  float3 EmitterDirection;
  float EmissionSpeed;
  float2 LifetimeRange;
  float2 SizeRange;
  uint FrameCount;
  float NoiseScale;
  float NoiseStrength;
  uint _padding;
}

RWTexture3D<float4> VolumeTexture : register(u0);
float Hash(float n)
{
  return frac((sin(n) * 43758.5453f));
}

float Noise(float3 x)
{
  float3 p = floor(x);
  float3 f = frac(x);
  f = ((f * f) * 3.0f - (2.0f * f));
  float n = p.x + (p.y * 57.0f) + (113.0f * p.z);
  return lerp(lerp(lerp(Hash(n + 0.0f), Hash(n + 1.0f), f.x), lerp(Hash(n + 57.0f), Hash(n + 58.0f), f.x), f.y), lerp(lerp(Hash(n + 113.0f), Hash(n + 114.0f), f.x), lerp(Hash(n + 170.0f), Hash(n + 171.0f), f.x), f.y), f.z);
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
  uint3 dimensions;
  VolumeTexture.GetDimensions(dimensions.x, dimensions.y, dimensions.z);
  if (any(id >= dimensions))
    return ;
  float3 uvw = float3(id) / float3(dimensions - 1);
  float3 worldPos = (uvw - 0.5f * 100.0f);
  float density = 0.0f;
  density += (Noise((worldPos * 0.01f) + (Time * 0.1f)) * 0.5f);
  density += (Noise((worldPos * 0.02f) + (Time * 0.05f)) * 0.3f);
  density += (Noise((worldPos * 0.04f) + (Time * 0.02f)) * 0.2f);
  density = (saturate(density - 0.3f) * 2.0f);
  float4 color = float4(1.0f, 1.0f, 1.0f, density);
  VolumeTexture[id] = color;
}

