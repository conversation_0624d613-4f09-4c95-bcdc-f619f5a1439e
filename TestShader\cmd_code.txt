rga -s dx12 --offline --vs "SimpleVS.hlsl" --vs-entry main --vs-model vs_6_0 --ps "SimplePS.hlsl" --ps-entry main --ps-model ps_6_0 -c gfx1030 --binary "simple_binary.bin" 

rga -s dx12 --offline --vs "MeshViewerVS.hlsl" --vs-entry vsmain --vs-model vs_6_0 --ps "MeshViewerPS.hlsl" --ps-entry psmain --ps-model ps_6_0 -c gfx1030 --binary "MeshViewer_binary.bin" 

rga -s dx12 --offline --vs "MeshViewerVS.hlsl" --vs-entry vsmain --vs-model vs_6_0  -c gfx1030 --binary "just_vs_binary.bin"



malioc -c Mali-G76 complex.vert
malioc -c Mali-G76 simple.frag --format json