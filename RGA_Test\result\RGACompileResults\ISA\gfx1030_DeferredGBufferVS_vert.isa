_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v8, s3, 5, v1                                // 000000000040: D76F0008 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v8                            // 000000000048: 7D881001
	s_and_saveexec_b32 s34, vcc_lo                             // 00000000004C: BEA23C6A
	s_cbranch_execz _L1                                        // 000000000050: BF880080
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s5, s3                                           // 000000000060: BE850303
	s_load_dwordx16 s[12:27], s[10:11], null                   // 000000000064: F4100305 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[16:18], v5, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80031005
	tbuffer_load_format_xyz v[19:21], v5, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80041305
	tbuffer_load_format_xyz v[22:24], v5, s[20:23], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000080: EA522000 80051605
	tbuffer_load_format_xyz v[25:27], v5, s[24:27], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000088: EA522000 80061905
	s_load_dwordx8 s[8:15], s[10:11], 0x40                     // 000000000090: F40C0205 FA000040
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	tbuffer_load_format_xyzw v[1:4], v5, s[12:15], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 00000000009C: EA6B2000 80030105
	tbuffer_load_format_xy v[6:7], v5, s[8:11], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 0000000000A4: EA012000 80020605
	s_load_dwordx4 s[28:31], s[4:5], null                      // 0000000000AC: F4080702 FA000000
	s_waitcnt lgkmcnt(0)                                       // 0000000000B4: BF8CC07F
	s_clause 0x9                                               // 0000000000B8: BFA10009
	s_buffer_load_dwordx8 s[0:7], s[28:31], null               // 0000000000BC: F42C000E FA000000
	s_buffer_load_dwordx4 s[24:27], s[28:31], 0x20             // 0000000000C4: F428060E FA000020
	s_buffer_load_dwordx8 s[8:15], s[28:31], 0xc0              // 0000000000CC: F42C020E FA0000C0
	s_buffer_load_dwordx8 s[16:23], s[28:31], 0xe0             // 0000000000D4: F42C040E FA0000E0
	s_buffer_load_dwordx2 s[36:37], s[28:31], 0x100            // 0000000000DC: F424090E FA000100
	s_buffer_load_dwordx2 s[38:39], s[28:31], 0x110            // 0000000000E4: F424098E FA000110
	s_buffer_load_dwordx2 s[40:41], s[28:31], 0x120            // 0000000000EC: F4240A0E FA000120
	s_buffer_load_dword s35, s[28:31], 0x118                   // 0000000000F4: F42008CE FA000118
	s_buffer_load_dword s42, s[28:31], 0x108                   // 0000000000FC: F4200A8E FA000108
	s_buffer_load_dword s28, s[28:31], 0x128                   // 000000000104: F420070E FA000128
	s_waitcnt vmcnt(5) lgkmcnt(0)                              // 00000000010C: BF8C0075
	v_fma_f32 v13, s0, v16, s3                                 // 000000000110: D54B000D 000E2000
	s_waitcnt vmcnt(4)                                         // 000000000118: BF8C3F74
	v_mul_f32_e32 v28, s38, v19                                // 00000000011C: 10382626
	s_waitcnt vmcnt(3)                                         // 000000000120: BF8C3F73
	v_mul_f32_e32 v30, s38, v22                                // 000000000124: 103C2C26
	s_waitcnt vmcnt(2)                                         // 000000000128: BF8C3F72
	v_mul_f32_e32 v32, s38, v25                                // 00000000012C: 10403226
	v_mul_f32_e32 v5, s36, v19                                 // 000000000130: 100A2624
	v_mul_f32_e32 v29, s36, v22                                // 000000000134: 103A2C24
	v_mul_f32_e32 v31, s36, v25                                // 000000000138: 103E3224
	v_fmac_f32_e32 v28, s39, v20                               // 00000000013C: 56382827
	v_fmac_f32_e32 v30, s39, v23                               // 000000000140: 563C2E27
	v_fmac_f32_e32 v32, s39, v26                               // 000000000144: 56403427
	v_fma_f32 v14, s4, v16, s7                                 // 000000000148: D54B000E 001E2004
	v_fma_f32 v15, s24, v16, s27                               // 000000000150: D54B000F 006E2018
	v_fma_f32 v9, s8, v16, s11                                 // 000000000158: D54B0009 002E2008
	v_fma_f32 v10, s12, v16, s15                               // 000000000160: D54B000A 003E200C
	v_fma_f32 v11, s16, v16, s19                               // 000000000168: D54B000B 004E2010
	v_fma_f32 v12, s20, v16, s23                               // 000000000170: D54B000C 005E2014
	v_mul_f32_e32 v19, s40, v19                                // 000000000178: 10262628
	v_mul_f32_e32 v22, s40, v22                                // 00000000017C: 102C2C28
	v_mul_f32_e32 v25, s40, v25                                // 000000000180: 10323228
	v_fmac_f32_e32 v5, s37, v20                                // 000000000184: 560A2825
	v_fmac_f32_e32 v29, s37, v23                               // 000000000188: 563A2E25
	v_fmac_f32_e32 v31, s37, v26                               // 00000000018C: 563E3425
	v_fmac_f32_e32 v28, s35, v21                               // 000000000190: 56382A23
	v_fmac_f32_e32 v30, s35, v24                               // 000000000194: 563C3023
	v_fmac_f32_e32 v32, s35, v27                               // 000000000198: 56403623
	v_fmac_f32_e32 v13, s1, v17                                // 00000000019C: 561A2201
	v_fmac_f32_e32 v14, s5, v17                                // 0000000001A0: 561C2205
	v_fmac_f32_e32 v15, s25, v17                               // 0000000001A4: 561E2219
	v_fmac_f32_e32 v9, s9, v17                                 // 0000000001A8: 56122209
	v_fmac_f32_e32 v10, s13, v17                               // 0000000001AC: 5614220D
	v_fmac_f32_e32 v11, s17, v17                               // 0000000001B0: 56162211
	v_fmac_f32_e32 v12, s21, v17                               // 0000000001B4: 56182215
	v_fmac_f32_e32 v19, s41, v20                               // 0000000001B8: 56262829
	v_fmac_f32_e32 v22, s41, v23                               // 0000000001BC: 562C2E29
	v_fmac_f32_e32 v25, s41, v26                               // 0000000001C0: 56323429
	v_fmac_f32_e32 v5, s42, v21                                // 0000000001C4: 560A2A2A
	v_mul_f32_e32 v16, v28, v28                                // 0000000001C8: 1020391C
	v_fmac_f32_e32 v29, s42, v24                               // 0000000001CC: 563A302A
	v_mul_f32_e32 v17, v30, v30                                // 0000000001D0: 10223D1E
	v_fmac_f32_e32 v31, s42, v27                               // 0000000001D4: 563E362A
	v_mul_f32_e32 v20, v32, v32                                // 0000000001D8: 10284120
	v_fmac_f32_e32 v19, s28, v21                               // 0000000001DC: 56262A1C
	v_fmac_f32_e32 v16, v5, v5                                 // 0000000001E0: 56200B05
	v_fmac_f32_e32 v22, s28, v24                               // 0000000001E4: 562C301C
	v_fmac_f32_e32 v17, v29, v29                               // 0000000001E8: 56223B1D
	v_fmac_f32_e32 v25, s28, v27                               // 0000000001EC: 5632361C
	v_fmac_f32_e32 v20, v31, v31                               // 0000000001F0: 56283F1F
	v_fmac_f32_e32 v16, v19, v19                               // 0000000001F4: 56202713
	v_fmac_f32_e32 v12, s22, v18                               // 0000000001F8: 56182416
	v_fmac_f32_e32 v17, v22, v22                               // 0000000001FC: 56222D16
	v_fmac_f32_e32 v11, s18, v18                               // 000000000200: 56162412
	v_fmac_f32_e32 v20, v25, v25                               // 000000000204: 56283319
	v_rsq_f32_e32 v21, v16                                     // 000000000208: 7E2A5D10
	v_rcp_f32_e32 v27, v12                                     // 00000000020C: 7E36550C
	v_rsq_f32_e32 v24, v17                                     // 000000000210: 7E305D11
	v_fmac_f32_e32 v13, s2, v18                                // 000000000214: 561A2402
	v_rsq_f32_e32 v26, v20                                     // 000000000218: 7E345D14
	v_fmac_f32_e32 v14, s6, v18                                // 00000000021C: 561C2406
	v_fmac_f32_e32 v15, s26, v18                               // 000000000220: 561E241A
	v_fmac_f32_e32 v9, s10, v18                                // 000000000224: 5612240A
	v_fmac_f32_e32 v10, s14, v18                               // 000000000228: 5614240E
	v_mul_legacy_f32_e32 v16, v5, v21                          // 00000000022C: 0E202B05
	v_mul_legacy_f32_e32 v17, v28, v21                         // 000000000230: 0E222B1C
	v_mul_legacy_f32_e32 v19, v19, v21                         // 000000000234: 0E262B13
	v_mul_legacy_f32_e32 v21, v29, v24                         // 000000000238: 0E2A311D
	v_mul_legacy_f32_e32 v23, v30, v24                         // 00000000023C: 0E2E311E
	v_mul_legacy_f32_e32 v24, v22, v24                         // 000000000240: 0E303116
	v_mul_legacy_f32_e32 v18, v31, v26                         // 000000000244: 0E24351F
	v_mul_legacy_f32_e32 v20, v32, v26                         // 000000000248: 0E283520
	v_mul_legacy_f32_e32 v22, v25, v26                         // 00000000024C: 0E2C3519
	v_mul_f32_e32 v25, v11, v27                                // 000000000250: 1032370B
_L1:
	s_or_b32 exec_lo, exec_lo, s34                             // 000000000254: 887E227E
	s_mov_b32 s1, exec_lo                                      // 000000000258: BE81037E
	v_cmpx_gt_u32_e64 s33, v8                                  // 00000000025C: D4D4007E 00021021
	s_cbranch_execz _L2                                        // 000000000264: BF880002
	exp prim v0, off, off, off done                            // 000000000268: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 000000000270: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000274: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 000000000278: BE803C6A
	s_cbranch_execz _L3                                        // 00000000027C: BF880012
	exp pos0 v9, v10, v11, v12 done                            // 000000000280: F80008CF 0C0B0A09
	s_waitcnt vmcnt(1)                                         // 000000000288: BF8C3F71
	exp param5 v1, v2, v3, v4                                  // 00000000028C: F800025F 04030201
	exp param3 v18, v20, v22, off                              // 000000000294: F8000237 00161412
	exp param1 v16, v17, v19, off                              // 00000000029C: F8000217 00131110
	exp param6 v25, off, off, off                              // 0000000002A4: F8000261 00000019
	s_waitcnt vmcnt(0)                                         // 0000000002AC: BF8C3F70
	exp param4 v6, v7, off, off                                // 0000000002B0: F8000243 00000706
	exp param2 v21, v23, v24, off                              // 0000000002B8: F8000227 00181715
	exp param0 v13, v14, v15, off                              // 0000000002C0: F8000207 000F0E0D
_L3:
	s_endpgm                                                   // 0000000002C8: BF810000
