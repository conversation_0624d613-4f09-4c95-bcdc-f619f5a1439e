; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 249
; Schema: 0
               OpCapability Shader
               OpCapability ImageQuery
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_ComputeParams "type.ComputeParams"
               OpMemberName %type_ComputeParams 0 "ParticleCount"
               OpMemberName %type_ComputeParams 1 "MaxParticles"
               OpMemberName %type_ComputeParams 2 "DeltaTime"
               OpMemberName %type_ComputeParams 3 "Time"
               OpMemberName %type_ComputeParams 4 "Gravity"
               OpMemberName %type_ComputeParams 5 "Damping"
               OpMemberName %type_ComputeParams 6 "EmitterPosition"
               OpMemberName %type_ComputeParams 7 "EmissionRate"
               OpMemberName %type_ComputeParams 8 "EmitterDirection"
               OpMemberName %type_ComputeParams 9 "EmissionSpeed"
               OpMemberName %type_ComputeParams 10 "LifetimeRange"
               OpMemberName %type_ComputeParams 11 "SizeRange"
               OpMemberName %type_ComputeParams 12 "FrameCount"
               OpMemberName %type_ComputeParams 13 "NoiseScale"
               OpMemberName %type_ComputeParams 14 "NoiseStrength"
               OpMemberName %type_ComputeParams 15 "_padding"
               OpName %ComputeParams "ComputeParams"
               OpName %type_3d_image "type.3d.image"
               OpName %VolumeTexture "VolumeTexture"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %ComputeParams DescriptorSet 0
               OpDecorate %ComputeParams Binding 0
               OpDecorate %VolumeTexture DescriptorSet 0
               OpDecorate %VolumeTexture Binding 0
               OpMemberDecorate %type_ComputeParams 0 Offset 0
               OpMemberDecorate %type_ComputeParams 1 Offset 4
               OpMemberDecorate %type_ComputeParams 2 Offset 8
               OpMemberDecorate %type_ComputeParams 3 Offset 12
               OpMemberDecorate %type_ComputeParams 4 Offset 16
               OpMemberDecorate %type_ComputeParams 5 Offset 28
               OpMemberDecorate %type_ComputeParams 6 Offset 32
               OpMemberDecorate %type_ComputeParams 7 Offset 44
               OpMemberDecorate %type_ComputeParams 8 Offset 48
               OpMemberDecorate %type_ComputeParams 9 Offset 60
               OpMemberDecorate %type_ComputeParams 10 Offset 64
               OpMemberDecorate %type_ComputeParams 11 Offset 72
               OpMemberDecorate %type_ComputeParams 12 Offset 80
               OpMemberDecorate %type_ComputeParams 13 Offset 84
               OpMemberDecorate %type_ComputeParams 14 Offset 88
               OpMemberDecorate %type_ComputeParams 15 Offset 92
               OpDecorate %type_ComputeParams Block
        %int = OpTypeInt 32 1
       %uint = OpTypeInt 32 0
     %uint_1 = OpConstant %uint 1
     %v3uint = OpTypeVector %uint 3
         %12 = OpConstantComposite %v3uint %uint_1 %uint_1 %uint_1
      %float = OpTypeFloat 32
   %float_50 = OpConstant %float 50
    %v3float = OpTypeVector %float 3
         %16 = OpConstantComposite %v3float %float_50 %float_50 %float_50
    %float_0 = OpConstant %float 0
%float_0_00999999978 = OpConstant %float 0.00999999978
      %int_3 = OpConstant %int 3
%float_0_100000001 = OpConstant %float 0.100000001
  %float_0_5 = OpConstant %float 0.5
%float_0_0199999996 = OpConstant %float 0.0199999996
%float_0_0500000007 = OpConstant %float 0.0500000007
%float_0_300000012 = OpConstant %float 0.300000012
%float_0_0399999991 = OpConstant %float 0.0399999991
%float_0_200000003 = OpConstant %float 0.200000003
    %float_1 = OpConstant %float 1
    %float_2 = OpConstant %float 2
    %float_3 = OpConstant %float 3
   %float_57 = OpConstant %float 57
  %float_113 = OpConstant %float 113
   %float_58 = OpConstant %float 58
  %float_114 = OpConstant %float 114
  %float_170 = OpConstant %float 170
  %float_171 = OpConstant %float 171
%float_43758_5469 = OpConstant %float 43758.5469
    %v2float = OpTypeVector %float 2
%type_ComputeParams = OpTypeStruct %uint %uint %float %float %v3float %float %v3float %float %v3float %float %v2float %v2float %uint %float %float %uint
%_ptr_Uniform_type_ComputeParams = OpTypePointer Uniform %type_ComputeParams
%type_3d_image = OpTypeImage %float 3D 2 0 0 2 Rgba32f
%_ptr_UniformConstant_type_3d_image = OpTypePointer UniformConstant %type_3d_image
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %42 = OpTypeFunction %void
    %v4float = OpTypeVector %float 4
       %bool = OpTypeBool
     %v3bool = OpTypeVector %bool 3
%_ptr_Uniform_float = OpTypePointer Uniform %float
%ComputeParams = OpVariable %_ptr_Uniform_type_ComputeParams Uniform
%VolumeTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
     %uint_0 = OpConstant %uint 0
       %main = OpFunction %void None %42
         %48 = OpLabel
         %49 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %50 None
               OpSwitch %uint_0 %51
         %51 = OpLabel
         %52 = OpLoad %type_3d_image %VolumeTexture
         %53 = OpImageQuerySize %v3uint %52
         %54 = OpUGreaterThanEqual %v3bool %49 %53
         %55 = OpAny %bool %54
               OpSelectionMerge %56 None
               OpBranchConditional %55 %57 %56
         %57 = OpLabel
               OpBranch %50
         %56 = OpLabel
         %58 = OpConvertUToF %v3float %49
         %59 = OpISub %v3uint %53 %12
         %60 = OpConvertUToF %v3float %59
         %61 = OpFDiv %v3float %58 %60
         %62 = OpFSub %v3float %61 %16
         %63 = OpVectorTimesScalar %v3float %62 %float_0_00999999978
         %64 = OpAccessChain %_ptr_Uniform_float %ComputeParams %int_3
         %65 = OpLoad %float %64
         %66 = OpFMul %float %65 %float_0_100000001
         %67 = OpCompositeConstruct %v3float %66 %66 %66
         %68 = OpFAdd %v3float %63 %67
         %69 = OpExtInst %v3float %1 Floor %68
         %70 = OpExtInst %v3float %1 Fract %68
         %71 = OpFMul %v3float %70 %70
         %72 = OpVectorTimesScalar %v3float %71 %float_3
         %73 = OpVectorTimesScalar %v3float %70 %float_2
         %74 = OpFSub %v3float %72 %73
         %75 = OpCompositeExtract %float %69 0
         %76 = OpCompositeExtract %float %69 1
         %77 = OpFMul %float %76 %float_57
         %78 = OpFAdd %float %75 %77
         %79 = OpCompositeExtract %float %69 2
         %80 = OpFMul %float %float_113 %79
         %81 = OpFAdd %float %78 %80
         %82 = OpExtInst %float %1 Sin %81
         %83 = OpFMul %float %82 %float_43758_5469
         %84 = OpExtInst %float %1 Fract %83
         %85 = OpFAdd %float %81 %float_1
         %86 = OpExtInst %float %1 Sin %85
         %87 = OpFMul %float %86 %float_43758_5469
         %88 = OpExtInst %float %1 Fract %87
         %89 = OpCompositeExtract %float %74 0
         %90 = OpExtInst %float %1 FMix %84 %88 %89
         %91 = OpFAdd %float %81 %float_57
         %92 = OpExtInst %float %1 Sin %91
         %93 = OpFMul %float %92 %float_43758_5469
         %94 = OpExtInst %float %1 Fract %93
         %95 = OpFAdd %float %81 %float_58
         %96 = OpExtInst %float %1 Sin %95
         %97 = OpFMul %float %96 %float_43758_5469
         %98 = OpExtInst %float %1 Fract %97
         %99 = OpExtInst %float %1 FMix %94 %98 %89
        %100 = OpCompositeExtract %float %74 1
        %101 = OpExtInst %float %1 FMix %90 %99 %100
        %102 = OpFAdd %float %81 %float_113
        %103 = OpExtInst %float %1 Sin %102
        %104 = OpFMul %float %103 %float_43758_5469
        %105 = OpExtInst %float %1 Fract %104
        %106 = OpFAdd %float %81 %float_114
        %107 = OpExtInst %float %1 Sin %106
        %108 = OpFMul %float %107 %float_43758_5469
        %109 = OpExtInst %float %1 Fract %108
        %110 = OpExtInst %float %1 FMix %105 %109 %89
        %111 = OpFAdd %float %81 %float_170
        %112 = OpExtInst %float %1 Sin %111
        %113 = OpFMul %float %112 %float_43758_5469
        %114 = OpExtInst %float %1 Fract %113
        %115 = OpFAdd %float %81 %float_171
        %116 = OpExtInst %float %1 Sin %115
        %117 = OpFMul %float %116 %float_43758_5469
        %118 = OpExtInst %float %1 Fract %117
        %119 = OpExtInst %float %1 FMix %114 %118 %89
        %120 = OpExtInst %float %1 FMix %110 %119 %100
        %121 = OpCompositeExtract %float %74 2
        %122 = OpExtInst %float %1 FMix %101 %120 %121
        %123 = OpFMul %float %122 %float_0_5
        %124 = OpVectorTimesScalar %v3float %62 %float_0_0199999996
        %125 = OpFMul %float %65 %float_0_0500000007
        %126 = OpCompositeConstruct %v3float %125 %125 %125
        %127 = OpFAdd %v3float %124 %126
        %128 = OpExtInst %v3float %1 Floor %127
        %129 = OpExtInst %v3float %1 Fract %127
        %130 = OpFMul %v3float %129 %129
        %131 = OpVectorTimesScalar %v3float %130 %float_3
        %132 = OpVectorTimesScalar %v3float %129 %float_2
        %133 = OpFSub %v3float %131 %132
        %134 = OpCompositeExtract %float %128 0
        %135 = OpCompositeExtract %float %128 1
        %136 = OpFMul %float %135 %float_57
        %137 = OpFAdd %float %134 %136
        %138 = OpCompositeExtract %float %128 2
        %139 = OpFMul %float %float_113 %138
        %140 = OpFAdd %float %137 %139
        %141 = OpExtInst %float %1 Sin %140
        %142 = OpFMul %float %141 %float_43758_5469
        %143 = OpExtInst %float %1 Fract %142
        %144 = OpFAdd %float %140 %float_1
        %145 = OpExtInst %float %1 Sin %144
        %146 = OpFMul %float %145 %float_43758_5469
        %147 = OpExtInst %float %1 Fract %146
        %148 = OpCompositeExtract %float %133 0
        %149 = OpExtInst %float %1 FMix %143 %147 %148
        %150 = OpFAdd %float %140 %float_57
        %151 = OpExtInst %float %1 Sin %150
        %152 = OpFMul %float %151 %float_43758_5469
        %153 = OpExtInst %float %1 Fract %152
        %154 = OpFAdd %float %140 %float_58
        %155 = OpExtInst %float %1 Sin %154
        %156 = OpFMul %float %155 %float_43758_5469
        %157 = OpExtInst %float %1 Fract %156
        %158 = OpExtInst %float %1 FMix %153 %157 %148
        %159 = OpCompositeExtract %float %133 1
        %160 = OpExtInst %float %1 FMix %149 %158 %159
        %161 = OpFAdd %float %140 %float_113
        %162 = OpExtInst %float %1 Sin %161
        %163 = OpFMul %float %162 %float_43758_5469
        %164 = OpExtInst %float %1 Fract %163
        %165 = OpFAdd %float %140 %float_114
        %166 = OpExtInst %float %1 Sin %165
        %167 = OpFMul %float %166 %float_43758_5469
        %168 = OpExtInst %float %1 Fract %167
        %169 = OpExtInst %float %1 FMix %164 %168 %148
        %170 = OpFAdd %float %140 %float_170
        %171 = OpExtInst %float %1 Sin %170
        %172 = OpFMul %float %171 %float_43758_5469
        %173 = OpExtInst %float %1 Fract %172
        %174 = OpFAdd %float %140 %float_171
        %175 = OpExtInst %float %1 Sin %174
        %176 = OpFMul %float %175 %float_43758_5469
        %177 = OpExtInst %float %1 Fract %176
        %178 = OpExtInst %float %1 FMix %173 %177 %148
        %179 = OpExtInst %float %1 FMix %169 %178 %159
        %180 = OpCompositeExtract %float %133 2
        %181 = OpExtInst %float %1 FMix %160 %179 %180
        %182 = OpFMul %float %181 %float_0_300000012
        %183 = OpFAdd %float %123 %182
        %184 = OpVectorTimesScalar %v3float %62 %float_0_0399999991
        %185 = OpFMul %float %65 %float_0_0199999996
        %186 = OpCompositeConstruct %v3float %185 %185 %185
        %187 = OpFAdd %v3float %184 %186
        %188 = OpExtInst %v3float %1 Floor %187
        %189 = OpExtInst %v3float %1 Fract %187
        %190 = OpFMul %v3float %189 %189
        %191 = OpVectorTimesScalar %v3float %190 %float_3
        %192 = OpVectorTimesScalar %v3float %189 %float_2
        %193 = OpFSub %v3float %191 %192
        %194 = OpCompositeExtract %float %188 0
        %195 = OpCompositeExtract %float %188 1
        %196 = OpFMul %float %195 %float_57
        %197 = OpFAdd %float %194 %196
        %198 = OpCompositeExtract %float %188 2
        %199 = OpFMul %float %float_113 %198
        %200 = OpFAdd %float %197 %199
        %201 = OpExtInst %float %1 Sin %200
        %202 = OpFMul %float %201 %float_43758_5469
        %203 = OpExtInst %float %1 Fract %202
        %204 = OpFAdd %float %200 %float_1
        %205 = OpExtInst %float %1 Sin %204
        %206 = OpFMul %float %205 %float_43758_5469
        %207 = OpExtInst %float %1 Fract %206
        %208 = OpCompositeExtract %float %193 0
        %209 = OpExtInst %float %1 FMix %203 %207 %208
        %210 = OpFAdd %float %200 %float_57
        %211 = OpExtInst %float %1 Sin %210
        %212 = OpFMul %float %211 %float_43758_5469
        %213 = OpExtInst %float %1 Fract %212
        %214 = OpFAdd %float %200 %float_58
        %215 = OpExtInst %float %1 Sin %214
        %216 = OpFMul %float %215 %float_43758_5469
        %217 = OpExtInst %float %1 Fract %216
        %218 = OpExtInst %float %1 FMix %213 %217 %208
        %219 = OpCompositeExtract %float %193 1
        %220 = OpExtInst %float %1 FMix %209 %218 %219
        %221 = OpFAdd %float %200 %float_113
        %222 = OpExtInst %float %1 Sin %221
        %223 = OpFMul %float %222 %float_43758_5469
        %224 = OpExtInst %float %1 Fract %223
        %225 = OpFAdd %float %200 %float_114
        %226 = OpExtInst %float %1 Sin %225
        %227 = OpFMul %float %226 %float_43758_5469
        %228 = OpExtInst %float %1 Fract %227
        %229 = OpExtInst %float %1 FMix %224 %228 %208
        %230 = OpFAdd %float %200 %float_170
        %231 = OpExtInst %float %1 Sin %230
        %232 = OpFMul %float %231 %float_43758_5469
        %233 = OpExtInst %float %1 Fract %232
        %234 = OpFAdd %float %200 %float_171
        %235 = OpExtInst %float %1 Sin %234
        %236 = OpFMul %float %235 %float_43758_5469
        %237 = OpExtInst %float %1 Fract %236
        %238 = OpExtInst %float %1 FMix %233 %237 %208
        %239 = OpExtInst %float %1 FMix %229 %238 %219
        %240 = OpCompositeExtract %float %193 2
        %241 = OpExtInst %float %1 FMix %220 %239 %240
        %242 = OpFMul %float %241 %float_0_200000003
        %243 = OpFAdd %float %183 %242
        %244 = OpFSub %float %243 %float_0_300000012
        %245 = OpExtInst %float %1 FClamp %244 %float_0 %float_1
        %246 = OpFMul %float %245 %float_2
        %247 = OpCompositeConstruct %v4float %float_1 %float_1 %float_1 %246
        %248 = OpLoad %type_3d_image %VolumeTexture
               OpImageWrite %248 %49 %247 None
               OpBranch %50
         %50 = OpLabel
               OpReturn
               OpFunctionEnd
