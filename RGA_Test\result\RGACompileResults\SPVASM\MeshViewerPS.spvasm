; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 116
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_NORMAL %in_var_POSITION %out_var_SV_Target0
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_ConstantBuffer_Struct__MeshViewerPSCB "type.ConstantBuffer.Struct__MeshViewerPSCB"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerPSCB 0 "CameraPos"
               OpMemberName %type_ConstantBuffer_Struct__MeshViewerPSCB 1 "ViewMode"
               OpName %_MeshViewerPSCB "_MeshViewerPSCB"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_NORMAL "in.var.NORMAL"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %main "main"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_NORMAL Location 1
               OpDecorate %in_var_POSITION Location 2
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %_MeshViewerPSCB DescriptorSet 0
               OpDecorate %_MeshViewerPSCB Binding 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB 0 Offset 0
               OpMemberDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB 1 Offset 12
               OpDecorate %type_ConstantBuffer_Struct__MeshViewerPSCB Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_1 = OpConstant %int 1
     %int_11 = OpConstant %int 11
      %float = OpTypeFloat 32
    %v4float = OpTypeVector %float 4
    %float_2 = OpConstant %float 2
    %float_3 = OpConstant %float 3
    %v3float = OpTypeVector %float 3
         %18 = OpConstantComposite %v3float %float_2 %float_3 %float_2
    %float_1 = OpConstant %float 1
%float_0_800000012 = OpConstant %float 0.800000012
         %21 = OpConstantComposite %v3float %float_0_800000012 %float_0_800000012 %float_0_800000012
    %float_0 = OpConstant %float 0
%float_0_0199999996 = OpConstant %float 0.0199999996
         %24 = OpConstantComposite %v3float %float_0_0199999996 %float_0_0199999996 %float_0_0199999996
    %float_4 = OpConstant %float 4
%float_0_00100000005 = OpConstant %float 0.00100000005
%float_0_318309873 = OpConstant %float 0.318309873
    %float_5 = OpConstant %float 5
%type_ConstantBuffer_Struct__MeshViewerPSCB = OpTypeStruct %v3float %int
%_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerPSCB = OpTypePointer Uniform %type_ConstantBuffer_Struct__MeshViewerPSCB
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %34 = OpTypeFunction %void
%_ptr_Uniform_int = OpTypePointer Uniform %int
       %bool = OpTypeBool
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_MeshViewerPSCB = OpVariable %_ptr_Uniform_type_ConstantBuffer_Struct__MeshViewerPSCB Uniform
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v4float Input
%in_var_NORMAL = OpVariable %_ptr_Input_v3float Input
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
         %38 = OpConstantComposite %v3float %float_2 %float_2 %float_2
%float_0_0399999991 = OpConstant %float 0.0399999991
         %40 = OpConstantComposite %v3float %float_0_0399999991 %float_0_0399999991 %float_0_0399999991
%float_0_0256000031 = OpConstant %float 0.0256000031
%float_0_00814873353 = OpConstant %float 0.00814873353
%float_0_0800000057 = OpConstant %float 0.0800000057
%float_1_03999996 = OpConstant %float 1.03999996
         %45 = OpConstantComposite %v3float %float_1_03999996 %float_1_03999996 %float_1_03999996
%float_n0_0399999619 = OpConstant %float -0.0399999619
         %47 = OpConstantComposite %v3float %float_n0_0399999619 %float_n0_0399999619 %float_n0_0399999619
       %main = OpFunction %void None %34
         %48 = OpLabel
         %49 = OpLoad %v4float %in_var_TEXCOORD0
         %50 = OpLoad %v3float %in_var_NORMAL
         %51 = OpLoad %v3float %in_var_POSITION
         %52 = OpAccessChain %_ptr_Uniform_int %_MeshViewerPSCB %int_1
         %53 = OpLoad %int %52
         %54 = OpIEqual %bool %53 %int_11
               OpSelectionMerge %55 None
               OpBranchConditional %54 %56 %57
         %56 = OpLabel
         %58 = OpAccessChain %_ptr_Uniform_v3float %_MeshViewerPSCB %int_0
         %59 = OpLoad %v3float %58
         %60 = OpFSub %v3float %59 %51
         %61 = OpExtInst %v3float %1 Normalize %60
         %62 = OpFSub %v3float %18 %51
         %63 = OpExtInst %v3float %1 Normalize %62
         %64 = OpExtInst %float %1 Length %62
         %65 = OpFMul %float %64 %64
         %66 = OpFAdd %float %float_1 %65
         %67 = OpVectorTimesScalar %v3float %38 %66
         %68 = OpDot %float %63 %50
         %69 = OpExtInst %float %1 NMax %68 %float_0
         %70 = OpVectorTimesScalar %v3float %67 %69
         %71 = OpFAdd %v3float %61 %63
         %72 = OpExtInst %v3float %1 Normalize %71
         %73 = OpDot %float %50 %61
         %74 = OpExtInst %float %1 FClamp %73 %float_0 %float_1
         %75 = OpDot %float %50 %63
         %76 = OpExtInst %float %1 FClamp %75 %float_0 %float_1
         %77 = OpDot %float %50 %72
         %78 = OpExtInst %float %1 FClamp %77 %float_0 %float_1
         %79 = OpDot %float %61 %72
         %80 = OpExtInst %float %1 FClamp %79 %float_0 %float_1
         %81 = OpFSub %float %float_1 %80
         %82 = OpExtInst %float %1 Pow %81 %float_5
         %83 = OpVectorTimesScalar %v3float %40 %82
         %84 = OpFSub %v3float %45 %83
         %85 = OpFMul %float %78 %78
         %86 = OpFMul %float %85 %float_0_0256000031
         %87 = OpFMul %float %86 %86
         %88 = OpFDiv %float %float_0_00814873353 %87
         %89 = OpExtInst %float %1 NMax %76 %float_0_00100000005
         %90 = OpFSub %float %76 %float_0_0800000057
         %91 = OpFDiv %float %89 %90
         %92 = OpFAdd %float %91 %float_0_0800000057
         %93 = OpExtInst %float %1 NMax %74 %float_0_00100000005
         %94 = OpFSub %float %74 %float_0_0800000057
         %95 = OpFDiv %float %93 %94
         %96 = OpFAdd %float %95 %float_0_0800000057
         %97 = OpFMul %float %92 %96
         %98 = OpVectorTimesScalar %v3float %84 %88
         %99 = OpVectorTimesScalar %v3float %98 %97
        %100 = OpFMul %float %float_4 %93
        %101 = OpFMul %float %100 %89
        %102 = OpCompositeConstruct %v3float %101 %101 %101
        %103 = OpFDiv %v3float %99 %102
        %104 = OpFAdd %v3float %83 %47
        %105 = OpFMul %v3float %21 %104
        %106 = OpVectorTimesScalar %v3float %105 %float_1
        %107 = OpVectorTimesScalar %v3float %106 %float_0_318309873
        %108 = OpFAdd %v3float %107 %103
        %109 = OpFMul %v3float %70 %108
        %110 = OpFAdd %v3float %109 %24
        %111 = OpCompositeExtract %float %110 0
        %112 = OpCompositeExtract %float %110 1
        %113 = OpCompositeExtract %float %110 2
        %114 = OpCompositeConstruct %v4float %111 %112 %113 %float_1
               OpBranch %55
         %57 = OpLabel
               OpBranch %55
         %55 = OpLabel
        %115 = OpPhi %v4float %114 %56 %49 %57
               OpStore %out_var_SV_Target0 %115
               OpReturn
               OpFunctionEnd
