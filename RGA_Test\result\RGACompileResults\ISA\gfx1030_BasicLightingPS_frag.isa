_amdgpu_ps_main:
	s_mov_b64 s[16:17], exec                                   // 000000000000: BE90047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 m0, s2                                           // 000000000008: BEFC0302
	s_getpc_b64 s[2:3]                                         // 00000000000C: BE821F00
	s_mov_b32 s0, s1                                           // 000000000010: BE800301
	s_mov_b32 s1, s3                                           // 000000000014: BE810303
	s_clause 0x1                                               // 000000000018: BFA10001
	s_load_dwordx4 s[12:15], s[0:1], 0x20                      // 00000000001C: F4080300 FA000020
	s_load_dwordx8 s[4:11], s[0:1], null                       // 000000000024: F40C0100 FA000000
	v_interp_p1_f32_e32 v2, v0, attr1.x                        // 00000000002C: C8080400
	v_interp_p1_f32_e32 v3, v0, attr1.y                        // 000000000030: C80C0500
	v_interp_p2_f32_e32 v2, v1, attr1.x                        // 000000000034: C8090401
	v_interp_p2_f32_e32 v3, v1, attr1.y                        // 000000000038: C80D0501
	s_and_b64 exec, exec, s[16:17]                             // 00000000003C: 87FE107E
	s_waitcnt lgkmcnt(0)                                       // 000000000040: BF8CC07F
	image_sample v[2:5], v[2:3], s[4:11], s[12:15] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000044: F0800F08 00610202
	v_interp_p1_f32_e32 v6, v0, attr0.y                        // 00000000004C: C8180100
	v_interp_p1_f32_e32 v7, v0, attr3.y                        // 000000000050: C81C0D00
	v_interp_p1_f32_e32 v9, v0, attr0.x                        // 000000000054: C8240000
	v_interp_p1_f32_e32 v8, v0, attr3.x                        // 000000000058: C8200C00
	v_interp_p1_f32_e32 v11, v0, attr0.z                       // 00000000005C: C82C0200
	v_interp_p2_f32_e32 v6, v1, attr0.y                        // 000000000060: C8190101
	v_interp_p2_f32_e32 v7, v1, attr3.y                        // 000000000064: C81D0D01
	v_interp_p2_f32_e32 v9, v1, attr0.x                        // 000000000068: C8250001
	v_interp_p1_f32_e32 v10, v0, attr3.z                       // 00000000006C: C8280E00
	v_interp_p2_f32_e32 v8, v1, attr3.x                        // 000000000070: C8210C01
	v_mul_f32_e32 v12, v6, v6                                  // 000000000074: 10180D06
	v_mul_f32_e32 v13, v7, v7                                  // 000000000078: 101A0F07
	v_interp_p2_f32_e32 v11, v1, attr0.z                       // 00000000007C: C82D0201
	v_interp_p2_f32_e32 v10, v1, attr3.z                       // 000000000080: C8290E01
	v_interp_p1_f32_e32 v14, v0, attr2.y                       // 000000000084: C8380900
	v_fmac_f32_e32 v12, v9, v9                                 // 000000000088: 56181309
	v_fmac_f32_e32 v13, v8, v8                                 // 00000000008C: 561A1108
	v_interp_p1_f32_e32 v15, v0, attr2.x                       // 000000000090: C83C0800
	v_interp_p1_f32_e32 v0, v0, attr2.z                        // 000000000094: C8000A00
	v_interp_p2_f32_e32 v14, v1, attr2.y                       // 000000000098: C8390901
	v_fmac_f32_e32 v12, v11, v11                               // 00000000009C: 5618170B
	v_fmac_f32_e32 v13, v10, v10                               // 0000000000A0: 561A150A
	v_interp_p2_f32_e32 v15, v1, attr2.x                       // 0000000000A4: C83D0801
	v_interp_p2_f32_e32 v0, v1, attr2.z                        // 0000000000A8: C8010A01
	v_mul_f32_e32 v18, v14, v14                                // 0000000000AC: 10241D0E
	v_rsq_f32_e32 v16, v12                                     // 0000000000B0: 7E205D0C
	v_rsq_f32_e32 v17, v13                                     // 0000000000B4: 7E225D0D
	v_cmp_neq_f32_e32 vcc_lo, 0, v12                           // 0000000000B8: 7C1A1880
	s_load_dwordx4 s[8:11], s[0:1], null                       // 0000000000BC: F4080200 FA000000
	v_fmac_f32_e32 v18, v15, v15                               // 0000000000C4: 56241F0F
	v_fmac_f32_e32 v18, v0, v0                                 // 0000000000C8: 56240100
	v_cndmask_b32_e32 v1, 0, v16, vcc_lo                       // 0000000000CC: 02022080
	v_cmp_neq_f32_e32 vcc_lo, 0, v13                           // 0000000000D0: 7C1A1A80
	v_rsq_f32_e32 v13, v18                                     // 0000000000D4: 7E1A5D12
	v_mul_f32_e32 v9, v1, v9                                   // 0000000000D8: 10121301
	v_cndmask_b32_e32 v12, 0, v17, vcc_lo                      // 0000000000DC: 02182280
	v_mul_f32_e32 v6, v1, v6                                   // 0000000000E0: 100C0D01
	v_mul_f32_e32 v1, v1, v11                                  // 0000000000E4: 10021701
	v_cmp_neq_f32_e32 vcc_lo, 0, v18                           // 0000000000E8: 7C1A2480
	v_mul_f32_e32 v8, v12, v8                                  // 0000000000EC: 1010110C
	v_mul_f32_e32 v7, v12, v7                                  // 0000000000F0: 100E0F0C
	v_mul_f32_e32 v10, v12, v10                                // 0000000000F4: 1014150C
	v_cndmask_b32_e32 v12, 0, v13, vcc_lo                      // 0000000000F8: 02181A80
	s_waitcnt lgkmcnt(0)                                       // 0000000000FC: BF8CC07F
	s_buffer_load_dwordx8 s[0:7], s[8:11], 0x10                // 000000000100: F42C0004 FA000010
	v_mul_f32_e32 v16, v9, v8                                  // 000000000108: 10201109
	s_buffer_load_dwordx4 s[12:15], s[8:11], null              // 00000000010C: F4280304 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000114: BF8CC07F
	s_buffer_load_dword s3, s[8:11], 0xc                       // 000000000118: F42000C4 FA00000C
	v_mul_f32_e32 v0, v12, v0                                  // 000000000120: 1000010C
	v_fma_f32 v11, v6, -v7, -v16                               // 000000000124: D54B000B C4420F06
	v_fma_f32 v13, v6, v7, v16                                 // 00000000012C: D54B000D 04420F06
	v_fma_f32 v11, -v1, v10, v11 mul:2                         // 000000000134: D54B000B 2C2E1501
	v_fmac_f32_e32 v13, v1, v10                                // 00000000013C: 561A1501
	v_fmac_f32_e32 v10, v1, v11                                // 000000000140: 56141701
	v_mul_f32_e32 v1, v12, v15                                 // 000000000144: 10021F0C
	v_fmac_f32_e32 v8, v9, v11                                 // 000000000148: 56101709
	v_mul_f32_e32 v9, v12, v14                                 // 00000000014C: 10121D0C
	v_fma_f32 v6, -v6, v11, -v7                                // 000000000150: D54B0006 A41E1706
	v_mul_f32_e32 v0, v10, v0                                  // 000000000158: 1000010A
	v_fmac_f32_e32 v0, v8, v1                                  // 00000000015C: 56000308
	v_max_f32_e32 v1, 0, v13                                   // 000000000160: 20021A80
	v_fma_f32 v0, v6, v9, -v0                                  // 000000000164: D54B0000 84021306
	v_max_f32_e32 v0, 0, v0                                    // 00000000016C: 20000080
	v_log_f32_e32 v0, v0                                       // 000000000170: 7E004F00
	v_mul_legacy_f32_e32 v0, s4, v0                            // 000000000174: 0E000004
	v_exp_f32_e32 v0, v0                                       // 000000000178: 7E004B00
	s_waitcnt vmcnt(0)                                         // 00000000017C: BF8C3F70
	v_mul_f32_e32 v6, s5, v2                                   // 000000000180: 100C0405
	v_mul_f32_e32 v7, s6, v3                                   // 000000000184: 100E0606
	v_mul_f32_e32 v8, s7, v4                                   // 000000000188: 10100807
	v_mul_f32_e32 v2, s12, v2                                  // 00000000018C: 1004040C
	v_mul_f32_e32 v3, s13, v3                                  // 000000000190: 1006060D
	v_mul_f32_e32 v4, s14, v4                                  // 000000000194: 1008080E
	v_fmac_f32_e32 v6, v2, v1                                  // 000000000198: 560C0302
	v_fmac_f32_e32 v7, v3, v1                                  // 00000000019C: 560E0303
	v_fmac_f32_e32 v8, v4, v1                                  // 0000000001A0: 56100304
	v_fmac_f32_e32 v6, s0, v0                                  // 0000000001A4: 560C0000
	v_fmac_f32_e32 v7, s1, v0                                  // 0000000001A8: 560E0001
	v_fmac_f32_e32 v8, s2, v0                                  // 0000000001AC: 56100002
	s_waitcnt lgkmcnt(0)                                       // 0000000001B0: BF8CC07F
	v_mul_f32_e32 v0, s3, v5                                   // 0000000001B4: 10000A03
	exp mrt0 v6, v7, v8, v0 done vm                            // 0000000001B8: F800180F 00080706
	s_endpgm                                                   // 0000000001C0: BF810000
