// Ray Tracing Test Compute Shader
// Tests ray tracing algorithms and advanced compute features

// Ray structure
struct Ray
{
    float3 Origin;
    float3 Direction;
    float MinT;
    float MaxT;
};

// Hit information
struct HitInfo
{
    bool Hit;
    float T;
    float3 Position;
    float3 Normal;
    float2 UV;
    uint MaterialID;
    float3 Tangent;
    float3 Bitangent;
};

// Sphere primitive
struct Sphere
{
    float3 Center;
    float Radius;
    uint MaterialID;
    float _padding;
};

// Triangle primitive
struct Triangle
{
    float3 V0, V1, V2;
    float3 N0, N1, N2;
    float2 UV0, UV1, UV2;
    uint MaterialID;
    float3 _padding;
};

// Material structure
struct Material
{
    float3 Albedo;
    float Metallic;
    float3 Emission;
    float Roughness;
    float IOR;
    float Transparency;
    float2 _padding;
};

// BVH node for acceleration structure
struct BVHNode
{
    float3 AABBMin;
    uint LeftChild;
    float3 AABBMax;
    uint RightChild;
    uint PrimitiveStart;
    uint PrimitiveCount;
    uint2 _padding;
};

// Constant buffer
cbuffer RayTracingParams : register(b0)
{
    float4x4 InverseViewMatrix;
    float4x4 InverseProjectionMatrix;
    float3 CameraPosition;
    float Time;
    uint FrameCount;
    uint MaxBounces;
    uint SamplesPerPixel;
    uint RandomSeed;
    float2 ScreenResolution;
    float2 JitterOffset;
    uint NumSpheres;
    uint NumTriangles;
    uint NumMaterials;
    uint _padding;
};

// Structured buffers
StructuredBuffer<Sphere> Spheres : register(t0);
StructuredBuffer<Triangle> Triangles : register(t1);
StructuredBuffer<Material> Materials : register(t2);
StructuredBuffer<BVHNode> BVHNodes : register(t3);

// Textures
RWTexture2D<float4> OutputTexture : register(u0);
RWTexture2D<float4> AccumulationTexture : register(u1);
Texture2D<float4> EnvironmentMap : register(t4);

SamplerState LinearSampler : register(s0);

// Shared memory for group operations
groupshared float3 SharedRadiance[64];
groupshared uint SharedSampleCount[64];

// Random number generation
uint WangHash(uint seed)
{
    seed = (seed ^ 61) ^ (seed >> 16);
    seed *= 9;
    seed = seed ^ (seed >> 4);
    seed *= 0x27d4eb2d;
    seed = seed ^ (seed >> 15);
    return seed;
}

float RandomFloat(inout uint seed)
{
    seed = WangHash(seed);
    return float(seed) / 4294967296.0;
}

float2 RandomFloat2(inout uint seed)
{
    return float2(RandomFloat(seed), RandomFloat(seed));
}

float3 RandomFloat3(inout uint seed)
{
    return float3(RandomFloat(seed), RandomFloat(seed), RandomFloat(seed));
}

float3 RandomUnitVector(inout uint seed)
{
    float3 v;
    do {
        v = RandomFloat3(seed) * 2.0 - 1.0;
    } while (dot(v, v) >= 1.0);
    return normalize(v);
}

float3 RandomHemisphereVector(float3 normal, inout uint seed)
{
    float3 v = RandomUnitVector(seed);
    return dot(v, normal) > 0.0 ? v : -v;
}

// Ray-sphere intersection
bool IntersectSphere(Ray ray, Sphere sphere, out HitInfo hit)
{
    hit.Hit = false;
    
    float3 oc = ray.Origin - sphere.Center;
    float a = dot(ray.Direction, ray.Direction);
    float b = 2.0 * dot(oc, ray.Direction);
    float c = dot(oc, oc) - sphere.Radius * sphere.Radius;
    
    float discriminant = b * b - 4 * a * c;
    if (discriminant < 0.0)
        return false;
    
    float sqrt_discriminant = sqrt(discriminant);
    float t1 = (-b - sqrt_discriminant) / (2.0 * a);
    float t2 = (-b + sqrt_discriminant) / (2.0 * a);
    
    float t = (t1 > ray.MinT && t1 < ray.MaxT) ? t1 : t2;
    if (t < ray.MinT || t > ray.MaxT)
        return false;
    
    hit.Hit = true;
    hit.T = t;
    hit.Position = ray.Origin + t * ray.Direction;
    hit.Normal = normalize(hit.Position - sphere.Center);
    hit.MaterialID = sphere.MaterialID;
    
    // Calculate UV coordinates for sphere
    float phi = atan2(hit.Normal.z, hit.Normal.x);
    float theta = acos(hit.Normal.y);
    hit.UV = float2((phi + 3.14159265359) / (2.0 * 3.14159265359), theta / 3.14159265359);
    
    return true;
}

// Ray-triangle intersection using Möller-Trumbore algorithm
bool IntersectTriangle(Ray ray, Triangle tri, out HitInfo hit)
{
    hit.Hit = false;
    
    float3 edge1 = tri.V1 - tri.V0;
    float3 edge2 = tri.V2 - tri.V0;
    float3 h = cross(ray.Direction, edge2);
    float a = dot(edge1, h);
    
    if (a > -0.00001 && a < 0.00001)
        return false;
    
    float f = 1.0 / a;
    float3 s = ray.Origin - tri.V0;
    float u = f * dot(s, h);
    
    if (u < 0.0 || u > 1.0)
        return false;
    
    float3 q = cross(s, edge1);
    float v = f * dot(ray.Direction, q);
    
    if (v < 0.0 || u + v > 1.0)
        return false;
    
    float t = f * dot(edge2, q);
    
    if (t < ray.MinT || t > ray.MaxT)
        return false;
    
    hit.Hit = true;
    hit.T = t;
    hit.Position = ray.Origin + t * ray.Direction;
    
    // Interpolate normal and UV
    float w = 1.0 - u - v;
    hit.Normal = normalize(w * tri.N0 + u * tri.N1 + v * tri.N2);
    hit.UV = w * tri.UV0 + u * tri.UV1 + v * tri.UV2;
    hit.MaterialID = tri.MaterialID;
    
    return true;
}

// Scene intersection
bool IntersectScene(Ray ray, out HitInfo closestHit)
{
    closestHit.Hit = false;
    closestHit.T = ray.MaxT;
    
    HitInfo hit;
    
    // Test spheres
    for (uint i = 0; i < NumSpheres; ++i)
    {
        if (IntersectSphere(ray, Spheres[i], hit))
        {
            if (hit.T < closestHit.T)
            {
                closestHit = hit;
                ray.MaxT = hit.T;
            }
        }
    }
    
    // Test triangles
    for (uint j = 0; j < NumTriangles; ++j)
    {
        if (IntersectTriangle(ray, Triangles[j], hit))
        {
            if (hit.T < closestHit.T)
            {
                closestHit = hit;
                ray.MaxT = hit.T;
            }
        }
    }
    
    return closestHit.Hit;
}

// Sample environment map
float3 SampleEnvironment(float3 direction)
{
    float phi = atan2(direction.z, direction.x);
    float theta = acos(direction.y);
    float2 uv = float2((phi + 3.14159265359) / (2.0 * 3.14159265359), theta / 3.14159265359);
    return EnvironmentMap.SampleLevel(LinearSampler, uv, 0).rgb;
}

// BRDF sampling
float3 SampleBRDF(float3 normal, float3 viewDir, Material material, inout uint seed, out float3 sampleDir, out float pdf)
{
    float3 albedo = material.Albedo;
    float metallic = material.Metallic;
    float roughness = material.Roughness;
    
    // Simple importance sampling between diffuse and specular
    float specularChance = 0.5;
    
    if (RandomFloat(seed) < specularChance)
    {
        // Sample specular reflection
        float3 reflectDir = reflect(-viewDir, normal);
        
        // Add roughness by perturbing the reflection direction
        float3 perturbation = RandomUnitVector(seed) * roughness;
        sampleDir = normalize(reflectDir + perturbation);
        
        // Ensure the sample is in the correct hemisphere
        if (dot(sampleDir, normal) <= 0.0)
            sampleDir = reflect(sampleDir, normal);
        
        pdf = 1.0 / specularChance;
        
        // Fresnel approximation
        float cosTheta = max(dot(normal, viewDir), 0.0);
        float3 F0 = lerp(float3(0.04, 0.04, 0.04), albedo, metallic);
        float3 fresnel = F0 + (1.0 - F0) * pow(1.0 - cosTheta, 5.0);
        
        return fresnel;
    }
    else
    {
        // Sample diffuse reflection
        sampleDir = RandomHemisphereVector(normal, seed);
        pdf = 1.0 / (1.0 - specularChance);
        
        return albedo * (1.0 - metallic) / 3.14159265359;
    }
}

// Path tracing
float3 TraceRay(Ray ray, inout uint seed)
{
    float3 radiance = float3(0, 0, 0);
    float3 throughput = float3(1, 1, 1);
    
    for (uint bounce = 0; bounce < MaxBounces; ++bounce)
    {
        HitInfo hit;
        if (!IntersectScene(ray, hit))
        {
            // Hit environment
            radiance += throughput * SampleEnvironment(ray.Direction);
            break;
        }
        
        Material material = Materials[hit.MaterialID];
        
        // Add emission
        radiance += throughput * material.Emission;
        
        // Sample BRDF
        float3 sampleDir;
        float pdf;
        float3 brdf = SampleBRDF(hit.Normal, -ray.Direction, material, seed, sampleDir, pdf);
        
        if (pdf <= 0.0)
            break;
        
        // Update throughput
        float cosTheta = max(dot(hit.Normal, sampleDir), 0.0);
        throughput *= brdf * cosTheta / pdf;
        
        // Russian roulette termination
        float maxComponent = max(max(throughput.r, throughput.g), throughput.b);
        if (maxComponent < 0.1 && bounce > 3)
        {
            float roulette = RandomFloat(seed);
            if (roulette > maxComponent)
                break;
            throughput /= maxComponent;
        }
        
        // Setup next ray
        ray.Origin = hit.Position + hit.Normal * 0.001; // Offset to avoid self-intersection
        ray.Direction = sampleDir;
        ray.MinT = 0.001;
        ray.MaxT = 1000.0;
    }
    
    return radiance;
}

// Generate camera ray
Ray GenerateCameraRay(float2 screenCoord, float2 jitter)
{
    float2 ndc = (screenCoord + jitter) / ScreenResolution * 2.0 - 1.0;
    ndc.y = -ndc.y; // Flip Y coordinate
    
    float4 clipPos = float4(ndc, 1.0, 1.0);
    float4 viewPos = mul(clipPos, InverseProjectionMatrix);
    viewPos /= viewPos.w;
    
    float3 worldPos = mul(float4(viewPos.xyz, 1.0), InverseViewMatrix).xyz;
    float3 rayDir = normalize(worldPos - CameraPosition);
    
    Ray ray;
    ray.Origin = CameraPosition;
    ray.Direction = rayDir;
    ray.MinT = 0.001;
    ray.MaxT = 1000.0;
    
    return ray;
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID, uint3 localId : SV_GroupThreadID)
{
    if (id.x >= (uint)ScreenResolution.x || id.y >= (uint)ScreenResolution.y)
        return;
    
    uint pixelIndex = id.y * (uint)ScreenResolution.x + id.x;
    uint seed = RandomSeed + pixelIndex + FrameCount * 719393;
    
    float3 pixelRadiance = float3(0, 0, 0);
    
    // Multiple samples per pixel for anti-aliasing and noise reduction
    for (uint sample = 0; sample < SamplesPerPixel; ++sample)
    {
        float2 jitter = RandomFloat2(seed) - 0.5;
        Ray ray = GenerateCameraRay(float2(id.xy), jitter);
        
        float3 sampleRadiance = TraceRay(ray, seed);
        pixelRadiance += sampleRadiance;
    }
    
    pixelRadiance /= float(SamplesPerPixel);
    
    // Store in shared memory for potential group operations
    uint localIndex = localId.y * 8 + localId.x;
    SharedRadiance[localIndex] = pixelRadiance;
    SharedSampleCount[localIndex] = SamplesPerPixel;
    
    GroupMemoryBarrierWithGroupSync();
    
    // Progressive accumulation
    float4 previousColor = AccumulationTexture[id.xy];
    float3 accumulatedColor = previousColor.rgb;
    float sampleCount = previousColor.a;
    
    // Blend with previous samples
    float blendFactor = 1.0 / (sampleCount + 1.0);
    accumulatedColor = lerp(accumulatedColor, pixelRadiance, blendFactor);
    sampleCount += 1.0;
    
    // Tone mapping and gamma correction
    float3 toneMapped = accumulatedColor / (accumulatedColor + 1.0); // Simple Reinhard
    float3 gammaCorrected = pow(toneMapped, 1.0 / 2.2);
    
    // Output final color
    OutputTexture[id.xy] = float4(gammaCorrected, 1.0);
    AccumulationTexture[id.xy] = float4(accumulatedColor, sampleCount);
}
