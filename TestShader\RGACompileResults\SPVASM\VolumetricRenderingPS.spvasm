; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 210
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD2 %in_var_TEXCOORD3 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_VolumeParams "type.VolumeParams"
               OpMemberName %type_VolumeParams 0 "VolumeMin"
               OpMemberName %type_VolumeParams 1 "StepSize"
               OpMemberName %type_VolumeParams 2 "VolumeMax"
               OpMemberName %type_VolumeParams 3 "MaxSteps"
               OpMemberName %type_VolumeParams 4 "LightDirection"
               OpMemberName %type_VolumeParams 5 "Density"
               OpMemberName %type_VolumeParams 6 "LightColor"
               OpMemberName %type_VolumeParams 7 "Absorption"
               OpMemberName %type_VolumeParams 8 "ScatteringColor"
               OpMemberName %type_VolumeParams 9 "Scattering"
               OpMemberName %type_VolumeParams 10 "Time"
               OpMemberName %type_VolumeParams 11 "NoiseScale"
               OpMemberName %type_VolumeParams 12 "NoiseStrength"
               OpName %VolumeParams "VolumeParams"
               OpName %type_3d_image "type.3d.image"
               OpName %VolumeTexture "VolumeTexture"
               OpName %NoiseTexture "NoiseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %VolumeParams DescriptorSet 0
               OpDecorate %VolumeParams Binding 0
               OpDecorate %VolumeTexture DescriptorSet 0
               OpDecorate %VolumeTexture Binding 0
               OpDecorate %NoiseTexture DescriptorSet 0
               OpDecorate %NoiseTexture Binding 1
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_VolumeParams 0 Offset 0
               OpMemberDecorate %type_VolumeParams 1 Offset 12
               OpMemberDecorate %type_VolumeParams 2 Offset 16
               OpMemberDecorate %type_VolumeParams 3 Offset 28
               OpMemberDecorate %type_VolumeParams 4 Offset 32
               OpMemberDecorate %type_VolumeParams 5 Offset 44
               OpMemberDecorate %type_VolumeParams 6 Offset 48
               OpMemberDecorate %type_VolumeParams 7 Offset 60
               OpMemberDecorate %type_VolumeParams 8 Offset 64
               OpMemberDecorate %type_VolumeParams 9 Offset 76
               OpMemberDecorate %type_VolumeParams 10 Offset 80
               OpMemberDecorate %type_VolumeParams 11 Offset 84
               OpMemberDecorate %type_VolumeParams 12 Offset 88
               OpDecorate %type_VolumeParams Block
        %int = OpTypeInt 32 1
      %int_4 = OpConstant %int 4
      %int_3 = OpConstant %int 3
      %int_0 = OpConstant %int 0
      %int_2 = OpConstant %int 2
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
    %v3float = OpTypeVector %float 3
         %22 = OpConstantComposite %v3float %float_0 %float_0 %float_0
    %float_1 = OpConstant %float 1
       %bool = OpTypeBool
      %false = OpConstantFalse %bool
%float_0_00100000005 = OpConstant %float 0.00100000005
      %int_6 = OpConstant %int 6
      %int_8 = OpConstant %int 8
      %int_9 = OpConstant %int 9
      %int_7 = OpConstant %int 7
      %int_1 = OpConstant %int 1
%float_0_00999999978 = OpConstant %float 0.00999999978
         %33 = OpConstantComposite %v3float %float_1 %float_1 %float_1
     %int_11 = OpConstant %int 11
     %int_10 = OpConstant %int 10
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_0500000007 = OpConstant %float 0.0500000007
%float_0_0799999982 = OpConstant %float 0.0799999982
     %int_12 = OpConstant %int 12
      %int_5 = OpConstant %int 5
    %float_2 = OpConstant %float 2
  %float_1_5 = OpConstant %float 1.5
  %float_0_5 = OpConstant %float 0.5
%type_VolumeParams = OpTypeStruct %v3float %float %v3float %int %v3float %float %v3float %float %v3float %float %float %float %float
%_ptr_Uniform_type_VolumeParams = OpTypePointer Uniform %type_VolumeParams
%type_3d_image = OpTypeImage %float 3D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_3d_image = OpTypePointer UniformConstant %type_3d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
    %v4float = OpTypeVector %float 4
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %51 = OpTypeFunction %void
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_int = OpTypePointer Uniform %int
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image = OpTypeSampledImage %type_3d_image
%VolumeParams = OpVariable %_ptr_Uniform_type_VolumeParams Uniform
%VolumeTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%NoiseTexture = OpVariable %_ptr_UniformConstant_type_3d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_1_09000003 = OpConstant %float 1.09000003
%float_0_600000024 = OpConstant %float 0.600000024
%float_0_0724154934 = OpConstant %float 0.0724154934
       %main = OpFunction %void None %51
         %58 = OpLabel
         %59 = OpLoad %v3float %in_var_TEXCOORD2
         %60 = OpLoad %v3float %in_var_TEXCOORD3
         %61 = OpExtInst %v3float %1 Normalize %59
         %62 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_0
         %63 = OpLoad %v3float %62
         %64 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_2
         %65 = OpLoad %v3float %64
         %66 = OpFDiv %v3float %33 %61
         %67 = OpFSub %v3float %63 %60
         %68 = OpFMul %v3float %67 %66
         %69 = OpFSub %v3float %65 %60
         %70 = OpFMul %v3float %69 %66
         %71 = OpExtInst %v3float %1 NMin %68 %70
         %72 = OpExtInst %v3float %1 NMax %68 %70
         %73 = OpCompositeExtract %float %71 0
         %74 = OpCompositeExtract %float %71 1
         %75 = OpExtInst %float %1 NMax %73 %74
         %76 = OpCompositeExtract %float %71 2
         %77 = OpExtInst %float %1 NMax %75 %76
         %78 = OpCompositeExtract %float %72 0
         %79 = OpCompositeExtract %float %72 1
         %80 = OpExtInst %float %1 NMin %78 %79
         %81 = OpCompositeExtract %float %72 2
         %82 = OpExtInst %float %1 NMin %80 %81
         %83 = OpFOrdGreaterThan %bool %82 %77
               OpSelectionMerge %84 None
               OpBranchConditional %83 %85 %84
         %85 = OpLabel
         %86 = OpFOrdGreaterThan %bool %82 %float_0
               OpBranch %84
         %84 = OpLabel
         %87 = OpPhi %bool %false %58 %86 %85
         %88 = OpLogicalNot %bool %87
               OpSelectionMerge %89 None
               OpBranchConditional %88 %90 %89
         %90 = OpLabel
               OpKill
         %89 = OpLabel
         %91 = OpExtInst %float %1 NMax %float_0 %77
               OpBranch %92
         %92 = OpLabel
         %93 = OpPhi %v3float %22 %89 %94 %95
         %96 = OpPhi %float %float_1 %89 %97 %95
         %98 = OpPhi %float %91 %89 %99 %95
        %100 = OpPhi %int %int_0 %89 %101 %95
               OpLoopMerge %102 %95 None
               OpBranch %103
        %103 = OpLabel
        %104 = OpAccessChain %_ptr_Uniform_int %VolumeParams %int_3
        %105 = OpLoad %int %104
        %106 = OpSLessThan %bool %100 %105
               OpSelectionMerge %107 None
               OpBranchConditional %106 %108 %107
        %108 = OpLabel
        %109 = OpFOrdLessThan %bool %98 %82
               OpBranch %107
        %107 = OpLabel
        %110 = OpPhi %bool %false %103 %109 %108
               OpBranchConditional %110 %111 %102
        %111 = OpLabel
        %112 = OpVectorTimesScalar %v3float %61 %98
        %113 = OpFAdd %v3float %60 %112
        %114 = OpFSub %v3float %113 %63
        %115 = OpFSub %v3float %65 %63
        %116 = OpFDiv %v3float %114 %115
        %117 = OpLoad %type_3d_image %VolumeTexture
        %118 = OpLoad %type_sampler %LinearSampler
        %119 = OpSampledImage %type_sampled_image %117 %118
        %120 = OpImageSampleExplicitLod %v4float %119 %116 Lod %float_0
        %121 = OpCompositeExtract %float %120 0
        %122 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_11
        %123 = OpLoad %float %122
        %124 = OpVectorTimesScalar %v3float %113 %123
        %125 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_10
        %126 = OpLoad %float %125
        %127 = OpFMul %float %126 %float_0_100000001
        %128 = OpFMul %float %126 %float_0_0500000007
        %129 = OpFMul %float %126 %float_0_0799999982
        %130 = OpCompositeConstruct %v3float %127 %128 %129
        %131 = OpFAdd %v3float %124 %130
               OpBranch %132
        %132 = OpLabel
        %133 = OpPhi %float %float_1 %111 %134 %135
        %136 = OpPhi %float %float_0_5 %111 %137 %135
        %138 = OpPhi %float %float_0 %111 %139 %135
        %140 = OpPhi %int %int_0 %111 %141 %135
        %142 = OpSLessThan %bool %140 %int_4
               OpLoopMerge %143 %135 None
               OpBranchConditional %142 %135 %143
        %135 = OpLabel
        %144 = OpVectorTimesScalar %v3float %131 %133
        %145 = OpLoad %type_3d_image %NoiseTexture
        %146 = OpLoad %type_sampler %LinearSampler
        %147 = OpSampledImage %type_sampled_image %145 %146
        %148 = OpImageSampleExplicitLod %v4float %147 %144 Lod %float_0
        %149 = OpCompositeExtract %float %148 0
        %150 = OpFMul %float %136 %149
        %139 = OpFAdd %float %138 %150
        %137 = OpFMul %float %136 %float_0_5
        %134 = OpFMul %float %133 %float_2
        %141 = OpIAdd %int %140 %int_1
               OpBranch %132
        %143 = OpLabel
        %151 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_12
        %152 = OpLoad %float %151
        %153 = OpFMul %float %138 %152
        %154 = OpFAdd %float %121 %153
        %155 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_5
        %156 = OpLoad %float %155
        %157 = OpFMul %float %154 %156
        %158 = OpExtInst %float %1 NMax %float_0 %157
        %159 = OpFOrdGreaterThan %bool %158 %float_0_00100000005
               OpSelectionMerge %160 None
               OpBranchConditional %159 %161 %160
        %161 = OpLabel
        %162 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_4
        %163 = OpLoad %v3float %162
        %164 = OpFNegate %v3float %163
        %165 = OpExtInst %v3float %1 Normalize %164
        %166 = OpFNegate %v3float %61
        %167 = OpDot %float %165 %166
        %168 = OpFMul %float %float_0_600000024 %167
        %169 = OpFSub %float %float_1_09000003 %168
        %170 = OpExtInst %float %1 Pow %169 %float_1_5
        %171 = OpFDiv %float %float_0_0724154934 %170
        %172 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_6
        %173 = OpLoad %v3float %172
        %174 = OpAccessChain %_ptr_Uniform_v3float %VolumeParams %int_8
        %175 = OpLoad %v3float %174
        %176 = OpFMul %v3float %173 %175
        %177 = OpVectorTimesScalar %v3float %176 %158
        %178 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_9
        %179 = OpLoad %float %178
        %180 = OpVectorTimesScalar %v3float %177 %179
        %181 = OpVectorTimesScalar %v3float %180 %171
        %182 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_7
        %183 = OpLoad %float %182
        %184 = OpFAdd %float %183 %179
        %185 = OpFMul %float %158 %184
        %186 = OpFNegate %float %185
        %187 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_1
        %188 = OpLoad %float %187
        %189 = OpFMul %float %186 %188
        %190 = OpExtInst %float %1 Exp %189
        %191 = OpVectorTimesScalar %v3float %181 %96
        %192 = OpFSub %float %float_1 %190
        %193 = OpVectorTimesScalar %v3float %191 %192
        %194 = OpCompositeConstruct %v3float %185 %185 %185
        %195 = OpFDiv %v3float %193 %194
        %196 = OpFAdd %v3float %93 %195
        %197 = OpFMul %float %96 %190
        %198 = OpFOrdLessThan %bool %197 %float_0_00999999978
               OpSelectionMerge %199 None
               OpBranchConditional %198 %200 %199
        %200 = OpLabel
               OpBranch %102
        %199 = OpLabel
               OpBranch %160
        %160 = OpLabel
         %94 = OpPhi %v3float %93 %143 %196 %199
         %97 = OpPhi %float %96 %143 %197 %199
        %201 = OpAccessChain %_ptr_Uniform_float %VolumeParams %int_1
        %202 = OpLoad %float %201
         %99 = OpFAdd %float %98 %202
               OpBranch %95
         %95 = OpLabel
        %101 = OpIAdd %int %100 %int_1
               OpBranch %92
        %102 = OpLabel
        %203 = OpPhi %v3float %93 %107 %196 %200
        %204 = OpPhi %float %96 %107 %197 %200
        %205 = OpFSub %float %float_1 %204
        %206 = OpCompositeExtract %float %203 0
        %207 = OpCompositeExtract %float %203 1
        %208 = OpCompositeExtract %float %203 2
        %209 = OpCompositeConstruct %v4float %206 %207 %208 %205
               OpStore %out_var_SV_TARGET %209
               OpReturn
               OpFunctionEnd
