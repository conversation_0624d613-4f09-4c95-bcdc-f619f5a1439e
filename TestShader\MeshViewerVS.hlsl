// Unnamed technique, shader MeshViewerVS
struct ViewModes
{
    static const int InputPos = 0;
    static const int OutputPos = 1;
    static const int VertexID = 2;
    static const int InstanceID = 3;
    static const int Color = 4;
    static const int Normal = 5;
    static const int Tangent = 6;
    static const int UV = 7;
    static const int MaterialID = 8;
    static const int ShapeID = 9;
    static const int ViewerColor = 10;
    static const int PlasticShaded = 11;
};

struct Struct__MeshViewerVSCB
{
    uint RemapRanges;
    int ViewMode;
    float2 _padding0;
    float4x4 ViewProjMtx;
    float4 ViewerColor;
};

ConstantBuffer<Struct__MeshViewerVSCB> _MeshViewerVSCB : register(b0);


struct VSInput
{
	float3 Position   : POSITION;
	uint   VertexID   : SV_VertexID;
	uint   InstanceId : SV_InstanceID;
	float3 Color      : COLOR;
	float3 Normal     : NORMAL;
	float4 Tangent    : TANGENT;
	float2 UV         : TEXCOORD0;
	int MaterialID    : TEXCOORD1;
	int ShapeID       : TEXCOORD2;
};

struct VSOutput // AKA PSInput
{
	float4 Position   : SV_POSITION;
	float4 Color      : TEXCOORD0;
	float3 Normal     : NORMAL;
	float3 WorldPos   : POSITION;
};


VSOutput main(VSInput input)
{
	VSOutput ret = (VSOutput)0;
	float4 outPos = mul(float4(input.Position, 1.0f), _MeshViewerVSCB.ViewProjMtx);

	switch (_MeshViewerVSCB.ViewMode)
	{
		case ViewModes::InputPos:
			ret.Color = float4(input.Position, 1);
			break;
		case ViewModes::OutputPos:
		{
			ret.Color = float4(outPos.xyz / outPos.w, 1.0f);
			break;
		}

		case ViewModes::VertexID:
			 ret.Color = float4(input.VertexID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::InstanceID:
			ret.Color = float4(input.InstanceId, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::Color:
			ret.Color = float4(input.Color, 1.0f);
			break;
		case ViewModes::Normal:
		{
			float3 normalizedNormal = normalize(input.Normal);
			float3 displayNormal = _MeshViewerVSCB.RemapRanges ? (normalizedNormal + 1.0f) / 2.0f : normalizedNormal;
			ret.Color = float4(displayNormal, 1.0f);
			break;
		}
		case ViewModes::Tangent:
		{
			float3 normalizedTangent = normalize(input.Tangent.xyz);
			float3 displayTangent = _MeshViewerVSCB.RemapRanges ? (normalizedTangent + 1.0f) / 2.0f : normalizedTangent;
			ret.Color = float4(displayTangent, 1.0f);
			break;
		}
		case ViewModes::UV:
			ret.Color = float4(input.UV, 0.0f, 1.0f);
			break;
		case ViewModes::MaterialID:
			ret.Color = float4(input.MaterialID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::ShapeID:
			ret.Color = float4(input.ShapeID, 0.0f, 0.0f, 1.0f);
			break;
		case ViewModes::ViewerColor:
			ret.Color = _MeshViewerVSCB.ViewerColor;
			break;
		default:
			ret.Color = _MeshViewerVSCB.ViewerColor;
			break;
	}
    ret.Position = outPos;
	ret.Normal = input.Normal;
	ret.WorldPos = input.Position;
	return ret;
}
/*
Shader Samplers:
	samLinear filter: MinMagMipLinear addressmode: Wrap
*/
