// Instanced Rendering Pixel Shader
// Tests per-instance material variations and lighting

cbuffer Material : register(b0)
{
    float4 BaseColor;
    float Metallic;
    float Roughness;
    float3 LightDirection;
    float3 LightColor;
    float3 AmbientColor;
    float Time;
};

Texture2DArray DiffuseTextureArray : register(t0);  // Different textures for different instance types
Texture2D NormalTexture : register(t1);
Texture2D NoiseTexture : register(t2);
SamplerState LinearSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float4 Color : TEXCOORD3;
    float3 ViewDir : TEXCOORD4;
    float InstanceType : TEXCOORD5;
    float Health : TEXCOORD6;
};

// Simple noise function
float noise(float2 p)
{
    return NoiseTexture.Sample(LinearSampler, p * 0.1).r;
}

// Material variation based on instance type
float4 getMaterialProperties(float instanceType, float2 texCoord)
{
    float4 material;
    
    if (instanceType < 1.0) // Vegetation
    {
        material = float4(0.1, 0.8, 0.0, 1.0); // Low metallic, high roughness
    }
    else if (instanceType < 2.0) // Rocks
    {
        material = float4(0.0, 0.9, 0.0, 1.0); // No metallic, very rough
    }
    else // Metal objects
    {
        material = float4(0.8, 0.2, 0.0, 1.0); // High metallic, low roughness
    }
    
    // Add some noise variation
    float noiseValue = noise(texCoord + instanceType);
    material.y += (noiseValue - 0.5) * 0.2; // Vary roughness
    
    return material;
}

// Subsurface scattering approximation for vegetation
float3 subsurfaceScattering(float3 normal, float3 lightDir, float3 viewDir, float3 color)
{
    float3 backLightDir = -lightDir;
    float backScatter = max(0.0, dot(backLightDir, normal));
    float viewScatter = max(0.0, dot(viewDir, backLightDir));
    
    float scatterStrength = pow(viewScatter, 4.0) * backScatter;
    return color * scatterStrength * 0.5;
}

// Wind effect on surface
float3 applyWindEffect(float3 color, float3 worldPos, float instanceType)
{
    if (instanceType < 1.0) // Only apply to vegetation
    {
        float windNoise = noise(worldPos.xz * 0.1 + Time * 0.1);
        float windEffect = sin(Time * 2.0 + worldPos.x * 0.1) * windNoise;
        
        // Slight color shift due to wind movement
        color.g += windEffect * 0.1;
        color.b -= windEffect * 0.05;
    }
    
    return color;
}

float4 main(PSInput input) : SV_TARGET
{
    // Normalize interpolated vectors
    float3 normal = normalize(input.Normal);
    float3 lightDir = normalize(-LightDirection);
    float3 viewDir = normalize(input.ViewDir);
    
    // Sample appropriate texture based on instance type
    int textureIndex = (int)input.InstanceType;
    float4 albedo = DiffuseTextureArray.Sample(LinearSampler, float3(input.TexCoord, textureIndex));
    
    // Apply instance color tint
    albedo *= input.Color;
    
    // Get material properties based on instance type
    float4 materialProps = getMaterialProperties(input.InstanceType, input.TexCoord);
    float metallic = materialProps.x;
    float roughness = materialProps.y;
    
    // Basic lighting calculations
    float NdotL = max(0.0, dot(normal, lightDir));
    
    // Diffuse lighting
    float3 diffuse = albedo.rgb * LightColor * NdotL;
    
    // Specular lighting (simplified)
    float3 halfDir = normalize(lightDir + viewDir);
    float NdotH = max(0.0, dot(normal, halfDir));
    float specularPower = lerp(32.0, 256.0, 1.0 - roughness);
    float3 specular = LightColor * pow(NdotH, specularPower) * metallic;
    
    // Ambient lighting
    float3 ambient = AmbientColor * albedo.rgb;
    
    // Special effects based on instance type
    float3 finalColor = ambient + diffuse + specular;
    
    if (input.InstanceType < 1.0) // Vegetation
    {
        // Add subsurface scattering
        float3 sss = subsurfaceScattering(normal, lightDir, viewDir, albedo.rgb);
        finalColor += sss;
        
        // Apply wind effect
        finalColor = applyWindEffect(finalColor, input.WorldPos, input.InstanceType);
    }
    
    // Health-based effects
    if (input.Health < 1.0)
    {
        // Darken and desaturate unhealthy instances
        float healthFactor = input.Health;
        finalColor = lerp(finalColor * 0.5, finalColor, healthFactor);
        
        // Add some red tint for damage
        if (input.Health < 0.3)
        {
            finalColor.r += (0.3 - input.Health) * 2.0;
        }
    }
    
    // Distance-based fog (simple)
    float distance = length(input.WorldPos - float3(0, 0, 0)); // Assuming camera at origin for simplicity
    float fogFactor = exp(-distance * 0.01);
    finalColor = lerp(float3(0.7, 0.8, 0.9), finalColor, fogFactor);
    
    return float4(finalColor, albedo.a);
}
