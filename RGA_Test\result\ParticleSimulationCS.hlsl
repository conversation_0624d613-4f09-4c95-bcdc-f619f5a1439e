// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct Particle
{
  float3 Position;
  float Mass;
  float3 Velocity;
  float Life;
  float3 Force;
  float Size;
  float4 Color;
};

cbuffer SimulationParams : register(b0)
{
  float DeltaTime;
  float3 Gravity;
  float3 WindForce;
  float Damping;
  uint ParticleCount;
  float3 BoundsMin;
  float3 BoundsMax;
  float GroundHeight;
  float Restitution;
}

RWStructuredBuffer<Particle> ParticleBuffer : register(u0);
[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
  uint index = id.x;
  if (index >= ParticleCount)
    return ;
  Particle particle = ParticleBuffer[index];
  if (particle.Life <= 0.0f)
    return ;
  float3 totalForce = particle.Force;
  totalForce += (Gravity * particle.Mass);
  totalForce += WindForce;
  float3 acceleration = totalForce / particle.Mass;
  particle.Velocity += (acceleration * DeltaTime);
  particle.Velocity *= Damping;
  particle.Position += (particle.Velocity * DeltaTime);
  if (particle.Position.y <= GroundHeight)
  {
    particle.Position.y = GroundHeight;
    particle.Velocity.y = ((-particle.Velocity.y) * Restitution);
    particle.Velocity.x *= 0.8f;
    particle.Velocity.z *= 0.8f;
  }
  if (particle.Position.x < BoundsMin.x || particle.Position.x > BoundsMax.x)
  {
    particle.Velocity.x = ((-particle.Velocity.x) * Restitution);
    particle.Position.x = clamp(particle.Position.x, BoundsMin.x, BoundsMax.x);
  }
  if (particle.Position.z < BoundsMin.z || particle.Position.z > BoundsMax.z)
  {
    particle.Velocity.z = ((-particle.Velocity.z) * Restitution);
    particle.Position.z = clamp(particle.Position.z, BoundsMin.z, BoundsMax.z);
  }
  particle.Life -= DeltaTime;
  float lifeRatio = saturate(particle.Life / 5.0f);
  particle.Color.a = lifeRatio;
  particle.Size = lerp(0.1f, 1.0f, lifeRatio);
  particle.Force = float3(0, 0, 0);
  ParticleBuffer[index] = particle;
}

