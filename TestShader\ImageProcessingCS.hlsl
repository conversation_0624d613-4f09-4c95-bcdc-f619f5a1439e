// Image Processing Compute Shader
// Tests various image processing algorithms

cbuffer ProcessingParams : register(b0)
{
    uint ImageWidth;
    uint ImageHeight;
    float BlurRadius;
    float Brightness;
    float Contrast;
    float Saturation;
    float Gamma;
    uint FilterType; // 0=Blur, 1=Sharpen, 2=<PERSON>, 3=Emboss
};

Texture2D<float4> InputTexture : register(t0);
RWTexture2D<float4> OutputTexture : register(u0);

SamplerState LinearSampler : register(s0);

// Gaussian blur kernel
static const float GaussianKernel[25] = {
    0.003765, 0.015019, 0.023792, 0.015019, 0.003765,
    0.015019, 0.059912, 0.094907, 0.059912, 0.015019,
    0.023792, 0.094907, 0.150342, 0.094907, 0.023792,
    0.015019, 0.059912, 0.094907, 0.059912, 0.015019,
    0.003765, 0.015019, 0.023792, 0.015019, 0.003765
};

// Sharpen kernel
static const float SharpenKernel[9] = {
    0, -1, 0,
    -1, 5, -1,
    0, -1, 0
};

// Edge detection kernel (Sobel)
static const float EdgeKernelX[9] = {
    -1, 0, 1,
    -2, 0, 2,
    -1, 0, 1
};

static const float EdgeKernelY[9] = {
    -1, -2, -1,
    0, 0, 0,
    1, 2, 1
};

// Emboss kernel
static const float EmbossKernel[9] = {
    -2, -1, 0,
    -1, 1, 1,
    0, 1, 2
};

float3 RGBToHSV(float3 rgb)
{
    float4 K = float4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
    float4 p = lerp(float4(rgb.bg, K.wz), float4(rgb.gb, K.xy), step(rgb.b, rgb.g));
    float4 q = lerp(float4(p.xyw, rgb.r), float4(rgb.r, p.yzx), step(p.x, rgb.r));
    
    float d = q.x - min(q.w, q.y);
    float e = 1.0e-10;
    return float3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

float3 HSVToRGB(float3 hsv)
{
    float4 K = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    float3 p = abs(frac(hsv.xxx + K.xyz) * 6.0 - K.www);
    return hsv.z * lerp(K.xxx, clamp(p - K.xxx, 0.0, 1.0), hsv.y);
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
    if (id.x >= ImageWidth || id.y >= ImageHeight)
        return;
    
    float2 texCoord = float2(id.xy) / float2(ImageWidth, ImageHeight);
    float4 color = InputTexture.SampleLevel(LinearSampler, texCoord, 0);
    
    if (FilterType == 0) // Gaussian Blur
    {
        float4 blurredColor = float4(0, 0, 0, 0);
        float2 texelSize = 1.0 / float2(ImageWidth, ImageHeight);
        
        for (int y = -2; y <= 2; y++)
        {
            for (int x = -2; x <= 2; x++)
            {
                float2 offset = float2(x, y) * texelSize * BlurRadius;
                float2 sampleCoord = texCoord + offset;
                float4 sampleColor = InputTexture.SampleLevel(LinearSampler, sampleCoord, 0);
                blurredColor += sampleColor * GaussianKernel[(y + 2) * 5 + (x + 2)];
            }
        }
        color = blurredColor;
    }
    else if (FilterType == 1) // Sharpen
    {
        float4 sharpenedColor = float4(0, 0, 0, 0);
        float2 texelSize = 1.0 / float2(ImageWidth, ImageHeight);
        
        for (int y = -1; y <= 1; y++)
        {
            for (int x = -1; x <= 1; x++)
            {
                float2 offset = float2(x, y) * texelSize;
                float2 sampleCoord = texCoord + offset;
                float4 sampleColor = InputTexture.SampleLevel(LinearSampler, sampleCoord, 0);
                sharpenedColor += sampleColor * SharpenKernel[(y + 1) * 3 + (x + 1)];
            }
        }
        color = sharpenedColor;
    }
    else if (FilterType == 2) // Edge Detection
    {
        float4 edgeX = float4(0, 0, 0, 0);
        float4 edgeY = float4(0, 0, 0, 0);
        float2 texelSize = 1.0 / float2(ImageWidth, ImageHeight);
        
        for (int y = -1; y <= 1; y++)
        {
            for (int x = -1; x <= 1; x++)
            {
                float2 offset = float2(x, y) * texelSize;
                float2 sampleCoord = texCoord + offset;
                float4 sampleColor = InputTexture.SampleLevel(LinearSampler, sampleCoord, 0);
                edgeX += sampleColor * EdgeKernelX[(y + 1) * 3 + (x + 1)];
                edgeY += sampleColor * EdgeKernelY[(y + 1) * 3 + (x + 1)];
            }
        }
        color = sqrt(edgeX * edgeX + edgeY * edgeY);
    }
    else if (FilterType == 3) // Emboss
    {
        float4 embossedColor = float4(0, 0, 0, 0);
        float2 texelSize = 1.0 / float2(ImageWidth, ImageHeight);
        
        for (int y = -1; y <= 1; y++)
        {
            for (int x = -1; x <= 1; x++)
            {
                float2 offset = float2(x, y) * texelSize;
                float2 sampleCoord = texCoord + offset;
                float4 sampleColor = InputTexture.SampleLevel(LinearSampler, sampleCoord, 0);
                embossedColor += sampleColor * EmbossKernel[(y + 1) * 3 + (x + 1)];
            }
        }
        color = embossedColor + 0.5;
    }
    
    // Apply color adjustments
    color.rgb *= Brightness;
    color.rgb = ((color.rgb - 0.5) * Contrast) + 0.5;
    
    // Saturation adjustment
    float3 hsv = RGBToHSV(color.rgb);
    hsv.y *= Saturation;
    color.rgb = HSVToRGB(hsv);
    
    // Gamma correction
    color.rgb = pow(abs(color.rgb), Gamma);
    
    OutputTexture[id.xy] = saturate(color);
}
