struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
  float4 InstancePosition : TEXCOORD1;
  float4 InstanceRotation : TEXCOORD2;
  float4 InstanceColor : TEXCOORD3;
  float4 InstanceData : TEXCOORD4;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float4 Color : TEXCOORD3;
  float3 ViewDir : TEXCOORD4;
  float InstanceType : TEXCOORD5;
  float Health : TEXCOORD6;
};

cbuffer PerFrame : register(b0)
{
  uniform float4x4 ViewProjectionMatrix;
  uniform float3 CameraPosition;
  uniform float Time;
  uniform float3 WindDirection;
  uniform float WindStrength;
}

float3 rotateByQuaternion(float3 v, float4 q)
{
  float3 qvec = q.xyz;
  float3 uv = cross(qvec, v);
  float3 uuv = cross(qvec, uv);
  return v + (2.0f * (uv * q.w) + uuv);
}

float3 applyWindAnimation(float3 position, float3 worldPos, float animPhase, float age)
{
  float windEffect = (sin((Time * 2.0f) + animPhase + (worldPos.x * 0.1f)) * WindStrength);
  float heightFactor = position.y;
  return position + (((WindDirection * windEffect) * heightFactor) * 0.1f);
}

float3 applyGrowthAnimation(float3 position, float age, float health)
{
  float growthFactor = (saturate((age * 0.1f)) * health);
  return (position * growthFactor);
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float3 instancePos = input.InstancePosition.xyz;
  float instanceScale = input.InstancePosition.w;
  float4 instanceRot = input.InstanceRotation;
  float animPhase = input.InstanceData.x;
  float health = input.InstanceData.y;
  float instanceType = input.InstanceData.z;
  float age = input.InstanceData.w;
  float3 animatedPosition = input.Position;
  if (instanceType < 1.0f)
  {
    animatedPosition = applyWindAnimation(animatedPosition, instancePos, animPhase, age);
    animatedPosition = applyGrowthAnimation(animatedPosition, age, health);
  }
  else if (instanceType < 2.0f)
  {
  }
  else
  {
    float bobOffset = (sin((Time * 3.0f) + animPhase) * 0.1f);
    animatedPosition.y += bobOffset;
  }
  animatedPosition *= instanceScale;
  animatedPosition = rotateByQuaternion(animatedPosition, instanceRot);
  float3 worldNormal = rotateByQuaternion(input.Normal, instanceRot);
  float3 worldPos = animatedPosition + instancePos;
  output.WorldPos = worldPos;
  output.Position = mul(float4(worldPos, 1.0f), ViewProjectionMatrix);
  output.Normal = normalize(worldNormal);
  output.TexCoord = input.TexCoord;
  output.Color = (input.Color * input.InstanceColor);
  if (health < 0.5f)
  {
    output.Color.rgb = lerp(float3(0.5f, 0.3f, 0.1f), output.Color.rgb, (health * 2.0f));
  }
  output.ViewDir = normalize(CameraPosition - worldPos);
  output.InstanceType = instanceType;
  output.Health = health;
  return output;
}

