#version 320 es

// Tessellation Evaluation Shader - OpenGL ES Version
// Tests tessellation evaluation and displacement mapping

layout(triangles, equal_spacing, ccw) in;

in vec3 tcWorldPos[];
in vec3 tcNormal[];
in vec2 tcTexCoord[];

out vec3 teWorldPos;
out vec3 teNormal;
out vec2 teTexCoord;
out float teDisplacement;

uniform mat4 uViewProjectionMatrix;
uniform sampler2D uHeightTexture;
uniform float uDisplacementScale;
uniform float uTime;

vec3 interpolate3D(vec3 v0, vec3 v1, vec3 v2)
{
    return gl_TessCoord.x * v0 + gl_TessCoord.y * v1 + gl_TessCoord.z * v2;
}

vec2 interpolate2D(vec2 v0, vec2 v1, vec2 v2)
{
    return gl_TessCoord.x * v0 + gl_TessCoord.y * v1 + gl_TessCoord.z * v2;
}

void main()
{
    // Interpolate vertex attributes
    teWorldPos = interpolate3D(tcWorldPos[0], tcWorldPos[1], tcWorldPos[2]);
    teNormal = normalize(interpolate3D(tcNormal[0], tcNormal[1], tcNormal[2]));
    teTexCoord = interpolate2D(tcTexCoord[0], tcTexCoord[1], tcTexCoord[2]);
    
    // Sample height texture for displacement
    vec2 animatedTexCoord = teTexCoord + vec2(sin(uTime * 0.5) * 0.1, cos(uTime * 0.3) * 0.1);
    float height = texture(uHeightTexture, animatedTexCoord).r;
    
    // Add some procedural noise
    float noise = sin(teWorldPos.x * 10.0 + uTime) * sin(teWorldPos.z * 10.0 + uTime) * 0.1;
    height += noise;
    
    teDisplacement = height;
    
    // Apply displacement along normal
    vec3 displacedPosition = teWorldPos + teNormal * height * uDisplacementScale;
    
    // Update world position
    teWorldPos = displacedPosition;
    
    // Transform to clip space
    gl_Position = uViewProjectionMatrix * vec4(displacedPosition, 1.0);
}
