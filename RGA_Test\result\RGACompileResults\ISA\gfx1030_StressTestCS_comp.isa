_amdgpu_cs_main:
	s_mov_b32 s20, s1                                          // 000000000000: BE940301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v8, s2, 3, v0                               // 000000000008: D7460008 04010602
	s_mov_b32 s21, s1                                          // 000000000010: BE950301
	s_mov_b64 s[0:1], exec                                     // 000000000014: BE80047E
	s_load_dwordx4 s[12:15], s[20:21], null                    // 000000000018: F408030A FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000020: BF8CC07F
	s_buffer_load_dword s22, s[12:15], 0x4                     // 000000000024: F4200586 FA000004
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_cmpx_gt_u32_e64 s22, v8                                  // 000000000030: D4D4007E 00021016
	s_cbranch_execz _L0                                        // 000000000038: BF88027E
	s_buffer_load_dword s0, s[12:15], 0xc                      // 00000000003C: F4200006 FA00000C
	s_load_dwordx4 s[8:11], s[20:21], 0x30                     // 000000000044: F408020A FA000030
	s_waitcnt lgkmcnt(0)                                       // 00000000004C: BF8CC07F
	s_cmp_lg_u32 s0, 0                                         // 000000000050: BF078000
	s_cbranch_scc0 _L1                                         // 000000000054: BF8400E6
	s_cmp_lg_u32 s0, 1                                         // 000000000058: BF078100
	s_cbranch_scc0 _L2                                         // 00000000005C: BF8400E5
	s_cmp_lg_u32 s0, 2                                         // 000000000060: BF078200
	s_cbranch_scc0 _L3                                         // 000000000064: BF8400E4
	v_mov_b32_e32 v3, 0                                        // 000000000068: 7E060280
	v_mov_b32_e32 v2, 0                                        // 00000000006C: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000070: 7E020280
	v_mov_b32_e32 v0, 0                                        // 000000000074: 7E000280
	s_cmp_lg_u32 s0, 3                                         // 000000000078: BF078300
	s_cbranch_scc1 _L4                                         // 00000000007C: BF8500DA
	v_lshlrev_b32_e32 v0, 4, v8                                // 000000000080: 34001084
	v_mov_b32_e32 v4, 0                                        // 000000000084: 7E080280
	v_mov_b32_e32 v5, 0                                        // 000000000088: 7E0A0280
	v_mov_b32_e32 v6, 0                                        // 00000000008C: 7E0C0280
	v_mov_b32_e32 v7, 0                                        // 000000000090: 7E0E0280
	buffer_load_dwordx4 v[0:3], v0, s[12:15], 0 offen          // 000000000094: E0381000 80030000
	s_buffer_load_dword s23, s[12:15], 0x8                     // 00000000009C: F42005C6 FA000008
	s_waitcnt lgkmcnt(0)                                       // 0000000000A4: BF8CC07F
	s_cmp_eq_u32 s23, 0                                        // 0000000000A8: BF068017
	s_cbranch_scc1 _L5                                         // 0000000000AC: BF850088
	s_buffer_load_dword s24, s[12:15], 0x10                    // 0000000000B0: F4200606 FA000010
	s_waitcnt vmcnt(0)                                         // 0000000000B8: BF8C3F70
	v_mul_f32_e32 v4, 0xbe13bb63, v3                           // 0000000000BC: 100806FF BE13BB63
	s_clause 0x1                                               // 0000000000C4: BFA10001
	s_load_dwordx8 s[0:7], s[20:21], null                      // 0000000000C8: F40C000A FA000000
	s_load_dwordx4 s[16:19], s[20:21], 0x20                    // 0000000000D0: F408040A FA000020
	v_mul_f32_e32 v7, v2, v2                                   // 0000000000D8: 100E0502
	s_cmp_eq_u32 s23, 1                                        // 0000000000DC: BF068117
	s_mov_b32 s25, 1                                           // 0000000000E0: BE990381
	v_exp_f32_e32 v4, v4                                       // 0000000000E4: 7E084B04
	v_mul_f32_e32 v4, v7, v4                                   // 0000000000E8: 10080907
	s_waitcnt lgkmcnt(0)                                       // 0000000000EC: BF8CC07F
	v_add_f32_e32 v5, s24, v0                                  // 0000000000F0: 060A0018
	v_add_f32_e32 v6, s24, v1                                  // 0000000000F4: 060C0218
	v_mul_f32_e32 v5, 0.15915494, v5                           // 0000000000F8: 100A0AF8
	v_mul_f32_e32 v6, 0.15915494, v6                           // 0000000000FC: 100C0CF8
	v_sin_f32_e32 v5, v5                                       // 000000000100: 7E0A6B05
	v_cos_f32_e32 v6, v6                                       // 000000000104: 7E0C6D06
	v_fmac_f32_e32 v4, v5, v6                                  // 000000000108: 56080D05
	v_mov_b32_e32 v6, 0                                        // 00000000010C: 7E0C0280
	v_fma_f32 v5, v4, 0.5, 0.5                                 // 000000000110: D54B0005 03C1E104
	image_sample_lz v[9:12], v[5:6], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000118: F09C0F08 00800905
	s_waitcnt vmcnt(0)                                         // 000000000120: BF8C3F70
	v_add_f32_e32 v5, v9, v0                                   // 000000000124: 060A0109
	v_add_f32_e32 v7, v10, v1                                  // 000000000128: 060E030A
	v_add_f32_e32 v9, v11, v2                                  // 00000000012C: 0612050B
	v_add_f32_e32 v10, v12, v3                                 // 000000000130: 0614070C
	v_fmac_f32_e32 v4, 0x3dcccccd, v5                          // 000000000134: 56080AFF 3DCCCCCD
	v_fma_f32 v11, 0x3dcccccd, v7, -v1                         // 00000000013C: D54B000B 84060EFF 3DCCCCCD
	v_mul_f32_e32 v6, 0x3dcccccd, v9                           // 000000000148: 100C12FF 3DCCCCCD
	v_fma_f32 v9, 0x3dcccccd, v9, -v2                          // 000000000150: D54B0009 840A12FF 3DCCCCCD
	v_fma_f32 v13, 0x3dcccccd, v10, -v3                        // 00000000015C: D54B000D 840E14FF 3DCCCCCD
	v_sub_f32_e32 v12, v4, v0                                  // 000000000168: 08180104
	v_mul_f32_e32 v5, 0x3dcccccd, v7                           // 00000000016C: 100A0EFF 3DCCCCCD
	v_mul_f32_e32 v7, 0x3dcccccd, v10                          // 000000000174: 100E14FF 3DCCCCCD
	v_fmamk_f32 v1, v11, 0x3dcccccd, v1                        // 00000000017C: 5802030B 3DCCCCCD
	v_fmamk_f32 v2, v9, 0x3dcccccd, v2                         // 000000000184: 58040509 3DCCCCCD
	v_fmamk_f32 v0, v12, 0x3dcccccd, v0                        // 00000000018C: 5800010C 3DCCCCCD
	v_fmac_f32_e32 v3, 0x3dcccccd, v13                         // 000000000194: 56061AFF 3DCCCCCD
	s_cbranch_scc1 _L5                                         // 00000000019C: BF85004C
	v_cvt_f32_u32_e32 v9, s22                                  // 0000000001A0: 7E120C16
	v_cvt_f32_u32_e32 v10, s23                                 // 0000000001A4: 7E140C17
	s_sub_i32 s27, 0, s22                                      // 0000000001A8: 819B1680
	v_rcp_iflag_f32_e32 v9, v9                                 // 0000000001AC: 7E125709
	v_mul_f32_e32 v9, 0x4f7ffffe, v9                           // 0000000001B0: 101212FF 4F7FFFFE
	v_cvt_u32_f32_e32 v9, v9                                   // 0000000001B8: 7E120F09
	v_readfirstlane_b32 s26, v9                                // 0000000001BC: 7E340509
	v_rcp_iflag_f32_e32 v9, v10                                // 0000000001C0: 7E12570A
	s_mul_i32 s27, s27, s26                                    // 0000000001C4: 931B1A1B
	s_mul_hi_u32 s27, s26, s27                                 // 0000000001C8: 9A9B1B1A
	s_add_i32 s26, s26, s27                                    // 0000000001CC: 811A1B1A
	s_mov_b32 s27, 17                                          // 0000000001D0: BE9B0391
_L6:
	v_add_f32_e32 v10, s24, v0                                 // 0000000001D4: 06140018
	v_mul_f32_e32 v11, 0xbe13bb63, v3                          // 0000000001D8: 101606FF BE13BB63
	v_add_f32_e32 v12, s24, v1                                 // 0000000001E0: 06180218
	s_mul_hi_u32 s28, s26, s27                                 // 0000000001E4: 9A9C1B1A
	v_mul_f32_e32 v13, v2, v2                                  // 0000000001E8: 101A0502
	v_mul_f32_e32 v10, 0.15915494, v10                         // 0000000001EC: 101414F8
	v_exp_f32_e32 v11, v11                                     // 0000000001F0: 7E164B0B
	v_mul_f32_e32 v12, 0.15915494, v12                         // 0000000001F4: 101818F8
	s_not_b32 s29, s28                                         // 0000000001F8: BE9D071C
	s_mul_i32 s28, s22, s28                                    // 0000000001FC: 931C1C16
	v_sin_f32_e32 v10, v10                                     // 000000000200: 7E146B0A
	s_mul_i32 s29, s22, s29                                    // 000000000204: 931D1D16
	v_cos_f32_e32 v12, v12                                     // 000000000208: 7E186D0C
	s_sub_i32 s28, s27, s28                                    // 00000000020C: 819C1C1B
	s_add_i32 s29, s27, s29                                    // 000000000210: 811D1D1B
	s_cmp_ge_u32 s28, s22                                      // 000000000214: BF09161C
	v_mul_f32_e32 v18, v13, v11                                // 000000000218: 1024170D
	s_cselect_b32 s28, s29, s28                                // 00000000021C: 851C1C1D
	v_cvt_f32_u32_e32 v11, s25                                 // 000000000220: 7E160C19
	s_sub_i32 s29, s28, s22                                    // 000000000224: 819D161C
	s_cmp_ge_u32 s28, s22                                      // 000000000228: BF09161C
	v_fmac_f32_e32 v18, v10, v12                               // 00000000022C: 5624190A
	s_cselect_b32 s28, s29, s28                                // 000000000230: 851C1C1D
	v_mul_f32_e32 v15, v11, v9                                 // 000000000234: 101E130B
	v_add_lshl_u32 v10, s28, v8, 4                             // 000000000238: D747000A 0212101C
	s_add_i32 s25, s25, 1                                      // 000000000240: 81198119
	v_fma_f32 v14, v18, 0.5, 0.5                               // 000000000244: D54B000E 03C1E112
	v_add_f32_e32 v4, v18, v4                                  // 00000000024C: 06080912
	s_add_i32 s27, s27, 17                                     // 000000000250: 811B911B
	buffer_load_dwordx4 v[10:13], v10, s[12:15], 0 offen       // 000000000254: E0381000 80030A0A
	s_cmp_lt_u32 s25, s23                                      // 00000000025C: BF0A1719
	image_sample_lz v[14:17], v[14:15], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000260: F09C0F08 00800E0E
	s_waitcnt vmcnt(0)                                         // 000000000268: BF8C3F70
	v_add_f32_e32 v10, v14, v10                                // 00000000026C: 0614150E
	v_add_f32_e32 v11, v15, v11                                // 000000000270: 0616170F
	v_add_f32_e32 v12, v16, v12                                // 000000000274: 06181910
	v_add_f32_e32 v13, v17, v13                                // 000000000278: 061A1B11
	v_fmac_f32_e32 v4, 0x3dcccccd, v10                         // 00000000027C: 560814FF 3DCCCCCD
	v_fmac_f32_e32 v5, 0x3dcccccd, v11                         // 000000000284: 560A16FF 3DCCCCCD
	v_fmac_f32_e32 v6, 0x3dcccccd, v12                         // 00000000028C: 560C18FF 3DCCCCCD
	v_fmac_f32_e32 v7, 0x3dcccccd, v13                         // 000000000294: 560E1AFF 3DCCCCCD
	v_sub_f32_e32 v10, v4, v0                                  // 00000000029C: 08140104
	v_sub_f32_e32 v11, v5, v1                                  // 0000000002A0: 08160305
	v_sub_f32_e32 v12, v6, v2                                  // 0000000002A4: 08180506
	v_sub_f32_e32 v13, v7, v3                                  // 0000000002A8: 081A0707
	v_fmac_f32_e32 v0, 0x3dcccccd, v10                         // 0000000002AC: 560014FF 3DCCCCCD
	v_fmac_f32_e32 v1, 0x3dcccccd, v11                         // 0000000002B4: 560216FF 3DCCCCCD
	v_fmac_f32_e32 v2, 0x3dcccccd, v12                         // 0000000002BC: 560418FF 3DCCCCCD
	v_fmac_f32_e32 v3, 0x3dcccccd, v13                         // 0000000002C4: 56061AFF 3DCCCCCD
	s_cbranch_scc1 _L6                                         // 0000000002CC: BF85FFC1
_L5:
	v_mul_f32_e32 v9, v5, v5                                   // 0000000002D0: 10120B05
	s_waitcnt vmcnt(0)                                         // 0000000002D4: BF8C3F70
	v_mul_f32_e32 v1, v1, v1                                   // 0000000002D8: 10020301
	s_mov_b32 s0, 0xbc46c6a5                                   // 0000000002DC: BE8003FF BC46C6A5
	v_fmac_f32_e32 v9, v4, v4                                  // 0000000002E4: 56120904
	v_fmac_f32_e32 v1, v0, v0                                  // 0000000002E8: 56020100
	v_fmac_f32_e32 v9, v6, v6                                  // 0000000002EC: 56120D06
	v_fmac_f32_e32 v1, v2, v2                                  // 0000000002F0: 56020502
	v_fmac_f32_e32 v9, v7, v7                                  // 0000000002F4: 56120F07
	v_fmac_f32_e32 v1, v3, v3                                  // 0000000002F8: 56020703
	v_rsq_f32_e32 v0, v9                                       // 0000000002FC: 7E005D09
	v_sqrt_f32_e32 v9, v1                                      // 000000000300: 7E126701
	v_mul_legacy_f32_e32 v1, v7, v0                            // 000000000304: 0E020107
	v_mul_legacy_f32_e32 v6, v6, v0                            // 000000000308: 0E0C0106
	v_mul_legacy_f32_e32 v4, v4, v0                            // 00000000030C: 0E080104
	v_mul_legacy_f32_e32 v5, v5, v0                            // 000000000310: 0E0A0105
	v_mul_f32_e32 v3, v1, v9                                   // 000000000314: 10061301
	v_mul_f32_e32 v10, v6, v9                                  // 000000000318: 10141306
	v_mul_f32_e32 v2, 0x40a00000, v3                           // 00000000031C: 100406FF 40A00000
	v_mul_f32_e32 v10, 0x3f4bb7e4, v10                         // 000000000324: 101414FF 3F4BB7E4
	v_max_f32_e64 v1, |v2|, 1.0                                // 00000000032C: D5100101 0001E502
	v_min_f32_e64 v7, |v2|, 1.0                                // 000000000334: D50F0107 0001E502
	v_cos_f32_e32 v0, v10                                      // 00000000033C: 7E006D0A
	v_cmp_gt_f32_e64 vcc_lo, |v2|, 1.0                         // 000000000340: D404016A 0001E502
	v_sin_f32_e32 v10, v10                                     // 000000000348: 7E146B0A
	v_rcp_f32_e32 v1, v1                                       // 00000000034C: 7E025501
	v_mul_f32_e32 v1, v7, v1                                   // 000000000350: 10020307
	v_mul_f32_e32 v7, v1, v1                                   // 000000000354: 100E0301
	v_mul_f32_e32 v11, v7, v1                                  // 000000000358: 10160307
	v_mul_f32_e32 v1, 0x3f7ffea5, v1                           // 00000000035C: 100202FF 3F7FFEA5
	v_fmaak_f32 v12, s0, v7, 0x3d5be101                        // 000000000364: 5A180E00 3D5BE101
	v_mul_f32_e32 v13, v11, v7                                 // 00000000036C: 101A0F0B
	v_fmamk_f32 v1, v11, 0xbeaa5476, v1                        // 000000000370: 5802030B BEAA5476
	v_fmaak_f32 v11, v12, v7, 0xbdf0555d                       // 000000000378: 5A160F0C BDF0555D
	v_mul_f32_e32 v7, v13, v7                                  // 000000000380: 100E0F0D
	v_fmamk_f32 v12, v13, 0x3e468bc1, v1                       // 000000000384: 5818030D 3E468BC1
	v_mul_f32_e32 v1, v4, v9                                   // 00000000038C: 10021304
	v_mul_f32_e32 v13, v5, v9                                  // 000000000390: 101A1305
	v_fmac_f32_e32 v12, v7, v11                                // 000000000394: 56181707
	v_mul_f32_e32 v1, 0x3fcbb7e4, v1                           // 000000000398: 100202FF 3FCBB7E4
	v_mul_f32_e32 v7, 0x3fcbb7e4, v13                          // 0000000003A0: 100E1AFF 3FCBB7E4
	v_rcp_f32_e32 v11, v0                                      // 0000000003A8: 7E165500
	v_fmaak_f32 v13, -2.0, v12, 0x3fc90fdb                     // 0000000003AC: 5A1A18F5 3FC90FDB
	v_sin_f32_e32 v0, v1                                       // 0000000003B4: 7E006B01
	v_cos_f32_e32 v1, v7                                       // 0000000003B8: 7E026D07
	v_cndmask_b32_e32 v7, 0, v13, vcc_lo                       // 0000000003BC: 020E1A80
	v_cmp_ge_f32_e32 vcc_lo, 0, v2                             // 0000000003C0: 7C0C0480
	v_add_f32_e32 v7, v7, v12                                  // 0000000003C4: 060E1907
	v_cndmask_b32_e32 v13, 1.0, v2, vcc_lo                     // 0000000003C8: 021A04F2
	v_mul_f32_e32 v2, v10, v11                                 // 0000000003CC: 1004170A
	v_fmac_f32_e32 v0, v4, v9                                  // 0000000003D0: 56001304
	v_fmac_f32_e32 v1, v5, v9                                  // 0000000003D4: 56021305
	v_cmp_le_f32_e32 vcc_lo, 0, v13                            // 0000000003D8: 7C061A80
	v_fmac_f32_e32 v2, v6, v9                                  // 0000000003DC: 56041306
	v_cndmask_b32_e32 v10, -1.0, v13, vcc_lo                   // 0000000003E0: 02141AF3
	v_fmac_f32_e32 v3, v7, v10                                 // 0000000003E4: 56061507
_L4:
	s_cbranch_execz _L3                                        // 0000000003E8: BF880003
	s_branch _L7                                               // 0000000003EC: BF820099
_L1:
	s_branch _L8                                               // 0000000003F0: BF820100
_L2:
	s_branch _L9                                               // 0000000003F4: BF820098
_L3:
	s_load_dwordx8 s[0:7], s[20:21], null                      // 0000000003F8: F40C000A FA000000
	s_buffer_load_dword s25, s[12:15], 0x8                     // 000000000400: F4200646 FA000008
	v_mov_b32_e32 v5, 0                                        // 000000000408: 7E0A0280
	v_mov_b32_e32 v3, 0                                        // 00000000040C: 7E060280
	v_mov_b32_e32 v2, 0                                        // 000000000410: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000414: 7E020280
	s_mov_b32 s26, 0                                           // 000000000418: BE9A0380
	s_waitcnt lgkmcnt(0)                                       // 00000000041C: BF8CC07F
	v_alignbit_b32 v0, s2, s1, 30                              // 000000000420: D54E0000 02780202
	s_bfe_u32 s17, s3, 0x4000c                                 // 000000000428: 9391FF03 0004000C
	v_readfirstlane_b32 s16, v0                                // 000000000430: 7E200500
	s_and_b32 s16, s16, 0x3fff                                 // 000000000434: 8710FF10 00003FFF
	s_add_i32 s16, s16, 1                                      // 00000000043C: 81108110
	s_lshr_b32 s16, s16, s17                                   // 000000000440: 90101110
	s_max_u32 s23, s16, 1                                      // 000000000444: 84978110
	s_bfe_u32 s16, s2, 0xe000e                                 // 000000000448: 9390FF02 000E000E
	v_cvt_f32_u32_e32 v0, s23                                  // 000000000450: 7E000C17
	s_add_i32 s16, s16, 1                                      // 000000000454: 81108110
	s_lshr_b32 s16, s16, s17                                   // 000000000458: 90101110
	s_max_u32 s24, s16, 1                                      // 00000000045C: 84988110
	v_rcp_iflag_f32_e32 v4, v0                                 // 000000000460: 7E085700
	v_mov_b32_e32 v0, 0                                        // 000000000464: 7E000280
	s_cmp_eq_u32 s25, 0                                        // 000000000468: BF068019
	s_cbranch_scc1 _L10                                        // 00000000046C: BF850051
	s_buffer_load_dword s27, s[12:15], 0x10                    // 000000000470: F42006C6 FA000010
	s_load_dwordx4 s[16:19], s[20:21], 0x20                    // 000000000478: F408040A FA000020
	v_cvt_f32_u32_e32 v0, s24                                  // 000000000480: 7E000C18
	v_cvt_f32_u32_e32 v1, s22                                  // 000000000484: 7E020C16
	v_mov_b32_e32 v7, 0                                        // 000000000488: 7E0E0280
	v_mov_b32_e32 v9, 1.0                                      // 00000000048C: 7E1202F2
	v_mov_b32_e32 v10, 2.0                                     // 000000000490: 7E1402F4
	v_rcp_iflag_f32_e32 v5, v0                                 // 000000000494: 7E0A5700
	v_rcp_iflag_f32_e32 v6, v1                                 // 000000000498: 7E0C5701
	v_mov_b32_e32 v11, 0x40400000                              // 00000000049C: 7E1602FF 40400000
	v_mov_b32_e32 v0, 0                                        // 0000000004A4: 7E000280
	v_mov_b32_e32 v1, 0                                        // 0000000004A8: 7E020280
	v_mov_b32_e32 v2, 0                                        // 0000000004AC: 7E040280
	v_mov_b32_e32 v3, 0                                        // 0000000004B0: 7E060280
_L11:
	v_cvt_f32_u32_e32 v12, s26                                 // 0000000004B4: 7E180C1A
	v_add_nc_u32_e32 v13, s26, v8                              // 0000000004B8: 4A1A101A
	s_add_i32 s26, s26, 1                                      // 0000000004BC: 811A811A
	s_cmp_lt_u32 s26, s25                                      // 0000000004C0: BF0A191A
	s_waitcnt lgkmcnt(0)                                       // 0000000004C4: BF8CC07F
	v_add_f32_e32 v12, s27, v12                                // 0000000004C8: 0618181B
	v_cvt_f32_u32_e32 v13, v13                                 // 0000000004CC: 7E1A0D0D
	v_mul_f32_e32 v12, 0.15915494, v12                         // 0000000004D0: 101818F8
	v_mul_f32_e32 v29, v13, v6                                 // 0000000004D4: 103A0D0D
	v_sin_f32_e32 v12, v12                                     // 0000000004D8: 7E186B0C
	v_add_f32_e32 v16, v29, v29                                // 0000000004DC: 06203B1D
	v_mul_f32_e32 v21, 4.0, v29                                // 0000000004E0: 102A3AF6
	v_mul_f32_e32 v22, 0x41000000, v29                         // 0000000004E4: 102C3AFF 41000000
	v_fma_f32 v30, v12, 0.5, 0.5                               // 0000000004EC: D54B001E 03C1E10C
	image_sample_lz v[12:15], v[29:30], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 0000000004F4: F09C0F08 00800C1D
	v_add_f32_e32 v17, v30, v30                                // 0000000004FC: 06223D1E
	v_mul_f32_e32 v24, 4.0, v30                                // 000000000500: 10303CF6
	image_sample_l  v[16:19], [v16, v17, v9], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000504: F0900F0A 00801010 00000911
	s_waitcnt vmcnt(1)                                         // 000000000510: BF8C3F71
	v_add_f32_e32 v25, v12, v0                                 // 000000000514: 0632010C
	v_mul_f32_e32 v12, 0x41000000, v30                         // 000000000518: 10183CFF 41000000
	v_add_f32_e32 v26, v13, v1                                 // 000000000520: 0634030D
	v_add_f32_e32 v27, v14, v2                                 // 000000000524: 0636050E
	v_add_f32_e32 v28, v15, v3                                 // 000000000528: 0638070F
	s_clause 0x2                                               // 00000000052C: BFA10002
	image_sample_l  v[0:3], [v21, v24, v10], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000530: F0900F0A 00800015 00000A18
	image_sample_l  v[12:15], [v22, v12, v11], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 00000000053C: F0900F0A 00800C16 00000B0C
	image_sample_d  v[20:23], [v4, v7, v7, v5, v29, v30], s[0:7], s[16:19] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000548: F0880F0C 00801404 1D050707 0000001E
	s_waitcnt vmcnt(3)                                         // 000000000558: BF8C3F73
	v_add_f32_e32 v16, v25, v16                                // 00000000055C: 06202119
	v_add_f32_e32 v17, v26, v17                                // 000000000560: 0622231A
	v_add_f32_e32 v18, v27, v18                                // 000000000564: 0624251B
	v_add_f32_e32 v19, v28, v19                                // 000000000568: 0626271C
	s_waitcnt vmcnt(2)                                         // 00000000056C: BF8C3F72
	v_add_f32_e32 v0, v16, v0                                  // 000000000570: 06000110
	v_add_f32_e32 v1, v17, v1                                  // 000000000574: 06020311
	v_add_f32_e32 v2, v18, v2                                  // 000000000578: 06040512
	v_add_f32_e32 v3, v19, v3                                  // 00000000057C: 06060713
	s_waitcnt vmcnt(1)                                         // 000000000580: BF8C3F71
	v_add_f32_e32 v0, v0, v12                                  // 000000000584: 06001900
	v_add_f32_e32 v1, v1, v13                                  // 000000000588: 06021B01
	v_add_f32_e32 v2, v2, v14                                  // 00000000058C: 06041D02
	v_add_f32_e32 v3, v3, v15                                  // 000000000590: 06061F03
	s_waitcnt vmcnt(0)                                         // 000000000594: BF8C3F70
	v_add_f32_e32 v0, v0, v20                                  // 000000000598: 06002900
	v_add_f32_e32 v1, v1, v21                                  // 00000000059C: 06022B01
	v_add_f32_e32 v2, v2, v22                                  // 0000000005A0: 06042D02
	v_add_f32_e32 v3, v3, v23                                  // 0000000005A4: 06062F03
	s_cbranch_scc1 _L11                                        // 0000000005A8: BF85FFC2
	s_mul_i32 s25, s25, 5                                      // 0000000005AC: 93198519
	v_cvt_f32_u32_e32 v5, s25                                  // 0000000005B0: 7E0A0C19
_L10:
	v_mul_f32_e32 v4, 0x4f7ffffe, v4                           // 0000000005B4: 100808FF 4F7FFFFE
	s_sub_i32 s0, 0, s23                                       // 0000000005BC: 81801780
	v_cvt_u32_f32_e32 v4, v4                                   // 0000000005C0: 7E080F04
	v_mul_lo_u32 v6, s0, v4                                    // 0000000005C4: D5690006 00020800
	s_mov_b64 s[0:1], exec                                     // 0000000005CC: BE80047E
	v_mul_hi_u32 v6, v4, v6                                    // 0000000005D0: D56A0006 00020D04
	v_add_nc_u32_e32 v4, v4, v6                                // 0000000005D8: 4A080D04
	v_mul_hi_u32 v4, v8, v4                                    // 0000000005DC: D56A0004 00020908
	v_mul_lo_u32 v6, v4, s23                                   // 0000000005E4: D5690006 00002F04
	v_add_nc_u32_e32 v7, 1, v4                                 // 0000000005EC: 4A0E0881
	v_sub_nc_u32_e32 v6, v8, v6                                // 0000000005F0: 4C0C0D08
	v_subrev_nc_u32_e32 v9, s23, v6                            // 0000000005F4: 4E120C17
	v_cmp_le_u32_e32 vcc_lo, s23, v6                           // 0000000005F8: 7D860C17
	v_cndmask_b32_e32 v4, v4, v7, vcc_lo                       // 0000000005FC: 02080F04
	v_cndmask_b32_e32 v6, v6, v9, vcc_lo                       // 000000000600: 020C1306
	v_add_nc_u32_e32 v7, 1, v4                                 // 000000000604: 4A0E0881
	v_cmp_le_u32_e32 vcc_lo, s23, v6                           // 000000000608: 7D860C17
	v_cndmask_b32_e32 v7, v4, v7, vcc_lo                       // 00000000060C: 020E0F04
	v_cmpx_gt_u32_e64 s24, v7                                  // 000000000610: D4D4007E 00020E18
	s_cbranch_execz _L12                                       // 000000000618: BF88000D
	s_load_dwordx8 s[24:31], s[20:21], 0x40                    // 00000000061C: F40C060A FA000040
	v_rcp_f32_e32 v5, v5                                       // 000000000624: 7E0A5505
	v_mul_lo_u32 v6, v7, s23                                   // 000000000628: D5690006 00002F07
	v_sub_nc_u32_e32 v6, v8, v6                                // 000000000630: 4C0C0D08
	v_mul_f32_e32 v9, v5, v0                                   // 000000000634: 10120105
	v_mul_f32_e32 v10, v5, v1                                  // 000000000638: 10140305
	v_mul_f32_e32 v11, v5, v2                                  // 00000000063C: 10160505
	v_mul_f32_e32 v12, v5, v3                                  // 000000000640: 10180705
	s_waitcnt lgkmcnt(0)                                       // 000000000644: BF8CC07F
	image_store v[9:12], v[6:7], s[24:31] dmask:0xf dim:SQ_RSRC_IMG_2D unorm// 000000000648: F0201F08 00060906
_L12:
	s_or_b64 exec, exec, s[0:1]                                // 000000000650: 88FE007E
_L7:
	s_cbranch_execnz _L13                                      // 000000000654: BF890065
_L9:
	s_buffer_load_dword s0, s[12:15], 0x8                      // 000000000658: F4200006 FA000008
	v_mov_b32_e32 v3, 0                                        // 000000000660: 7E060280
	v_mov_b32_e32 v2, 0                                        // 000000000664: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000668: 7E020280
	v_mov_b32_e32 v0, 0                                        // 00000000066C: 7E000280
	s_mov_b32 s1, 0                                            // 000000000670: BE810380
	s_waitcnt lgkmcnt(0)                                       // 000000000674: BF8CC07F
	s_cmp_eq_u32 s0, 0                                         // 000000000678: BF068000
	s_cbranch_scc1 _L13                                        // 00000000067C: BF85005B
	v_cvt_f32_u32_e32 v0, s22                                  // 000000000680: 7E000C16
	s_sub_i32 s2, 0, s22                                       // 000000000684: 81821680
	s_mov_b32 s5, 0x19660d                                     // 000000000688: BE8503FF 0019660D
	v_mad_u64_u32 v[4:5], null, v8, s5, 0x3c6ef35f             // 000000000690: D5767D04 03FC0B08 3C6EF35F
	v_rcp_iflag_f32_e32 v0, v0                                 // 00000000069C: 7E005700
	v_mul_f32_e32 v0, 0x4f7ffffe, v0                           // 0000000006A0: 100000FF 4F7FFFFE
	v_cvt_u32_f32_e32 v0, v0                                   // 0000000006A8: 7E000F00
	v_readfirstlane_b32 s3, v0                                 // 0000000006AC: 7E060500
	v_mov_b32_e32 v0, 0                                        // 0000000006B0: 7E000280
	s_mul_i32 s2, s2, s3                                       // 0000000006B4: 93020302
	v_mov_b32_e32 v1, v0                                       // 0000000006B8: 7E020300
	s_mul_hi_u32 s4, s3, s2                                    // 0000000006BC: 9A840203
	s_buffer_load_dword s2, s[12:15], 0x10                     // 0000000006C0: F4200086 FA000010
	s_add_i32 s3, s3, s4                                       // 0000000006C8: 81030403
	v_mov_b32_e32 v2, v0                                       // 0000000006CC: 7E040300
	s_mul_hi_u32 s4, s3, 0x3039                                // 0000000006D0: 9A84FF03 00003039
	v_mov_b32_e32 v3, v0                                       // 0000000006D8: 7E060300
	s_mul_i32 s4, s4, s22                                      // 0000000006DC: 93041604
	s_sub_i32 s4, 0x3039, s4                                   // 0000000006E0: 818404FF 00003039
	s_sub_i32 s5, s4, s22                                      // 0000000006E8: 81851604
	s_cmp_ge_u32 s4, s22                                       // 0000000006EC: BF091604
	s_cselect_b32 s4, s5, s4                                   // 0000000006F0: 85040405
	s_sub_i32 s5, s4, s22                                      // 0000000006F4: 81851604
	s_cmp_ge_u32 s4, s22                                       // 0000000006F8: BF091604
	s_cselect_b32 s4, s5, s4                                   // 0000000006FC: 85040405
	s_inst_prefetch 0x1                                        // 000000000700: BFA00001
	s_nop 0                                                    // 000000000704: BF800000
	s_nop 0                                                    // 000000000708: BF800000
	s_nop 0                                                    // 00000000070C: BF800000
	s_nop 0                                                    // 000000000710: BF800000
	s_nop 0                                                    // 000000000714: BF800000
	s_nop 0                                                    // 000000000718: BF800000
	s_nop 0                                                    // 00000000071C: BF800000
	s_nop 0                                                    // 000000000720: BF800000
	s_nop 0                                                    // 000000000724: BF800000
	s_nop 0                                                    // 000000000728: BF800000
	s_nop 0                                                    // 00000000072C: BF800000
	s_nop 0                                                    // 000000000730: BF800000
	s_nop 0                                                    // 000000000734: BF800000
	s_nop 0                                                    // 000000000738: BF800000
	s_nop 0                                                    // 00000000073C: BF800000
_L14:
	s_mul_hi_u32 s5, s3, s1                                    // 000000000740: 9A850103
	s_not_b32 s6, s5                                           // 000000000744: BE860705
	s_mul_i32 s5, s22, s5                                      // 000000000748: 93050516
	s_mul_i32 s6, s22, s6                                      // 00000000074C: 93060616
	s_sub_i32 s5, s1, s5                                       // 000000000750: 81850501
	s_add_i32 s6, s1, s6                                       // 000000000754: 81060601
	s_cmp_ge_u32 s5, s22                                       // 000000000758: BF091605
	s_cselect_b32 s5, s6, s5                                   // 00000000075C: 85050506
	s_sub_i32 s6, s5, s22                                      // 000000000760: 81861605
	s_cmp_ge_u32 s5, s22                                       // 000000000764: BF091605
	s_cselect_b32 s5, s6, s5                                   // 000000000768: 85050506
	v_add_nc_u32_e32 v5, s5, v4                                // 00000000076C: 4A0A0805
	v_lshlrev_b32_e32 v6, 4, v5                                // 000000000770: 340C0A84
	v_mul_lo_u32 v5, 0x1c64e6d, v5                             // 000000000774: D5690005 00020AFF 01C64E6D
	buffer_load_dwordx4 v[9:12], v6, s[12:15], 0 offen         // 000000000780: E0381000 80030906
	v_cvt_f32_u32_e32 v6, s1                                   // 000000000788: 7E0C0C01
	s_add_i32 s1, s1, 1                                        // 00000000078C: 81018101
	v_add_lshl_u32 v5, s4, v5, 4                               // 000000000790: D7470005 02120A04
	s_cmp_ge_u32 s1, s0                                        // 000000000798: BF090001
	s_waitcnt lgkmcnt(0)                                       // 00000000079C: BF8CC07F
	v_add_f32_e32 v6, s2, v6                                   // 0000000007A0: 060C0C02
	v_mul_f32_e32 v6, 0.15915494, v6                           // 0000000007A4: 100C0CF8
	v_sin_f32_e32 v6, v6                                       // 0000000007A8: 7E0C6B06
	s_waitcnt vmcnt(0)                                         // 0000000007AC: BF8C3F70
	v_fmac_f32_e32 v0, v6, v9                                  // 0000000007B0: 56001306
	v_fmac_f32_e32 v1, v6, v10                                 // 0000000007B4: 56021506
	v_fmac_f32_e32 v2, v6, v11                                 // 0000000007B8: 56041706
	v_fmac_f32_e32 v3, v6, v12                                 // 0000000007BC: 56061906
	v_mul_f32_e32 v9, 0x3dcccccd, v0                           // 0000000007C0: 101200FF 3DCCCCCD
	v_mul_f32_e32 v10, 0x3dcccccd, v1                          // 0000000007C8: 101402FF 3DCCCCCD
	v_mul_f32_e32 v11, 0x3dcccccd, v2                          // 0000000007D0: 101604FF 3DCCCCCD
	v_mul_f32_e32 v12, 0x3dcccccd, v3                          // 0000000007D8: 101806FF 3DCCCCCD
	buffer_store_dwordx4 v[9:12], v5, s[8:11], 0 offen         // 0000000007E0: E0781000 80020905
	s_cbranch_scc0 _L14                                        // 0000000007E8: BF84FFD5
_L13:
	s_inst_prefetch 0x2                                        // 0000000007EC: BFA00002
	s_cbranch_execnz _L15                                      // 0000000007F0: BF89008D
_L8:
	s_buffer_load_dword s0, s[12:15], 0x8                      // 0000000007F4: F4200006 FA000008
	v_mov_b32_e32 v3, 0                                        // 0000000007FC: 7E060280
	v_mov_b32_e32 v2, 0                                        // 000000000800: 7E040280
	v_mov_b32_e32 v1, 0                                        // 000000000804: 7E020280
	v_mov_b32_e32 v0, 0                                        // 000000000808: 7E000280
	s_mov_b32 s1, 0                                            // 00000000080C: BE810380
	s_waitcnt lgkmcnt(0)                                       // 000000000810: BF8CC07F
	s_cmp_eq_u32 s0, 0                                         // 000000000814: BF068000
	s_cbranch_scc1 _L15                                        // 000000000818: BF850083
	v_lshlrev_b32_e32 v0, 4, v8                                // 00000000081C: 34001084
	s_mov_b32 s3, 0xbc46c6a5                                   // 000000000820: BE8303FF BC46C6A5
	s_buffer_load_dword s2, s[12:15], 0x10                     // 000000000828: F4200086 FA000010
	buffer_load_dwordx4 v[4:7], v0, s[12:15], 0 offen          // 000000000830: E0381000 80030400
	v_mov_b32_e32 v0, 0                                        // 000000000838: 7E000280
	v_mov_b32_e32 v1, v0                                       // 00000000083C: 7E020300
	v_mov_b32_e32 v2, v0                                       // 000000000840: 7E040300
	v_mov_b32_e32 v3, v0                                       // 000000000844: 7E060300
_L16:
	v_cvt_f32_u32_e32 v9, s1                                   // 000000000848: 7E120C01
	s_waitcnt vmcnt(0)                                         // 00000000084C: BF8C3F70
	v_sqrt_f32_e64 v12, |v6|                                   // 000000000850: D5B3010C 00000106
	v_mul_f32_e32 v13, v6, v6                                  // 000000000858: 101A0D06
	v_mul_f32_e32 v15, 0xbe13bb63, v6                          // 00000000085C: 101E0CFF BE13BB63
	v_mul_f32_e32 v16, v4, v5                                  // 000000000864: 10200B04
	s_waitcnt lgkmcnt(0)                                       // 000000000868: BF8CC07F
	v_fma_f32 v9, 0x3c23d70a, v9, s2                           // 00000000086C: D54B0009 000A12FF 3C23D70A
	v_mul_f32_e32 v10, 0x3e13bb63, v4                          // 000000000878: 101408FF 3E13BB63
	v_add_f32_e64 v14, |v7|, 1.0                               // 000000000880: D503010E 0001E507
	v_exp_f32_e32 v15, v15                                     // 000000000888: 7E1E4B0F
	v_add_f32_e64 v16, |v16|, 1.0                              // 00000000088C: D5030110 0001E510
	v_mul_f32_e32 v17, v9, v7                                  // 000000000894: 10220F09
	v_mul_f32_e32 v19, v9, v4                                  // 000000000898: 10260909
	v_mul_f32_e32 v20, v9, v5                                  // 00000000089C: 10280B09
	v_mul_f32_e32 v21, v9, v6                                  // 0000000008A0: 102A0D09
	v_mul_f32_e32 v12, v13, v12                                // 0000000008A4: 1018190D
	v_max_f32_e64 v18, |v17|, 1.0                              // 0000000008A8: D5100112 0001E511
	v_min_f32_e64 v23, |v17|, 1.0                              // 0000000008B0: D50F0117 0001E511
	v_cmp_ge_f32_e32 vcc_lo, 0, v17                            // 0000000008B8: 7C0C2280
	v_add_f32_e32 v13, v9, v4                                  // 0000000008BC: 061A0909
	v_add_f32_e32 v22, v9, v5                                  // 0000000008C0: 062C0B09
	v_rcp_f32_e32 v18, v18                                     // 0000000008C4: 7E245512
	v_add_f32_e32 v9, v9, v7                                   // 0000000008C8: 06120F09
	v_cndmask_b32_e32 v25, 1.0, v17, vcc_lo                    // 0000000008CC: 023222F2
	v_mul_f32_e32 v21, 0.15915494, v21                         // 0000000008D0: 102A2AF8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000008D4: 102C2CF8
	v_mul_f32_e32 v13, 0.15915494, v13                         // 0000000008D8: 101A1AF8
	v_mul_f32_e32 v9, 0.15915494, v9                           // 0000000008DC: 101212F8
	v_cmp_le_f32_e32 vcc_lo, 0, v25                            // 0000000008E0: 7C063280
	v_add_f32_e64 v11, |v5|, 1.0                               // 0000000008E4: D503010B 0001E505
	v_exp_f32_e32 v10, v10                                     // 0000000008EC: 7E144B0A
	v_mul_f32_e32 v18, v23, v18                                // 0000000008F0: 10242517
	v_sin_f32_e32 v9, v9                                       // 0000000008F4: 7E126B09
	v_sqrt_f32_e32 v14, v14                                    // 0000000008F8: 7E1C670E
	v_log_f32_e32 v16, v16                                     // 0000000008FC: 7E204F10
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000900: 102626F8
	v_mul_f32_e32 v23, v18, v18                                // 000000000904: 102E2512
	v_mul_f32_e32 v24, 0x3f7ffea5, v18                         // 000000000908: 103024FF 3F7FFEA5
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000910: 102828F8
	v_sin_f32_e32 v13, v13                                     // 000000000914: 7E1A6B0D
	v_log_f32_e32 v11, v11                                     // 000000000918: 7E164F0B
	v_mul_f32_e32 v18, v23, v18                                // 00000000091C: 10242517
	v_fmaak_f32 v26, s3, v23, 0x3d5be101                       // 000000000920: 5A342E03 3D5BE101
	v_sin_f32_e32 v19, v19                                     // 000000000928: 7E266B13
	v_cos_f32_e32 v20, v20                                     // 00000000092C: 7E286D14
	v_mul_f32_e32 v10, 0x3f317218, v10                         // 000000000930: 101414FF 3F317218
	v_mul_f32_e32 v27, v18, v23                                // 000000000938: 10362F12
	v_fmac_f32_e32 v24, 0xbeaa5476, v18                        // 00000000093C: 563024FF BEAA5476
	v_fmaak_f32 v18, v26, v23, 0xbdf0555d                      // 000000000944: 5A242F1A BDF0555D
	v_sin_f32_e32 v26, v21                                     // 00000000094C: 7E346B15
	v_fmac_f32_e32 v3, v12, v14                                // 000000000950: 56061D0C
	v_mul_f32_e32 v23, v27, v23                                // 000000000954: 102E2F1B
	v_fmac_f32_e32 v24, 0x3e468bc1, v27                        // 000000000958: 563036FF 3E468BC1
	v_mul_f32_e32 v12, 0x3f317218, v16                         // 000000000960: 101820FF 3F317218
	v_fmac_f32_e32 v2, v10, v11                                // 000000000968: 5604170A
	v_fmac_f32_e32 v0, v19, v20                                // 00000000096C: 56002913
	v_mul_f32_e32 v4, 0x3f8147ae, v4                           // 000000000970: 100808FF 3F8147AE
	v_fmac_f32_e32 v24, v23, v18                               // 000000000978: 56302517
	v_cos_f32_e32 v18, v21                                     // 00000000097C: 7E246D15
	v_cos_f32_e32 v21, v22                                     // 000000000980: 7E2A6D16
	v_cndmask_b32_e32 v23, -1.0, v25, vcc_lo                   // 000000000984: 022E32F3
	v_cmp_gt_f32_e64 vcc_lo, |v17|, 1.0                        // 000000000988: D404016A 0001E511
	v_fmaak_f32 v22, -2.0, v24, 0x3fc90fdb                     // 000000000990: 5A2C30F5 3FC90FDB
	v_mul_f32_e32 v5, 0x3f8147ae, v5                           // 000000000998: 100A0AFF 3F8147AE
	v_mul_f32_e32 v6, 0x3f8147ae, v6                           // 0000000009A0: 100C0CFF 3F8147AE
	v_mul_f32_e32 v7, 0x3f8147ae, v7                           // 0000000009A8: 100E0EFF 3F8147AE
	s_add_i32 s1, s1, 1                                        // 0000000009B0: 81018101
	v_cndmask_b32_e32 v17, 0, v22, vcc_lo                      // 0000000009B4: 02222C80
	v_mul_f32_e32 v22, v23, v26                                // 0000000009B8: 102C3517
	v_rcp_f32_e32 v18, v18                                     // 0000000009BC: 7E245512
	v_mul_f32_e64 v23, |v9|, |v9|                              // 0000000009C0: D5080317 00021309
	v_mul_f32_e32 v15, v21, v15                                // 0000000009C8: 101E1F15
	v_add_f32_e32 v17, v17, v24                                // 0000000009CC: 06223111
	s_cmp_ge_u32 s1, s0                                        // 0000000009D0: BF090001
	v_mul_f32_e64 v9, |v9|, v23                                // 0000000009D4: D5080109 00022F09
	v_mul_f32_e32 v14, v15, v13                                // 0000000009DC: 101C1B0F
	v_mul_f32_e32 v16, v22, v17                                // 0000000009E0: 10202316
	v_fmac_f32_e32 v0, v15, v13                                // 0000000009E4: 56001B0F
	v_mul_f32_e32 v10, v12, v9                                 // 0000000009E8: 1014130C
	v_fma_f32 v11, v12, v9, v14                                // 0000000009EC: D54B000B 043A130C
	v_fmac_f32_e32 v1, v16, v18                                // 0000000009F4: 56022510
	v_fmac_f32_e32 v4, 0x3c23d70a, v0                          // 0000000009F8: 560800FF 3C23D70A
	v_fmac_f32_e32 v2, v10, v14                                // 000000000A00: 56041D0A
	v_add_f32_e32 v3, v3, v11                                  // 000000000A04: 06061703
	v_fmac_f32_e32 v1, v12, v9                                 // 000000000A08: 5602130C
	v_fmac_f32_e32 v6, 0x3c23d70a, v2                          // 000000000A0C: 560C04FF 3C23D70A
	v_fmac_f32_e32 v7, 0x3c23d70a, v3                          // 000000000A14: 560E06FF 3C23D70A
	v_fmac_f32_e32 v5, 0x3c23d70a, v1                          // 000000000A1C: 560A02FF 3C23D70A
	s_cbranch_scc0 _L16                                        // 000000000A24: BF84FF88
_L15:
	v_lshlrev_b32_e32 v4, 4, v8                                // 000000000A28: 34081084
	buffer_store_dwordx4 v[0:3], v4, s[8:11], 0 offen          // 000000000A2C: E0781000 80020004
_L0:
	s_endpgm                                                   // 000000000A34: BF810000
