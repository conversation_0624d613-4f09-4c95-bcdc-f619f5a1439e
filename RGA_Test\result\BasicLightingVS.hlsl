struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float2 TexCoord : TEXCOORD2;
  float4 Color : TEXCOORD3;
  float3 ViewDir : TEXCOORD4;
  float3 LightDir : TEXCOORD5;
};

cbuffer PerFrame : register(b0)
{
  uniform float4x4 ViewProjectionMatrix;
  uniform float4x4 WorldMatrix;
  uniform float4x4 NormalMatrix;
  uniform float3 LightDirection;
  uniform float3 CameraPosition;
  uniform float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4 worldPos = mul(float4(input.Position, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  output.Position = mul(worldPos, ViewProjectionMatrix);
  output.Normal = normalize(mul(input.Normal, (float3x3)NormalMatrix));
  output.TexCoord = input.TexCoord;
  output.Color = input.Color;
  output.ViewDir = normalize(CameraPosition - output.WorldPos);
  output.LightDir = normalize((-LightDirection));
  return output;
}

