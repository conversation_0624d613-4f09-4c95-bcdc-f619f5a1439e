// Modern HLSL Features Test Shader
// Tests all the newly added features in hlsloptconv

// Modern texture types (without register syntax for compatibility)
Texture2D diffuseTexture;
Texture2D normalTexture;
TextureCube skyboxTexture;
Texture2DArray textureArray;

// Modern sampler types
SamplerState linearSampler;
SamplerComparisonState shadowSampler;

// Modern buffer types
StructuredBuffer<float4> vertexBuffer;
Buffer<float> indexBuffer;

// Constant buffer with modern syntax
cbuffer SceneConstants
{
    float4x4 worldMatrix;
    float4x4 viewMatrix;
    float4x4 projMatrix;
    float3 lightDirection;
    float lightIntensity;
    float3 cameraPosition;
    float time;
};

// Input structure with modern semantics
struct VertexInput
{
    float3 position : POSITION;
    float3 normal : NORMAL;
    float2 texCoord : TEXCOORD0;
    uint vertexID : SV_VertexID;
    uint instanceID : SV_InstanceID;
};

// Output structure with modern semantics
struct PixelInput
{
    float4 position : SV_POSITION;
    float3 worldPos : TEXCOORD0;
    float3 normal : TEXCOORD1;
    float2 texCoord : TEXCOORD2;
    float3 viewDir : TEXCOORD3;
};

// Vertex shader using modern HLSL functions
PixelInput VSMain(VertexInput input)
{
    PixelInput output;
    
    // Test modern vector construction
    float4 worldPos = mul(float4(input.position, 1.0), worldMatrix);
    output.worldPos = worldPos.xyz;
    
    // Test matrix multiplication
    float4 viewPos = mul(worldPos, viewMatrix);
    output.position = mul(viewPos, projMatrix);
    
    // Test normalize function
    output.normal = normalize(mul(input.normal, (float3x3)worldMatrix));
    
    // Test modern texture coordinate handling
    output.texCoord = input.texCoord;
    
    // Test vector operations
    output.viewDir = normalize(cameraPosition - worldPos.xyz);
    
    return output;
}

// Pixel shader using modern HLSL functions and texture sampling
float4 PSMain(PixelInput input) : SV_TARGET
{
    // Test modern texture sampling
    float4 diffuseColor = diffuseTexture.Sample(linearSampler, input.texCoord);
    float3 normalMap = normalTexture.Sample(linearSampler, input.texCoord).xyz;
    
    // Test modern math functions
    float3 normal = normalize(input.normal);
    float3 lightDir = normalize(-lightDirection);
    
    // Test dot product and saturate
    float NdotL = saturate(dot(normal, lightDir));
    
    // Test lerp function
    float3 ambient = lerp(float3(0.1, 0.1, 0.2), float3(0.2, 0.2, 0.3), 0.5);
    
    // Test smoothstep function
    float falloff = smoothstep(0.0, 1.0, NdotL);
    
    // Test step function
    float threshold = step(0.5, NdotL);
    
    // Test fwidth for derivatives (pixel shader only)
    float2 texelSize = fwidth(input.texCoord);
    
    // Test ddx and ddy
    float2 ddxCoord = ddx(input.texCoord);
    float2 ddyCoord = ddy(input.texCoord);
    
    // Test rsqrt
    float invLength = rsqrt(dot(input.viewDir, input.viewDir));
    
    // Combine all effects
    float3 finalColor = diffuseColor.rgb * ambient * falloff * threshold;
    finalColor += texelSize.x * 0.01; // Use derivative result
    finalColor += (ddxCoord.x + ddyCoord.y) * 0.001; // Use derivative results
    finalColor *= invLength * 0.1 + 0.9; // Use rsqrt result
    
    return float4(finalColor, diffuseColor.a);
}

// Compute shader test (if supported)
[numthreads(8, 8, 1)]
void CSMain(uint3 id : SV_DispatchThreadID)
{
    // Test structured buffer access
    float4 data = vertexBuffer[id.x];
    
    // Test buffer access
    float index = indexBuffer[id.y];
    
    // Modern compute operations would go here
    // This is mainly to test parsing of compute shader syntax
}
