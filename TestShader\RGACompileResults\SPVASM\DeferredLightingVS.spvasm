; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 24
; Schema: 0
               OpCapability Shader
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_TEXCOORD0 %gl_Position %out_var_TEXCOORD0
               OpSource HLSL 600
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_TEXCOORD0 Location 1
               OpDecorate %out_var_TEXCOORD0 Location 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v3float = OpTypeVector %float 3
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
    %v4float = OpTypeVector %float 4
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v2float = OpTypePointer Output %v2float
       %void = OpTypeVoid
         %16 = OpTypeFunction %void
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v2float Output
       %main = OpFunction %void None %16
         %17 = OpLabel
         %18 = OpLoad %v3float %in_var_POSITION
         %19 = OpLoad %v2float %in_var_TEXCOORD0
         %20 = OpCompositeExtract %float %18 0
         %21 = OpCompositeExtract %float %18 1
         %22 = OpCompositeExtract %float %18 2
         %23 = OpCompositeConstruct %v4float %20 %21 %22 %float_1
               OpStore %gl_Position %23
               OpStore %out_var_TEXCOORD0 %19
               OpReturn
               OpFunctionEnd
