#version 320 es

// Shadow Mapping Vertex Shader - OpenGL ES Version
// Tests shadow map generation and depth rendering

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;
layout(location = 3) in vec4 aColor;

uniform mat4 uWorldMatrix;
uniform mat4 uViewMatrix;
uniform mat4 uProjectionMatrix;
uniform mat4 uLightViewProjectionMatrix;
uniform vec3 uLightPosition;
uniform vec3 uCameraPosition;

out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;
out vec4 vColor;
out vec4 vLightSpacePos;
out vec3 vLightDir;
out vec3 vViewDir;
out float vDepth;

void main()
{
    // Transform to world space
    vec4 worldPos = uWorldMatrix * vec4(aPosition, 1.0);
    vWorldPos = worldPos.xyz;
    
    // Transform to clip space
    vec4 viewPos = uViewMatrix * worldPos;
    gl_Position = uProjectionMatrix * viewPos;
    
    // Transform normal to world space
    vNormal = normalize((uWorldMatrix * vec4(aNormal, 0.0)).xyz);
    
    // Pass through texture coordinates and color
    vTexCoord = aTexCoord;
    vColor = aColor;
    
    // Calculate light space position for shadow mapping
    vLightSpacePos = uLightViewProjectionMatrix * worldPos;
    
    // Calculate light direction
    vLightDir = normalize(uLightPosition - vWorldPos);
    
    // Calculate view direction
    vViewDir = normalize(uCameraPosition - vWorldPos);
    
    // Store depth for shadow comparison
    vDepth = gl_Position.z / gl_Position.w;
}
