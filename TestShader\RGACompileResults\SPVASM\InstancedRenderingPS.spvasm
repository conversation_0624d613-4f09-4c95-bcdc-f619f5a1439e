; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 199
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %in_var_TEXCOORD0 %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %out_var_SV_TARGET
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_Material "type.Material"
               OpMemberName %type_Material 0 "BaseColor"
               OpMemberName %type_Material 1 "Metallic"
               OpMemberName %type_Material 2 "Roughness"
               OpMemberName %type_Material 3 "LightDirection"
               OpMemberName %type_Material 4 "LightColor"
               OpMemberName %type_Material 5 "AmbientColor"
               OpMemberName %type_Material 6 "Time"
               OpName %Material "Material"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %DiffuseTextureArray "DiffuseTextureArray"
               OpName %type_2d_image "type.2d.image"
               OpName %NoiseTexture "NoiseTexture"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %out_var_SV_TARGET "out.var.SV_TARGET"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpDecorate %in_var_TEXCOORD0 Location 0
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 6
               OpDecorate %out_var_SV_TARGET Location 0
               OpDecorate %Material DescriptorSet 0
               OpDecorate %Material Binding 0
               OpDecorate %DiffuseTextureArray DescriptorSet 0
               OpDecorate %DiffuseTextureArray Binding 0
               OpDecorate %NoiseTexture DescriptorSet 0
               OpDecorate %NoiseTexture Binding 2
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpMemberDecorate %type_Material 0 Offset 0
               OpMemberDecorate %type_Material 1 Offset 16
               OpMemberDecorate %type_Material 2 Offset 20
               OpMemberDecorate %type_Material 3 Offset 32
               OpMemberDecorate %type_Material 4 Offset 48
               OpMemberDecorate %type_Material 5 Offset 64
               OpMemberDecorate %type_Material 6 Offset 76
               OpDecorate %type_Material Block
        %int = OpTypeInt 32 1
      %int_3 = OpConstant %int 3
      %int_5 = OpConstant %int 5
      %int_6 = OpConstant %int 6
      %int_4 = OpConstant %int 4
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
   %float_32 = OpConstant %float 32
  %float_256 = OpConstant %float 256
    %float_1 = OpConstant %float 1
  %float_0_5 = OpConstant %float 0.5
%float_0_300000012 = OpConstant %float 0.300000012
    %float_2 = OpConstant %float 2
    %v3float = OpTypeVector %float 3
%float_0_699999988 = OpConstant %float 0.699999988
%float_0_800000012 = OpConstant %float 0.800000012
%float_0_899999976 = OpConstant %float 0.899999976
         %38 = OpConstantComposite %v3float %float_0_699999988 %float_0_800000012 %float_0_899999976
%float_0_100000001 = OpConstant %float 0.100000001
    %v4float = OpTypeVector %float 4
         %41 = OpConstantComposite %v4float %float_0_100000001 %float_0_800000012 %float_0 %float_1
         %42 = OpConstantComposite %v4float %float_0 %float_0_899999976 %float_0 %float_1
%float_0_200000003 = OpConstant %float 0.200000003
         %44 = OpConstantComposite %v4float %float_0_800000012 %float_0_200000003 %float_0 %float_1
    %float_4 = OpConstant %float 4
%float_0_0500000007 = OpConstant %float 0.0500000007
%type_Material = OpTypeStruct %v4float %float %float %v3float %v3float %v3float %float
%_ptr_Uniform_type_Material = OpTypePointer Uniform %type_Material
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %58 = OpTypeFunction %void
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%type_sampled_image = OpTypeSampledImage %type_2d_image_array
       %bool = OpTypeBool
%_ptr_Uniform_float = OpTypePointer Uniform %float
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image
   %Material = OpVariable %_ptr_Uniform_type_Material Uniform
%DiffuseTextureArray = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NoiseTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_float Input
%out_var_SV_TARGET = OpVariable %_ptr_Output_v4float Output
%float_n0_00999999978 = OpConstant %float -0.00999999978
     %v4bool = OpTypeVector %bool 4
       %main = OpFunction %void None %58
         %64 = OpLabel
         %65 = OpLoad %v3float %in_var_TEXCOORD0
         %66 = OpLoad %v3float %in_var_TEXCOORD1
         %67 = OpLoad %v2float %in_var_TEXCOORD2
         %68 = OpLoad %v4float %in_var_TEXCOORD3
         %69 = OpLoad %v3float %in_var_TEXCOORD4
         %70 = OpLoad %float %in_var_TEXCOORD5
         %71 = OpLoad %float %in_var_TEXCOORD6
         %72 = OpExtInst %v3float %1 Normalize %66
         %73 = OpAccessChain %_ptr_Uniform_v3float %Material %int_3
         %74 = OpLoad %v3float %73
         %75 = OpFNegate %v3float %74
         %76 = OpExtInst %v3float %1 Normalize %75
         %77 = OpExtInst %v3float %1 Normalize %69
         %78 = OpConvertFToS %int %70
         %79 = OpLoad %type_2d_image_array %DiffuseTextureArray
         %80 = OpLoad %type_sampler %LinearSampler
         %81 = OpConvertSToF %float %78
         %82 = OpCompositeExtract %float %67 0
         %83 = OpCompositeExtract %float %67 1
         %84 = OpCompositeConstruct %v3float %82 %83 %81
         %85 = OpSampledImage %type_sampled_image %79 %80
         %86 = OpImageSampleImplicitLod %v4float %85 %84 None
         %87 = OpFMul %v4float %86 %68
         %88 = OpFOrdLessThan %bool %70 %float_1
               OpSelectionMerge %89 None
               OpBranchConditional %88 %90 %91
         %90 = OpLabel
               OpBranch %89
         %91 = OpLabel
         %92 = OpFOrdLessThan %bool %70 %float_2
         %93 = OpCompositeConstruct %v4bool %92 %92 %92 %92
         %94 = OpSelect %v4float %93 %42 %44
               OpBranch %89
         %89 = OpLabel
         %95 = OpPhi %v4float %41 %90 %94 %91
         %96 = OpCompositeConstruct %v2float %70 %70
         %97 = OpFAdd %v2float %67 %96
         %98 = OpLoad %type_2d_image %NoiseTexture
         %99 = OpLoad %type_sampler %LinearSampler
        %100 = OpVectorTimesScalar %v2float %97 %float_0_100000001
        %101 = OpSampledImage %type_sampled_image_0 %98 %99
        %102 = OpImageSampleImplicitLod %v4float %101 %100 None
        %103 = OpCompositeExtract %float %102 0
        %104 = OpFSub %float %103 %float_0_5
        %105 = OpFMul %float %104 %float_0_200000003
        %106 = OpCompositeExtract %float %95 1
        %107 = OpFAdd %float %106 %105
        %108 = OpCompositeExtract %float %95 0
        %109 = OpDot %float %72 %76
        %110 = OpExtInst %float %1 NMax %float_0 %109
        %111 = OpVectorShuffle %v3float %87 %87 0 1 2
        %112 = OpAccessChain %_ptr_Uniform_v3float %Material %int_4
        %113 = OpLoad %v3float %112
        %114 = OpFMul %v3float %111 %113
        %115 = OpVectorTimesScalar %v3float %114 %110
        %116 = OpFAdd %v3float %76 %77
        %117 = OpExtInst %v3float %1 Normalize %116
        %118 = OpDot %float %72 %117
        %119 = OpExtInst %float %1 NMax %float_0 %118
        %120 = OpFSub %float %float_1 %107
        %121 = OpExtInst %float %1 FMix %float_32 %float_256 %120
        %122 = OpExtInst %float %1 Pow %119 %121
        %123 = OpVectorTimesScalar %v3float %113 %122
        %124 = OpVectorTimesScalar %v3float %123 %108
        %125 = OpAccessChain %_ptr_Uniform_v3float %Material %int_5
        %126 = OpLoad %v3float %125
        %127 = OpFMul %v3float %126 %111
        %128 = OpFAdd %v3float %127 %115
        %129 = OpFAdd %v3float %128 %124
               OpSelectionMerge %130 None
               OpBranchConditional %88 %131 %130
        %131 = OpLabel
        %132 = OpFNegate %v3float %76
        %133 = OpDot %float %132 %72
        %134 = OpExtInst %float %1 NMax %float_0 %133
        %135 = OpDot %float %77 %132
        %136 = OpExtInst %float %1 NMax %float_0 %135
        %137 = OpExtInst %float %1 Pow %136 %float_4
        %138 = OpFMul %float %137 %134
        %139 = OpVectorTimesScalar %v3float %111 %138
        %140 = OpVectorTimesScalar %v3float %139 %float_0_5
        %141 = OpFAdd %v3float %129 %140
               OpSelectionMerge %142 None
               OpBranchConditional %88 %143 %142
        %143 = OpLabel
        %144 = OpVectorShuffle %v2float %65 %65 0 2
        %145 = OpVectorTimesScalar %v2float %144 %float_0_100000001
        %146 = OpAccessChain %_ptr_Uniform_float %Material %int_6
        %147 = OpLoad %float %146
        %148 = OpFMul %float %147 %float_0_100000001
        %149 = OpCompositeConstruct %v2float %148 %148
        %150 = OpFAdd %v2float %145 %149
        %151 = OpLoad %type_2d_image %NoiseTexture
        %152 = OpLoad %type_sampler %LinearSampler
        %153 = OpVectorTimesScalar %v2float %150 %float_0_100000001
        %154 = OpSampledImage %type_sampled_image_0 %151 %152
        %155 = OpImageSampleImplicitLod %v4float %154 %153 None
        %156 = OpCompositeExtract %float %155 0
        %157 = OpFMul %float %147 %float_2
        %158 = OpCompositeExtract %float %65 0
        %159 = OpFMul %float %158 %float_0_100000001
        %160 = OpFAdd %float %157 %159
        %161 = OpExtInst %float %1 Sin %160
        %162 = OpFMul %float %161 %156
        %163 = OpFMul %float %162 %float_0_100000001
        %164 = OpCompositeExtract %float %141 1
        %165 = OpFAdd %float %164 %163
        %166 = OpCompositeInsert %v3float %165 %141 1
        %167 = OpFMul %float %162 %float_0_0500000007
        %168 = OpCompositeExtract %float %141 2
        %169 = OpFSub %float %168 %167
        %170 = OpCompositeInsert %v3float %169 %166 2
               OpBranch %142
        %142 = OpLabel
        %171 = OpPhi %v3float %141 %131 %170 %143
               OpBranch %130
        %130 = OpLabel
        %172 = OpPhi %v3float %129 %89 %171 %142
        %173 = OpFOrdLessThan %bool %71 %float_1
               OpSelectionMerge %174 None
               OpBranchConditional %173 %175 %174
        %175 = OpLabel
        %176 = OpVectorTimesScalar %v3float %172 %float_0_5
        %177 = OpCompositeConstruct %v3float %71 %71 %71
        %178 = OpExtInst %v3float %1 FMix %176 %172 %177
        %179 = OpFOrdLessThan %bool %71 %float_0_300000012
               OpSelectionMerge %180 None
               OpBranchConditional %179 %181 %180
        %181 = OpLabel
        %182 = OpFSub %float %float_0_300000012 %71
        %183 = OpFMul %float %182 %float_2
        %184 = OpCompositeExtract %float %178 0
        %185 = OpFAdd %float %184 %183
        %186 = OpCompositeInsert %v3float %185 %178 0
               OpBranch %180
        %180 = OpLabel
        %187 = OpPhi %v3float %178 %175 %186 %181
               OpBranch %174
        %174 = OpLabel
        %188 = OpPhi %v3float %172 %130 %187 %180
        %189 = OpExtInst %float %1 Length %65
        %190 = OpFMul %float %189 %float_n0_00999999978
        %191 = OpExtInst %float %1 Exp %190
        %192 = OpCompositeConstruct %v3float %191 %191 %191
        %193 = OpExtInst %v3float %1 FMix %38 %188 %192
        %194 = OpCompositeExtract %float %87 3
        %195 = OpCompositeExtract %float %193 0
        %196 = OpCompositeExtract %float %193 1
        %197 = OpCompositeExtract %float %193 2
        %198 = OpCompositeConstruct %v4float %195 %196 %197 %194
               OpStore %out_var_SV_TARGET %198
               OpReturn
               OpFunctionEnd
