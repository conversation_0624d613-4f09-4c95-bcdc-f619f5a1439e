#version 320 es

// Tessellation Fragment Shader - OpenGL ES Version
// Tests fragment processing for tessellated geometry

precision highp float;

in vec3 teWorldPos;
in vec3 teNormal;
in vec2 teTexCoord;
in float teDisplacement;

out vec4 fragColor;

uniform vec3 uLightDirection;
uniform vec3 uLightColor;
uniform vec3 uCameraPosition;
uniform sampler2D uDiffuseTexture;
uniform sampler2D uNormalTexture;
uniform float uTime;

vec3 calculateNormalFromHeight(vec2 texCoord, float heightScale)
{
    vec2 texelSize = vec2(1.0) / vec2(textureSize(uDiffuseTexture, 0));
    
    float heightL = texture(uDiffuseTexture, texCoord - vec2(texelSize.x, 0.0)).r;
    float heightR = texture(uDiffuseTexture, texCoord + vec2(texelSize.x, 0.0)).r;
    float heightD = texture(uDiffuseTexture, texCoord - vec2(0.0, texelSize.y)).r;
    float heightU = texture(uDiffuseTexture, texCoord + vec2(0.0, texelSize.y)).r;
    
    vec3 normal;
    normal.x = (heightL - heightR) * heightScale;
    normal.y = 2.0;
    normal.z = (heightD - heightU) * heightScale;
    
    return normalize(normal);
}

void main()
{
    // Sample textures
    vec4 diffuseColor = texture(uDiffuseTexture, teTexCoord);
    vec3 normalMap = texture(uNormalTexture, teTexCoord).rgb * 2.0 - 1.0;
    
    // Calculate surface normal (combine vertex normal with height-based normal)
    vec3 heightNormal = calculateNormalFromHeight(teTexCoord, 10.0);
    vec3 finalNormal = normalize(teNormal + heightNormal * 0.5 + normalMap * 0.3);
    
    // Lighting calculations
    vec3 lightDir = normalize(-uLightDirection);
    vec3 viewDir = normalize(uCameraPosition - teWorldPos);
    vec3 halfDir = normalize(lightDir + viewDir);
    
    // Diffuse lighting
    float NdotL = max(0.0, dot(finalNormal, lightDir));
    vec3 diffuse = uLightColor * diffuseColor.rgb * NdotL;
    
    // Specular lighting
    float NdotH = max(0.0, dot(finalNormal, halfDir));
    float specularPower = 32.0 + teDisplacement * 64.0; // Vary specular based on displacement
    vec3 specular = uLightColor * pow(NdotH, specularPower) * 0.5;
    
    // Ambient lighting
    vec3 ambient = diffuseColor.rgb * 0.2;
    
    // Color based on displacement
    vec3 displacementColor = mix(vec3(0.2, 0.4, 0.8), vec3(1.0, 0.8, 0.6), teDisplacement);
    
    // Combine lighting
    vec3 finalColor = (ambient + diffuse + specular) * displacementColor;
    
    // Add some animation
    float pulse = sin(uTime * 2.0 + teDisplacement * 20.0) * 0.1 + 0.9;
    finalColor *= pulse;
    
    fragColor = vec4(finalColor, diffuseColor.a);
}
