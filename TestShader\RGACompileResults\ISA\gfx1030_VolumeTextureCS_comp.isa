_amdgpu_cs_main:
	s_mov_b32 s6, s1                                           // 000000000000: BE860301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v0, s2, 2, v0                               // 000000000008: D7460000 04010402
	s_mov_b32 s7, s1                                           // 000000000010: BE870301
	v_lshl_add_u32 v1, s3, 2, v1                               // 000000000014: D7460001 04050403
	s_load_dwordx8 s[8:15], s[6:7], null                       // 00000000001C: F40C0203 FA000000
	v_lshl_add_u32 v2, s4, 2, v2                               // 000000000024: D7460002 04090404
	s_waitcnt lgkmcnt(0)                                       // 00000000002C: BF8CC07F
	v_alignbit_b32 v3, s10, s9, 30                             // 000000000030: D54E0003 0278120A
	s_bfe_u32 s1, s10, 0xe000e                                 // 000000000038: 9381FF0A 000E000E
	s_and_b32 s2, s12, 0x1fff                                  // 000000000040: 8702FF0C 00001FFF
	s_bfe_u32 s3, s11, 0x4000c                                 // 000000000048: 9383FF0B 0004000C
	s_add_i32 s1, s1, 1                                        // 000000000050: 81018101
	v_readfirstlane_b32 s5, v3                                 // 000000000054: 7E0A0503
	s_bfe_u32 s4, s12, 0xd0010                                 // 000000000058: 9384FF0C 000D0010
	s_add_i32 s2, s2, 1                                        // 000000000060: 81028102
	s_lshr_b32 s1, s1, s3                                      // 000000000064: 90010301
	s_lshr_b32 s16, s2, s3                                     // 000000000068: 90100302
	s_and_b32 s5, s5, 0x3fff                                   // 00000000006C: 8705FF05 00003FFF
	s_sub_i32 s2, s2, s4                                       // 000000000074: 81820402
	s_add_i32 s5, s5, 1                                        // 000000000078: 81058105
	s_max_u32 s4, s1, 1                                        // 00000000007C: 84848101
	s_lshr_b32 s1, s5, s3                                      // 000000000080: 90010305
	s_and_b32 s0, 1, s13                                       // 000000000084: 87000D81
	s_max_u32 s3, s16, 1                                       // 000000000088: 84838110
	s_max_u32 s5, s1, 1                                        // 00000000008C: 84858101
	s_cmp_eq_u32 s0, 1                                         // 000000000090: BF068100
	v_cmp_le_u32_e32 vcc_lo, s5, v0                            // 000000000094: 7D860005
	s_cselect_b32 s16, s2, s3                                  // 000000000098: 85100302
	v_cmp_le_u32_e64 s0, s4, v1                                // 00000000009C: D4C30000 00020204
	v_cmp_le_u32_e64 s2, s16, v2                               // 0000000000A4: D4C30002 00020410
	s_or_b64 s[0:1], vcc, s[0:1]                               // 0000000000AC: 8880006A
	s_or_b64 s[0:1], s[2:3], s[0:1]                            // 0000000000B0: 88800002
	s_xor_b64 s[0:1], s[0:1], -1                               // 0000000000B4: 8980C100
	s_and_saveexec_b64 s[2:3], s[0:1]                          // 0000000000B8: BE822400
	s_cbranch_execz _L0                                        // 0000000000BC: BF880153
	s_load_dwordx4 s[0:3], s[6:7], null                        // 0000000000C0: F4080003 FA000000
	s_add_i32 s5, s5, -1                                       // 0000000000C8: 8105C105
	s_add_i32 s4, s4, -1                                       // 0000000000CC: 8104C104
	v_cvt_f32_u32_e32 v4, s5                                   // 0000000000D0: 7E080C05
	v_cvt_f32_u32_e32 v5, s4                                   // 0000000000D4: 7E0A0C04
	s_add_i32 s16, s16, -1                                     // 0000000000D8: 8110C110
	v_cvt_f32_u32_e32 v3, v0                                   // 0000000000DC: 7E060D00
	v_cvt_f32_u32_e32 v6, s16                                  // 0000000000E0: 7E0C0C10
	v_cvt_f32_u32_e32 v7, v1                                   // 0000000000E4: 7E0E0D01
	v_rcp_iflag_f32_e32 v4, v4                                 // 0000000000E8: 7E085704
	v_rcp_iflag_f32_e32 v5, v5                                 // 0000000000EC: 7E0A5705
	v_cvt_f32_u32_e32 v8, v2                                   // 0000000000F0: 7E100D02
	v_rcp_iflag_f32_e32 v6, v6                                 // 0000000000F4: 7E0C5706
	v_mul_f32_e32 v3, 0x42c80000, v3                           // 0000000000F8: 100606FF 42C80000
	v_mul_f32_e32 v7, 0x42c80000, v7                           // 000000000100: 100E0EFF 42C80000
	v_mul_f32_e32 v8, 0x42c80000, v8                           // 000000000108: 101010FF 42C80000
	v_fmaak_f32 v3, v3, v4, 0xc2480000                         // 000000000110: 5A060903 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000118: BF8CC07F
	s_buffer_load_dword s0, s[0:3], 0xc                        // 00000000011C: F4200000 FA00000C
	v_fmaak_f32 v5, v7, v5, 0xc2480000                         // 000000000124: 5A0A0B07 C2480000
	v_fmaak_f32 v6, v8, v6, 0xc2480000                         // 00000000012C: 5A0C0D08 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000134: BF8CC07F
	v_mul_f32_e64 v4, 0x3dcccccd, s0                           // 000000000138: D5080004 000000FF 3DCCCCCD
	v_mul_f32_e64 v7, 0x3d4ccccd, s0                           // 000000000144: D5080007 000000FF 3D4CCCCD
	v_fmamk_f32 v8, v3, 0x3c23d70a, v4                         // 000000000150: 58100903 3C23D70A
	v_fmamk_f32 v9, v5, 0x3c23d70a, v4                         // 000000000158: 58120905 3C23D70A
	v_fmac_f32_e32 v4, 0x3c23d70a, v6                          // 000000000160: 56080CFF 3C23D70A
	v_fmamk_f32 v10, v3, 0x3ca3d70a, v7                        // 000000000168: 58140F03 3CA3D70A
	v_fmamk_f32 v11, v5, 0x3ca3d70a, v7                        // 000000000170: 58160F05 3CA3D70A
	v_floor_f32_e32 v12, v8                                    // 000000000178: 7E184908
	v_floor_f32_e32 v13, v9                                    // 00000000017C: 7E1A4909
	v_floor_f32_e32 v14, v4                                    // 000000000180: 7E1C4904
	v_fract_f32_e32 v8, v8                                     // 000000000184: 7E104108
	v_floor_f32_e32 v15, v10                                   // 000000000188: 7E1E490A
	v_floor_f32_e32 v16, v11                                   // 00000000018C: 7E20490B
	v_fmac_f32_e32 v12, 0x42640000, v13                        // 000000000190: 56181AFF 42640000
	v_fract_f32_e32 v4, v4                                     // 000000000198: 7E084104
	v_mul_f32_e32 v13, v8, v8                                  // 00000000019C: 101A1108
	v_fmaak_f32 v8, -2.0, v8, 0x40400000                       // 0000000001A0: 5A1010F5 40400000
	v_fmac_f32_e32 v15, 0x42640000, v16                        // 0000000001A8: 561E20FF 42640000
	v_fmac_f32_e32 v12, 0x42e20000, v14                        // 0000000001B0: 56181CFF 42E20000
	v_fract_f32_e32 v9, v9                                     // 0000000001B8: 7E124109
	v_mul_f32_e32 v19, v4, v4                                  // 0000000001BC: 10260904
	v_mul_f32_e32 v8, v13, v8                                  // 0000000001C0: 1010110D
	v_fmaak_f32 v4, -2.0, v4, 0x40400000                       // 0000000001C4: 5A0808F5 40400000
	v_mul_f32_e32 v13, 0.15915494, v12                         // 0000000001CC: 101A18F8
	v_add_f32_e32 v14, 1.0, v12                                // 0000000001D0: 061C18F2
	v_add_f32_e32 v16, 0x42640000, v12                         // 0000000001D4: 062018FF 42640000
	v_add_f32_e32 v20, 0x42680000, v12                         // 0000000001DC: 062818FF 42680000
	v_add_f32_e32 v21, 0x42e20000, v12                         // 0000000001E4: 062A18FF 42E20000
	v_add_f32_e32 v22, 0x42e40000, v12                         // 0000000001EC: 062C18FF 42E40000
	v_add_f32_e32 v23, 0x432a0000, v12                         // 0000000001F4: 062E18FF 432A0000
	v_add_f32_e32 v12, 0x432b0000, v12                         // 0000000001FC: 061818FF 432B0000
	v_mul_f32_e32 v14, 0.15915494, v14                         // 000000000204: 101C1CF8
	v_mul_f32_e32 v21, 0.15915494, v21                         // 000000000208: 102A2AF8
	v_sin_f32_e32 v13, v13                                     // 00000000020C: 7E1A6B0D
	v_mul_f32_e32 v23, 0.15915494, v23                         // 000000000210: 102E2EF8
	v_mul_f32_e32 v12, 0.15915494, v12                         // 000000000214: 101818F8
	v_mul_f32_e32 v16, 0.15915494, v16                         // 000000000218: 102020F8
	v_mul_f32_e32 v20, 0.15915494, v20                         // 00000000021C: 102828F8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 000000000220: 102C2CF8
	v_sin_f32_e32 v14, v14                                     // 000000000224: 7E1C6B0E
	v_sin_f32_e32 v21, v21                                     // 000000000228: 7E2A6B15
	v_sin_f32_e32 v23, v23                                     // 00000000022C: 7E2E6B17
	v_sin_f32_e32 v12, v12                                     // 000000000230: 7E186B0C
	v_sin_f32_e32 v16, v16                                     // 000000000234: 7E206B10
	v_sin_f32_e32 v20, v20                                     // 000000000238: 7E286B14
	v_sin_f32_e32 v22, v22                                     // 00000000023C: 7E2C6B16
	v_fmac_f32_e32 v7, 0x3ca3d70a, v6                          // 000000000240: 560E0CFF 3CA3D70A
	v_mul_f32_e32 v18, v9, v9                                  // 000000000248: 10241309
	v_fmaak_f32 v9, -2.0, v9, 0x40400000                       // 00000000024C: 5A1212F5 40400000
	v_mul_f32_e32 v13, 0x472aee8c, v13                         // 000000000254: 101A1AFF 472AEE8C
	v_mul_f32_e32 v4, v19, v4                                  // 00000000025C: 10080913
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 000000000260: 101C1CFF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v21                         // 000000000268: 10262AFF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v23                         // 000000000270: 102A2EFF 472AEE8C
	v_mul_f32_e32 v12, 0x472aee8c, v12                         // 000000000278: 101818FF 472AEE8C
	v_floor_f32_e32 v17, v7                                    // 000000000280: 7E224907
	v_mul_f32_e32 v9, v18, v9                                  // 000000000284: 10121312
	v_fract_f32_e32 v13, v13                                   // 000000000288: 7E1A410D
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 00000000028C: 102020FF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v20                         // 000000000294: 102428FF 472AEE8C
	v_mul_f32_e32 v20, 0x472aee8c, v22                         // 00000000029C: 10282CFF 472AEE8C
	v_fract_f32_e32 v14, v14                                   // 0000000002A4: 7E1C410E
	v_fract_f32_e32 v21, v21                                   // 0000000002A8: 7E2A4115
	v_fract_f32_e32 v12, v12                                   // 0000000002AC: 7E18410C
	v_fract_f32_e32 v16, v16                                   // 0000000002B0: 7E204110
	v_fract_f32_e32 v18, v18                                   // 0000000002B4: 7E244112
	v_fract_f32_e32 v19, v19                                   // 0000000002B8: 7E264113
	v_fract_f32_e32 v20, v20                                   // 0000000002BC: 7E284114
	v_fmac_f32_e32 v15, 0x42e20000, v17                        // 0000000002C0: 561E22FF 42E20000
	v_sub_f32_e32 v14, v14, v13                                // 0000000002C8: 081C1B0E
	v_sub_f32_e32 v12, v12, v21                                // 0000000002CC: 08182B0C
	v_sub_f32_e32 v17, v18, v16                                // 0000000002D0: 08222112
	v_sub_f32_e32 v18, v20, v19                                // 0000000002D4: 08242714
	v_fract_f32_e32 v10, v10                                   // 0000000002D8: 7E14410A
	v_fmac_f32_e32 v13, v14, v8                                // 0000000002DC: 561A110E
	v_fmac_f32_e32 v21, v12, v8                                // 0000000002E0: 562A110C
	v_add_f32_e32 v12, 0x42640000, v15                         // 0000000002E4: 06181EFF 42640000
	v_add_f32_e32 v14, 0x42680000, v15                         // 0000000002EC: 061C1EFF 42680000
	v_fmac_f32_e32 v16, v17, v8                                // 0000000002F4: 56201111
	v_fmac_f32_e32 v19, v18, v8                                // 0000000002F8: 56261112
	v_add_f32_e32 v8, 1.0, v15                                 // 0000000002FC: 06101EF2
	v_mul_f32_e32 v12, 0.15915494, v12                         // 000000000300: 101818F8
	v_mul_f32_e32 v14, 0.15915494, v14                         // 000000000304: 101C1CF8
	v_fract_f32_e32 v11, v11                                   // 000000000308: 7E16410B
	v_mul_f32_e32 v20, 0.15915494, v15                         // 00000000030C: 10281EF8
	v_sub_f32_e32 v16, v16, v13                                // 000000000310: 08201B10
	v_sub_f32_e32 v17, v21, v19                                // 000000000314: 08222715
	v_mul_f32_e32 v8, 0.15915494, v8                           // 000000000318: 101010F8
	v_sin_f32_e32 v12, v12                                     // 00000000031C: 7E186B0C
	v_sin_f32_e32 v14, v14                                     // 000000000320: 7E1C6B0E
	v_fmac_f32_e32 v13, v16, v9                                // 000000000324: 561A1310
	v_fmac_f32_e32 v19, v17, v9                                // 000000000328: 56261311
	v_sin_f32_e32 v9, v20                                      // 00000000032C: 7E126B14
	v_sin_f32_e32 v8, v8                                       // 000000000330: 7E106B08
	v_mul_f32_e32 v16, v10, v10                                // 000000000334: 1020150A
	v_mul_f32_e32 v17, v11, v11                                // 000000000338: 1022170B
	v_fmaak_f32 v10, -2.0, v10, 0x40400000                     // 00000000033C: 5A1414F5 40400000
	v_fmaak_f32 v11, -2.0, v11, 0x40400000                     // 000000000344: 5A1616F5 40400000
	v_mul_f32_e32 v12, 0x472aee8c, v12                         // 00000000034C: 101818FF 472AEE8C
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 000000000354: 101C1CFF 472AEE8C
	v_add_f32_e32 v20, 0x432a0000, v15                         // 00000000035C: 06281EFF 432A0000
	v_mul_f32_e32 v10, v16, v10                                // 000000000364: 10141510
	v_add_f32_e32 v16, 0x42e20000, v15                         // 000000000368: 06201EFF 42E20000
	v_mul_f32_e32 v11, v17, v11                                // 000000000370: 10161711
	v_add_f32_e32 v17, 0x42e40000, v15                         // 000000000374: 06221EFF 42E40000
	v_add_f32_e32 v15, 0x432b0000, v15                         // 00000000037C: 061E1EFF 432B0000
	v_mul_f32_e64 v21, 0x3ca3d70a, s0                          // 000000000384: D5080015 000000FF 3CA3D70A
	v_mul_f32_e32 v9, 0x472aee8c, v9                           // 000000000390: 101212FF 472AEE8C
	v_mul_f32_e32 v8, 0x472aee8c, v8                           // 000000000398: 101010FF 472AEE8C
	v_fract_f32_e32 v12, v12                                   // 0000000003A0: 7E18410C
	v_fract_f32_e32 v14, v14                                   // 0000000003A4: 7E1C410E
	v_mul_f32_e32 v16, 0.15915494, v16                         // 0000000003A8: 102020F8
	v_mul_f32_e32 v17, 0.15915494, v17                         // 0000000003AC: 102222F8
	v_mul_f32_e32 v20, 0.15915494, v20                         // 0000000003B0: 102828F8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 0000000003B4: 101E1EF8
	v_fmamk_f32 v3, v3, 0x3d23d70a, v21                        // 0000000003B8: 58062B03 3D23D70A
	v_fmamk_f32 v5, v5, 0x3d23d70a, v21                        // 0000000003C0: 580A2B05 3D23D70A
	v_fract_f32_e32 v9, v9                                     // 0000000003C8: 7E124109
	v_fract_f32_e32 v8, v8                                     // 0000000003CC: 7E104108
	v_sub_f32_e32 v14, v14, v12                                // 0000000003D0: 081C190E
	v_sin_f32_e32 v16, v16                                     // 0000000003D4: 7E206B10
	v_sin_f32_e32 v17, v17                                     // 0000000003D8: 7E226B11
	v_sin_f32_e32 v20, v20                                     // 0000000003DC: 7E286B14
	v_sin_f32_e32 v15, v15                                     // 0000000003E0: 7E1E6B0F
	v_fmac_f32_e32 v21, 0x3d23d70a, v6                         // 0000000003E4: 562A0CFF 3D23D70A
	v_floor_f32_e32 v6, v3                                     // 0000000003EC: 7E0C4903
	v_floor_f32_e32 v22, v5                                    // 0000000003F0: 7E2C4905
	v_sub_f32_e32 v8, v8, v9                                   // 0000000003F4: 08101308
	v_fmac_f32_e32 v12, v14, v10                               // 0000000003F8: 5618150E
	v_floor_f32_e32 v14, v21                                   // 0000000003FC: 7E1C4915
	v_fract_f32_e32 v7, v7                                     // 000000000400: 7E0E4107
	v_fmac_f32_e32 v6, 0x42640000, v22                         // 000000000404: 560C2CFF 42640000
	v_fmac_f32_e32 v9, v8, v10                                 // 00000000040C: 56121508
	v_mul_f32_e32 v8, 0x472aee8c, v16                          // 000000000410: 101020FF 472AEE8C
	v_mul_f32_e32 v16, 0x472aee8c, v17                         // 000000000418: 102022FF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v20                         // 000000000420: 102228FF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 000000000428: 101E1EFF 472AEE8C
	v_fmac_f32_e32 v6, 0x42e20000, v14                         // 000000000430: 560C1CFF 42E20000
	v_fract_f32_e32 v8, v8                                     // 000000000438: 7E104108
	v_fract_f32_e32 v14, v16                                   // 00000000043C: 7E1C4110
	v_fract_f32_e32 v16, v17                                   // 000000000440: 7E204111
	v_fract_f32_e32 v15, v15                                   // 000000000444: 7E1E410F
	v_add_f32_e32 v17, 1.0, v6                                 // 000000000448: 06220CF2
	v_add_f32_e32 v20, 0x42640000, v6                          // 00000000044C: 06280CFF 42640000
	v_mul_f32_e32 v18, v7, v7                                  // 000000000454: 10240F07
	v_fmaak_f32 v7, -2.0, v7, 0x40400000                       // 000000000458: 5A0E0EF5 40400000
	v_sub_f32_e32 v14, v14, v8                                 // 000000000460: 081C110E
	v_sub_f32_e32 v15, v15, v16                                // 000000000464: 081E210F
	v_mul_f32_e32 v22, 0.15915494, v6                          // 000000000468: 102C0CF8
	v_mul_f32_e32 v17, 0.15915494, v17                         // 00000000046C: 102222F8
	v_mul_f32_e32 v20, 0.15915494, v20                         // 000000000470: 102828F8
	v_fmac_f32_e32 v8, v14, v10                                // 000000000474: 5610150E
	v_fmac_f32_e32 v16, v15, v10                               // 000000000478: 5620150F
	v_sin_f32_e32 v10, v22                                     // 00000000047C: 7E146B16
	v_sin_f32_e32 v14, v17                                     // 000000000480: 7E1C6B11
	v_sin_f32_e32 v15, v20                                     // 000000000484: 7E1E6B14
	v_sub_f32_e32 v17, v19, v13                                // 000000000488: 08221B13
	v_mul_f32_e32 v7, v18, v7                                  // 00000000048C: 100E0F12
	v_add_f32_e32 v18, 0x42680000, v6                          // 000000000490: 06240CFF 42680000
	v_add_f32_e32 v19, 0x42e20000, v6                          // 000000000498: 06260CFF 42E20000
	v_add_f32_e32 v20, 0x42e40000, v6                          // 0000000004A0: 06280CFF 42E40000
	v_add_f32_e32 v22, 0x432a0000, v6                          // 0000000004A8: 062C0CFF 432A0000
	v_add_f32_e32 v6, 0x432b0000, v6                           // 0000000004B0: 060C0CFF 432B0000
	v_mul_f32_e32 v18, 0.15915494, v18                         // 0000000004B8: 102424F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000004BC: 102626F8
	v_mul_f32_e32 v20, 0.15915494, v20                         // 0000000004C0: 102828F8
	v_mul_f32_e32 v22, 0.15915494, v22                         // 0000000004C4: 102C2CF8
	v_mul_f32_e32 v6, 0.15915494, v6                           // 0000000004C8: 100C0CF8
	v_sin_f32_e32 v18, v18                                     // 0000000004CC: 7E246B12
	v_sin_f32_e32 v19, v19                                     // 0000000004D0: 7E266B13
	v_sin_f32_e32 v20, v20                                     // 0000000004D4: 7E286B14
	v_sin_f32_e32 v22, v22                                     // 0000000004D8: 7E2C6B16
	v_sin_f32_e32 v6, v6                                       // 0000000004DC: 7E0C6B06
	v_sub_f32_e32 v12, v12, v9                                 // 0000000004E0: 0818130C
	v_fract_f32_e32 v3, v3                                     // 0000000004E4: 7E064103
	v_mul_f32_e32 v10, 0x472aee8c, v10                         // 0000000004E8: 101414FF 472AEE8C
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 0000000004F0: 101C1CFF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 0000000004F8: 101E1EFF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 000000000500: 102424FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000508: 102626FF 472AEE8C
	v_mul_f32_e32 v20, 0x472aee8c, v20                         // 000000000510: 102828FF 472AEE8C
	v_mul_f32_e32 v22, 0x472aee8c, v22                         // 000000000518: 102C2CFF 472AEE8C
	v_mul_f32_e32 v6, 0x472aee8c, v6                           // 000000000520: 100C0CFF 472AEE8C
	v_fmac_f32_e32 v9, v12, v11                                // 000000000528: 5612170C
	v_sub_f32_e32 v12, v16, v8                                 // 00000000052C: 08181110
	v_mul_f32_e32 v16, v3, v3                                  // 000000000530: 10200703
	v_fmaak_f32 v3, -2.0, v3, 0x40400000                       // 000000000534: 5A0606F5 40400000
	v_fract_f32_e32 v10, v10                                   // 00000000053C: 7E14410A
	v_fract_f32_e32 v14, v14                                   // 000000000540: 7E1C410E
	v_fract_f32_e32 v15, v15                                   // 000000000544: 7E1E410F
	v_fract_f32_e32 v18, v18                                   // 000000000548: 7E244112
	v_fract_f32_e32 v19, v19                                   // 00000000054C: 7E264113
	v_fract_f32_e32 v20, v20                                   // 000000000550: 7E284114
	v_fract_f32_e32 v22, v22                                   // 000000000554: 7E2C4116
	v_fract_f32_e32 v6, v6                                     // 000000000558: 7E0C4106
	v_fract_f32_e32 v5, v5                                     // 00000000055C: 7E0A4105
	v_mul_f32_e32 v3, v16, v3                                  // 000000000560: 10060710
	v_sub_f32_e32 v14, v14, v10                                // 000000000564: 081C150E
	v_sub_f32_e32 v16, v18, v15                                // 000000000568: 08201F12
	v_sub_f32_e32 v18, v20, v19                                // 00000000056C: 08242714
	v_sub_f32_e32 v6, v6, v22                                  // 000000000570: 080C2D06
	v_mul_f32_e32 v23, v5, v5                                  // 000000000574: 102E0B05
	v_fmaak_f32 v5, -2.0, v5, 0x40400000                       // 000000000578: 5A0A0AF5 40400000
	v_fmac_f32_e32 v10, v14, v3                                // 000000000580: 5614070E
	v_fmac_f32_e32 v15, v16, v3                                // 000000000584: 561E0710
	v_fmac_f32_e32 v19, v18, v3                                // 000000000588: 56260712
	v_fmac_f32_e32 v22, v6, v3                                 // 00000000058C: 562C0706
	v_fmac_f32_e32 v8, v12, v11                                // 000000000590: 5610170C
	v_fract_f32_e32 v3, v21                                    // 000000000594: 7E064115
	v_mul_f32_e32 v5, v23, v5                                  // 000000000598: 100A0B17
	v_sub_f32_e32 v6, v15, v10                                 // 00000000059C: 080C150F
	v_sub_f32_e32 v11, v22, v19                                // 0000000005A0: 08162716
	v_sub_f32_e32 v8, v8, v9                                   // 0000000005A4: 08101308
	v_mul_f32_e32 v12, v3, v3                                  // 0000000005A8: 10180703
	v_fmaak_f32 v3, -2.0, v3, 0x40400000                       // 0000000005AC: 5A0606F5 40400000
	v_fmac_f32_e32 v10, v6, v5                                 // 0000000005B4: 56140B06
	v_fmac_f32_e32 v19, v11, v5                                // 0000000005B8: 56260B0B
	v_fmac_f32_e32 v9, v8, v7                                  // 0000000005BC: 56120F08
	s_mov_b32 s0, 0x3e99999a                                   // 0000000005C0: BE8003FF 3E99999A
	v_fmac_f32_e32 v13, v17, v4                                // 0000000005C8: 561A0911
	v_mul_f32_e32 v3, v12, v3                                  // 0000000005CC: 1006070C
	v_sub_f32_e32 v4, v19, v10                                 // 0000000005D0: 08081513
	v_fmaak_f32 v5, s0, v9, 0xbe99999a                         // 0000000005D4: 5A0A1200 BE99999A
	v_fmac_f32_e32 v10, v4, v3                                 // 0000000005DC: 56140704
	v_fmac_f32_e32 v5, 0.5, v13                                // 0000000005E0: 560A1AF0
	v_fmamk_f32 v3, v10, 0x3e4ccccd, v5                        // 0000000005E4: 58060B0A 3E4CCCCD
	v_max_f32_e64 v4, v3, v3 clamp                             // 0000000005EC: D5108004 00020703
	v_mov_b32_e32 v3, 1.0                                      // 0000000005F4: 7E0602F2
	v_add_f32_e32 v6, v4, v4                                   // 0000000005F8: 060C0904
	v_mov_b32_e32 v4, v3                                       // 0000000005FC: 7E080303
	v_mov_b32_e32 v5, v3                                       // 000000000600: 7E0A0303
	image_store v[3:6], v[0:2], s[8:15] dmask:0xf dim:SQ_RSRC_IMG_3D unorm// 000000000604: F0201F10 00020300
_L0:
	s_endpgm                                                   // 00000000060C: BF810000
