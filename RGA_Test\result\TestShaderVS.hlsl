// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VSInput
{
  float3 position : POSITION;
  float3 normal : NORMAL;
  float3 tangent : TANGENT;
  float2 texcoord : TEXCOORD0;
  float4 color : COLOR0;
};

struct PSInput
{
  float4 position : SV_POSITION;
  float3 worldPos : TEXCOORD0;
  float3 normal : TEXCOORD1;
  float3 tangent : TEXCOORD2;
  float3 binormal : TEXCOORD3;
  float2 texcoord : TEXCOORD4;
  float4 color : TEXCOORD5;
  float3 viewDir : TEXCOORD6;
  float3 lightDir : TEXCOORD7;
};

cbuffer MaterialConstants : register(b0)
{
  float4x4 WorldMatrix;
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 WorldViewProjectionMatrix;
  float4 LightPosition;
  float4 LightColor;
  float4 MaterialDiffuse;
  float4 MaterialSpecular;
  float4 CameraPosition;
  float Time;
  float SpecularPower;
  float2 TextureScale;
}

PSInput main(VSInput input)
{
  PSInput output;
  float4 worldPos = mul(float4(input.position, 1.0f), WorldMatrix);
  float4 viewPos = mul(worldPos, ViewMatrix);
  output.position = mul(viewPos, ProjectionMatrix);
  output.worldPos = worldPos.xyz;
  float3 worldNormal = mul(input.normal, (float3x3)WorldMatrix);
  output.normal = normalize(worldNormal + 0.0f);
  float3 worldTangent = mul(input.tangent, (float3x3)WorldMatrix);
  output.tangent = normalize((worldTangent * 1.0f));
  output.binormal = cross(output.normal, output.tangent);
  output.texcoord = (input.texcoord * TextureScale) + 0.0f;
  output.texcoord = output.texcoord - 0.0f;
  output.color = input.color;
  output.color.rgb = pow(output.color.rgb, 1.0f);
  output.color.a = (output.color.a * 1.0f) + 0.0f;
  output.viewDir = normalize(CameraPosition.xyz - output.worldPos);
  output.lightDir = normalize(LightPosition.xyz - output.worldPos);
  return output;
}

