_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v8, s3, 5, v1                                // 000000000040: D76F0008 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v8                            // 000000000048: 7D881001
	s_and_saveexec_b32 s56, vcc_lo                             // 00000000004C: BEB83C6A
	s_cbranch_execz _L1                                        // 000000000050: BF8800A4
	s_getpc_b64 s[2:3]                                         // 000000000054: BE821F00
	v_add_nc_u32_e32 v5, s0, v5                                // 000000000058: 4A0A0A00
	s_mov_b32 s11, s3                                          // 00000000005C: BE8B0303
	s_mov_b32 s5, s3                                           // 000000000060: BE850303
	s_load_dwordx16 s[12:27], s[10:11], null                   // 000000000064: F4100305 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xyz v[12:14], v5, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000070: EA522000 80030C05
	tbuffer_load_format_xyz v[15:17], v5, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80040F05
	tbuffer_load_format_xyz v[18:20], v5, s[20:23], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000080: EA522000 80051205
	tbuffer_load_format_xyz v[21:23], v5, s[24:27], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000088: EA522000 80061505
	s_load_dwordx8 s[8:15], s[10:11], 0x40                     // 000000000090: F40C0205 FA000040
	s_waitcnt lgkmcnt(0)                                       // 000000000098: BF8CC07F
	tbuffer_load_format_xyzw v[1:4], v5, s[12:15], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 00000000009C: EA6B2000 80030105
	tbuffer_load_format_xy v[6:7], v5, s[8:11], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 0000000000A4: EA012000 80020605
	s_load_dwordx4 s[36:39], s[4:5], null                      // 0000000000AC: F4080902 FA000000
	s_waitcnt lgkmcnt(0)                                       // 0000000000B4: BF8CC07F
	s_clause 0xd                                               // 0000000000B8: BFA1000D
	s_buffer_load_dwordx8 s[0:7], s[36:39], 0x40               // 0000000000BC: F42C0012 FA000040
	s_buffer_load_dwordx8 s[8:15], s[36:39], 0x60              // 0000000000C4: F42C0212 FA000060
	s_buffer_load_dwordx2 s[34:35], s[36:39], 0x80             // 0000000000CC: F4240892 FA000080
	s_buffer_load_dwordx2 s[52:53], s[36:39], 0x90             // 0000000000D4: F4240D12 FA000090
	s_buffer_load_dwordx2 s[54:55], s[36:39], 0xa0             // 0000000000DC: F4240D92 FA0000A0
	s_buffer_load_dwordx8 s[24:31], s[36:39], null             // 0000000000E4: F42C0612 FA000000
	s_buffer_load_dwordx8 s[16:23], s[36:39], 0x20             // 0000000000EC: F42C0412 FA000020
	s_buffer_load_dword s57, s[36:39], 0x98                    // 0000000000F4: F4200E52 FA000098
	s_buffer_load_dword s60, s[36:39], 0x88                    // 0000000000FC: F4200F12 FA000088
	s_buffer_load_dword s61, s[36:39], 0xa8                    // 000000000104: F4200F52 FA0000A8
	s_buffer_load_dwordx2 s[58:59], s[36:39], 0x100            // 00000000010C: F4240E92 FA000100
	s_buffer_load_dword s62, s[36:39], 0x108                   // 000000000114: F4200F92 FA000108
	s_buffer_load_dwordx8 s[44:51], s[36:39], 0xc0             // 00000000011C: F42C0B12 FA0000C0
	s_buffer_load_dwordx8 s[36:43], s[36:39], 0xe0             // 000000000124: F42C0912 FA0000E0
	s_waitcnt vmcnt(5) lgkmcnt(0)                              // 00000000012C: BF8C0075
	v_fma_f32 v10, s4, v12, s7                                 // 000000000130: D54B000A 001E1804
	v_fma_f32 v9, s0, v12, s3                                  // 000000000138: D54B0009 000E1800
	s_waitcnt vmcnt(4)                                         // 000000000140: BF8C3F74
	v_mul_f32_e32 v25, s52, v15                                // 000000000144: 10321E34
	s_waitcnt vmcnt(3)                                         // 000000000148: BF8C3F73
	v_mul_f32_e32 v28, s52, v18                                // 00000000014C: 10382434
	s_waitcnt vmcnt(2)                                         // 000000000150: BF8C3F72
	v_mul_f32_e32 v31, s52, v21                                // 000000000154: 103E2A34
	v_fmac_f32_e32 v10, s5, v13                                // 000000000158: 56141A05
	v_fma_f32 v11, s8, v12, s11                                // 00000000015C: D54B000B 002E1808
	v_mul_f32_e32 v24, s34, v15                                // 000000000164: 10301E22
	v_mul_f32_e32 v27, s34, v18                                // 000000000168: 10362422
	v_mul_f32_e32 v29, s54, v18                                // 00000000016C: 103A2436
	v_mul_f32_e32 v30, s34, v21                                // 000000000170: 103C2A22
	v_mul_f32_e32 v32, s54, v21                                // 000000000174: 10402A36
	v_fmac_f32_e32 v9, s1, v13                                 // 000000000178: 56121A01
	v_fmac_f32_e32 v25, s53, v16                               // 00000000017C: 56322035
	v_fmac_f32_e32 v28, s53, v19                               // 000000000180: 56382635
	v_fmac_f32_e32 v31, s53, v22                               // 000000000184: 563E2C35
	v_fmac_f32_e32 v10, s6, v14                                // 000000000188: 56141C06
	v_mul_f32_e32 v26, s54, v15                                // 00000000018C: 10341E36
	v_fmac_f32_e32 v11, s9, v13                                // 000000000190: 56161A09
	v_fmac_f32_e32 v24, s35, v16                               // 000000000194: 56302023
	v_fmac_f32_e32 v27, s35, v19                               // 000000000198: 56362623
	v_fmac_f32_e32 v29, s55, v19                               // 00000000019C: 563A2637
	v_fmac_f32_e32 v30, s35, v22                               // 0000000001A0: 563C2C23
	v_fmac_f32_e32 v32, s55, v22                               // 0000000001A4: 56402C37
	v_fmac_f32_e32 v9, s2, v14                                 // 0000000001A8: 56121C02
	v_fmac_f32_e32 v25, s57, v17                               // 0000000001AC: 56322239
	v_fmac_f32_e32 v28, s57, v20                               // 0000000001B0: 56382839
	v_fmac_f32_e32 v31, s57, v23                               // 0000000001B4: 563E2E39
	v_sub_f32_e32 v34, s59, v10                                // 0000000001B8: 0844143B
	v_fma_f32 v5, s12, v12, s15                                // 0000000001BC: D54B0005 003E180C
	v_fmac_f32_e32 v26, s55, v16                               // 0000000001C4: 56342037
	v_fmac_f32_e32 v11, s10, v14                               // 0000000001C8: 56161C0A
	v_fmac_f32_e32 v24, s60, v17                               // 0000000001CC: 5630223C
	v_fmac_f32_e32 v27, s60, v20                               // 0000000001D0: 5636283C
	v_fmac_f32_e32 v29, s61, v20                               // 0000000001D4: 563A283D
	v_fmac_f32_e32 v30, s60, v23                               // 0000000001D8: 563C2E3C
	v_fmac_f32_e32 v32, s61, v23                               // 0000000001DC: 56402E3D
	v_mul_f32_e32 v20, v25, v25                                // 0000000001E0: 10283319
	v_mul_f32_e32 v21, v28, v28                                // 0000000001E4: 102A391C
	v_mul_f32_e32 v22, v31, v31                                // 0000000001E8: 102C3F1F
	v_sub_f32_e32 v33, s58, v9                                 // 0000000001EC: 0842123A
	v_mul_f32_e32 v23, v34, v34                                // 0000000001F0: 102E4522
	v_fmac_f32_e32 v5, s13, v13                                // 0000000001F4: 560A1A0D
	v_fmac_f32_e32 v26, s61, v17                               // 0000000001F8: 5634223D
	v_sub_f32_e32 v35, s62, v11                                // 0000000001FC: 0846163E
	v_fmac_f32_e32 v20, v24, v24                               // 000000000200: 56283118
	v_fmac_f32_e32 v21, v27, v27                               // 000000000204: 562A371B
	v_fmac_f32_e32 v22, v30, v30                               // 000000000208: 562C3D1E
	v_fmac_f32_e32 v23, v33, v33                               // 00000000020C: 562E4321
	v_fmac_f32_e32 v5, s14, v14                                // 000000000210: 560A1C0E
	v_mul_f32_e32 v12, s24, v9                                 // 000000000214: 10181218
	v_mul_f32_e32 v13, s28, v9                                 // 000000000218: 101A121C
	v_mul_f32_e32 v14, s16, v9                                 // 00000000021C: 101C1210
	v_mul_f32_e32 v15, s20, v9                                 // 000000000220: 101E1214
	v_mul_f32_e32 v16, s44, v9                                 // 000000000224: 1020122C
	v_mul_f32_e32 v17, s48, v9                                 // 000000000228: 10221230
	v_mul_f32_e32 v18, s36, v9                                 // 00000000022C: 10241224
	v_mul_f32_e32 v19, s40, v9                                 // 000000000230: 10261228
	v_fmac_f32_e32 v20, v26, v26                               // 000000000234: 5628351A
	v_fmac_f32_e32 v21, v29, v29                               // 000000000238: 562A3B1D
	v_fmac_f32_e32 v22, v32, v32                               // 00000000023C: 562C4120
	v_fmac_f32_e32 v23, v35, v35                               // 000000000240: 562E4723
	v_fmac_f32_e32 v12, s25, v10                               // 000000000244: 56181419
	v_fmac_f32_e32 v13, s29, v10                               // 000000000248: 561A141D
	v_fmac_f32_e32 v14, s17, v10                               // 00000000024C: 561C1411
	v_fmac_f32_e32 v15, s21, v10                               // 000000000250: 561E1415
	v_fmac_f32_e32 v16, s45, v10                               // 000000000254: 5620142D
	v_fmac_f32_e32 v17, s49, v10                               // 000000000258: 56221431
	v_fmac_f32_e32 v18, s37, v10                               // 00000000025C: 56241425
	v_fmac_f32_e32 v19, s41, v10                               // 000000000260: 56261429
	v_rsq_f32_e32 v36, v20                                     // 000000000264: 7E485D14
	v_rsq_f32_e32 v37, v21                                     // 000000000268: 7E4A5D15
	v_rsq_f32_e32 v38, v22                                     // 00000000026C: 7E4C5D16
	v_rsq_f32_e32 v39, v23                                     // 000000000270: 7E4E5D17
	v_fmac_f32_e32 v12, s26, v11                               // 000000000274: 5618161A
	v_fmac_f32_e32 v13, s30, v11                               // 000000000278: 561A161E
	v_fmac_f32_e32 v14, s18, v11                               // 00000000027C: 561C1612
	v_fmac_f32_e32 v15, s22, v11                               // 000000000280: 561E1616
	v_fmac_f32_e32 v16, s46, v11                               // 000000000284: 5620162E
	v_fmac_f32_e32 v17, s50, v11                               // 000000000288: 56221632
	v_fmac_f32_e32 v18, s38, v11                               // 00000000028C: 56241626
	v_fmac_f32_e32 v19, s42, v11                               // 000000000290: 5626162A
	v_fmac_f32_e32 v12, s27, v5                                // 000000000294: 56180A1B
	v_fmac_f32_e32 v13, s31, v5                                // 000000000298: 561A0A1F
	v_fmac_f32_e32 v14, s19, v5                                // 00000000029C: 561C0A13
	v_fmac_f32_e32 v15, s23, v5                                // 0000000002A0: 561E0A17
	v_fmac_f32_e32 v16, s47, v5                                // 0000000002A4: 56200A2F
	v_fmac_f32_e32 v17, s51, v5                                // 0000000002A8: 56220A33
	v_fmac_f32_e32 v18, s39, v5                                // 0000000002AC: 56240A27
	v_mul_legacy_f32_e32 v20, v24, v36                         // 0000000002B0: 0E284918
	v_mul_legacy_f32_e32 v21, v25, v36                         // 0000000002B4: 0E2A4919
	v_mul_legacy_f32_e32 v23, v26, v36                         // 0000000002B8: 0E2E491A
	v_mul_legacy_f32_e32 v26, v27, v37                         // 0000000002BC: 0E344B1B
	v_mul_legacy_f32_e32 v27, v28, v37                         // 0000000002C0: 0E364B1C
	v_mul_legacy_f32_e32 v29, v29, v37                         // 0000000002C4: 0E3A4B1D
	v_mul_legacy_f32_e32 v22, v30, v38                         // 0000000002C8: 0E2C4D1E
	v_mul_legacy_f32_e32 v24, v31, v38                         // 0000000002CC: 0E304D1F
	v_mul_legacy_f32_e32 v25, v32, v38                         // 0000000002D0: 0E324D20
	v_mul_legacy_f32_e32 v28, v33, v39                         // 0000000002D4: 0E384F21
	v_mul_legacy_f32_e32 v30, v34, v39                         // 0000000002D8: 0E3C4F22
	v_mul_legacy_f32_e32 v31, v35, v39                         // 0000000002DC: 0E3E4F23
	v_fmac_f32_e32 v19, s43, v5                                // 0000000002E0: 56260A2B
_L1:
	s_or_b32 exec_lo, exec_lo, s56                             // 0000000002E4: 887E387E
	s_mov_b32 s1, exec_lo                                      // 0000000002E8: BE81037E
	v_cmpx_gt_u32_e64 s33, v8                                  // 0000000002EC: D4D4007E 00021021
	s_cbranch_execz _L2                                        // 0000000002F4: BF880002
	exp prim v0, off, off, off done                            // 0000000002F8: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 000000000300: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000304: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 000000000308: BE803C6A
	s_cbranch_execz _L3                                        // 00000000030C: BF880014
	exp pos0 v12, v13, v14, v15 done                           // 000000000310: F80008CF 0F0E0D0C
	s_waitcnt vmcnt(1)                                         // 000000000318: BF8C3F71
	exp param5 v1, v2, v3, v4                                  // 00000000031C: F800025F 04030201
	exp param3 v22, v24, v25, off                              // 000000000324: F8000237 00191816
	exp param1 v20, v21, v23, off                              // 00000000032C: F8000217 00171514
	exp param6 v28, v30, v31, off                              // 000000000334: F8000267 001F1E1C
	s_waitcnt vmcnt(0)                                         // 00000000033C: BF8C3F70
	exp param4 v6, v7, off, off                                // 000000000340: F8000243 00000706
	exp param2 v26, v27, v29, off                              // 000000000348: F8000227 001D1B1A
	exp param7 v16, v17, v18, v19                              // 000000000350: F800027F 13121110
	exp param0 v9, v10, v11, off                               // 000000000358: F8000207 000B0A09
_L3:
	s_endpgm                                                   // 000000000360: BF810000
