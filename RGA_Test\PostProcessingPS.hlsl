// Post Processing Pixel Shader
// Tests various post-processing effects and image filters

cbuffer PostProcessParams : register(b0)
{
    float2 TexelSize;
    float BlurRadius;
    float Time;
    float Brightness;
    float Contrast;
    float Saturation;
    float Gamma;
    float VignetteStrength;
    float ChromaticAberration;
    float FilmGrain;
    int EffectType; // 0=None, 1=Blur, 2=Bloom, 3=DOF, 4=Distortion
};

Texture2D SceneTexture : register(t0);
Texture2D BloomTexture : register(t1);
Texture2D DepthTexture : register(t2);
Texture2D NoiseTexture : register(t3);
SamplerState LinearSampler : register(s0);
SamplerState PointSampler : register(s1);

struct PSInput
{
    float4 Position : SV_POSITION;
    float2 TexCoord : TEXCOORD0;
    float2 TexCoordOffset[8] : TEXCOORD1;
};

float3 RGBToHSV(float3 rgb)
{
    float4 K = float4(0.0, -1.0 / 3.0, 2.0 / 3.0, -1.0);
    float4 p = lerp(float4(rgb.bg, K.wz), float4(rgb.gb, <PERSON>.xy), step(rgb.b, rgb.g));
    float4 q = lerp(float4(p.xyw, rgb.r), float4(rgb.r, p.yzx), step(p.x, rgb.r));
    
    float d = q.x - min(q.w, q.y);
    float e = 1.0e-10;
    return float3(abs(q.z + (q.w - q.y) / (6.0 * d + e)), d / (q.x + e), q.x);
}

float3 HSVToRGB(float3 hsv)
{
    float4 K = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    float3 p = abs(frac(hsv.xxx + K.xyz) * 6.0 - K.www);
    return hsv.z * lerp(K.xxx, saturate(p - K.xxx), hsv.y);
}

float4 gaussianBlur(PSInput input)
{
    float4 color = float4(0, 0, 0, 0);
    float weights[9] = { 0.0625, 0.125, 0.0625, 0.125, 0.25, 0.125, 0.0625, 0.125, 0.0625 };

    color += SceneTexture.Sample(LinearSampler, input.TexCoord) * weights[4]; // Center

    for(int i = 0; i < 8; i++)
    {
        color += SceneTexture.Sample(LinearSampler, input.TexCoordOffset[i]) * weights[i];
    }

    return color;
}

float4 chromaticAberration(float2 texCoord)
{
    float2 center = float2(0.5, 0.5);
    float2 direction = texCoord - center;
    float distance = length(direction);
    
    float2 offsetR = direction * ChromaticAberration * distance;
    float2 offsetG = direction * ChromaticAberration * distance * 0.5;
    float2 offsetB = float2(0, 0);
    
    float r = SceneTexture.Sample(LinearSampler, texCoord + offsetR).r;
    float g = SceneTexture.Sample(LinearSampler, texCoord + offsetG).g;
    float b = SceneTexture.Sample(LinearSampler, texCoord + offsetB).b;
    float a = SceneTexture.Sample(LinearSampler, texCoord).a;
    
    return float4(r, g, b, a);
}

float4 depthOfField(PSInput input)
{
    float depth = DepthTexture.Sample(PointSampler, input.TexCoord).r;
    float focusDistance = 0.5; // Focus at middle distance
    float focusRange = 0.2;

    float blur = abs(depth - focusDistance) / focusRange;
    blur = saturate(blur);

    float4 sharpColor = SceneTexture.Sample(LinearSampler, input.TexCoord);
    float4 blurredColor = gaussianBlur(input);

    return lerp(sharpColor, blurredColor, blur);
}

float4 barrelDistortion(float2 texCoord)
{
    float2 center = float2(0.5, 0.5);
    float2 coord = texCoord - center;
    
    float distortion = 0.2;
    float r2 = dot(coord, coord);
    float distortionFactor = 1.0 + distortion * r2;
    
    float2 distortedCoord = center + coord * distortionFactor;
    
    if (distortedCoord.x < 0.0 || distortedCoord.x > 1.0 || 
        distortedCoord.y < 0.0 || distortedCoord.y > 1.0)
        return float4(0, 0, 0, 1);
    
    return SceneTexture.Sample(LinearSampler, distortedCoord);
}

float4 main(PSInput input) : SV_TARGET
{
    float4 color;
    
    // Apply main effect based on type
    if (EffectType == 1) // Blur
    {
        color = gaussianBlur(input);
    }
    else if (EffectType == 2) // Bloom
    {
        float4 sceneColor = SceneTexture.Sample(LinearSampler, input.TexCoord);
        float4 bloomColor = BloomTexture.Sample(LinearSampler, input.TexCoord);
        color = sceneColor + bloomColor * 0.3;
    }
    else if (EffectType == 3) // Depth of Field
    {
        color = depthOfField(input);
    }
    else if (EffectType == 4) // Distortion
    {
        color = barrelDistortion(input.TexCoord);
    }
    else // No effect
    {
        color = SceneTexture.Sample(LinearSampler, input.TexCoord);
    }
    
    // Apply chromatic aberration
    if (ChromaticAberration > 0.0)
    {
        color = chromaticAberration(input.TexCoord);
    }
    
    // Color grading
    color.rgb *= Brightness;
    color.rgb = ((color.rgb - 0.5) * Contrast) + 0.5;
    
    // Saturation
    float3 hsv = RGBToHSV(color.rgb);
    hsv.y *= Saturation;
    color.rgb = HSVToRGB(hsv);
    
    // Vignette effect
    float2 center = float2(0.5, 0.5);
    float distance = length(input.TexCoord - center);
    float vignette = 1.0 - smoothstep(0.3, 0.8, distance * VignetteStrength);
    color.rgb *= vignette;
    
    // Film grain
    if (FilmGrain > 0.0)
    {
        float2 noiseCoord = input.TexCoord * 10.0 + Time * 0.1;
        float noise = NoiseTexture.Sample(LinearSampler, noiseCoord).r;
        noise = (noise - 0.5) * FilmGrain;
        color.rgb += noise;
    }
    
    // Gamma correction
    color.rgb = pow(abs(color.rgb), Gamma);
    
    return saturate(color);
}
