_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s1, s2, 0x90016                                  // 00000000000C: 9381FF02 00090016
	s_bfe_u32 s2, s2, 0x9000c                                  // 000000000014: 9382FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s5, s1, 12                                      // 00000000002C: 8F058C01
	s_or_b32 m0, s5, s2                                        // 000000000030: 887C0205
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v4, s3, 5, v1                                // 000000000040: D76F0004 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s2, v4                            // 000000000048: 7D880802
	s_and_saveexec_b32 s2, vcc_lo                              // 00000000004C: BE823C6A
	s_cbranch_execz _L1                                        // 000000000050: BF88001D
	s_getpc_b64 s[6:7]                                         // 000000000054: BE861F00
	v_add_nc_u32_e32 v1, s0, v5                                // 000000000058: 4A020A00
	s_mov_b32 s11, s7                                          // 00000000005C: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000060: BE850307
	s_load_dwordx8 s[8:15], s[10:11], null                     // 000000000064: F40C0205 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	tbuffer_load_format_xy v[5:6], v1, s[12:15], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000070: EA012000 80030501
	tbuffer_load_format_xyz v[1:3], v1, s[8:11], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000078: EA522000 80020101
	s_load_dwordx4 s[4:7], s[4:5], null                        // 000000000080: F4080102 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000088: BF8CC07F
	s_clause 0x1                                               // 00000000008C: BFA10001
	s_buffer_load_dwordx2 s[8:9], s[4:7], null                 // 000000000090: F4240202 FA000000
	s_buffer_load_dword s0, s[4:7], 0x8                        // 000000000098: F4200002 FA000008
	s_waitcnt lgkmcnt(0)                                       // 0000000000A0: BF8CC07F
	v_mul_f32_e64 v9, s9, s0                                   // 0000000000A4: D5080009 00000009
	v_mul_f32_e64 v10, s8, s0                                  // 0000000000AC: D508000A 00000008
	s_waitcnt vmcnt(1)                                         // 0000000000B4: BF8C3F71
	v_add_f32_e32 v7, v9, v6                                   // 0000000000B8: 060E0D09
	v_add_f32_e32 v8, v10, v5                                  // 0000000000BC: 06100B0A
	v_sub_f32_e32 v9, v6, v9                                   // 0000000000C0: 08121306
	v_sub_f32_e32 v10, v5, v10                                 // 0000000000C4: 08141505
_L1:
	s_or_b32 exec_lo, exec_lo, s2                              // 0000000000C8: 887E027E
	v_cmp_gt_u32_e64 s0, s1, v4                                // 0000000000CC: D4C40000 00020801
	s_and_saveexec_b32 s1, s0                                  // 0000000000D4: BE813C00
	s_cbranch_execz _L2                                        // 0000000000D8: BF880002
	exp prim v0, off, off, off done                            // 0000000000DC: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000000E4: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000000E8: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000000EC: BE803C6A
	s_cbranch_execz _L3                                        // 0000000000F0: BF880016
	v_mov_b32_e32 v0, 1.0                                      // 0000000000F4: 7E0002F2
	s_waitcnt vmcnt(0)                                         // 0000000000F8: BF8C3F70
	exp pos0 v1, v2, v3, v0 done                               // 0000000000FC: F80008CF 00030201
	exp param5 v8, v6, off, off                                // 000000000104: F8000253 00000608
	exp param3 v8, v9, off, off                                // 00000000010C: F8000233 00000908
	exp param8 v8, v7, off, off                                // 000000000114: F8000283 00000708
	exp param1 v10, v9, off, off                               // 00000000011C: F8000213 0000090A
	exp param6 v10, v7, off, off                               // 000000000124: F8000263 0000070A
	exp param4 v10, v6, off, off                               // 00000000012C: F8000243 0000060A
	exp param2 v5, v9, off, off                                // 000000000134: F8000223 00000905
	exp param7 v5, v7, off, off                                // 00000000013C: F8000273 00000705
	exp param0 v5, v6, off, off                                // 000000000144: F8000203 00000605
_L3:
	s_endpgm                                                   // 00000000014C: BF810000
