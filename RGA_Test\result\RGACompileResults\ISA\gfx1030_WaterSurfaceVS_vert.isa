_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s30, s2, 0x90016                                 // 00000000000C: 939EFF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s30, 12                                     // 00000000002C: 8F028C1E
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v3, s3, 5, v1                                // 000000000040: D76F0003 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v3                            // 000000000048: 7D880601
	s_and_saveexec_b32 s31, vcc_lo                             // 00000000004C: BE9F3C6A
	s_cbranch_execz _L1                                        // 000000000050: BF8800D7
	s_getpc_b64 s[6:7]                                         // 000000000054: BE861F00
	v_add_nc_u32_e32 v1, s0, v5                                // 000000000058: 4A020A00
	s_mov_b32 s11, s7                                          // 00000000005C: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000060: BE850307
	s_clause 0x1                                               // 000000000064: BFA10001
	s_load_dwordx4 s[12:15], s[10:11], null                    // 000000000068: F4080305 FA000000
	s_load_dwordx4 s[0:3], s[10:11], 0x20                      // 000000000070: F4080005 FA000020
	s_waitcnt lgkmcnt(0)                                       // 000000000078: BF8CC07F
	tbuffer_load_format_xyz v[8:10], v1, s[12:15], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000007C: EA522000 80030801
	tbuffer_load_format_xy v[1:2], v1, s[0:3], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000084: EA012000 80000101
	s_load_dwordx4 s[16:19], s[4:5], null                      // 00000000008C: F4080402 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	s_clause 0x4                                               // 000000000098: BFA10004
	s_buffer_load_dwordx4 s[8:11], s[16:19], 0xa0              // 00000000009C: F4280208 FA0000A0
	s_buffer_load_dwordx2 s[28:29], s[16:19], 0x90             // 0000000000A4: F4240708 FA000090
	s_buffer_load_dwordx4 s[12:15], s[16:19], 0x60             // 0000000000AC: F4280308 FA000060
	s_buffer_load_dwordx8 s[0:7], s[16:19], 0x40               // 0000000000B4: F42C0008 FA000040
	s_buffer_load_dwordx4 s[24:27], s[16:19], 0x80             // 0000000000BC: F4280608 FA000080
	s_waitcnt lgkmcnt(0)                                       // 0000000000C4: BF8CC07F
	v_mul_f32_e64 v4, s11, s11                                 // 0000000000C8: D5080004 0000160B
	v_mul_f32_e64 v5, s9, s9                                   // 0000000000D0: D5080005 00001209
	v_rcp_f32_e32 v11, s29                                     // 0000000000D8: 7E16541D
	v_mul_f32_e64 v19, 0x4116cbdc, s29                         // 0000000000DC: D5080013 00003AFF 4116CBDC
	v_mul_f32_e64 v14, 0x40c90fd0, s29                         // 0000000000E8: D508000E 00003AFF 40C90FD0
	v_fmac_f32_e64 v4, s10, s10                                // 0000000000F4: D52B0004 0000140A
	v_fmac_f32_e64 v5, s8, s8                                  // 0000000000FC: D52B0005 00001008
	v_mul_f32_e64 v21, s28, 0.5                                // 000000000104: D5080015 0001E01C
	v_mul_f32_e64 v22, 0x3d594cbb, s28                         // 00000000010C: D5080016 000038FF 3D594CBB
	v_rsq_f32_e32 v7, v4                                       // 000000000118: 7E0E5D04
	v_rsq_f32_e32 v5, v5                                       // 00000000011C: 7E0A5D05
	v_mul_f32_e32 v13, 0x3f851899, v11                         // 000000000120: 101A16FF 3F851899
	v_mul_f32_e32 v12, 0x3fc7a4e5, v11                         // 000000000128: 101816FF 3FC7A4E5
	v_sqrt_f32_e32 v13, v13                                    // 000000000130: 7E1A670D
	v_sqrt_f32_e32 v12, v12                                    // 000000000134: 7E18670C
	v_mul_legacy_f32_e32 v15, s11, v7                          // 000000000138: 0E1E0E0B
	v_mul_legacy_f32_e32 v16, s9, v5                           // 00000000013C: 0E200A09
	v_mul_legacy_f32_e32 v17, s10, v7                          // 000000000140: 0E220E0A
	v_mul_legacy_f32_e32 v5, s8, v5                            // 000000000144: 0E0A0A08
	v_mul_f32_e32 v13, s27, v13                                // 000000000148: 101A1A1B
	v_mul_f32_e32 v12, s27, v12                                // 00000000014C: 1018181B
	s_waitcnt vmcnt(1)                                         // 000000000150: BF8C3F71
	v_fma_f32 v4, s12, v8, s15                                 // 000000000154: D54B0004 003E100C
	v_fma_f32 v6, s0, v8, s3                                   // 00000000015C: D54B0006 000E1000
	v_fma_f32 v8, s4, v8, s7                                   // 000000000164: D54B0008 001E1004
	v_fmac_f32_e32 v4, s13, v9                                 // 00000000016C: 5608120D
	v_fmac_f32_e32 v6, s1, v9                                  // 000000000170: 560C1201
	v_fmac_f32_e32 v8, s5, v9                                  // 000000000174: 56101205
	v_fmac_f32_e32 v4, s14, v10                                // 000000000178: 5608140E
	v_fmac_f32_e32 v6, s2, v10                                 // 00000000017C: 560C1402
	s_clause 0x2                                               // 000000000180: BFA10002
	s_buffer_load_dword s2, s[16:19], 0xb8                     // 000000000184: F4200088 FA0000B8
	s_buffer_load_dwordx2 s[0:1], s[16:19], 0xb0               // 00000000018C: F4240008 FA0000B0
	s_buffer_load_dwordx8 s[8:15], s[16:19], null              // 000000000194: F42C0208 FA000000
	v_fmac_f32_e32 v8, s6, v10                                 // 00000000019C: 56101406
	s_buffer_load_dwordx8 s[16:23], s[16:19], 0x20             // 0000000001A0: F42C0408 FA000020
	v_mul_f32_e32 v7, v15, v4                                  // 0000000001A8: 100E090F
	v_mul_f32_e32 v18, v16, v4                                 // 0000000001AC: 10240910
	v_fmac_f32_e32 v7, v17, v6                                 // 0000000001B0: 560E0D11
	v_fmac_f32_e32 v18, v5, v6                                 // 0000000001B4: 56240D05
	v_fma_f32 v7, v19, v7, -v13                                // 0000000001B8: D54B0007 84360F13
	v_fma_f32 v12, v14, v18, -v12                              // 0000000001C0: D54B000C 8432250E
	v_add_f32_e64 v13, s27, s27                                // 0000000001C8: D503000D 0000361B
	v_add_f32_e32 v14, v4, v6                                  // 0000000001D0: 061C0D04
	v_mul_f32_e64 v19, 0x3e22f98c, s28                         // 0000000001D4: D5080013 000038FF 3E22F98C
	v_mul_f32_e32 v7, 0.15915494, v7                           // 0000000001E0: 100E0EF8
	v_mul_f32_e32 v12, 0.15915494, v12                         // 0000000001E4: 101818F8
	v_fmac_f32_e32 v13, 0x3dcccccd, v14                        // 0000000001E8: 561A1CFF 3DCCCCCD
	v_mul_f32_e32 v19, v19, v11                                // 0000000001F0: 10261713
	v_sin_f32_e32 v18, v7                                      // 0000000001F4: 7E246B07
	v_sin_f32_e32 v20, v12                                     // 0000000001F8: 7E286B0C
	v_cos_f32_e32 v14, v7                                      // 0000000001FC: 7E1C6D07
	v_cos_f32_e32 v12, v12                                     // 000000000200: 7E186D0C
	v_mul_f32_e32 v13, 0.15915494, v13                         // 000000000204: 101A1AF8
	v_mul_f32_e32 v11, v22, v11                                // 000000000208: 10161716
	v_mul_f32_e64 v7, 0x3dcccccd, s27                          // 00000000020C: D5080007 000036FF 3DCCCCCD
	v_sin_f32_e32 v13, v13                                     // 000000000218: 7E1A6B0D
	v_mul_f32_e32 v23, v18, v21                                // 00000000021C: 102E2B12
	v_mul_f32_e32 v24, s28, v20                                // 000000000220: 1030281C
	v_mul_f32_e32 v21, v14, v21                                // 000000000224: 102A2B0E
	v_mul_f32_e32 v26, s28, v12                                // 000000000228: 1034181C
	v_mul_f32_e32 v18, v18, v11                                // 00000000022C: 10241712
	v_mul_f32_e64 v25, v23, -v17                               // 000000000230: D5080019 40022317
	v_mul_f32_e64 v27, v24, -v5                                // 000000000238: D508001B 40020B18
	v_mul_f32_e32 v28, v21, v17                                // 000000000240: 10382315
	v_mul_f32_e32 v24, v24, v16                                // 000000000244: 10302118
	v_mul_f32_e32 v21, v21, v15                                // 000000000248: 102A1F15
	v_mul_f32_e32 v29, v25, v15                                // 00000000024C: 103A1F19
	v_fma_f32 v30, v27, v5, 1.0                                // 000000000250: D54B001E 03CA0B1B
	v_fmac_f32_e32 v28, v26, v5                                // 000000000258: 56380B1A
	v_mul_f32_e32 v23, v23, v15                                // 00000000025C: 102E1F17
	v_fma_f32 v24, -v24, v16, 1.0                              // 000000000260: D54B0018 23CA2118
	v_fmac_f32_e32 v29, v27, v16                               // 000000000268: 563A211B
	v_fmac_f32_e32 v21, v26, v16                               // 00000000026C: 562A211A
	v_fmac_f32_e32 v30, v25, v17                               // 000000000270: 563C2319
	v_mul_f32_e32 v25, v28, v28                                // 000000000274: 1032391C
	v_fma_f32 v9, -v23, v15, v24                               // 000000000278: D54B0009 24621F17
	v_mul_f32_e32 v26, v29, v29                                // 000000000280: 10343B1D
	s_waitcnt lgkmcnt(0)                                       // 000000000284: BF8CC07F
	v_mul_f32_e32 v10, s2, v13                                 // 000000000288: 10141A02
	v_mul_f32_e32 v13, v20, v19                                // 00000000028C: 101A2714
	v_fmac_f32_e32 v25, v30, v30                               // 000000000290: 56323D1E
	v_fmac_f32_e32 v8, v12, v19                                // 000000000294: 5610270C
	v_fmac_f32_e32 v26, v21, v21                               // 000000000298: 56342B15
	v_mul_f32_e32 v20, s0, v10                                 // 00000000029C: 10281400
	v_fmac_f32_e32 v6, v13, v5                                 // 0000000002A0: 560C0B0D
	v_fmac_f32_e32 v25, v29, v29                               // 0000000002A4: 56323B1D
	v_fmac_f32_e32 v4, v13, v16                                // 0000000002A8: 5608210D
	v_fmac_f32_e32 v26, v9, v9                                 // 0000000002AC: 56341309
	v_mul_f32_e32 v5, s1, v10                                  // 0000000002B0: 100A1401
	v_fma_f32 v13, v10, s1, s27                                // 0000000002B4: D54B000D 006C030A
	v_rsq_f32_e32 v22, v25                                     // 0000000002BC: 7E2C5D19
	v_fmac_f32_e32 v8, v14, v11                                // 0000000002C0: 5610170E
	v_rsq_f32_e32 v23, v26                                     // 0000000002C4: 7E2E5D1A
	v_fmac_f32_e32 v6, v18, v17                                // 0000000002C8: 560C2312
	v_fmac_f32_e32 v4, v18, v15                                // 0000000002CC: 56081F12
	v_fmac_f32_e32 v7, 0x3d4ccccd, v20                         // 0000000002D0: 560E28FF 3D4CCCCD
	v_sub_f32_e32 v15, s25, v8                                 // 0000000002D8: 081E1019
	v_fmac_f32_e32 v6, 0x3dcccccd, v20                         // 0000000002DC: 560C28FF 3DCCCCCD
	v_fmac_f32_e32 v4, 0x3dcccccd, v5                          // 0000000002E4: 56080AFF 3DCCCCCD
	v_mul_legacy_f32_e32 v10, v30, v22                         // 0000000002EC: 0E142D1E
	v_mul_legacy_f32_e32 v11, v29, v22                         // 0000000002F0: 0E162D1D
	v_mul_legacy_f32_e32 v9, v9, v23                           // 0000000002F4: 0E122F09
	v_mul_legacy_f32_e32 v12, v29, v23                         // 0000000002F8: 0E182F1D
	v_mul_legacy_f32_e32 v14, v21, v23                         // 0000000002FC: 0E1C2F15
	v_mul_legacy_f32_e32 v5, v28, v22                          // 000000000300: 0E0A2D1C
	v_sub_f32_e32 v19, s24, v6                                 // 000000000304: 08260C18
	v_mul_f32_e32 v16, v10, v9                                 // 000000000308: 1020130A
	v_sub_f32_e32 v20, s26, v4                                 // 00000000030C: 0828081A
	v_mul_f32_e32 v17, v11, v14                                // 000000000310: 10221D0B
	v_fma_f32 v18, v11, v12, -v16                              // 000000000314: D54B0012 8442190B
	v_mul_f32_e32 v16, v15, v15                                // 00000000031C: 10201F0F
	v_mul_f32_e32 v11, v5, v12                                 // 000000000320: 10161905
	v_fma_f32 v5, v5, v9, -v17                                 // 000000000324: D54B0005 84461305
	v_fma_f32 v9, v8, s9, s11                                  // 00000000032C: D54B0009 002C1308
	v_mul_f32_e32 v17, v18, v18                                // 000000000334: 10222512
	v_fmac_f32_e32 v16, v19, v19                               // 000000000338: 56202713
	v_fma_f32 v21, v10, v14, -v11                              // 00000000033C: D54B0015 842E1D0A
	v_fma_f32 v10, v8, s13, s15                                // 000000000344: D54B000A 003C1B08
	v_fma_f32 v11, v8, s17, s19                                // 00000000034C: D54B000B 004C2308
	v_fmac_f32_e32 v17, v5, v5                                 // 000000000354: 56220B05
	v_fmac_f32_e32 v16, v20, v20                               // 000000000358: 56202914
	v_fma_f32 v12, v8, s21, s23                                // 00000000035C: D54B000C 005C2B08
	v_fmac_f32_e32 v9, s8, v6                                  // 000000000364: 56120C08
	v_fmac_f32_e32 v10, s12, v6                                // 000000000368: 56140C0C
	v_fmac_f32_e32 v17, v21, v21                               // 00000000036C: 56222B15
	v_rsq_f32_e32 v22, v16                                     // 000000000370: 7E2C5D10
	v_fmac_f32_e32 v11, s16, v6                                // 000000000374: 56160C10
	v_fmac_f32_e32 v12, s20, v6                                // 000000000378: 56180C14
	v_mul_f32_e32 v14, 0x3d4ccccd, v13                         // 00000000037C: 101C1AFF 3D4CCCCD
	v_rsq_f32_e32 v23, v17                                     // 000000000384: 7E2E5D11
	v_fmac_f32_e32 v9, s10, v4                                 // 000000000388: 5612080A
	v_fmac_f32_e32 v10, s14, v4                                // 00000000038C: 5614080E
	v_fmac_f32_e32 v11, s18, v4                                // 000000000390: 56160812
	v_fmac_f32_e32 v12, s22, v4                                // 000000000394: 56180816
	v_mul_legacy_f32_e32 v13, v19, v22                         // 000000000398: 0E1A2D13
	v_mul_legacy_f32_e32 v15, v15, v22                         // 00000000039C: 0E1E2D0F
	v_mul_legacy_f32_e32 v16, v5, v23                          // 0000000003A0: 0E202F05
	v_mul_legacy_f32_e32 v17, v18, v23                         // 0000000003A4: 0E222F12
	v_mul_legacy_f32_e32 v19, v21, v23                         // 0000000003A8: 0E262F15
	v_mul_legacy_f32_e32 v18, v20, v22                         // 0000000003AC: 0E242D14
_L1:
	s_or_b32 exec_lo, exec_lo, s31                             // 0000000003B0: 887E1F7E
	s_mov_b32 s1, exec_lo                                      // 0000000003B4: BE81037E
	v_cmpx_gt_u32_e64 s30, v3                                  // 0000000003B8: D4D4007E 0002061E
	s_cbranch_execz _L2                                        // 0000000003C0: BF880002
	exp prim v0, off, off, off done                            // 0000000003C4: F8000941 00000000
_L2:
	s_waitcnt expcnt(0)                                        // 0000000003CC: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000003D0: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 0000000003D4: BE803C6A
	s_cbranch_execz _L3                                        // 0000000003D8: BF880011
	exp pos0 v9, v10, v11, v12 done                            // 0000000003DC: F80008CF 0C0B0A09
	exp param5 v9, v10, v11, v12                               // 0000000003E4: F800025F 0C0B0A09
	exp param3 v13, v15, v18, off                              // 0000000003EC: F8000237 00120F0D
	exp param1 v16, v17, v19, off                              // 0000000003F4: F8000217 00131110
	exp param6 v7, v14, off, off                               // 0000000003FC: F8000263 00000E07
	exp param4 v9, v10, v11, v12                               // 000000000404: F800024F 0C0B0A09
	s_waitcnt vmcnt(0)                                         // 00000000040C: BF8C3F70
	exp param2 v1, v2, off, off                                // 000000000410: F8000223 00000201
	exp param0 v6, v8, v4, off                                 // 000000000418: F8000207 00040806
_L3:
	s_endpgm                                                   // 000000000420: BF810000
