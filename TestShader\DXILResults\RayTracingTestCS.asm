;
; Note: shader requires additional functionality:
;       Typed UAV Load Additional Formats
;
;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 12c42dfedae19fb72fed6df0947a4416
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(8,8,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer RayTracingParams
; {
;
;   struct hostlayout.RayTracingParams
;   {
;
;       column_major float4x4 InverseViewMatrix;      ; Offset:    0
;       column_major float4x4 InverseProjectionMatrix;; Offset:   64
;       float3 CameraPosition;                        ; Offset:  128
;       float Time;                                   ; Offset:  140
;       uint FrameCount;                              ; Offset:  144
;       uint MaxBounces;                              ; Offset:  148
;       uint SamplesPerPixel;                         ; Offset:  152
;       uint RandomSeed;                              ; Offset:  156
;       float2 ScreenResolution;                      ; Offset:  160
;       float2 JitterOffset;                          ; Offset:  168
;       uint NumSpheres;                              ; Offset:  176
;       uint NumTriangles;                            ; Offset:  180
;       uint NumMaterials;                            ; Offset:  184
;       uint _padding;                                ; Offset:  188
;   
;   } RayTracingParams;                               ; Offset:    0 Size:   192
;
; }
;
; Resource bind info for Spheres
; {
;
;   struct struct.Sphere
;   {
;
;       float3 Center;                                ; Offset:    0
;       float Radius;                                 ; Offset:   12
;       uint MaterialID;                              ; Offset:   16
;       float _padding;                               ; Offset:   20
;   
;   } $Element;                                       ; Offset:    0 Size:    24
;
; }
;
; Resource bind info for Triangles
; {
;
;   struct struct.Triangle
;   {
;
;       float3 V0;                                    ; Offset:    0
;       float3 V1;                                    ; Offset:   12
;       float3 V2;                                    ; Offset:   24
;       float3 N0;                                    ; Offset:   36
;       float3 N1;                                    ; Offset:   48
;       float3 N2;                                    ; Offset:   60
;       float2 UV0;                                   ; Offset:   72
;       float2 UV1;                                   ; Offset:   80
;       float2 UV2;                                   ; Offset:   88
;       uint MaterialID;                              ; Offset:   96
;       float3 _padding;                              ; Offset:  100
;   
;   } $Element;                                       ; Offset:    0 Size:   112
;
; }
;
; Resource bind info for Materials
; {
;
;   struct struct.Material
;   {
;
;       float3 Albedo;                                ; Offset:    0
;       float Metallic;                               ; Offset:   12
;       float3 Emission;                              ; Offset:   16
;       float Roughness;                              ; Offset:   28
;       float IOR;                                    ; Offset:   32
;       float Transparency;                           ; Offset:   36
;       float2 _padding;                              ; Offset:   40
;   
;   } $Element;                                       ; Offset:    0 Size:    48
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; RayTracingParams                  cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; Spheres                           texture  struct         r/o      T0             t0     1
; Triangles                         texture  struct         r/o      T1             t1     1
; Materials                         texture  struct         r/o      T2             t2     1
; EnvironmentMap                    texture     f32          2d      T3             t4     1
; OutputTexture                         UAV     f32          2d      U0             u0     1
; AccumulationTexture                   UAV     f32          2d      U1             u1     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.ResRet.i32 = type { i32, i32, i32, i32, i32 }
%"class.StructuredBuffer<Sphere>" = type { %struct.Sphere }
%struct.Sphere = type { <3 x float>, float, i32, float }
%"class.StructuredBuffer<Triangle>" = type { %struct.Triangle }
%struct.Triangle = type { <3 x float>, <3 x float>, <3 x float>, <3 x float>, <3 x float>, <3 x float>, <2 x float>, <2 x float>, <2 x float>, i32, <3 x float> }
%"class.StructuredBuffer<Material>" = type { %struct.Material }
%struct.Material = type { <3 x float>, float, <3 x float>, float, float, float, <2 x float> }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.RWTexture2D<vector<float, 4> >" = type { <4 x float> }
%hostlayout.RayTracingParams = type { [4 x <4 x float>], [4 x <4 x float>], <3 x float>, float, i32, i32, i32, i32, <2 x float>, <2 x float>, i32, i32, i32, i32 }
%struct.SamplerState = type { i32 }

@"\01?SharedSampleCount@@3PAIA" = external addrspace(3) global [64 x i32], align 4
@"\01?SharedRadiance@@3PAV?$vector@M$02@@A.v.1dim" = addrspace(3) global [192 x float] undef, align 4

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 4, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %9 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %10 = call i32 @dx.op.threadId.i32(i32 93, i32 1)  ; ThreadId(component)
  %11 = call i32 @dx.op.threadIdInGroup.i32(i32 95, i32 0)  ; ThreadIdInGroup(component)
  %12 = call i32 @dx.op.threadIdInGroup.i32(i32 95, i32 1)  ; ThreadIdInGroup(component)
  %13 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %14 = extractvalue %dx.types.CBufRet.f32 %13, 0
  %15 = fptoui float %14 to i32
  %16 = icmp ult i32 %9, %15
  br i1 %16, label %17, label %900

; <label>:17                                      ; preds = %0
  %18 = extractvalue %dx.types.CBufRet.f32 %13, 1
  %19 = fptoui float %18 to i32
  %20 = icmp ult i32 %10, %19
  br i1 %20, label %21, label %900

; <label>:21                                      ; preds = %17
  %22 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %23 = extractvalue %dx.types.CBufRet.i32 %22, 2
  %24 = icmp eq i32 %23, 0
  br i1 %24, label %845, label %25

; <label>:25                                      ; preds = %21
  %26 = extractvalue %dx.types.CBufRet.i32 %22, 3
  %27 = mul i32 %15, %10
  %28 = extractvalue %dx.types.CBufRet.i32 %22, 0
  %29 = mul i32 %28, 719393
  %30 = add i32 %27, %9
  %31 = add i32 %30, %26
  %32 = add i32 %31, %29
  br label %33

; <label>:33                                      ; preds = %809, %25
  %34 = phi float [ %810, %809 ], [ undef, %25 ]
  %35 = phi float [ %811, %809 ], [ undef, %25 ]
  %36 = phi float [ %812, %809 ], [ undef, %25 ]
  %37 = phi float [ %813, %809 ], [ undef, %25 ]
  %38 = phi float [ %814, %809 ], [ undef, %25 ]
  %39 = phi float [ %815, %809 ], [ undef, %25 ]
  %40 = phi i32 [ %816, %809 ], [ undef, %25 ]
  %41 = phi float [ %817, %809 ], [ undef, %25 ]
  %42 = phi float [ %818, %809 ], [ undef, %25 ]
  %43 = phi float [ %819, %809 ], [ undef, %25 ]
  %44 = phi float [ %820, %809 ], [ undef, %25 ]
  %45 = phi float [ %821, %809 ], [ undef, %25 ]
  %46 = phi float [ %822, %809 ], [ undef, %25 ]
  %47 = phi float [ %823, %809 ], [ undef, %25 ]
  %48 = phi i32 [ %824, %809 ], [ undef, %25 ]
  %49 = phi float [ %825, %809 ], [ undef, %25 ]
  %50 = phi float [ %826, %809 ], [ undef, %25 ]
  %51 = phi float [ %827, %809 ], [ undef, %25 ]
  %52 = phi float [ %828, %809 ], [ undef, %25 ]
  %53 = phi float [ %829, %809 ], [ undef, %25 ]
  %54 = phi float [ %830, %809 ], [ undef, %25 ]
  %55 = phi float [ %831, %809 ], [ undef, %25 ]
  %56 = phi i32 [ %832, %809 ], [ undef, %25 ]
  %57 = phi i32 [ %836, %809 ], [ %32, %25 ]
  %58 = phi float [ %837, %809 ], [ 0.000000e+00, %25 ]
  %59 = phi float [ %838, %809 ], [ 0.000000e+00, %25 ]
  %60 = phi float [ %839, %809 ], [ 0.000000e+00, %25 ]
  %61 = phi i32 [ %840, %809 ], [ 0, %25 ]
  %62 = xor i32 %57, 61
  %63 = lshr i32 %57, 16
  %64 = xor i32 %62, %63
  %65 = mul i32 %64, 9
  %66 = lshr i32 %65, 4
  %67 = xor i32 %66, %65
  %68 = mul i32 %67, 668265261
  %69 = lshr i32 %68, 15
  %70 = xor i32 %69, %68
  %71 = uitofp i32 %70 to float
  %72 = fmul fast float %71, 0x3DF0000000000000
  %73 = xor i32 %70, 61
  %74 = lshr i32 %70, 16
  %75 = xor i32 %73, %74
  %76 = mul i32 %75, 9
  %77 = lshr i32 %76, 4
  %78 = xor i32 %77, %76
  %79 = mul i32 %78, 668265261
  %80 = lshr i32 %79, 15
  %81 = xor i32 %80, %79
  %82 = uitofp i32 %81 to float
  %83 = fmul fast float %82, 0x3DF0000000000000
  %84 = uitofp i32 %9 to float
  %85 = uitofp i32 %10 to float
  %86 = fadd fast float %84, -5.000000e-01
  %87 = fadd fast float %86, %72
  %88 = fadd fast float %85, -5.000000e-01
  %89 = fadd fast float %88, %83
  %90 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 10)  ; CBufferLoadLegacy(handle,regIndex)
  %91 = extractvalue %dx.types.CBufRet.f32 %90, 0
  %92 = extractvalue %dx.types.CBufRet.f32 %90, 1
  %93 = fdiv fast float %87, %91
  %94 = fdiv fast float %89, %92
  %95 = fmul fast float %93, 2.000000e+00
  %96 = fmul fast float %94, 2.000000e+00
  %97 = fadd fast float %95, -1.000000e+00
  %98 = fadd fast float %96, -1.000000e+00
  %99 = fsub fast float -0.000000e+00, %98
  %100 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %101 = extractvalue %dx.types.CBufRet.f32 %100, 0
  %102 = extractvalue %dx.types.CBufRet.f32 %100, 1
  %103 = extractvalue %dx.types.CBufRet.f32 %100, 2
  %104 = extractvalue %dx.types.CBufRet.f32 %100, 3
  %105 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %106 = extractvalue %dx.types.CBufRet.f32 %105, 0
  %107 = extractvalue %dx.types.CBufRet.f32 %105, 1
  %108 = extractvalue %dx.types.CBufRet.f32 %105, 2
  %109 = extractvalue %dx.types.CBufRet.f32 %105, 3
  %110 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 6)  ; CBufferLoadLegacy(handle,regIndex)
  %111 = extractvalue %dx.types.CBufRet.f32 %110, 0
  %112 = extractvalue %dx.types.CBufRet.f32 %110, 1
  %113 = extractvalue %dx.types.CBufRet.f32 %110, 2
  %114 = extractvalue %dx.types.CBufRet.f32 %110, 3
  %115 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 7)  ; CBufferLoadLegacy(handle,regIndex)
  %116 = extractvalue %dx.types.CBufRet.f32 %115, 0
  %117 = extractvalue %dx.types.CBufRet.f32 %115, 1
  %118 = extractvalue %dx.types.CBufRet.f32 %115, 2
  %119 = extractvalue %dx.types.CBufRet.f32 %115, 3
  %120 = fmul fast float %101, %97
  %121 = call float @dx.op.tertiary.f32(i32 46, float %99, float %102, float %120)  ; FMad(a,b,c)
  %122 = fadd fast float %104, %103
  %123 = fadd fast float %122, %121
  %124 = fmul fast float %106, %97
  %125 = call float @dx.op.tertiary.f32(i32 46, float %99, float %107, float %124)  ; FMad(a,b,c)
  %126 = fadd fast float %109, %108
  %127 = fadd fast float %126, %125
  %128 = fmul fast float %111, %97
  %129 = call float @dx.op.tertiary.f32(i32 46, float %99, float %112, float %128)  ; FMad(a,b,c)
  %130 = fadd fast float %114, %113
  %131 = fadd fast float %130, %129
  %132 = fmul fast float %116, %97
  %133 = call float @dx.op.tertiary.f32(i32 46, float %99, float %117, float %132)  ; FMad(a,b,c)
  %134 = fadd fast float %119, %118
  %135 = fadd fast float %134, %133
  %136 = fdiv fast float %123, %135
  %137 = fdiv fast float %127, %135
  %138 = fdiv fast float %131, %135
  %139 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %140 = extractvalue %dx.types.CBufRet.f32 %139, 0
  %141 = extractvalue %dx.types.CBufRet.f32 %139, 1
  %142 = extractvalue %dx.types.CBufRet.f32 %139, 2
  %143 = extractvalue %dx.types.CBufRet.f32 %139, 3
  %144 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %145 = extractvalue %dx.types.CBufRet.f32 %144, 0
  %146 = extractvalue %dx.types.CBufRet.f32 %144, 1
  %147 = extractvalue %dx.types.CBufRet.f32 %144, 2
  %148 = extractvalue %dx.types.CBufRet.f32 %144, 3
  %149 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %150 = extractvalue %dx.types.CBufRet.f32 %149, 0
  %151 = extractvalue %dx.types.CBufRet.f32 %149, 1
  %152 = extractvalue %dx.types.CBufRet.f32 %149, 2
  %153 = extractvalue %dx.types.CBufRet.f32 %149, 3
  %154 = fmul fast float %140, %136
  %155 = call float @dx.op.tertiary.f32(i32 46, float %137, float %141, float %154)  ; FMad(a,b,c)
  %156 = call float @dx.op.tertiary.f32(i32 46, float %138, float %142, float %155)  ; FMad(a,b,c)
  %157 = fadd fast float %156, %143
  %158 = fmul fast float %145, %136
  %159 = call float @dx.op.tertiary.f32(i32 46, float %137, float %146, float %158)  ; FMad(a,b,c)
  %160 = call float @dx.op.tertiary.f32(i32 46, float %138, float %147, float %159)  ; FMad(a,b,c)
  %161 = fadd fast float %160, %148
  %162 = fmul fast float %150, %136
  %163 = call float @dx.op.tertiary.f32(i32 46, float %137, float %151, float %162)  ; FMad(a,b,c)
  %164 = call float @dx.op.tertiary.f32(i32 46, float %138, float %152, float %163)  ; FMad(a,b,c)
  %165 = fadd fast float %164, %153
  %166 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %8, i32 8)  ; CBufferLoadLegacy(handle,regIndex)
  %167 = extractvalue %dx.types.CBufRet.f32 %166, 0
  %168 = extractvalue %dx.types.CBufRet.f32 %166, 1
  %169 = extractvalue %dx.types.CBufRet.f32 %166, 2
  %170 = fsub fast float %157, %167
  %171 = fsub fast float %161, %168
  %172 = fsub fast float %165, %169
  %173 = call float @dx.op.dot3.f32(i32 55, float %170, float %171, float %172, float %170, float %171, float %172)  ; Dot3(ax,ay,az,bx,by,bz)
  %174 = call float @dx.op.unary.f32(i32 25, float %173)  ; Rsqrt(value)
  %175 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %176 = extractvalue %dx.types.CBufRet.i32 %175, 1
  %177 = icmp eq i32 %176, 0
  br i1 %177, label %809, label %178

; <label>:178                                     ; preds = %33
  %179 = fmul fast float %172, %174
  %180 = fmul fast float %171, %174
  %181 = fmul fast float %170, %174
  br label %182

; <label>:182                                     ; preds = %792, %178
  %183 = phi float [ %511, %792 ], [ %34, %178 ]
  %184 = phi float [ %512, %792 ], [ %35, %178 ]
  %185 = phi float [ %513, %792 ], [ %36, %178 ]
  %186 = phi float [ %514, %792 ], [ %37, %178 ]
  %187 = phi float [ %515, %792 ], [ %38, %178 ]
  %188 = phi float [ %516, %792 ], [ %39, %178 ]
  %189 = phi i32 [ %518, %792 ], [ %40, %178 ]
  %190 = phi float [ %519, %792 ], [ %41, %178 ]
  %191 = phi float [ %520, %792 ], [ %42, %178 ]
  %192 = phi float [ %521, %792 ], [ %43, %178 ]
  %193 = phi float [ %522, %792 ], [ %44, %178 ]
  %194 = phi float [ %523, %792 ], [ %45, %178 ]
  %195 = phi float [ %524, %792 ], [ %46, %178 ]
  %196 = phi float [ %525, %792 ], [ %47, %178 ]
  %197 = phi i32 [ %526, %792 ], [ %48, %178 ]
  %198 = phi float [ %342, %792 ], [ %49, %178 ]
  %199 = phi float [ %343, %792 ], [ %50, %178 ]
  %200 = phi float [ %344, %792 ], [ %51, %178 ]
  %201 = phi float [ %345, %792 ], [ %52, %178 ]
  %202 = phi float [ %346, %792 ], [ %53, %178 ]
  %203 = phi float [ %347, %792 ], [ %54, %178 ]
  %204 = phi float [ %348, %792 ], [ %55, %178 ]
  %205 = phi float [ %754, %792 ], [ %181, %178 ]
  %206 = phi float [ %755, %792 ], [ %180, %178 ]
  %207 = phi float [ %756, %792 ], [ %179, %178 ]
  %208 = phi float [ %800, %792 ], [ %167, %178 ]
  %209 = phi float [ %801, %792 ], [ %168, %178 ]
  %210 = phi float [ %802, %792 ], [ %169, %178 ]
  %211 = phi i32 [ %350, %792 ], [ %56, %178 ]
  %212 = phi float [ %575, %792 ], [ 0.000000e+00, %178 ]
  %213 = phi float [ %576, %792 ], [ 0.000000e+00, %178 ]
  %214 = phi float [ %577, %792 ], [ 0.000000e+00, %178 ]
  %215 = phi float [ %793, %792 ], [ 1.000000e+00, %178 ]
  %216 = phi float [ %794, %792 ], [ 1.000000e+00, %178 ]
  %217 = phi float [ %795, %792 ], [ 1.000000e+00, %178 ]
  %218 = phi i32 [ %803, %792 ], [ 0, %178 ]
  %219 = phi i32 [ %796, %792 ], [ %81, %178 ]
  %220 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %221 = extractvalue %dx.types.CBufRet.i32 %220, 0
  %222 = icmp eq i32 %221, 0
  br i1 %222, label %324, label %223

; <label>:223                                     ; preds = %182
  br label %224

; <label>:224                                     ; preds = %296, %223
  %225 = phi float [ %309, %296 ], [ %183, %223 ]
  %226 = phi float [ %310, %296 ], [ %184, %223 ]
  %227 = phi float [ %311, %296 ], [ %185, %223 ]
  %228 = phi float [ %312, %296 ], [ %186, %223 ]
  %229 = phi float [ %313, %296 ], [ %187, %223 ]
  %230 = phi float [ %314, %296 ], [ %188, %223 ]
  %231 = phi float [ %315, %296 ], [ 1.000000e+03, %223 ]
  %232 = phi i32 [ %316, %296 ], [ 0, %223 ]
  %233 = phi i32 [ %317, %296 ], [ %189, %223 ]
  %234 = phi float [ %298, %296 ], [ %198, %223 ]
  %235 = phi float [ %299, %296 ], [ %199, %223 ]
  %236 = phi float [ %300, %296 ], [ %200, %223 ]
  %237 = phi float [ %301, %296 ], [ %201, %223 ]
  %238 = phi float [ %302, %296 ], [ %202, %223 ]
  %239 = phi float [ %303, %296 ], [ %203, %223 ]
  %240 = phi float [ %304, %296 ], [ %204, %223 ]
  %241 = phi float [ %318, %296 ], [ 1.000000e+03, %223 ]
  %242 = phi i32 [ %305, %296 ], [ %211, %223 ]
  %243 = phi i32 [ %319, %296 ], [ 0, %223 ]
  %244 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %6, i32 %243, i32 0)  ; BufferLoad(srv,index,wot)
  %245 = extractvalue %dx.types.ResRet.f32 %244, 0
  %246 = extractvalue %dx.types.ResRet.f32 %244, 1
  %247 = extractvalue %dx.types.ResRet.f32 %244, 2
  %248 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %6, i32 %243, i32 12)  ; BufferLoad(srv,index,wot)
  %249 = extractvalue %dx.types.ResRet.f32 %248, 0
  %250 = call %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32 68, %dx.types.Handle %6, i32 %243, i32 16)  ; BufferLoad(srv,index,wot)
  %251 = extractvalue %dx.types.ResRet.i32 %250, 0
  %252 = fsub fast float %208, %245
  %253 = fsub fast float %209, %246
  %254 = fsub fast float %210, %247
  %255 = call float @dx.op.dot3.f32(i32 55, float %205, float %206, float %207, float %205, float %206, float %207)  ; Dot3(ax,ay,az,bx,by,bz)
  %256 = call float @dx.op.dot3.f32(i32 55, float %252, float %253, float %254, float %205, float %206, float %207)  ; Dot3(ax,ay,az,bx,by,bz)
  %257 = fmul fast float %256, 2.000000e+00
  %258 = call float @dx.op.dot3.f32(i32 55, float %252, float %253, float %254, float %252, float %253, float %254)  ; Dot3(ax,ay,az,bx,by,bz)
  %259 = fmul fast float %249, %249
  %260 = fsub fast float %258, %259
  %261 = fmul fast float %257, %257
  %262 = fmul fast float %255, 4.000000e+00
  %263 = fmul fast float %262, %260
  %264 = fsub fast float %261, %263
  %265 = fcmp fast olt float %264, 0.000000e+00
  br i1 %265, label %296, label %266

; <label>:266                                     ; preds = %224
  %267 = call float @dx.op.unary.f32(i32 24, float %264)  ; Sqrt(value)
  %268 = fsub fast float -0.000000e+00, %257
  %269 = fsub fast float %268, %267
  %270 = fmul fast float %255, 2.000000e+00
  %271 = fdiv fast float %269, %270
  %272 = fsub fast float %267, %257
  %273 = fdiv fast float %272, %270
  %274 = fcmp fast ogt float %271, 0x3F50624DE0000000
  %275 = fcmp fast olt float %271, %241
  %276 = and i1 %274, %275
  %277 = select i1 %276, float %271, float %273
  %278 = fcmp fast olt float %277, 0x3F50624DE0000000
  %279 = fcmp fast ogt float %277, %241
  %280 = or i1 %278, %279
  br i1 %280, label %296, label %281

; <label>:281                                     ; preds = %266
  %282 = fmul fast float %277, %205
  %283 = fmul fast float %277, %206
  %284 = fmul fast float %277, %207
  %285 = fadd fast float %282, %208
  %286 = fadd fast float %283, %209
  %287 = fadd fast float %284, %210
  %288 = fsub fast float %285, %245
  %289 = fsub fast float %286, %246
  %290 = fsub fast float %287, %247
  %291 = call float @dx.op.dot3.f32(i32 55, float %288, float %289, float %290, float %288, float %289, float %290)  ; Dot3(ax,ay,az,bx,by,bz)
  %292 = call float @dx.op.unary.f32(i32 25, float %291)  ; Rsqrt(value)
  %293 = fmul fast float %292, %288
  %294 = fmul fast float %292, %289
  %295 = fmul fast float %292, %290
  br label %296

; <label>:296                                     ; preds = %281, %266, %224
  %297 = phi i32 [ 1, %281 ], [ 0, %224 ], [ 0, %266 ]
  %298 = phi float [ %277, %281 ], [ %234, %224 ], [ %234, %266 ]
  %299 = phi float [ %285, %281 ], [ %235, %224 ], [ %235, %266 ]
  %300 = phi float [ %286, %281 ], [ %236, %224 ], [ %236, %266 ]
  %301 = phi float [ %287, %281 ], [ %237, %224 ], [ %237, %266 ]
  %302 = phi float [ %293, %281 ], [ %238, %224 ], [ %238, %266 ]
  %303 = phi float [ %294, %281 ], [ %239, %224 ], [ %239, %266 ]
  %304 = phi float [ %295, %281 ], [ %240, %224 ], [ %240, %266 ]
  %305 = phi i32 [ %251, %281 ], [ %242, %224 ], [ %242, %266 ]
  %306 = phi i1 [ true, %281 ], [ false, %224 ], [ false, %266 ]
  %307 = fcmp fast olt float %298, %231
  %308 = and i1 %307, %306
  %309 = select i1 %308, float %302, float %225
  %310 = select i1 %308, float %303, float %226
  %311 = select i1 %308, float %304, float %227
  %312 = select i1 %308, float %299, float %228
  %313 = select i1 %308, float %300, float %229
  %314 = select i1 %308, float %301, float %230
  %315 = select i1 %308, float %298, float %231
  %316 = select i1 %308, i32 %297, i32 %232
  %317 = select i1 %308, i32 %305, i32 %233
  %318 = select i1 %308, float %298, float %241
  %319 = add i32 %243, 1
  %320 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %321 = extractvalue %dx.types.CBufRet.i32 %320, 0
  %322 = icmp ult i32 %319, %321
  br i1 %322, label %224, label %323

; <label>:323                                     ; preds = %296
  br label %324

; <label>:324                                     ; preds = %323, %182
  %325 = phi float [ %183, %182 ], [ %309, %323 ]
  %326 = phi float [ %184, %182 ], [ %310, %323 ]
  %327 = phi float [ %185, %182 ], [ %311, %323 ]
  %328 = phi float [ %186, %182 ], [ %312, %323 ]
  %329 = phi float [ %187, %182 ], [ %313, %323 ]
  %330 = phi float [ %188, %182 ], [ %314, %323 ]
  %331 = phi float [ 1.000000e+03, %182 ], [ %315, %323 ]
  %332 = phi i32 [ 0, %182 ], [ %316, %323 ]
  %333 = phi i32 [ %189, %182 ], [ %317, %323 ]
  %334 = phi float [ %190, %182 ], [ %298, %323 ]
  %335 = phi float [ %191, %182 ], [ %299, %323 ]
  %336 = phi float [ %192, %182 ], [ %300, %323 ]
  %337 = phi float [ %193, %182 ], [ %301, %323 ]
  %338 = phi float [ %194, %182 ], [ %302, %323 ]
  %339 = phi float [ %195, %182 ], [ %303, %323 ]
  %340 = phi float [ %196, %182 ], [ %304, %323 ]
  %341 = phi i32 [ %197, %182 ], [ %305, %323 ]
  %342 = phi float [ %198, %182 ], [ %298, %323 ]
  %343 = phi float [ %199, %182 ], [ %299, %323 ]
  %344 = phi float [ %200, %182 ], [ %300, %323 ]
  %345 = phi float [ %201, %182 ], [ %301, %323 ]
  %346 = phi float [ %202, %182 ], [ %302, %323 ]
  %347 = phi float [ %203, %182 ], [ %303, %323 ]
  %348 = phi float [ %204, %182 ], [ %304, %323 ]
  %349 = phi float [ 1.000000e+03, %182 ], [ %318, %323 ]
  %350 = phi i32 [ %211, %182 ], [ %305, %323 ]
  %351 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %352 = extractvalue %dx.types.CBufRet.i32 %351, 1
  %353 = icmp eq i32 %352, 0
  br i1 %353, label %510, label %354

; <label>:354                                     ; preds = %324
  br label %355

; <label>:355                                     ; preds = %482, %354
  %356 = phi float [ %495, %482 ], [ %325, %354 ]
  %357 = phi float [ %496, %482 ], [ %326, %354 ]
  %358 = phi float [ %497, %482 ], [ %327, %354 ]
  %359 = phi float [ %498, %482 ], [ %328, %354 ]
  %360 = phi float [ %499, %482 ], [ %329, %354 ]
  %361 = phi float [ %500, %482 ], [ %330, %354 ]
  %362 = phi float [ %501, %482 ], [ %331, %354 ]
  %363 = phi i32 [ %502, %482 ], [ %332, %354 ]
  %364 = phi i32 [ %503, %482 ], [ %333, %354 ]
  %365 = phi float [ %484, %482 ], [ %334, %354 ]
  %366 = phi float [ %485, %482 ], [ %335, %354 ]
  %367 = phi float [ %486, %482 ], [ %336, %354 ]
  %368 = phi float [ %487, %482 ], [ %337, %354 ]
  %369 = phi float [ %488, %482 ], [ %338, %354 ]
  %370 = phi float [ %489, %482 ], [ %339, %354 ]
  %371 = phi float [ %490, %482 ], [ %340, %354 ]
  %372 = phi i32 [ %491, %482 ], [ %341, %354 ]
  %373 = phi float [ %504, %482 ], [ %349, %354 ]
  %374 = phi i32 [ %505, %482 ], [ 0, %354 ]
  %375 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 0)  ; BufferLoad(srv,index,wot)
  %376 = extractvalue %dx.types.ResRet.f32 %375, 0
  %377 = extractvalue %dx.types.ResRet.f32 %375, 1
  %378 = extractvalue %dx.types.ResRet.f32 %375, 2
  %379 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 12)  ; BufferLoad(srv,index,wot)
  %380 = extractvalue %dx.types.ResRet.f32 %379, 0
  %381 = extractvalue %dx.types.ResRet.f32 %379, 1
  %382 = extractvalue %dx.types.ResRet.f32 %379, 2
  %383 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 24)  ; BufferLoad(srv,index,wot)
  %384 = extractvalue %dx.types.ResRet.f32 %383, 0
  %385 = extractvalue %dx.types.ResRet.f32 %383, 1
  %386 = extractvalue %dx.types.ResRet.f32 %383, 2
  %387 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 36)  ; BufferLoad(srv,index,wot)
  %388 = extractvalue %dx.types.ResRet.f32 %387, 0
  %389 = extractvalue %dx.types.ResRet.f32 %387, 1
  %390 = extractvalue %dx.types.ResRet.f32 %387, 2
  %391 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 48)  ; BufferLoad(srv,index,wot)
  %392 = extractvalue %dx.types.ResRet.f32 %391, 0
  %393 = extractvalue %dx.types.ResRet.f32 %391, 1
  %394 = extractvalue %dx.types.ResRet.f32 %391, 2
  %395 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %5, i32 %374, i32 60)  ; BufferLoad(srv,index,wot)
  %396 = extractvalue %dx.types.ResRet.f32 %395, 0
  %397 = extractvalue %dx.types.ResRet.f32 %395, 1
  %398 = extractvalue %dx.types.ResRet.f32 %395, 2
  %399 = call %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32 68, %dx.types.Handle %5, i32 %374, i32 96)  ; BufferLoad(srv,index,wot)
  %400 = extractvalue %dx.types.ResRet.i32 %399, 0
  %401 = fsub fast float %380, %376
  %402 = fsub fast float %381, %377
  %403 = fsub fast float %382, %378
  %404 = fsub fast float %384, %376
  %405 = fsub fast float %385, %377
  %406 = fsub fast float %386, %378
  %407 = fmul fast float %406, %206
  %408 = fmul fast float %405, %207
  %409 = fsub fast float %407, %408
  %410 = fmul fast float %404, %207
  %411 = fmul fast float %406, %205
  %412 = fsub fast float %410, %411
  %413 = fmul fast float %405, %205
  %414 = fmul fast float %404, %206
  %415 = fsub fast float %413, %414
  %416 = call float @dx.op.dot3.f32(i32 55, float %401, float %402, float %403, float %409, float %412, float %415)  ; Dot3(ax,ay,az,bx,by,bz)
  %417 = fcmp fast ogt float %416, 0xBEE4F8B580000000
  %418 = fcmp fast olt float %416, 0x3EE4F8B580000000
  %419 = and i1 %417, %418
  br i1 %419, label %482, label %420

; <label>:420                                     ; preds = %355
  %421 = fdiv fast float 1.000000e+00, %416
  %422 = fsub fast float %208, %376
  %423 = fsub fast float %209, %377
  %424 = fsub fast float %210, %378
  %425 = call float @dx.op.dot3.f32(i32 55, float %422, float %423, float %424, float %409, float %412, float %415)  ; Dot3(ax,ay,az,bx,by,bz)
  %426 = fmul fast float %425, %421
  %427 = fcmp fast olt float %426, 0.000000e+00
  %428 = fcmp fast ogt float %426, 1.000000e+00
  %429 = or i1 %427, %428
  br i1 %429, label %482, label %430

; <label>:430                                     ; preds = %420
  %431 = fmul fast float %403, %423
  %432 = fmul fast float %402, %424
  %433 = fsub fast float %431, %432
  %434 = fmul fast float %401, %424
  %435 = fmul fast float %403, %422
  %436 = fsub fast float %434, %435
  %437 = fmul fast float %402, %422
  %438 = fmul fast float %401, %423
  %439 = fsub fast float %437, %438
  %440 = call float @dx.op.dot3.f32(i32 55, float %205, float %206, float %207, float %433, float %436, float %439)  ; Dot3(ax,ay,az,bx,by,bz)
  %441 = fmul fast float %440, %421
  %442 = fcmp fast olt float %441, 0.000000e+00
  %443 = fadd fast float %440, %425
  %444 = fmul fast float %443, %421
  %445 = fcmp fast ogt float %444, 1.000000e+00
  %446 = or i1 %442, %445
  br i1 %446, label %482, label %447

; <label>:447                                     ; preds = %430
  %448 = call float @dx.op.dot3.f32(i32 55, float %404, float %405, float %406, float %433, float %436, float %439)  ; Dot3(ax,ay,az,bx,by,bz)
  %449 = fmul fast float %448, %421
  %450 = fcmp fast olt float %449, 0x3F50624DE0000000
  %451 = fcmp fast ogt float %449, %373
  %452 = or i1 %450, %451
  br i1 %452, label %482, label %453

; <label>:453                                     ; preds = %447
  %454 = fmul fast float %449, %205
  %455 = fmul fast float %449, %206
  %456 = fmul fast float %449, %207
  %457 = fadd fast float %454, %208
  %458 = fadd fast float %455, %209
  %459 = fadd fast float %456, %210
  %460 = fsub fast float 1.000000e+00, %426
  %461 = fsub fast float %460, %441
  %462 = fmul fast float %461, %388
  %463 = fmul fast float %461, %389
  %464 = fmul fast float %461, %390
  %465 = fmul fast float %426, %392
  %466 = fmul fast float %426, %393
  %467 = fmul fast float %426, %394
  %468 = fmul fast float %441, %396
  %469 = fmul fast float %441, %397
  %470 = fmul fast float %441, %398
  %471 = fadd fast float %468, %465
  %472 = fadd fast float %471, %462
  %473 = fadd fast float %469, %466
  %474 = fadd fast float %473, %463
  %475 = fadd fast float %470, %467
  %476 = fadd fast float %475, %464
  %477 = call float @dx.op.dot3.f32(i32 55, float %472, float %474, float %476, float %472, float %474, float %476)  ; Dot3(ax,ay,az,bx,by,bz)
  %478 = call float @dx.op.unary.f32(i32 25, float %477)  ; Rsqrt(value)
  %479 = fmul fast float %478, %472
  %480 = fmul fast float %478, %474
  %481 = fmul fast float %478, %476
  br label %482

; <label>:482                                     ; preds = %453, %447, %430, %420, %355
  %483 = phi i32 [ 1, %453 ], [ 0, %355 ], [ 0, %420 ], [ 0, %430 ], [ 0, %447 ]
  %484 = phi float [ %449, %453 ], [ %365, %355 ], [ %365, %420 ], [ %365, %430 ], [ %365, %447 ]
  %485 = phi float [ %457, %453 ], [ %366, %355 ], [ %366, %420 ], [ %366, %430 ], [ %366, %447 ]
  %486 = phi float [ %458, %453 ], [ %367, %355 ], [ %367, %420 ], [ %367, %430 ], [ %367, %447 ]
  %487 = phi float [ %459, %453 ], [ %368, %355 ], [ %368, %420 ], [ %368, %430 ], [ %368, %447 ]
  %488 = phi float [ %479, %453 ], [ %369, %355 ], [ %369, %420 ], [ %369, %430 ], [ %369, %447 ]
  %489 = phi float [ %480, %453 ], [ %370, %355 ], [ %370, %420 ], [ %370, %430 ], [ %370, %447 ]
  %490 = phi float [ %481, %453 ], [ %371, %355 ], [ %371, %420 ], [ %371, %430 ], [ %371, %447 ]
  %491 = phi i32 [ %400, %453 ], [ %372, %355 ], [ %372, %420 ], [ %372, %430 ], [ %372, %447 ]
  %492 = phi i1 [ true, %453 ], [ false, %355 ], [ false, %420 ], [ false, %430 ], [ false, %447 ]
  %493 = fcmp fast olt float %484, %362
  %494 = and i1 %493, %492
  %495 = select i1 %494, float %488, float %356
  %496 = select i1 %494, float %489, float %357
  %497 = select i1 %494, float %490, float %358
  %498 = select i1 %494, float %485, float %359
  %499 = select i1 %494, float %486, float %360
  %500 = select i1 %494, float %487, float %361
  %501 = select i1 %494, float %484, float %362
  %502 = select i1 %494, i32 %483, i32 %363
  %503 = select i1 %494, i32 %491, i32 %364
  %504 = select i1 %494, float %484, float %373
  %505 = add i32 %374, 1
  %506 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 11)  ; CBufferLoadLegacy(handle,regIndex)
  %507 = extractvalue %dx.types.CBufRet.i32 %506, 1
  %508 = icmp ult i32 %505, %507
  br i1 %508, label %355, label %509

; <label>:509                                     ; preds = %482
  br label %510

; <label>:510                                     ; preds = %509, %324
  %511 = phi float [ %325, %324 ], [ %495, %509 ]
  %512 = phi float [ %326, %324 ], [ %496, %509 ]
  %513 = phi float [ %327, %324 ], [ %497, %509 ]
  %514 = phi float [ %328, %324 ], [ %498, %509 ]
  %515 = phi float [ %329, %324 ], [ %499, %509 ]
  %516 = phi float [ %330, %324 ], [ %500, %509 ]
  %517 = phi i32 [ %332, %324 ], [ %502, %509 ]
  %518 = phi i32 [ %333, %324 ], [ %503, %509 ]
  %519 = phi float [ %334, %324 ], [ %484, %509 ]
  %520 = phi float [ %335, %324 ], [ %485, %509 ]
  %521 = phi float [ %336, %324 ], [ %486, %509 ]
  %522 = phi float [ %337, %324 ], [ %487, %509 ]
  %523 = phi float [ %338, %324 ], [ %488, %509 ]
  %524 = phi float [ %339, %324 ], [ %489, %509 ]
  %525 = phi float [ %340, %324 ], [ %490, %509 ]
  %526 = phi i32 [ %341, %324 ], [ %491, %509 ]
  %527 = icmp eq i32 %517, 0
  br i1 %527, label %528, label %559

; <label>:528                                     ; preds = %510
  %529 = fdiv fast float %207, %205
  %530 = call float @dx.op.unary.f32(i32 17, float %529)  ; Atan(value)
  %531 = fadd fast float %530, 0x400921FB60000000
  %532 = fadd fast float %530, 0xC00921FB60000000
  %533 = fcmp fast olt float %205, 0.000000e+00
  %534 = fcmp fast oeq float %205, 0.000000e+00
  %535 = fcmp fast oge float %207, 0.000000e+00
  %536 = fcmp fast olt float %207, 0.000000e+00
  %537 = and i1 %533, %535
  %538 = select i1 %537, float %531, float %530
  %539 = and i1 %533, %536
  %540 = select i1 %539, float %532, float %538
  %541 = and i1 %534, %536
  %542 = and i1 %534, %535
  %543 = call float @dx.op.unary.f32(i32 15, float %206)  ; Acos(value)
  %544 = fadd fast float %540, 0x400921FB60000000
  %545 = fmul fast float %544, 0x3FC45F3060000000
  %546 = select i1 %541, float 2.500000e-01, float %545
  %547 = select i1 %542, float 7.500000e-01, float %546
  %548 = fmul fast float %543, 0x3FD45F3060000000
  %549 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %3, %dx.types.Handle %7, float %547, float %548, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %550 = extractvalue %dx.types.ResRet.f32 %549, 0
  %551 = extractvalue %dx.types.ResRet.f32 %549, 1
  %552 = extractvalue %dx.types.ResRet.f32 %549, 2
  %553 = fmul fast float %550, %215
  %554 = fmul fast float %551, %216
  %555 = fmul fast float %552, %217
  %556 = fadd fast float %553, %212
  %557 = fadd fast float %554, %213
  %558 = fadd fast float %555, %214
  br label %809

; <label>:559                                     ; preds = %510
  %560 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %4, i32 %518, i32 0)  ; BufferLoad(srv,index,wot)
  %561 = extractvalue %dx.types.ResRet.f32 %560, 0
  %562 = extractvalue %dx.types.ResRet.f32 %560, 1
  %563 = extractvalue %dx.types.ResRet.f32 %560, 2
  %564 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %4, i32 %518, i32 12)  ; BufferLoad(srv,index,wot)
  %565 = extractvalue %dx.types.ResRet.f32 %564, 0
  %566 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %4, i32 %518, i32 16)  ; BufferLoad(srv,index,wot)
  %567 = extractvalue %dx.types.ResRet.f32 %566, 0
  %568 = extractvalue %dx.types.ResRet.f32 %566, 1
  %569 = extractvalue %dx.types.ResRet.f32 %566, 2
  %570 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %4, i32 %518, i32 28)  ; BufferLoad(srv,index,wot)
  %571 = extractvalue %dx.types.ResRet.f32 %570, 0
  %572 = fmul fast float %567, %215
  %573 = fmul fast float %568, %216
  %574 = fmul fast float %569, %217
  %575 = fadd fast float %572, %212
  %576 = fadd fast float %573, %213
  %577 = fadd fast float %574, %214
  %578 = fsub fast float -0.000000e+00, %205
  %579 = fsub fast float -0.000000e+00, %206
  %580 = fsub fast float -0.000000e+00, %207
  %581 = xor i32 %219, 61
  %582 = lshr i32 %219, 16
  %583 = xor i32 %581, %582
  %584 = mul i32 %583, 9
  %585 = lshr i32 %584, 4
  %586 = xor i32 %585, %584
  %587 = mul i32 %586, 668265261
  %588 = lshr i32 %587, 15
  %589 = xor i32 %588, %587
  %590 = uitofp i32 %589 to float
  %591 = fmul fast float %590, 0x3DF0000000000000
  %592 = fcmp fast olt float %591, 5.000000e-01
  br i1 %592, label %594, label %593

; <label>:593                                     ; preds = %559
  br label %695

; <label>:594                                     ; preds = %559
  %595 = call float @dx.op.dot3.f32(i32 55, float %205, float %206, float %207, float %511, float %512, float %513)  ; Dot3(ax,ay,az,bx,by,bz)
  %596 = fmul fast float %595, 2.000000e+00
  %597 = fmul fast float %596, %512
  %598 = fmul fast float %596, %513
  %599 = fsub fast float %206, %597
  br label %600

; <label>:600                                     ; preds = %600, %594
  %601 = phi i32 [ %589, %594 ], [ %630, %600 ]
  %602 = xor i32 %601, 61
  %603 = lshr i32 %601, 16
  %604 = xor i32 %602, %603
  %605 = mul i32 %604, 9
  %606 = lshr i32 %605, 4
  %607 = xor i32 %606, %605
  %608 = mul i32 %607, 668265261
  %609 = lshr i32 %608, 15
  %610 = xor i32 %609, %608
  %611 = uitofp i32 %610 to float
  %612 = xor i32 %610, 61
  %613 = lshr i32 %610, 16
  %614 = xor i32 %612, %613
  %615 = mul i32 %614, 9
  %616 = lshr i32 %615, 4
  %617 = xor i32 %616, %615
  %618 = mul i32 %617, 668265261
  %619 = lshr i32 %618, 15
  %620 = xor i32 %619, %618
  %621 = uitofp i32 %620 to float
  %622 = xor i32 %620, 61
  %623 = lshr i32 %620, 16
  %624 = xor i32 %622, %623
  %625 = mul i32 %624, 9
  %626 = lshr i32 %625, 4
  %627 = xor i32 %626, %625
  %628 = mul i32 %627, 668265261
  %629 = lshr i32 %628, 15
  %630 = xor i32 %629, %628
  %631 = uitofp i32 %630 to float
  %632 = fmul fast float %611, 0x3E00000000000000
  %633 = fmul fast float %621, 0x3E00000000000000
  %634 = fmul fast float %631, 0x3E00000000000000
  %635 = fadd fast float %632, -1.000000e+00
  %636 = fadd fast float %633, -1.000000e+00
  %637 = fadd fast float %634, -1.000000e+00
  %638 = call float @dx.op.dot3.f32(i32 55, float %635, float %636, float %637, float %635, float %636, float %637)  ; Dot3(ax,ay,az,bx,by,bz)
  %639 = fcmp fast ult float %638, 1.000000e+00
  br i1 %639, label %640, label %600

; <label>:640                                     ; preds = %600
  %641 = fmul fast float %596, %511
  %642 = fsub fast float %205, %641
  %643 = fsub fast float %207, %598
  %644 = call float @dx.op.unary.f32(i32 25, float %638)  ; Rsqrt(value)
  %645 = fmul fast float %644, %571
  %646 = fmul fast float %645, %635
  %647 = fmul fast float %645, %636
  %648 = fmul fast float %645, %637
  %649 = fadd fast float %646, %642
  %650 = fadd fast float %647, %599
  %651 = fadd fast float %648, %643
  %652 = call float @dx.op.dot3.f32(i32 55, float %649, float %650, float %651, float %649, float %650, float %651)  ; Dot3(ax,ay,az,bx,by,bz)
  %653 = call float @dx.op.unary.f32(i32 25, float %652)  ; Rsqrt(value)
  %654 = fmul fast float %649, %653
  %655 = fmul fast float %650, %653
  %656 = fmul fast float %651, %653
  %657 = call float @dx.op.dot3.f32(i32 55, float %654, float %655, float %656, float %511, float %512, float %513)  ; Dot3(ax,ay,az,bx,by,bz)
  %658 = fcmp fast ugt float %657, 0.000000e+00
  br i1 %658, label %667, label %659

; <label>:659                                     ; preds = %640
  %660 = fmul fast float %657, 2.000000e+00
  %661 = fmul fast float %660, %511
  %662 = fmul fast float %660, %512
  %663 = fmul fast float %660, %513
  %664 = fsub fast float %654, %661
  %665 = fsub fast float %655, %662
  %666 = fsub fast float %656, %663
  br label %667

; <label>:667                                     ; preds = %659, %640
  %668 = phi float [ %664, %659 ], [ %654, %640 ]
  %669 = phi float [ %665, %659 ], [ %655, %640 ]
  %670 = phi float [ %666, %659 ], [ %656, %640 ]
  %671 = call float @dx.op.dot3.f32(i32 55, float %511, float %512, float %513, float %578, float %579, float %580)  ; Dot3(ax,ay,az,bx,by,bz)
  %672 = call float @dx.op.binary.f32(i32 35, float %671, float 0.000000e+00)  ; FMax(a,b)
  %673 = fadd fast float %561, 0xBFA47AE140000000
  %674 = fadd fast float %562, 0xBFA47AE140000000
  %675 = fadd fast float %563, 0xBFA47AE140000000
  %676 = fmul fast float %565, %673
  %677 = fmul fast float %565, %674
  %678 = fmul fast float %565, %675
  %679 = fadd fast float %676, 0x3FA47AE140000000
  %680 = fadd fast float %677, 0x3FA47AE140000000
  %681 = fadd fast float %678, 0x3FA47AE140000000
  %682 = fsub fast float 0x3FEEB851E0000000, %676
  %683 = fsub fast float 0x3FEEB851E0000000, %677
  %684 = fsub fast float 0x3FEEB851E0000000, %678
  %685 = fsub fast float 1.000000e+00, %672
  %686 = call float @dx.op.unary.f32(i32 23, float %685)  ; Log(value)
  %687 = fmul fast float %686, 5.000000e+00
  %688 = call float @dx.op.unary.f32(i32 21, float %687)  ; Exp(value)
  %689 = fmul fast float %688, %682
  %690 = fmul fast float %688, %683
  %691 = fmul fast float %688, %684
  %692 = fadd fast float %679, %689
  %693 = fadd fast float %680, %690
  %694 = fadd fast float %681, %691
  br label %753

; <label>:695                                     ; preds = %695, %593
  %696 = phi i32 [ %725, %695 ], [ %589, %593 ]
  %697 = xor i32 %696, 61
  %698 = lshr i32 %696, 16
  %699 = xor i32 %697, %698
  %700 = mul i32 %699, 9
  %701 = lshr i32 %700, 4
  %702 = xor i32 %701, %700
  %703 = mul i32 %702, 668265261
  %704 = lshr i32 %703, 15
  %705 = xor i32 %704, %703
  %706 = uitofp i32 %705 to float
  %707 = xor i32 %705, 61
  %708 = lshr i32 %705, 16
  %709 = xor i32 %707, %708
  %710 = mul i32 %709, 9
  %711 = lshr i32 %710, 4
  %712 = xor i32 %711, %710
  %713 = mul i32 %712, 668265261
  %714 = lshr i32 %713, 15
  %715 = xor i32 %714, %713
  %716 = uitofp i32 %715 to float
  %717 = xor i32 %715, 61
  %718 = lshr i32 %715, 16
  %719 = xor i32 %717, %718
  %720 = mul i32 %719, 9
  %721 = lshr i32 %720, 4
  %722 = xor i32 %721, %720
  %723 = mul i32 %722, 668265261
  %724 = lshr i32 %723, 15
  %725 = xor i32 %724, %723
  %726 = uitofp i32 %725 to float
  %727 = fmul fast float %706, 0x3E00000000000000
  %728 = fmul fast float %716, 0x3E00000000000000
  %729 = fmul fast float %726, 0x3E00000000000000
  %730 = fadd fast float %727, -1.000000e+00
  %731 = fadd fast float %728, -1.000000e+00
  %732 = fadd fast float %729, -1.000000e+00
  %733 = call float @dx.op.dot3.f32(i32 55, float %730, float %731, float %732, float %730, float %731, float %732)  ; Dot3(ax,ay,az,bx,by,bz)
  %734 = fcmp fast ult float %733, 1.000000e+00
  br i1 %734, label %735, label %695

; <label>:735                                     ; preds = %695
  %736 = call float @dx.op.unary.f32(i32 25, float %733)  ; Rsqrt(value)
  %737 = fmul fast float %736, %730
  %738 = fmul fast float %736, %731
  %739 = fmul fast float %736, %732
  %740 = call float @dx.op.dot3.f32(i32 55, float %737, float %738, float %739, float %511, float %512, float %513)  ; Dot3(ax,ay,az,bx,by,bz)
  %741 = fcmp fast ogt float %740, 0.000000e+00
  %742 = fsub fast float -0.000000e+00, %737
  %743 = fsub fast float -0.000000e+00, %738
  %744 = fsub fast float -0.000000e+00, %739
  %745 = select i1 %741, float %737, float %742
  %746 = select i1 %741, float %738, float %743
  %747 = select i1 %741, float %739, float %744
  %748 = fsub fast float 1.000000e+00, %565
  %749 = fmul fast float %748, 0x3FD45F3060000000
  %750 = fmul fast float %749, %561
  %751 = fmul fast float %749, %562
  %752 = fmul fast float %749, %563
  br label %753

; <label>:753                                     ; preds = %735, %667
  %754 = phi float [ %668, %667 ], [ %745, %735 ]
  %755 = phi float [ %669, %667 ], [ %746, %735 ]
  %756 = phi float [ %670, %667 ], [ %747, %735 ]
  %757 = phi float [ %692, %667 ], [ %750, %735 ]
  %758 = phi float [ %693, %667 ], [ %751, %735 ]
  %759 = phi float [ %694, %667 ], [ %752, %735 ]
  %760 = phi i32 [ %630, %667 ], [ %725, %735 ]
  %761 = call float @dx.op.dot3.f32(i32 55, float %511, float %512, float %513, float %754, float %755, float %756)  ; Dot3(ax,ay,az,bx,by,bz)
  %762 = call float @dx.op.binary.f32(i32 35, float %761, float 0.000000e+00)  ; FMax(a,b)
  %763 = fmul fast float %762, 5.000000e-01
  %764 = fmul fast float %757, %215
  %765 = fmul fast float %764, %763
  %766 = fmul fast float %758, %216
  %767 = fmul fast float %766, %763
  %768 = fmul fast float %759, %217
  %769 = fmul fast float %768, %763
  %770 = call float @dx.op.binary.f32(i32 35, float %765, float %767)  ; FMax(a,b)
  %771 = call float @dx.op.binary.f32(i32 35, float %770, float %769)  ; FMax(a,b)
  %772 = fcmp fast olt float %771, 0x3FB99999A0000000
  %773 = icmp ugt i32 %218, 3
  %774 = and i1 %773, %772
  br i1 %774, label %775, label %792

; <label>:775                                     ; preds = %753
  %776 = xor i32 %760, 61
  %777 = lshr i32 %760, 16
  %778 = xor i32 %776, %777
  %779 = mul i32 %778, 9
  %780 = lshr i32 %779, 4
  %781 = xor i32 %780, %779
  %782 = mul i32 %781, 668265261
  %783 = lshr i32 %782, 15
  %784 = xor i32 %783, %782
  %785 = uitofp i32 %784 to float
  %786 = fmul fast float %785, 0x3DF0000000000000
  %787 = fcmp fast ogt float %786, %771
  br i1 %787, label %807, label %788

; <label>:788                                     ; preds = %775
  %789 = fdiv fast float %765, %771
  %790 = fdiv fast float %767, %771
  %791 = fdiv fast float %769, %771
  br label %792

; <label>:792                                     ; preds = %788, %753
  %793 = phi float [ %789, %788 ], [ %765, %753 ]
  %794 = phi float [ %790, %788 ], [ %767, %753 ]
  %795 = phi float [ %791, %788 ], [ %769, %753 ]
  %796 = phi i32 [ %784, %788 ], [ %760, %753 ]
  %797 = fmul fast float %511, 0x3F50624DE0000000
  %798 = fmul fast float %512, 0x3F50624DE0000000
  %799 = fmul fast float %513, 0x3F50624DE0000000
  %800 = fadd fast float %514, %797
  %801 = fadd fast float %515, %798
  %802 = fadd fast float %516, %799
  %803 = add i32 %218, 1
  %804 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %805 = extractvalue %dx.types.CBufRet.i32 %804, 1
  %806 = icmp ult i32 %803, %805
  br i1 %806, label %182, label %807

; <label>:807                                     ; preds = %792, %775
  %808 = phi i32 [ %784, %775 ], [ %796, %792 ]
  br label %809

; <label>:809                                     ; preds = %807, %528, %33
  %810 = phi float [ %511, %528 ], [ %34, %33 ], [ %511, %807 ]
  %811 = phi float [ %512, %528 ], [ %35, %33 ], [ %512, %807 ]
  %812 = phi float [ %513, %528 ], [ %36, %33 ], [ %513, %807 ]
  %813 = phi float [ %514, %528 ], [ %37, %33 ], [ %514, %807 ]
  %814 = phi float [ %515, %528 ], [ %38, %33 ], [ %515, %807 ]
  %815 = phi float [ %516, %528 ], [ %39, %33 ], [ %516, %807 ]
  %816 = phi i32 [ %518, %528 ], [ %40, %33 ], [ %518, %807 ]
  %817 = phi float [ %519, %528 ], [ %41, %33 ], [ %519, %807 ]
  %818 = phi float [ %520, %528 ], [ %42, %33 ], [ %520, %807 ]
  %819 = phi float [ %521, %528 ], [ %43, %33 ], [ %521, %807 ]
  %820 = phi float [ %522, %528 ], [ %44, %33 ], [ %522, %807 ]
  %821 = phi float [ %523, %528 ], [ %45, %33 ], [ %523, %807 ]
  %822 = phi float [ %524, %528 ], [ %46, %33 ], [ %524, %807 ]
  %823 = phi float [ %525, %528 ], [ %47, %33 ], [ %525, %807 ]
  %824 = phi i32 [ %526, %528 ], [ %48, %33 ], [ %526, %807 ]
  %825 = phi float [ %342, %528 ], [ %49, %33 ], [ %342, %807 ]
  %826 = phi float [ %343, %528 ], [ %50, %33 ], [ %343, %807 ]
  %827 = phi float [ %344, %528 ], [ %51, %33 ], [ %344, %807 ]
  %828 = phi float [ %345, %528 ], [ %52, %33 ], [ %345, %807 ]
  %829 = phi float [ %346, %528 ], [ %53, %33 ], [ %346, %807 ]
  %830 = phi float [ %347, %528 ], [ %54, %33 ], [ %347, %807 ]
  %831 = phi float [ %348, %528 ], [ %55, %33 ], [ %348, %807 ]
  %832 = phi i32 [ %350, %528 ], [ %56, %33 ], [ %350, %807 ]
  %833 = phi float [ %556, %528 ], [ 0.000000e+00, %33 ], [ %575, %807 ]
  %834 = phi float [ %557, %528 ], [ 0.000000e+00, %33 ], [ %576, %807 ]
  %835 = phi float [ %558, %528 ], [ 0.000000e+00, %33 ], [ %577, %807 ]
  %836 = phi i32 [ %219, %528 ], [ %81, %33 ], [ %808, %807 ]
  %837 = fadd fast float %833, %58
  %838 = fadd fast float %834, %59
  %839 = fadd fast float %835, %60
  %840 = add i32 %61, 1
  %841 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %842 = extractvalue %dx.types.CBufRet.i32 %841, 2
  %843 = icmp ult i32 %840, %842
  br i1 %843, label %33, label %844

; <label>:844                                     ; preds = %809
  br label %845

; <label>:845                                     ; preds = %844, %21
  %846 = phi float [ 0.000000e+00, %21 ], [ %837, %844 ]
  %847 = phi float [ 0.000000e+00, %21 ], [ %838, %844 ]
  %848 = phi float [ 0.000000e+00, %21 ], [ %839, %844 ]
  %849 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %850 = extractvalue %dx.types.CBufRet.i32 %849, 2
  %851 = uitofp i32 %850 to float
  %852 = fdiv fast float %846, %851
  %853 = fdiv fast float %847, %851
  %854 = fdiv fast float %848, %851
  %855 = shl i32 %12, 3
  %856 = add i32 %855, %11
  %857 = mul i32 %856, 3
  %858 = add i32 0, %857
  %859 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedRadiance@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %858
  store float %852, float addrspace(3)* %859, align 4
  %860 = mul i32 %856, 3
  %861 = add i32 1, %860
  %862 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedRadiance@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %861
  store float %853, float addrspace(3)* %862, align 4
  %863 = mul i32 %856, 3
  %864 = add i32 2, %863
  %865 = getelementptr [192 x float], [192 x float] addrspace(3)* @"\01?SharedRadiance@@3PAV?$vector@M$02@@A.v.1dim", i32 0, i32 %864
  store float %854, float addrspace(3)* %865, align 4
  %866 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %8, i32 9)  ; CBufferLoadLegacy(handle,regIndex)
  %867 = extractvalue %dx.types.CBufRet.i32 %866, 2
  %868 = getelementptr [64 x i32], [64 x i32] addrspace(3)* @"\01?SharedSampleCount@@3PAIA", i32 0, i32 %856
  store i32 %867, i32 addrspace(3)* %868, align 4, !tbaa !24
  call void @dx.op.barrier(i32 80, i32 9)  ; Barrier(barrierMode)
  %869 = call %dx.types.ResRet.f32 @dx.op.textureLoad.f32(i32 66, %dx.types.Handle %1, i32 undef, i32 %9, i32 %10, i32 undef, i32 undef, i32 undef, i32 undef)  ; TextureLoad(srv,mipLevelOrSampleCount,coord0,coord1,coord2,offset0,offset1,offset2)
  %870 = extractvalue %dx.types.ResRet.f32 %869, 0
  %871 = extractvalue %dx.types.ResRet.f32 %869, 1
  %872 = extractvalue %dx.types.ResRet.f32 %869, 2
  %873 = extractvalue %dx.types.ResRet.f32 %869, 3
  %874 = fadd fast float %873, 1.000000e+00
  %875 = fdiv fast float 1.000000e+00, %874
  %876 = fsub fast float %852, %870
  %877 = fsub fast float %853, %871
  %878 = fsub fast float %854, %872
  %879 = fmul fast float %876, %875
  %880 = fmul fast float %877, %875
  %881 = fmul fast float %878, %875
  %882 = fadd fast float %879, %870
  %883 = fadd fast float %880, %871
  %884 = fadd fast float %881, %872
  %885 = fadd fast float %882, 1.000000e+00
  %886 = fadd fast float %883, 1.000000e+00
  %887 = fadd fast float %884, 1.000000e+00
  %888 = fdiv fast float %882, %885
  %889 = fdiv fast float %883, %886
  %890 = fdiv fast float %884, %887
  %891 = call float @dx.op.unary.f32(i32 23, float %888)  ; Log(value)
  %892 = call float @dx.op.unary.f32(i32 23, float %889)  ; Log(value)
  %893 = call float @dx.op.unary.f32(i32 23, float %890)  ; Log(value)
  %894 = fmul fast float %891, 0x3FDD1745E0000000
  %895 = fmul fast float %892, 0x3FDD1745E0000000
  %896 = fmul fast float %893, 0x3FDD1745E0000000
  %897 = call float @dx.op.unary.f32(i32 21, float %894)  ; Exp(value)
  %898 = call float @dx.op.unary.f32(i32 21, float %895)  ; Exp(value)
  %899 = call float @dx.op.unary.f32(i32 21, float %896)  ; Exp(value)
  call void @dx.op.textureStore.f32(i32 67, %dx.types.Handle %2, i32 %9, i32 %10, i32 undef, float %897, float %898, float %899, float 1.000000e+00, i8 15)  ; TextureStore(srv,coord0,coord1,coord2,value0,value1,value2,value3,mask)
  call void @dx.op.textureStore.f32(i32 67, %dx.types.Handle %1, i32 %9, i32 %10, i32 undef, float %882, float %883, float %884, float %874, i8 15)  ; TextureStore(srv,coord0,coord1,coord2,value0,value1,value2,value3,mask)
  br label %900

; <label>:900                                     ; preds = %845, %17, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadIdInGroup.i32(i32, i32) #0

; Function Attrs: noduplicate nounwind
declare void @dx.op.barrier(i32, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.textureLoad.f32(i32, %dx.types.Handle, i32, i32, i32, i32, i32, i32, i32) #2

; Function Attrs: nounwind
declare void @dx.op.textureStore.f32(i32, %dx.types.Handle, i32, i32, i32, float, float, float, float, i8) #3

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.i32 @dx.op.bufferLoad.i32(i32, %dx.types.Handle, i32, i32) #2

attributes #0 = { nounwind readnone }
attributes #1 = { noduplicate nounwind }
attributes #2 = { nounwind readonly }
attributes #3 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!21}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{!5, !14, !17, !19}
!5 = !{!6, !8, !10, !12}
!6 = !{i32 0, %"class.StructuredBuffer<Sphere>"* undef, !"", i32 0, i32 0, i32 1, i32 12, i32 0, !7}
!7 = !{i32 1, i32 24}
!8 = !{i32 1, %"class.StructuredBuffer<Triangle>"* undef, !"", i32 0, i32 1, i32 1, i32 12, i32 0, !9}
!9 = !{i32 1, i32 112}
!10 = !{i32 2, %"class.StructuredBuffer<Material>"* undef, !"", i32 0, i32 2, i32 1, i32 12, i32 0, !11}
!11 = !{i32 1, i32 48}
!12 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 4, i32 1, i32 2, i32 0, !13}
!13 = !{i32 0, i32 9}
!14 = !{!15, !16}
!15 = !{i32 0, %"class.RWTexture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i1 false, i1 false, i1 false, !13}
!16 = !{i32 1, %"class.RWTexture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i1 false, i1 false, i1 false, !13}
!17 = !{!18}
!18 = !{i32 0, %hostlayout.RayTracingParams* undef, !"", i32 0, i32 0, i32 1, i32 192, null}
!19 = !{!20}
!20 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!21 = !{void ()* @main, !"main", null, !4, !22}
!22 = !{i32 0, i64 8208, i32 4, !23}
!23 = !{i32 8, i32 8, i32 1}
!24 = !{!25, !25, i64 0}
!25 = !{!"int", !26, i64 0}
!26 = !{!"omnipotent char", !27, i64 0}
!27 = !{!"Simple C/C++ TBAA"}
