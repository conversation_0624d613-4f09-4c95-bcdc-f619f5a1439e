_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s8, s2, 0x90016                                  // 00000000000C: 9388FF02 00090016
	s_bfe_u32 s0, s2, 0x9000c                                  // 000000000014: 9380FF02 0009000C
	s_mov_b32 s1, s11                                          // 00000000001C: BE81030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s8, 12                                      // 00000000002C: 8F028C08
	s_or_b32 m0, s2, s0                                        // 000000000030: 887C0002
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v7, s3, 5, v1                                // 000000000040: D76F0007 04050A03
	v_cmp_gt_u32_e64 s0, s0, v7                                // 000000000048: D4C40000 00020E00
	s_and_saveexec_b32 s2, s0                                  // 000000000050: BE823C00
	s_cbranch_execz _L1                                        // 000000000054: BF8800DA
	s_getpc_b64 s[6:7]                                         // 000000000058: BE861F00
	v_add_nc_u32_e32 v16, s1, v5                               // 00000000005C: 4A200A01
	s_mov_b32 s11, s7                                          // 000000000060: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000064: BE850307
	s_clause 0x1                                               // 000000000068: BFA10001
	s_load_dwordx4 s[16:19], s[10:11], null                    // 00000000006C: F4080405 FA000000
	s_load_dwordx4 s[20:23], s[10:11], 0x20                    // 000000000074: F4080505 FA000020
	s_mov_b32 s3, 0                                            // 00000000007C: BE830380
	s_waitcnt lgkmcnt(0)                                       // 000000000080: BF8CC07F
	tbuffer_load_format_xyz v[1:3], v16, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000084: EA522000 80040110
	tbuffer_load_format_xyz v[4:6], v16, s[20:23], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000008C: EA522000 80050410
	s_load_dwordx4 s[4:7], s[4:5], null                        // 000000000094: F4080102 FA000000
	s_waitcnt lgkmcnt(0)                                       // 00000000009C: BF8CC07F
	s_clause 0x2                                               // 0000000000A0: BFA10002
	s_buffer_load_dwordx8 s[16:23], s[4:7], 0x10               // 0000000000A4: F42C0402 FA000010
	s_buffer_load_dwordx8 s[24:31], s[4:7], 0x30               // 0000000000AC: F42C0602 FA000030
	s_buffer_load_dword s1, s[4:7], 0x4                        // 0000000000B4: F4200042 FA000004
	s_waitcnt lgkmcnt(0)                                       // 0000000000BC: BF8CC07F
	s_cmp_lt_i32 s1, 5                                         // 0000000000C0: BF048501
	s_waitcnt vmcnt(1)                                         // 0000000000C4: BF8C3F71
	v_fma_f32 v15, s16, v1, s19                                // 0000000000C8: D54B000F 004E0210
	v_fma_f32 v14, s20, v1, s23                                // 0000000000D0: D54B000E 005E0214
	v_fma_f32 v13, s24, v1, s27                                // 0000000000D8: D54B000D 006E0218
	v_fma_f32 v12, s28, v1, s31                                // 0000000000E0: D54B000C 007E021C
	v_fmac_f32_e32 v15, s17, v2                                // 0000000000E8: 561E0411
	v_fmac_f32_e32 v14, s21, v2                                // 0000000000EC: 561C0415
	v_fmac_f32_e32 v13, s25, v2                                // 0000000000F0: 561A0419
	v_fmac_f32_e32 v12, s29, v2                                // 0000000000F4: 5618041D
	v_fmac_f32_e32 v15, s18, v3                                // 0000000000F8: 561E0612
	v_fmac_f32_e32 v14, s22, v3                                // 0000000000FC: 561C0616
	v_fmac_f32_e32 v13, s26, v3                                // 000000000100: 561A061A
	v_fmac_f32_e32 v12, s30, v3                                // 000000000104: 5618061E
	s_cbranch_scc1 _L2                                         // 000000000108: BF85000B
	s_cmp_gt_i32 s1, 7                                         // 00000000010C: BF028701
	s_cbranch_scc0 _L3                                         // 000000000110: BF84000B
	s_cmp_gt_i32 s1, 8                                         // 000000000114: BF028801
	s_cbranch_scc0 _L4                                         // 000000000118: BF84000B
	s_cmp_gt_i32 s1, 9                                         // 00000000011C: BF028901
	s_cbranch_scc0 _L5                                         // 000000000120: BF84000B
	s_cmp_eq_u32 s1, 10                                        // 000000000124: BF068A01
	s_cbranch_scc0 _L6                                         // 000000000128: BF84000B
	s_buffer_load_dwordx4 s[16:19], s[4:7], 0x50               // 00000000012C: F4280402 FA000050
	s_branch _L7                                               // 000000000134: BF820009
_L2:
	s_cbranch_execnz _L8                                       // 000000000138: BF89006A
	s_branch _L9                                               // 00000000013C: BF820098
_L3:
	s_mov_b32 s9, -1                                           // 000000000140: BE8903C1
	s_branch _L10                                              // 000000000144: BF820020
_L4:
	s_cbranch_execnz _L11                                      // 000000000148: BF890014
	s_branch _L12                                              // 00000000014C: BF82001D
_L5:
	s_mov_b32 s9, -1                                           // 000000000150: BE8903C1
	s_branch _L13                                              // 000000000154: BF820002
_L6:
	s_mov_b32 s3, -1                                           // 000000000158: BE8303C1
_L7:
	s_mov_b32 s9, 0                                            // 00000000015C: BE890380
_L13:
	s_waitcnt lgkmcnt(0)                                       // 000000000160: BF8CC07F
	v_mov_b32_e32 v9, s16                                      // 000000000164: 7E120210
	s_and_b32 vcc_lo, exec_lo, s9                              // 000000000168: 876A097E
	s_cbranch_vccz _L14                                        // 00000000016C: BF86000A
	s_load_dwordx4 s[16:19], s[10:11], 0x60                    // 000000000170: F4080405 FA000060
	s_waitcnt lgkmcnt(0)                                       // 000000000178: BF8CC07F
	tbuffer_load_format_x v9, v16, s[16:19], 0 format:[BUF_FMT_32_SINT] idxen// 00000000017C: E8A82000 80040910
	s_mov_b32 s18, 0                                           // 000000000184: BE920380
	s_mov_b32 s19, 1.0                                         // 000000000188: BE9303F2
	s_mov_b32 s17, 0                                           // 00000000018C: BE910380
	s_waitcnt vmcnt(0)                                         // 000000000190: BF8C3F70
	v_cvt_f32_i32_e32 v9, v9                                   // 000000000194: 7E120B09
_L14:
	s_branch _L12                                              // 000000000198: BF82000A
_L11:
	s_load_dwordx4 s[16:19], s[10:11], 0x50                    // 00000000019C: F4080405 FA000050
	s_waitcnt lgkmcnt(0)                                       // 0000000001A4: BF8CC07F
	tbuffer_load_format_x v9, v16, s[16:19], 0 format:[BUF_FMT_32_SINT] idxen// 0000000001A8: E8A82000 80040910
	s_mov_b32 s18, 0                                           // 0000000001B0: BE920380
	s_mov_b32 s19, 1.0                                         // 0000000001B4: BE9303F2
	s_mov_b32 s17, 0                                           // 0000000001B8: BE910380
	s_waitcnt vmcnt(0)                                         // 0000000001BC: BF8C3F70
	v_cvt_f32_i32_e32 v9, v9                                   // 0000000001C0: 7E120B09
_L12:
	s_mov_b32 s9, 0                                            // 0000000001C4: BE890380
_L10:
	v_mov_b32_e32 v11, s18                                     // 0000000001C8: 7E160212
	v_mov_b32_e32 v10, s17                                     // 0000000001CC: 7E140211
	s_and_b32 vcc_lo, exec_lo, s9                              // 0000000001D0: 876A097E
	s_cbranch_vccz _L15                                        // 0000000001D4: BF860042
	s_cmp_lt_i32 s1, 6                                         // 0000000001D8: BF048601
	s_cbranch_scc1 _L16                                        // 0000000001DC: BF850009
	s_cmp_gt_i32 s1, 6                                         // 0000000001E0: BF028601
	s_cbranch_scc0 _L17                                        // 0000000001E4: BF840008
	s_load_dwordx4 s[16:19], s[10:11], 0x40                    // 0000000001E8: F4080405 FA000040
	s_mov_b32 s9, 0                                            // 0000000001F0: BE890380
	s_waitcnt lgkmcnt(0)                                       // 0000000001F4: BF8CC07F
	tbuffer_load_format_xy v[9:10], v16, s[16:19], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 0000000001F8: EA012000 80040910
	s_branch _L18                                              // 000000000200: BF820002
_L16:
	s_branch _L19                                              // 000000000204: BF82001F
_L17:
	s_mov_b32 s9, -1                                           // 000000000208: BE8903C1
_L18:
	v_mov_b32_e32 v11, 0                                       // 00000000020C: 7E160280
	s_andn2_b32 vcc_lo, exec_lo, s9                            // 000000000210: 8A6A097E
	s_cbranch_vccnz _L20                                       // 000000000214: BF87001A
	s_load_dwordx4 s[16:19], s[10:11], 0x30                    // 000000000218: F4080405 FA000030
	s_buffer_load_dword s9, s[4:7], null                       // 000000000220: F4200242 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000228: BF8CC07F
	tbuffer_load_format_xyz v[9:11], v16, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000022C: EA522000 80040910
	s_cmp_eq_u32 s9, 0                                         // 000000000234: BF068009
	s_cselect_b32 vcc_lo, -1, 0                                // 000000000238: 856A80C1
	s_waitcnt vmcnt(0)                                         // 00000000023C: BF8C3F70
	v_mul_f32_e32 v17, v10, v10                                // 000000000240: 1022150A
	v_fmac_f32_e32 v17, v9, v9                                 // 000000000244: 56221309
	v_fmac_f32_e32 v17, v11, v11                               // 000000000248: 5622170B
	v_rsq_f32_e32 v17, v17                                     // 00000000024C: 7E225D11
	v_mul_legacy_f32_e32 v9, v9, v17                           // 000000000250: 0E122309
	v_mul_legacy_f32_e32 v10, v10, v17                         // 000000000254: 0E14230A
	v_mul_legacy_f32_e32 v11, v11, v17                         // 000000000258: 0E16230B
	v_fma_f32 v17, v9, 0.5, 0.5                                // 00000000025C: D54B0011 03C1E109
	v_fma_f32 v18, v10, 0.5, 0.5                               // 000000000264: D54B0012 03C1E10A
	v_fma_f32 v19, v11, 0.5, 0.5                               // 00000000026C: D54B0013 03C1E10B
	v_cndmask_b32_e32 v9, v17, v9, vcc_lo                      // 000000000274: 02121311
	v_cndmask_b32_e32 v10, v18, v10, vcc_lo                    // 000000000278: 02141512
	v_cndmask_b32_e32 v11, v19, v11, vcc_lo                    // 00000000027C: 02161713
_L20:
	s_cbranch_execnz _L21                                      // 000000000280: BF890016
_L19:
	s_waitcnt vmcnt(0)                                         // 000000000284: BF8C3F70
	v_mul_f32_e32 v9, v5, v5                                   // 000000000288: 10120B05
	s_buffer_load_dword s9, s[4:7], null                       // 00000000028C: F4200242 FA000000
	v_fmac_f32_e32 v9, v4, v4                                  // 000000000294: 56120904
	v_fmac_f32_e32 v9, v6, v6                                  // 000000000298: 56120D06
	v_rsq_f32_e32 v9, v9                                       // 00000000029C: 7E125D09
	s_waitcnt lgkmcnt(0)                                       // 0000000002A0: BF8CC07F
	s_cmp_eq_u32 s9, 0                                         // 0000000002A4: BF068009
	s_cselect_b32 vcc_lo, -1, 0                                // 0000000002A8: 856A80C1
	v_mul_legacy_f32_e32 v10, v4, v9                           // 0000000002AC: 0E141304
	v_mul_legacy_f32_e32 v11, v5, v9                           // 0000000002B0: 0E161305
	v_mul_legacy_f32_e32 v17, v6, v9                           // 0000000002B4: 0E221306
	v_fma_f32 v9, v10, 0.5, 0.5                                // 0000000002B8: D54B0009 03C1E10A
	v_fma_f32 v18, v11, 0.5, 0.5                               // 0000000002C0: D54B0012 03C1E10B
	v_fma_f32 v19, v17, 0.5, 0.5                               // 0000000002C8: D54B0013 03C1E111
	v_cndmask_b32_e32 v9, v9, v10, vcc_lo                      // 0000000002D0: 02121509
	v_cndmask_b32_e32 v10, v18, v11, vcc_lo                    // 0000000002D4: 02141712
	v_cndmask_b32_e32 v11, v19, v17, vcc_lo                    // 0000000002D8: 02162313
_L21:
	s_mov_b32 s19, 1.0                                         // 0000000002DC: BE9303F2
_L15:
	s_branch _L9                                               // 0000000002E0: BF82002F
_L8:
	s_cmp_gt_i32 s1, 1                                         // 0000000002E4: BF028101
	s_cbranch_scc0 _L22                                        // 0000000002E8: BF84000B
	s_cmp_lt_i32 s1, 3                                         // 0000000002EC: BF048301
	s_cbranch_scc1 _L23                                        // 0000000002F0: BF85000A
	s_cmp_gt_i32 s1, 3                                         // 0000000002F4: BF028301
	s_cbranch_scc0 _L24                                        // 0000000002F8: BF840009
	s_load_dwordx4 s[16:19], s[10:11], 0x10                    // 0000000002FC: F4080405 FA000010
	s_mov_b32 s9, 0                                            // 000000000304: BE890380
	s_waitcnt lgkmcnt(0)                                       // 000000000308: BF8CC07F
	tbuffer_load_format_xyz v[9:11], v16, s[16:19], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 00000000030C: EA522000 80040910
	s_branch _L25                                              // 000000000314: BF820003
_L22:
	s_branch _L26                                              // 000000000318: BF82000F
_L23:
	s_branch _L27                                              // 00000000031C: BF820009
_L24:
	s_mov_b32 s9, -1                                           // 000000000320: BE8903C1
_L25:
	s_andn2_b32 vcc_lo, exec_lo, s9                            // 000000000324: 8A6A097E
	s_cbranch_vccnz _L28                                       // 000000000328: BF870005
	v_add_nc_u32_e32 v8, s12, v8                               // 00000000032C: 4A10100C
	s_waitcnt vmcnt(0)                                         // 000000000330: BF8C3F70
	v_mov_b32_e32 v11, 0                                       // 000000000334: 7E160280
	v_mov_b32_e32 v10, 0                                       // 000000000338: 7E140280
	v_cvt_f32_u32_e32 v9, v8                                   // 00000000033C: 7E120D08
_L28:
	s_cbranch_execnz _L29                                      // 000000000340: BF890004
_L27:
	s_waitcnt vmcnt(0)                                         // 000000000344: BF8C3F70
	v_cvt_f32_u32_e32 v9, v16                                  // 000000000348: 7E120D10
	v_mov_b32_e32 v11, 0                                       // 00000000034C: 7E160280
	v_mov_b32_e32 v10, 0                                       // 000000000350: 7E140280
_L29:
	s_cbranch_execnz _L30                                      // 000000000354: BF890011
_L26:
	s_cmp_gt_i32 s1, 0                                         // 000000000358: BF028001
	s_mov_b32 s9, 0                                            // 00000000035C: BE890380
	s_cbranch_scc0 _L31                                        // 000000000360: BF840008
	v_rcp_f32_e32 v8, v12                                      // 000000000364: 7E10550C
	s_waitcnt vmcnt(0)                                         // 000000000368: BF8C3F70
	v_mul_f32_e32 v9, v15, v8                                  // 00000000036C: 1012110F
	v_mul_f32_e32 v10, v14, v8                                 // 000000000370: 1014110E
	v_mul_f32_e32 v11, v13, v8                                 // 000000000374: 1016110D
	s_andn2_b32 vcc_lo, exec_lo, s9                            // 000000000378: 8A6A097E
	s_cbranch_vccz _L31                                        // 00000000037C: BF860001
	s_branch _L30                                              // 000000000380: BF820006
_L31:
	s_waitcnt vmcnt(0)                                         // 000000000384: BF8C3F70
	v_mov_b32_e32 v11, v3                                      // 000000000388: 7E160303
	v_mov_b32_e32 v10, v2                                      // 00000000038C: 7E140302
	v_mov_b32_e32 v9, v1                                       // 000000000390: 7E120301
	s_cmp_lg_u32 s1, 0                                         // 000000000394: BF078001
	s_cselect_b32 s3, -1, 0                                    // 000000000398: 850380C1
_L30:
	s_mov_b32 s19, 1.0                                         // 00000000039C: BE9303F2
_L9:
	s_and_b32 vcc_lo, exec_lo, s3                              // 0000000003A0: 876A037E
	s_cbranch_vccz _L1                                         // 0000000003A4: BF860006
	s_buffer_load_dwordx4 s[16:19], s[4:7], 0x50               // 0000000003A8: F4280402 FA000050
	s_waitcnt vmcnt(0) lgkmcnt(0)                              // 0000000003B0: BF8C0070
	v_mov_b32_e32 v9, s16                                      // 0000000003B4: 7E120210
	v_mov_b32_e32 v10, s17                                     // 0000000003B8: 7E140211
	v_mov_b32_e32 v11, s18                                     // 0000000003BC: 7E160212
_L1:
	s_or_b32 exec_lo, exec_lo, s2                              // 0000000003C0: 887E027E
	s_mov_b32 s1, exec_lo                                      // 0000000003C4: BE81037E
	v_cmpx_gt_u32_e64 s8, v7                                   // 0000000003C8: D4D4007E 00020E08
	s_cbranch_execz _L32                                       // 0000000003D0: BF880002
	exp prim v0, off, off, off done                            // 0000000003D4: F8000941 00000000
_L32:
	s_waitcnt expcnt(0)                                        // 0000000003DC: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000003E0: 887E017E
	s_and_saveexec_b32 s1, s0                                  // 0000000003E4: BE813C00
	s_cbranch_execz _L33                                       // 0000000003E8: BF88000A
	exp pos0 v15, v14, v13, v12 done                           // 0000000003EC: F80008CF 0C0D0E0F
	v_mov_b32_e32 v0, s19                                      // 0000000003F4: 7E000213
	s_waitcnt vmcnt(0)                                         // 0000000003F8: BF8C3F70
	exp param1 v4, v5, v6, off                                 // 0000000003FC: F8000217 00060504
	exp param2 v1, v2, v3, off                                 // 000000000404: F8000227 00030201
	exp param0 v9, v10, v11, v0                                // 00000000040C: F800020F 000B0A09
_L33:
	s_endpgm                                                   // 000000000414: BF810000
