;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; NORMAL                   0   xyz         1     NONE   float   xyz 
; TANGENT                  0   xyz         2     NONE   float   xyz 
; BITANGENT                0   xyz         3     NONE   float   xyz 
; TEXCOORD                 0   xy          4     NONE   float   xy  
; COLOR                    0   xyzw        5     NONE   float   xyzw
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyz         1     NONE   float   xyz 
; TEXCOORD                 6      w        1     NONE   float      w
; TEXCOORD                 1   xyz         2     NONE   float   xyz 
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
; TEXCOORD                 4   xy          5     NONE   float   xy  
; TEXCOORD                 5   xyzw        6     NONE   float   xyzw
;
; shader hash: e52cda2da752e1ed45b47c5520d91dad
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 6
; SigOutputElements: 8
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 6
; SigOutputVectors[0]: 7
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; NORMAL                   0                              
; TANGENT                  0                              
; BITANGENT                0                              
; TEXCOORD                 0                              
; COLOR                    0                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
; TEXCOORD                 4                 linear       
; TEXCOORD                 5                 linear       
; TEXCOORD                 6                 linear       
;
; Buffer Definitions:
;
; cbuffer PerFrame
; {
;
;   struct hostlayout.PerFrame
;   {
;
;       column_major float4x4 WorldMatrix;            ; Offset:    0
;       column_major float4x4 ViewMatrix;             ; Offset:   64
;       column_major float4x4 ProjectionMatrix;       ; Offset:  128
;       column_major float4x4 WorldViewProjectionMatrix;; Offset:  192
;       column_major float4x4 NormalMatrix;           ; Offset:  256
;       float Time;                                   ; Offset:  320
;   
;   } PerFrame;                                       ; Offset:    0 Size:   324
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; PerFrame                          cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 24, outputs: 28
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2 }
;   output 1 depends on inputs: { 0, 1, 2 }
;   output 2 depends on inputs: { 0, 1, 2 }
;   output 3 depends on inputs: { 0, 1, 2 }
;   output 4 depends on inputs: { 0, 1, 2 }
;   output 5 depends on inputs: { 0, 1, 2 }
;   output 6 depends on inputs: { 0, 1, 2 }
;   output 7 depends on inputs: { 0, 1, 2 }
;   output 8 depends on inputs: { 4, 5, 6 }
;   output 9 depends on inputs: { 4, 5, 6 }
;   output 10 depends on inputs: { 4, 5, 6 }
;   output 12 depends on inputs: { 8, 9, 10 }
;   output 13 depends on inputs: { 8, 9, 10 }
;   output 14 depends on inputs: { 8, 9, 10 }
;   output 16 depends on inputs: { 12, 13, 14 }
;   output 17 depends on inputs: { 12, 13, 14 }
;   output 18 depends on inputs: { 12, 13, 14 }
;   output 20 depends on inputs: { 16 }
;   output 21 depends on inputs: { 17 }
;   output 24 depends on inputs: { 20 }
;   output 25 depends on inputs: { 21 }
;   output 26 depends on inputs: { 22 }
;   output 27 depends on inputs: { 23 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%hostlayout.PerFrame = type { [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], [4 x <4 x float>], float }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 3, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %12 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %13 = call float @dx.op.loadInput.f32(i32 4, i32 2, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %14 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %15 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %16 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %17 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %18 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %19 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %20 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %21 = extractvalue %dx.types.CBufRet.f32 %20, 0
  %22 = extractvalue %dx.types.CBufRet.f32 %20, 1
  %23 = extractvalue %dx.types.CBufRet.f32 %20, 2
  %24 = extractvalue %dx.types.CBufRet.f32 %20, 3
  %25 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %26 = extractvalue %dx.types.CBufRet.f32 %25, 0
  %27 = extractvalue %dx.types.CBufRet.f32 %25, 1
  %28 = extractvalue %dx.types.CBufRet.f32 %25, 2
  %29 = extractvalue %dx.types.CBufRet.f32 %25, 3
  %30 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %31 = extractvalue %dx.types.CBufRet.f32 %30, 0
  %32 = extractvalue %dx.types.CBufRet.f32 %30, 1
  %33 = extractvalue %dx.types.CBufRet.f32 %30, 2
  %34 = extractvalue %dx.types.CBufRet.f32 %30, 3
  %35 = fmul fast float %21, %17
  %36 = call float @dx.op.tertiary.f32(i32 46, float %18, float %22, float %35)  ; FMad(a,b,c)
  %37 = call float @dx.op.tertiary.f32(i32 46, float %19, float %23, float %36)  ; FMad(a,b,c)
  %38 = fadd fast float %37, %24
  %39 = fmul fast float %26, %17
  %40 = call float @dx.op.tertiary.f32(i32 46, float %18, float %27, float %39)  ; FMad(a,b,c)
  %41 = call float @dx.op.tertiary.f32(i32 46, float %19, float %28, float %40)  ; FMad(a,b,c)
  %42 = fadd fast float %41, %29
  %43 = fmul fast float %31, %17
  %44 = call float @dx.op.tertiary.f32(i32 46, float %18, float %32, float %43)  ; FMad(a,b,c)
  %45 = call float @dx.op.tertiary.f32(i32 46, float %19, float %33, float %44)  ; FMad(a,b,c)
  %46 = fadd fast float %45, %34
  %47 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 12)  ; CBufferLoadLegacy(handle,regIndex)
  %48 = extractvalue %dx.types.CBufRet.f32 %47, 0
  %49 = extractvalue %dx.types.CBufRet.f32 %47, 1
  %50 = extractvalue %dx.types.CBufRet.f32 %47, 2
  %51 = extractvalue %dx.types.CBufRet.f32 %47, 3
  %52 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 13)  ; CBufferLoadLegacy(handle,regIndex)
  %53 = extractvalue %dx.types.CBufRet.f32 %52, 0
  %54 = extractvalue %dx.types.CBufRet.f32 %52, 1
  %55 = extractvalue %dx.types.CBufRet.f32 %52, 2
  %56 = extractvalue %dx.types.CBufRet.f32 %52, 3
  %57 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 14)  ; CBufferLoadLegacy(handle,regIndex)
  %58 = extractvalue %dx.types.CBufRet.f32 %57, 0
  %59 = extractvalue %dx.types.CBufRet.f32 %57, 1
  %60 = extractvalue %dx.types.CBufRet.f32 %57, 2
  %61 = extractvalue %dx.types.CBufRet.f32 %57, 3
  %62 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 15)  ; CBufferLoadLegacy(handle,regIndex)
  %63 = extractvalue %dx.types.CBufRet.f32 %62, 0
  %64 = extractvalue %dx.types.CBufRet.f32 %62, 1
  %65 = extractvalue %dx.types.CBufRet.f32 %62, 2
  %66 = extractvalue %dx.types.CBufRet.f32 %62, 3
  %67 = fmul fast float %48, %17
  %68 = call float @dx.op.tertiary.f32(i32 46, float %18, float %49, float %67)  ; FMad(a,b,c)
  %69 = call float @dx.op.tertiary.f32(i32 46, float %19, float %50, float %68)  ; FMad(a,b,c)
  %70 = fadd fast float %69, %51
  %71 = fmul fast float %53, %17
  %72 = call float @dx.op.tertiary.f32(i32 46, float %18, float %54, float %71)  ; FMad(a,b,c)
  %73 = call float @dx.op.tertiary.f32(i32 46, float %19, float %55, float %72)  ; FMad(a,b,c)
  %74 = fadd fast float %73, %56
  %75 = fmul fast float %58, %17
  %76 = call float @dx.op.tertiary.f32(i32 46, float %18, float %59, float %75)  ; FMad(a,b,c)
  %77 = call float @dx.op.tertiary.f32(i32 46, float %19, float %60, float %76)  ; FMad(a,b,c)
  %78 = fadd fast float %77, %61
  %79 = fmul fast float %63, %17
  %80 = call float @dx.op.tertiary.f32(i32 46, float %18, float %64, float %79)  ; FMad(a,b,c)
  %81 = call float @dx.op.tertiary.f32(i32 46, float %19, float %65, float %80)  ; FMad(a,b,c)
  %82 = fadd fast float %81, %66
  %83 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 16)  ; CBufferLoadLegacy(handle,regIndex)
  %84 = extractvalue %dx.types.CBufRet.f32 %83, 0
  %85 = extractvalue %dx.types.CBufRet.f32 %83, 1
  %86 = extractvalue %dx.types.CBufRet.f32 %83, 2
  %87 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 17)  ; CBufferLoadLegacy(handle,regIndex)
  %88 = extractvalue %dx.types.CBufRet.f32 %87, 0
  %89 = extractvalue %dx.types.CBufRet.f32 %87, 1
  %90 = extractvalue %dx.types.CBufRet.f32 %87, 2
  %91 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 18)  ; CBufferLoadLegacy(handle,regIndex)
  %92 = extractvalue %dx.types.CBufRet.f32 %91, 0
  %93 = extractvalue %dx.types.CBufRet.f32 %91, 1
  %94 = extractvalue %dx.types.CBufRet.f32 %91, 2
  %95 = fmul fast float %84, %14
  %96 = call float @dx.op.tertiary.f32(i32 46, float %15, float %85, float %95)  ; FMad(a,b,c)
  %97 = call float @dx.op.tertiary.f32(i32 46, float %16, float %86, float %96)  ; FMad(a,b,c)
  %98 = fmul fast float %88, %14
  %99 = call float @dx.op.tertiary.f32(i32 46, float %15, float %89, float %98)  ; FMad(a,b,c)
  %100 = call float @dx.op.tertiary.f32(i32 46, float %16, float %90, float %99)  ; FMad(a,b,c)
  %101 = fmul fast float %92, %14
  %102 = call float @dx.op.tertiary.f32(i32 46, float %15, float %93, float %101)  ; FMad(a,b,c)
  %103 = call float @dx.op.tertiary.f32(i32 46, float %16, float %94, float %102)  ; FMad(a,b,c)
  %104 = call float @dx.op.dot3.f32(i32 55, float %97, float %100, float %103, float %97, float %100, float %103)  ; Dot3(ax,ay,az,bx,by,bz)
  %105 = call float @dx.op.unary.f32(i32 25, float %104)  ; Rsqrt(value)
  %106 = fmul fast float %105, %97
  %107 = fmul fast float %105, %100
  %108 = fmul fast float %105, %103
  %109 = fmul fast float %84, %11
  %110 = call float @dx.op.tertiary.f32(i32 46, float %12, float %85, float %109)  ; FMad(a,b,c)
  %111 = call float @dx.op.tertiary.f32(i32 46, float %13, float %86, float %110)  ; FMad(a,b,c)
  %112 = fmul fast float %88, %11
  %113 = call float @dx.op.tertiary.f32(i32 46, float %12, float %89, float %112)  ; FMad(a,b,c)
  %114 = call float @dx.op.tertiary.f32(i32 46, float %13, float %90, float %113)  ; FMad(a,b,c)
  %115 = fmul fast float %92, %11
  %116 = call float @dx.op.tertiary.f32(i32 46, float %12, float %93, float %115)  ; FMad(a,b,c)
  %117 = call float @dx.op.tertiary.f32(i32 46, float %13, float %94, float %116)  ; FMad(a,b,c)
  %118 = call float @dx.op.dot3.f32(i32 55, float %111, float %114, float %117, float %111, float %114, float %117)  ; Dot3(ax,ay,az,bx,by,bz)
  %119 = call float @dx.op.unary.f32(i32 25, float %118)  ; Rsqrt(value)
  %120 = fmul fast float %119, %111
  %121 = fmul fast float %119, %114
  %122 = fmul fast float %119, %117
  %123 = fmul fast float %84, %8
  %124 = call float @dx.op.tertiary.f32(i32 46, float %9, float %85, float %123)  ; FMad(a,b,c)
  %125 = call float @dx.op.tertiary.f32(i32 46, float %10, float %86, float %124)  ; FMad(a,b,c)
  %126 = fmul fast float %88, %8
  %127 = call float @dx.op.tertiary.f32(i32 46, float %9, float %89, float %126)  ; FMad(a,b,c)
  %128 = call float @dx.op.tertiary.f32(i32 46, float %10, float %90, float %127)  ; FMad(a,b,c)
  %129 = fmul fast float %92, %8
  %130 = call float @dx.op.tertiary.f32(i32 46, float %9, float %93, float %129)  ; FMad(a,b,c)
  %131 = call float @dx.op.tertiary.f32(i32 46, float %10, float %94, float %130)  ; FMad(a,b,c)
  %132 = call float @dx.op.dot3.f32(i32 55, float %125, float %128, float %131, float %125, float %128, float %131)  ; Dot3(ax,ay,az,bx,by,bz)
  %133 = call float @dx.op.unary.f32(i32 25, float %132)  ; Rsqrt(value)
  %134 = fmul fast float %133, %125
  %135 = fmul fast float %133, %128
  %136 = fmul fast float %133, %131
  %137 = fdiv fast float %78, %82
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %70)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %74)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %78)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %82)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %38)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %42)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %46)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %106)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %107)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %108)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %120)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %121)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %122)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 0, float %134)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 1, float %135)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 4, i32 0, i8 2, float %136)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 0, float %6)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 5, i32 0, i8 1, float %7)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 0, float %2)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 1, float %3)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 2, float %4)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 6, i32 0, i8 3, float %5)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 7, i32 0, i8 0, float %137)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %hostlayout.PerFrame* undef, !"", i32 0, i32 0, i32 1, i32 324, null}
!7 = !{[26 x i32] [i32 24, i32 28, i32 255, i32 255, i32 255, i32 0, i32 1792, i32 1792, i32 1792, i32 0, i32 28672, i32 28672, i32 28672, i32 0, i32 458752, i32 458752, i32 458752, i32 0, i32 1048576, i32 2097152, i32 0, i32 0, i32 16777216, i32 33554432, i32 67108864, i32 134217728]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !21, null}
!10 = !{!11, !14, !15, !16, !17, !19}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"NORMAL", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 1, i8 0, !13}
!15 = !{i32 2, !"TANGENT", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 2, i8 0, !13}
!16 = !{i32 3, !"BITANGENT", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 3, i8 0, !13}
!17 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 4, i8 0, !18}
!18 = !{i32 3, i32 3}
!19 = !{i32 5, !"COLOR", i8 9, i8 0, !12, i8 0, i32 1, i8 4, i32 5, i8 0, !20}
!20 = !{i32 3, i32 15}
!21 = !{!22, !23, !24, !26, !28, !30, !32, !34}
!22 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !20}
!23 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 1, i8 0, !13}
!24 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !25, i8 2, i32 1, i8 3, i32 2, i8 0, !13}
!25 = !{i32 1}
!26 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !27, i8 2, i32 1, i8 3, i32 3, i8 0, !13}
!27 = !{i32 2}
!28 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !29, i8 2, i32 1, i8 3, i32 4, i8 0, !13}
!29 = !{i32 3}
!30 = !{i32 5, !"TEXCOORD", i8 9, i8 0, !31, i8 2, i32 1, i8 2, i32 5, i8 0, !18}
!31 = !{i32 4}
!32 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !33, i8 2, i32 1, i8 4, i32 6, i8 0, !20}
!33 = !{i32 5}
!34 = !{i32 7, !"TEXCOORD", i8 9, i8 0, !35, i8 2, i32 1, i8 1, i32 1, i8 3, !36}
!35 = !{i32 6}
!36 = !{i32 3, i32 1}
