_amdgpu_ps_main:
	s_mov_b64 s[44:45], exec                                   // 000000000000: BEAC047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s16, s1                                          // 000000000008: BE900301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s17, s1                                          // 000000000014: BE910301
	v_interp_p1_f32_e32 v7, v0, attr3.x                        // 000000000018: C81C0C00
	s_load_dwordx4 s[36:39], s[16:17], null                    // 00000000001C: F4080908 FA000000
	v_interp_p1_f32_e32 v8, v0, attr3.y                        // 000000000024: C8200D00
	v_interp_p1_f32_e32 v6, v0, attr2.y                        // 000000000028: C8180900
	v_interp_p1_f32_e32 v5, v0, attr2.x                        // 00000000002C: C8140800
	v_interp_p2_f32_e32 v7, v1, attr3.x                        // 000000000030: C81D0C01
	v_interp_p1_f32_e32 v9, v0, attr2.z                        // 000000000034: C8240A00
	v_interp_p2_f32_e32 v8, v1, attr3.y                        // 000000000038: C8210D01
	v_interp_p2_f32_e32 v6, v1, attr2.y                        // 00000000003C: C8190901
	v_interp_p1_f32_e32 v11, v0, attr1.y                       // 000000000040: C82C0500
	v_interp_p2_f32_e32 v5, v1, attr2.x                        // 000000000044: C8150801
	v_interp_p1_f32_e32 v10, v0, attr1.x                       // 000000000048: C8280400
	v_interp_p1_f32_e32 v14, v0, attr0.y                       // 00000000004C: C8380100
	v_mul_f32_e32 v16, v6, v6                                  // 000000000050: 10200D06
	v_interp_p2_f32_e32 v9, v1, attr2.z                        // 000000000054: C8250A01
	v_interp_p2_f32_e32 v11, v1, attr1.y                       // 000000000058: C82D0501
	v_interp_p1_f32_e32 v12, v0, attr1.z                       // 00000000005C: C8300600
	v_interp_p1_f32_e32 v13, v0, attr0.x                       // 000000000060: C8340000
	v_fmac_f32_e32 v16, v5, v5                                 // 000000000064: 56200B05
	v_interp_p2_f32_e32 v10, v1, attr1.x                       // 000000000068: C8290401
	s_waitcnt lgkmcnt(0)                                       // 00000000006C: BF8CC07F
	s_buffer_load_dwordx2 s[34:35], s[36:39], 0x150            // 000000000070: F4240892 FA000150
	s_clause 0x1                                               // 000000000078: BFA10001
	s_load_dwordx4 s[40:43], s[16:17], 0x20                    // 00000000007C: F4080A08 FA000020
	s_load_dwordx16 s[0:15], s[16:17], 0x30                    // 000000000084: F4100008 FA000030
	v_interp_p2_f32_e32 v14, v1, attr0.y                       // 00000000008C: C8390101
	v_mul_f32_e32 v18, v11, v11                                // 000000000090: 1024170B
	v_fmac_f32_e32 v16, v9, v9                                 // 000000000094: 56201309
	v_interp_p1_f32_e32 v15, v0, attr0.z                       // 000000000098: C83C0200
	v_interp_p2_f32_e32 v12, v1, attr1.z                       // 00000000009C: C8310601
	v_interp_p2_f32_e32 v13, v1, attr0.x                       // 0000000000A0: C8350001
	v_mul_f32_e32 v19, v14, v14                                // 0000000000A4: 10261D0E
	v_fmac_f32_e32 v18, v10, v10                               // 0000000000A8: 5624150A
	v_rsq_f32_e32 v21, v16                                     // 0000000000AC: 7E2A5D10
	v_interp_p2_f32_e32 v15, v1, attr0.z                       // 0000000000B0: C83D0201
	v_cmp_neq_f32_e32 vcc_lo, 0, v16                           // 0000000000B4: 7C1A2080
	v_fmac_f32_e32 v19, v13, v13                               // 0000000000B8: 56261B0D
	v_fmac_f32_e32 v18, v12, v12                               // 0000000000BC: 5624190C
	v_interp_p1_f32_e32 v23, v0, attr4.y                       // 0000000000C0: C85C1100
	v_interp_p1_f32_e32 v20, v0, attr4.x                       // 0000000000C4: C8501000
	v_interp_p1_f32_e32 v25, v0, attr4.z                       // 0000000000C8: C8641200
	v_fmac_f32_e32 v19, v15, v15                               // 0000000000CC: 56261F0F
	s_waitcnt lgkmcnt(0)                                       // 0000000000D0: BF8CC07F
	v_mul_f32_e64 v2, 0x3da2f983, s34                          // 0000000000D4: D5080002 000044FF 3DA2F983
	v_rsq_f32_e32 v24, v18                                     // 0000000000E0: 7E305D12
	v_cndmask_b32_e32 v16, 0, v21, vcc_lo                      // 0000000000E4: 02202A80
	v_interp_p2_f32_e32 v23, v1, attr4.y                       // 0000000000E8: C85D1101
	v_rsq_f32_e32 v26, v19                                     // 0000000000EC: 7E345D13
	v_sin_f32_e32 v2, v2                                       // 0000000000F0: 7E046B02
	v_interp_p2_f32_e32 v20, v1, attr4.x                       // 0000000000F4: C8511001
	v_mul_f32_e32 v6, v16, v6                                  // 0000000000F8: 100C0D10
	v_mul_f32_e32 v5, v16, v5                                  // 0000000000FC: 100A0B10
	v_mul_f32_e32 v9, v16, v9                                  // 000000000100: 10121310
	v_interp_p2_f32_e32 v25, v1, attr4.z                       // 000000000104: C8651201
	v_fmac_f32_e32 v7, 0x3c23d70a, v2                          // 000000000108: 560E04FF 3C23D70A
	v_fmac_f32_e32 v8, 0x3c23d70a, v2                          // 000000000110: 561004FF 3C23D70A
	image_sample v[2:4], v[7:8], s[0:7], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000118: F0800708 01400207
	s_clause 0x1                                               // 000000000120: BFA10001
	s_load_dwordx8 s[0:7], s[16:17], null                      // 000000000124: F40C0008 FA000000
	s_load_dwordx16 s[16:31], s[16:17], 0x70                   // 00000000012C: F4100408 FA000070
	s_waitcnt vmcnt(0)                                         // 000000000134: BF8C3F70
	v_fma_f32 v3, v3, 2.0, -1.0                                // 000000000138: D54B0003 03CDE903
	v_fma_f32 v2, v2, 2.0, -1.0                                // 000000000140: D54B0002 03CDE902
	v_fma_f32 v4, v4, 2.0, -1.0                                // 000000000148: D54B0004 03CDE904
	v_mul_f32_e32 v17, v3, v3                                  // 000000000150: 10220703
	v_fmac_f32_e32 v17, v2, v2                                 // 000000000154: 56220502
	v_fmac_f32_e32 v17, v4, v4                                 // 000000000158: 56220904
	v_rsq_f32_e32 v22, v17                                     // 00000000015C: 7E2C5D11
	v_cmp_neq_f32_e32 vcc_lo, 0, v17                           // 000000000160: 7C1A2280
	v_cndmask_b32_e32 v17, 0, v22, vcc_lo                      // 000000000164: 02222C80
	v_cmp_neq_f32_e32 vcc_lo, 0, v18                           // 000000000168: 7C1A2480
	v_mul_f32_e32 v3, v17, v3                                  // 00000000016C: 10060711
	v_cndmask_b32_e32 v18, 0, v24, vcc_lo                      // 000000000170: 02243080
	v_cmp_neq_f32_e32 vcc_lo, 0, v19                           // 000000000174: 7C1A2680
	v_mul_f32_e32 v2, v17, v2                                  // 000000000178: 10040511
	v_mul_f32_e32 v4, v17, v4                                  // 00000000017C: 10080911
	v_mul_f32_e32 v6, v3, v6                                   // 000000000180: 100C0D03
	v_mul_f32_e32 v11, v18, v11                                // 000000000184: 10161712
	v_cndmask_b32_e32 v19, 0, v26, vcc_lo                      // 000000000188: 02263480
	v_mul_f32_e32 v10, v18, v10                                // 00000000018C: 10141512
	v_mul_f32_e32 v5, v3, v5                                   // 000000000190: 100A0B03
	v_mul_f32_e32 v3, v3, v9                                   // 000000000194: 10061303
	v_fmac_f32_e32 v6, v2, v11                                 // 000000000198: 560C1702
	v_mul_f32_e32 v14, v19, v14                                // 00000000019C: 101C1D13
	v_mul_f32_e32 v11, v18, v12                                // 0000000001A0: 10161912
	v_mul_f32_e32 v12, v19, v13                                // 0000000001A4: 10181B13
	v_fmac_f32_e32 v5, v2, v10                                 // 0000000001A8: 560A1502
	v_mul_f32_e32 v9, v23, v23                                 // 0000000001AC: 10122F17
	v_fmac_f32_e32 v6, v4, v14                                 // 0000000001B0: 560C1D04
	v_mul_f32_e32 v10, v19, v15                                // 0000000001B4: 10141F13
	v_fmac_f32_e32 v3, v2, v11                                 // 0000000001B8: 56061702
	v_fmac_f32_e32 v5, v4, v12                                 // 0000000001BC: 560A1904
	v_fmac_f32_e32 v9, v20, v20                                // 0000000001C0: 56122914
	v_mul_f32_e32 v2, v6, v6                                   // 0000000001C4: 10040D06
	v_interp_p1_f32_e32 v13, v0, attr5.y                       // 0000000001C8: C8341500
	v_fmac_f32_e32 v3, v4, v10                                 // 0000000001CC: 56061504
	v_interp_p1_f32_e32 v14, v0, attr5.x                       // 0000000001D0: C8381400
	v_fmac_f32_e32 v9, v25, v25                                // 0000000001D4: 56123319
	v_fmac_f32_e32 v2, v5, v5                                  // 0000000001D8: 56040B05
	v_interp_p1_f32_e32 v15, v0, attr5.z                       // 0000000001DC: C83C1600
	v_interp_p2_f32_e32 v13, v1, attr5.y                       // 0000000001E0: C8351501
	v_interp_p2_f32_e32 v14, v1, attr5.x                       // 0000000001E4: C8391401
	v_rsq_f32_e32 v0, v9                                       // 0000000001E8: 7E005D09
	v_fmac_f32_e32 v2, v3, v3                                  // 0000000001EC: 56040703
	v_cmp_neq_f32_e32 vcc_lo, 0, v9                            // 0000000001F0: 7C1A1280
	v_mul_f32_e32 v10, v13, v13                                // 0000000001F4: 10141B0D
	v_interp_p2_f32_e32 v15, v1, attr5.z                       // 0000000001F8: C83D1601
	v_rsq_f32_e32 v4, v2                                       // 0000000001FC: 7E085D02
	v_fmac_f32_e32 v10, v14, v14                               // 000000000200: 56141D0E
	v_cndmask_b32_e32 v0, 0, v0, vcc_lo                        // 000000000204: 02000080
	v_cmp_neq_f32_e32 vcc_lo, 0, v2                            // 000000000208: 7C1A0480
	v_fmac_f32_e32 v10, v15, v15                               // 00000000020C: 56141F0F
	v_mul_f32_e32 v11, v0, v20                                 // 000000000210: 10162900
	v_cndmask_b32_e32 v1, 0, v4, vcc_lo                        // 000000000214: 02020880
	v_mul_f32_e32 v17, v0, v23                                 // 000000000218: 10222F00
	v_rsq_f32_e32 v2, v10                                      // 00000000021C: 7E045D0A
	v_mul_f32_e32 v12, v0, v25                                 // 000000000220: 10183300
	v_cmp_neq_f32_e32 vcc_lo, 0, v10                           // 000000000224: 7C1A1480
	v_mul_f32_e32 v16, v1, v5                                  // 000000000228: 10200B01
	v_mul_f32_e32 v18, v1, v6                                  // 00000000022C: 10240D01
	v_mul_f32_e32 v19, v1, v3                                  // 000000000230: 10260701
	v_mul_f32_e32 v4, v16, v11                                 // 000000000234: 10081710
	v_cndmask_b32_e32 v20, 0, v2, vcc_lo                       // 000000000238: 02280480
	s_waitcnt lgkmcnt(0)                                       // 00000000023C: BF8CC07F
	image_sample v[0:3], v[7:8], s[0:7], s[40:43] dmask:0xf dim:SQ_RSRC_IMG_2D// 000000000240: F0800F08 01400007
	v_fma_f32 v5, v18, -v17, -v4                               // 000000000248: D54B0005 C4122312
	v_fma_f32 v21, v18, v17, v4                                // 000000000250: D54B0015 04122312
	v_fma_f32 v22, v20, v14, v11                               // 000000000258: D54B0016 042E1D14
	v_fma_f32 v23, v20, v15, v12                               // 000000000260: D54B0017 04321F14
	v_fma_f32 v9, -v19, v12, v5 mul:2                          // 000000000268: D54B0009 2C161913
	image_sample v[4:6], v[7:8], s[8:15], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 000000000270: F0800708 01420407
	v_fmac_f32_e32 v21, v19, v12                               // 000000000278: 562A1913
	v_fmac_f32_e32 v11, v16, v9                                // 00000000027C: 56161310
	v_fma_f32 v10, -v18, v9, -v17                              // 000000000280: D54B000A A4461312
	v_fmac_f32_e32 v12, v19, v9                                // 000000000288: 56181313
	image_sample v[7:9], v[7:8], s[16:23], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_2D// 00000000028C: F0800708 01440707
	v_cubema_f32 v24, -v11, v10, -v12                          // 000000000294: D5470018 A432150B
	v_cubeid_f32 v25, -v11, v10, -v12                          // 00000000029C: D5440019 A432150B
	v_cubesc_f32 v26, -v11, v10, -v12                          // 0000000002A4: D545001A A432150B
	v_cubetc_f32 v10, -v11, v10, -v12                          // 0000000002AC: D546000A A432150B
	v_rcp_f32_e64 v24, |v24|                                   // 0000000002B4: D5AA0118 00000118
	v_rndne_f32_e32 v27, v25                                   // 0000000002BC: 7E364719
	v_fmaak_f32 v25, v24, v26, 0x3fc00000                      // 0000000002C0: 5A323518 3FC00000
	v_fmaak_f32 v26, v24, v10, 0x3fc00000                      // 0000000002C8: 5A341518 3FC00000
	s_and_b64 exec, exec, s[44:45]                             // 0000000002D0: 87FE2C7E
	image_sample v[10:12], v[25:27], s[24:31], s[40:43] dmask:0x7 dim:SQ_RSRC_IMG_CUBE// 0000000002D4: F0800718 01460A19
	v_fma_f32 v17, v20, v13, v17                               // 0000000002DC: D54B0011 04461B14
	s_buffer_load_dwordx8 s[0:7], s[36:39], 0x110              // 0000000002E4: F42C0012 FA000110
	v_mul_f32_e32 v13, v20, v13                                // 0000000002EC: 101A1B14
	s_buffer_load_dwordx4 s[8:11], s[36:39], 0x130             // 0000000002F0: F4280212 FA000130
	s_waitcnt lgkmcnt(0)                                       // 0000000002F8: BF8CC07F
	s_buffer_load_dword s3, s[36:39], 0x12c                    // 0000000002FC: F42000D2 FA00012C
	v_mul_f32_e32 v24, v17, v17                                // 000000000304: 10302311
	v_mul_f32_e32 v14, v20, v14                                // 000000000308: 101C1D14
	v_mul_f32_e32 v13, v18, v13                                // 00000000030C: 101A1B12
	v_mul_f32_e32 v15, v20, v15                                // 000000000310: 101E1F14
	s_waitcnt vmcnt(3)                                         // 000000000314: BF8C3F73
	v_mul_f32_e32 v0, 0x3f4ccccd, v0                           // 000000000318: 100000FF 3F4CCCCD
	v_fmac_f32_e32 v24, v22, v22                               // 000000000320: 56302D16
	v_mul_f32_e32 v1, 0x3f19999a, v1                           // 000000000324: 100202FF 3F19999A
	v_fmac_f32_e32 v13, v16, v14                               // 00000000032C: 561A1D10
	v_mul_f32_e32 v2, 0x3ecccccd, v2                           // 000000000330: 100404FF 3ECCCCCD
	s_waitcnt vmcnt(1)                                         // 000000000338: BF8C3F71
	v_mul_f32_e32 v7, 0x3dcccccd, v7                           // 00000000033C: 100E0EFF 3DCCCCCD
	v_fmac_f32_e32 v24, v23, v23                               // 000000000344: 56302F17
	v_mul_f32_e32 v8, 0x3d4ccccd, v8                           // 000000000348: 101010FF 3D4CCCCD
	v_fmac_f32_e32 v13, v19, v15                               // 000000000350: 561A1F13
	v_mul_f32_e32 v9, 0, v9                                    // 000000000354: 10121280
	v_rsq_f32_e32 v25, v24                                     // 000000000358: 7E325D18
	v_cmp_neq_f32_e32 vcc_lo, 0, v24                           // 00000000035C: 7C1A3080
	v_mul_f32_e32 v0, s4, v0                                   // 000000000360: 10000004
	v_mul_f32_e32 v1, s5, v1                                   // 000000000364: 10020205
	v_max_f32_e32 v13, 0, v13                                  // 000000000368: 201A1A80
	v_mul_f32_e32 v2, s6, v2                                   // 00000000036C: 10040406
	v_mul_f32_e32 v4, s8, v4                                   // 000000000370: 10080808
	v_mul_f32_e32 v5, s9, v5                                   // 000000000374: 100A0A09
	v_mul_f32_e32 v6, s10, v6                                  // 000000000378: 100C0C0A
	v_fma_f32 v7, v7, s4, 2.0                                  // 00000000037C: D54B0007 03D00907
	v_cndmask_b32_e32 v24, 0, v25, vcc_lo                      // 000000000384: 02303280
	v_fmaak_f32 v8, s5, v8, 0x4011eb86                         // 000000000388: 5A101005 4011EB86
	v_mul_f32_e32 v0, v0, v13                                  // 000000000390: 10001B00
	v_mul_f32_e32 v1, v1, v13                                  // 000000000394: 10021B01
	v_mul_f32_e32 v2, v2, v13                                  // 000000000398: 10041B02
	v_mul_f32_e32 v17, v24, v17                                // 00000000039C: 10222318
	v_mul_f32_e32 v22, v24, v22                                // 0000000003A0: 102C2D18
	v_mul_f32_e32 v23, v24, v23                                // 0000000003A4: 102E2F18
	v_fmaak_f32 v9, s6, v9, 0x42140000                         // 0000000003A8: 5A121206 42140000
	s_waitcnt lgkmcnt(0)                                       // 0000000003B0: BF8CC07F
	v_mul_f32_e32 v3, s3, v3                                   // 0000000003B4: 10060603
	v_mul_f32_e32 v17, v18, v17                                // 0000000003B8: 10222312
	v_sub_f32_e32 v18, 1.0, v21                                // 0000000003BC: 08242AF2
	v_fmac_f32_e32 v17, v16, v22                               // 0000000003C0: 56222D10
	v_mul_f32_e32 v14, v18, v18                                // 0000000003C4: 101C2512
	v_fmac_f32_e32 v17, v19, v23                               // 0000000003C8: 56222F13
	v_mul_f32_e32 v14, v14, v14                                // 0000000003CC: 101C1D0E
	v_max_f32_e32 v17, 0, v17                                  // 0000000003D0: 20222280
	v_mul_f32_e32 v14, v18, v14                                // 0000000003D4: 101C1D12
	v_log_f32_e32 v17, v17                                     // 0000000003D8: 7E224F11
	v_mul_f32_e32 v13, 0x3e99999a, v14                         // 0000000003DC: 101A1CFF 3E99999A
	v_mul_legacy_f32_e32 v15, s35, v17                         // 0000000003E4: 0E1E2223
	v_exp_f32_e32 v15, v15                                     // 0000000003E8: 7E1E4B0F
	v_fmac_f32_e32 v0, v15, v4                                 // 0000000003EC: 5600090F
	v_fmac_f32_e32 v1, v15, v5                                 // 0000000003F0: 56020B0F
	v_fmac_f32_e32 v2, v15, v6                                 // 0000000003F4: 56040D0F
	s_waitcnt vmcnt(0)                                         // 0000000003F8: BF8C3F70
	v_fmac_f32_e32 v7, v13, v10                                // 0000000003FC: 560E150D
	v_fmac_f32_e32 v8, v13, v11                                // 000000000400: 5610170D
	v_fmac_f32_e32 v9, v13, v12                                // 000000000404: 5612190D
	v_fmac_f32_e32 v7, s0, v0                                  // 000000000408: 560E0000
	v_fmac_f32_e32 v8, s1, v1                                  // 00000000040C: 56100201
	v_fmac_f32_e32 v9, s2, v2                                  // 000000000410: 56120402
	v_rcp_f32_e32 v0, v7                                       // 000000000414: 7E005507
	v_rcp_f32_e32 v1, v8                                       // 000000000418: 7E025508
	v_rcp_f32_e32 v2, v9                                       // 00000000041C: 7E045509
	v_fma_f32 v0, v7, v0, 1.0                                  // 000000000420: D54B0000 03CA0107
	v_fma_f32 v1, v8, v1, 1.0                                  // 000000000428: D54B0001 03CA0308
	v_fma_f32 v2, v9, v2, 1.0                                  // 000000000430: D54B0002 03CA0509
	v_log_f32_e32 v0, v0                                       // 000000000438: 7E004F00
	v_log_f32_e32 v1, v1                                       // 00000000043C: 7E024F01
	v_log_f32_e32 v2, v2                                       // 000000000440: 7E044F02
	v_mul_legacy_f32_e32 v0, 0x3ee8ba2f, v0                    // 000000000444: 0E0000FF 3EE8BA2F
	v_mul_legacy_f32_e32 v1, 0x3ee8ba2f, v1                    // 00000000044C: 0E0202FF 3EE8BA2F
	v_mul_legacy_f32_e32 v2, 0x3ee8ba2f, v2                    // 000000000454: 0E0404FF 3EE8BA2F
	v_exp_f32_e32 v0, v0                                       // 00000000045C: 7E004B00
	v_exp_f32_e32 v1, v1                                       // 000000000460: 7E024B01
	v_exp_f32_e32 v2, v2                                       // 000000000464: 7E044B02
	v_mul_f32_e32 v0, 0x3f6d61a0, v0                           // 000000000468: 100000FF 3F6D61A0
	v_mul_f32_e32 v1, 0x3f6d61a0, v1                           // 000000000470: 100202FF 3F6D61A0
	v_mul_f32_e32 v2, 0x3f6d61a0, v2                           // 000000000478: 100404FF 3F6D61A0
	exp mrt0 v0, v1, v2, v3 done vm                            // 000000000480: F800180F 03020100
	s_endpgm                                                   // 000000000488: BF810000
