// Parallel Reduction Compute Shader
// Tests parallel algorithms and atomic operations

// Particle data structure
struct Particle
{
    float3 Position;
    float Life;
    float3 Velocity;
    float Size;
    float4 Color;
    float3 Acceleration;
    float Mass;
    uint Type;
    float3 _padding;
};

// Compute constants
cbuffer ComputeParams : register(b0)
{
    uint ParticleCount;
    uint MaxParticles;
    float DeltaTime;
    float Time;
    float3 Gravity;
    float Damping;
    float3 EmitterPosition;
    float EmissionRate;
    float3 EmitterDirection;
    float EmissionSpeed;
    float2 LifetimeRange;
    float2 SizeRange;
    uint FrameCount;
    float NoiseScale;
    float NoiseStrength;
    uint _padding;
};

// Buffers
StructuredBuffer<Particle> ParticleBuffer : register(t0);
RWByteAddressBuffer RawDataBuffer : register(u0);

// Shared memory for reduction
groupshared float SharedDistances[64];

[numthreads(64, 1, 1)]
void main(uint3 id : SV_DispatchThreadID, uint3 localId : SV_GroupThreadID)
{
    uint index = id.x;
    
    // Load data into shared memory
    if (index < ParticleCount)
    {
        SharedDistances[localId.x] = length(ParticleBuffer[index].Position);
    }
    else
    {
        SharedDistances[localId.x] = 0.0;
    }
    
    GroupMemoryBarrierWithGroupSync();
    
    // Parallel reduction to find maximum
    for (uint stride = 32; stride > 0; stride >>= 1)
    {
        if (localId.x < stride)
        {
            SharedDistances[localId.x] = max(SharedDistances[localId.x], SharedDistances[localId.x + stride]);
        }
        GroupMemoryBarrierWithGroupSync();
    }
    
    // Write result using atomic operation
    if (localId.x == 0)
    {
        uint originalValue;
        RawDataBuffer.InterlockedMax(0, asuint(SharedDistances[0]), originalValue);
    }
}
