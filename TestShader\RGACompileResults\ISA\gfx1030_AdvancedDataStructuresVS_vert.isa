_amdgpu_gs_main:
	s_mov_b32 exec_lo, -1                                      // 000000000000: BEFE03C1
	s_bfe_u32 s3, s3, 0x40018                                  // 000000000004: 9383FF03 00040018
	s_bfe_u32 s33, s2, 0x90016                                 // 00000000000C: 93A1FF02 00090016
	s_bfe_u32 s1, s2, 0x9000c                                  // 000000000014: 9381FF02 0009000C
	s_mov_b32 s0, s11                                          // 00000000001C: BE80030B
	s_mov_b32 s4, s9                                           // 000000000020: BE840309
	s_cmp_lg_u32 s3, 0                                         // 000000000024: BF078003
	s_cbranch_scc1 _L0                                         // 000000000028: BF850003
	s_lshl_b32 s2, s33, 12                                     // 00000000002C: 8F028C21
	s_or_b32 m0, s2, s1                                        // 000000000030: 887C0102
	s_sendmsg sendmsg(MSG_GS_ALLOC_REQ)                        // 000000000034: BF900009
_L0:
	v_mbcnt_lo_u32_b32 v1, -1, 0                               // 000000000038: D7650001 000100C1
	v_lshl_or_b32 v30, s3, 5, v1                               // 000000000040: D76F001E 04050A03
	v_cmp_gt_u32_e32 vcc_lo, s1, v30                           // 000000000048: 7D883C01
	s_and_saveexec_b32 s64, vcc_lo                             // 00000000004C: BEC03C6A
	s_cbranch_execz _L1                                        // 000000000050: BF8802A7
	s_getpc_b64 s[6:7]                                         // 000000000054: BE861F00
	v_add_nc_u32_e32 v31, s0, v5                               // 000000000058: 4A3E0A00
	s_mov_b32 s11, s7                                          // 00000000005C: BE8B0307
	s_mov_b32 s5, s7                                           // 000000000060: BE850307
	s_clause 0x2                                               // 000000000064: BFA10002
	s_load_dwordx8 s[20:27], s[10:11], 0x70                    // 000000000068: F40C0505 FA000070
	s_load_dwordx8 s[36:43], s[10:11], null                    // 000000000070: F40C0905 FA000000
	s_load_dwordx4 s[0:3], s[10:11], 0x20                      // 000000000078: F4080005 FA000020
	s_mov_b32 s8, exec_lo                                      // 000000000080: BE88037E
	s_waitcnt lgkmcnt(0)                                       // 000000000084: BF8CC07F
	tbuffer_load_format_xyz v[24:26], v31, s[36:39], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000088: EA522000 8009181F
	tbuffer_load_format_xyz v[27:29], v31, s[40:43], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000090: EA522000 800A1B1F
	tbuffer_load_format_xyz v[21:23], v31, s[0:3], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000098: EA522000 8000151F
	tbuffer_load_format_xyzw v[4:7], v31, s[20:23], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 0000000000A0: EA6B2000 8005041F
	s_clause 0x1                                               // 0000000000A8: BFA10001
	s_load_dwordx8 s[16:23], s[4:5], null                      // 0000000000AC: F40C0402 FA000000
	s_load_dwordx4 s[28:31], s[4:5], 0x20                      // 0000000000B4: F4080702 FA000020
	s_mov_b32 s4, 0                                            // 0000000000BC: BE840380
	s_waitcnt vmcnt(3)                                         // 0000000000C0: BF8C3F73
	v_mov_b32_e32 v3, v26                                      // 0000000000C4: 7E06031A
	v_mov_b32_e32 v18, v25                                     // 0000000000C8: 7E240319
	v_mov_b32_e32 v17, v24                                     // 0000000000CC: 7E220318
	s_waitcnt vmcnt(0)                                         // 0000000000D0: BF8C3F70
	v_add_f32_e32 v1, v5, v4                                   // 0000000000D4: 06020905
	v_mov_b32_e32 v12, v29                                     // 0000000000D8: 7E18031D
	v_mov_b32_e32 v16, v28                                     // 0000000000DC: 7E20031C
	v_mov_b32_e32 v20, v27                                     // 0000000000E0: 7E28031B
	v_add_f32_e32 v1, v1, v6                                   // 0000000000E4: 06020D01
	v_add_f32_e32 v1, v1, v7                                   // 0000000000E8: 06020F01
	v_cmpx_lt_f32_e32 0, v1                                    // 0000000000EC: 7C220280
	s_cbranch_execz _L2                                        // 0000000000F0: BF8801B1
	tbuffer_load_format_x v2, v31, s[24:27], 0 format:[BUF_FMT_32_UINT] idxen// 0000000000F4: E8A02000 8006021F
	v_rcp_f32_e32 v36, v1                                      // 0000000000FC: 7E485501
	s_mov_b32 s7, s4                                           // 000000000100: BE870304
	s_mov_b32 s5, s4                                           // 000000000104: BE850304
	s_mov_b32 s6, s4                                           // 000000000108: BE860304
	v_mov_b32_e32 v12, s7                                      // 00000000010C: 7E180207
	v_mov_b32_e32 v9, s4                                       // 000000000110: 7E120204
	v_mov_b32_e32 v11, s6                                      // 000000000114: 7E160206
	v_mov_b32_e32 v10, s5                                      // 000000000118: 7E140205
	v_mov_b32_e32 v20, v12                                     // 00000000011C: 7E28030C
	v_mul_f32_e32 v32, v36, v4                                 // 000000000120: 10400924
	v_mov_b32_e32 v16, v12                                     // 000000000124: 7E20030C
	v_mov_b32_e32 v19, v11                                     // 000000000128: 7E26030B
	v_mov_b32_e32 v18, v10                                     // 00000000012C: 7E24030A
	v_mov_b32_e32 v17, v9                                      // 000000000130: 7E220309
	v_mov_b32_e32 v15, v11                                     // 000000000134: 7E1E030B
	v_mov_b32_e32 v14, v10                                     // 000000000138: 7E1C030A
	v_mov_b32_e32 v13, v9                                      // 00000000013C: 7E1A0309
	v_cmp_lt_f32_e64 s0, 0, v32                                // 000000000140: D4010000 00024080
	s_waitcnt vmcnt(0)                                         // 000000000148: BF8C3F70
	v_lshlrev_b32_e32 v33, 7, v2                               // 00000000014C: 34420487
	v_mov_b32_e32 v1, v9                                       // 000000000150: 7E020309
	v_mov_b32_e32 v2, v10                                      // 000000000154: 7E04030A
	v_mov_b32_e32 v3, v11                                      // 000000000158: 7E06030B
	v_mov_b32_e32 v4, v12                                      // 00000000015C: 7E08030C
	s_and_saveexec_b32 s1, s0                                  // 000000000160: BE813C00
	s_cbranch_execz _L3                                        // 000000000164: BF88001E
	s_waitcnt lgkmcnt(0)                                       // 000000000168: BF8CC07F
	s_clause 0x3                                               // 00000000016C: BFA10003
	buffer_load_dwordx4 v[1:4], v33, s[20:23], 0 offen         // 000000000170: E0381000 80050121
	buffer_load_dwordx4 v[37:40], v33, s[20:23], 0 offen offset:16// 000000000178: E0381010 80052521
	buffer_load_dwordx4 v[41:44], v33, s[20:23], 0 offen offset:32// 000000000180: E0381020 80052921
	buffer_load_dwordx4 v[45:48], v33, s[20:23], 0 offen offset:48// 000000000188: E0381030 80052D21
	s_waitcnt vmcnt(3)                                         // 000000000190: BF8C3F73
	v_mul_f32_e32 v9, v32, v1                                  // 000000000194: 10120320
	s_waitcnt vmcnt(2)                                         // 000000000198: BF8C3F72
	v_mul_f32_e32 v10, v32, v37                                // 00000000019C: 10144B20
	s_waitcnt vmcnt(1)                                         // 0000000001A0: BF8C3F71
	v_mul_f32_e32 v11, v32, v41                                // 0000000001A4: 10165320
	s_waitcnt vmcnt(0)                                         // 0000000001A8: BF8C3F70
	v_mul_f32_e32 v12, v32, v45                                // 0000000001AC: 10185B20
	v_mul_f32_e32 v17, v32, v2                                 // 0000000001B0: 10220520
	v_mul_f32_e32 v18, v32, v38                                // 0000000001B4: 10244D20
	v_mul_f32_e32 v19, v32, v42                                // 0000000001B8: 10265520
	v_mul_f32_e32 v20, v32, v46                                // 0000000001BC: 10285D20
	v_mul_f32_e32 v13, v32, v3                                 // 0000000001C0: 101A0720
	v_mul_f32_e32 v14, v32, v39                                // 0000000001C4: 101C4F20
	v_mul_f32_e32 v15, v32, v43                                // 0000000001C8: 101E5720
	v_mul_f32_e32 v16, v32, v47                                // 0000000001CC: 10205F20
	v_mul_f32_e32 v1, v32, v4                                  // 0000000001D0: 10020920
	v_mul_f32_e32 v2, v32, v40                                 // 0000000001D4: 10045120
	v_mul_f32_e32 v3, v32, v44                                 // 0000000001D8: 10065920
	v_mul_f32_e32 v4, v32, v48                                 // 0000000001DC: 10086120
_L3:
	s_or_b32 exec_lo, exec_lo, s1                              // 0000000001E0: 887E017E
	tbuffer_load_format_x v34, v31, s[24:27], 0 format:[BUF_FMT_32_UINT] idxen offset:4// 0000000001E4: E8A02004 8006221F
	v_mul_f32_e32 v5, v36, v5                                  // 0000000001EC: 100A0B24
	v_cmp_lt_f32_e64 s1, 0, v5                                 // 0000000001F0: D4010001 00020A80
	s_waitcnt vmcnt(0)                                         // 0000000001F8: BF8C3F70
	v_lshlrev_b32_e32 v34, 7, v34                              // 0000000001FC: 34444487
	s_and_saveexec_b32 s2, s1                                  // 000000000200: BE823C01
	s_cbranch_execz _L4                                        // 000000000204: BF88002A
	s_waitcnt lgkmcnt(0)                                       // 000000000208: BF8CC07F
	s_clause 0x3                                               // 00000000020C: BFA10003
	buffer_load_dwordx4 v[37:40], v34, s[20:23], 0 offen       // 000000000210: E0381000 80052522
	buffer_load_dwordx4 v[41:44], v34, s[20:23], 0 offen offset:16// 000000000218: E0381010 80052922
	buffer_load_dwordx4 v[45:48], v34, s[20:23], 0 offen offset:32// 000000000220: E0381020 80052D22
	buffer_load_dwordx4 v[49:52], v34, s[20:23], 0 offen offset:48// 000000000228: E0381030 80053122
	s_waitcnt vmcnt(3)                                         // 000000000230: BF8C3F73
	v_fma_f32 v9, v5, v37, v9                                  // 000000000234: D54B0009 04264B05
	s_waitcnt vmcnt(2)                                         // 00000000023C: BF8C3F72
	v_fma_f32 v10, v5, v41, v10                                // 000000000240: D54B000A 042A5305
	s_waitcnt vmcnt(1)                                         // 000000000248: BF8C3F71
	v_fma_f32 v11, v5, v45, v11                                // 00000000024C: D54B000B 042E5B05
	s_waitcnt vmcnt(0)                                         // 000000000254: BF8C3F70
	v_fmac_f32_e32 v12, v5, v49                                // 000000000258: 56186305
	v_fma_f32 v17, v5, v38, v17                                // 00000000025C: D54B0011 04464D05
	v_fma_f32 v18, v5, v42, v18                                // 000000000264: D54B0012 044A5505
	v_fma_f32 v19, v5, v46, v19                                // 00000000026C: D54B0013 044E5D05
	v_fmac_f32_e32 v20, v5, v50                                // 000000000274: 56286505
	v_fma_f32 v13, v5, v39, v13                                // 000000000278: D54B000D 04364F05
	v_fma_f32 v14, v5, v43, v14                                // 000000000280: D54B000E 043A5705
	v_fma_f32 v15, v5, v47, v15                                // 000000000288: D54B000F 043E5F05
	v_fmac_f32_e32 v16, v5, v51                                // 000000000290: 56206705
	v_fma_f32 v1, v5, v40, v1                                  // 000000000294: D54B0001 04065105
	v_fma_f32 v2, v5, v44, v2                                  // 00000000029C: D54B0002 040A5905
	v_fma_f32 v3, v5, v48, v3                                  // 0000000002A4: D54B0003 040E6105
	v_fmac_f32_e32 v4, v5, v52                                 // 0000000002AC: 56086905
_L4:
	s_or_b32 exec_lo, exec_lo, s2                              // 0000000002B0: 887E027E
	tbuffer_load_format_x v35, v31, s[24:27], 0 format:[BUF_FMT_32_UINT] idxen offset:8// 0000000002B4: E8A02008 8006231F
	v_mul_f32_e32 v6, v36, v6                                  // 0000000002BC: 100C0D24
	v_cmp_lt_f32_e64 s2, 0, v6                                 // 0000000002C0: D4010002 00020C80
	s_waitcnt vmcnt(0)                                         // 0000000002C8: BF8C3F70
	v_lshlrev_b32_e32 v35, 7, v35                              // 0000000002CC: 34464687
	s_and_saveexec_b32 s3, s2                                  // 0000000002D0: BE833C02
	s_cbranch_execz _L5                                        // 0000000002D4: BF88002A
	s_waitcnt lgkmcnt(0)                                       // 0000000002D8: BF8CC07F
	s_clause 0x3                                               // 0000000002DC: BFA10003
	buffer_load_dwordx4 v[37:40], v35, s[20:23], 0 offen       // 0000000002E0: E0381000 80052523
	buffer_load_dwordx4 v[41:44], v35, s[20:23], 0 offen offset:16// 0000000002E8: E0381010 80052923
	buffer_load_dwordx4 v[45:48], v35, s[20:23], 0 offen offset:32// 0000000002F0: E0381020 80052D23
	buffer_load_dwordx4 v[49:52], v35, s[20:23], 0 offen offset:48// 0000000002F8: E0381030 80053123
	s_waitcnt vmcnt(3)                                         // 000000000300: BF8C3F73
	v_fma_f32 v9, v6, v37, v9                                  // 000000000304: D54B0009 04264B06
	s_waitcnt vmcnt(2)                                         // 00000000030C: BF8C3F72
	v_fma_f32 v10, v6, v41, v10                                // 000000000310: D54B000A 042A5306
	s_waitcnt vmcnt(1)                                         // 000000000318: BF8C3F71
	v_fma_f32 v11, v6, v45, v11                                // 00000000031C: D54B000B 042E5B06
	v_fma_f32 v17, v6, v38, v17                                // 000000000324: D54B0011 04464D06
	v_fma_f32 v18, v6, v42, v18                                // 00000000032C: D54B0012 044A5506
	v_fma_f32 v19, v6, v46, v19                                // 000000000334: D54B0013 044E5D06
	v_fma_f32 v13, v6, v39, v13                                // 00000000033C: D54B000D 04364F06
	v_fma_f32 v14, v6, v43, v14                                // 000000000344: D54B000E 043A5706
	v_fma_f32 v15, v6, v47, v15                                // 00000000034C: D54B000F 043E5F06
	v_fma_f32 v1, v6, v40, v1                                  // 000000000354: D54B0001 04065106
	v_fma_f32 v2, v6, v44, v2                                  // 00000000035C: D54B0002 040A5906
	v_fma_f32 v3, v6, v48, v3                                  // 000000000364: D54B0003 040E6106
	s_waitcnt vmcnt(0)                                         // 00000000036C: BF8C3F70
	v_fmac_f32_e32 v12, v6, v49                                // 000000000370: 56186306
	v_fmac_f32_e32 v20, v6, v50                                // 000000000374: 56286506
	v_fmac_f32_e32 v16, v6, v51                                // 000000000378: 56206706
	v_fmac_f32_e32 v4, v6, v52                                 // 00000000037C: 56086906
_L5:
	s_or_b32 exec_lo, exec_lo, s3                              // 000000000380: 887E037E
	tbuffer_load_format_x v12, v31, s[24:27], 0 format:[BUF_FMT_32_UINT] idxen offset:12// 000000000384: E8A0200C 80060C1F
	v_mul_f32_e32 v4, v36, v7                                  // 00000000038C: 10080F24
	v_cmp_lt_f32_e64 s3, 0, v4                                 // 000000000390: D4010003 00020880
	s_waitcnt vmcnt(0)                                         // 000000000398: BF8C3F70
	v_lshlrev_b32_e32 v7, 7, v12                               // 00000000039C: 340E1887
	s_and_saveexec_b32 s4, s3                                  // 0000000003A0: BE843C03
	s_cbranch_execz _L6                                        // 0000000003A4: BF88001F
	s_waitcnt lgkmcnt(0)                                       // 0000000003A8: BF8CC07F
	s_clause 0x2                                               // 0000000003AC: BFA10002
	buffer_load_dwordx4 v[36:39], v7, s[20:23], 0 offen        // 0000000003B0: E0381000 80052407
	buffer_load_dwordx4 v[40:43], v7, s[20:23], 0 offen offset:16// 0000000003B8: E0381010 80052807
	buffer_load_dwordx4 v[44:47], v7, s[20:23], 0 offen offset:32// 0000000003C0: E0381020 80052C07
	s_waitcnt vmcnt(2)                                         // 0000000003C8: BF8C3F72
	v_fma_f32 v9, v4, v36, v9                                  // 0000000003CC: D54B0009 04264904
	s_waitcnt vmcnt(1)                                         // 0000000003D4: BF8C3F71
	v_fma_f32 v10, v4, v40, v10                                // 0000000003D8: D54B000A 042A5104
	s_waitcnt vmcnt(0)                                         // 0000000003E0: BF8C3F70
	v_fmac_f32_e32 v11, v4, v44                                // 0000000003E4: 56165904
	v_fma_f32 v17, v4, v37, v17                                // 0000000003E8: D54B0011 04464B04
	v_fma_f32 v18, v4, v41, v18                                // 0000000003F0: D54B0012 044A5304
	v_fmac_f32_e32 v19, v4, v45                                // 0000000003F8: 56265B04
	v_fma_f32 v13, v4, v38, v13                                // 0000000003FC: D54B000D 04364D04
	v_fma_f32 v14, v4, v42, v14                                // 000000000404: D54B000E 043A5504
	v_fmac_f32_e32 v15, v4, v46                                // 00000000040C: 561E5D04
	v_fma_f32 v1, v4, v39, v1                                  // 000000000410: D54B0001 04064F04
	v_fma_f32 v2, v4, v43, v2                                  // 000000000418: D54B0002 040A5704
	v_fmac_f32_e32 v3, v4, v47                                 // 000000000420: 56065F04
_L6:
	s_or_b32 exec_lo, exec_lo, s4                              // 000000000424: 887E047E
	v_mov_b32_e32 v20, 0                                       // 000000000428: 7E280280
	v_mov_b32_e32 v16, 0                                       // 00000000042C: 7E200280
	v_mov_b32_e32 v12, 0                                       // 000000000430: 7E180280
	s_and_saveexec_b32 s4, s0                                  // 000000000434: BE843C00
	s_cbranch_execz _L7                                        // 000000000438: BF880017
	s_waitcnt lgkmcnt(0)                                       // 00000000043C: BF8CC07F
	s_clause 0x2                                               // 000000000440: BFA10002
	buffer_load_dwordx3 v[36:38], v33, s[20:23], 0 offen       // 000000000444: E03C1000 80052421
	buffer_load_dwordx3 v[39:41], v33, s[20:23], 0 offen offset:16// 00000000044C: E03C1010 80052721
	buffer_load_dwordx3 v[42:44], v33, s[20:23], 0 offen offset:32// 000000000454: E03C1020 80052A21
	s_waitcnt vmcnt(2)                                         // 00000000045C: BF8C3F72
	v_mul_f32_e32 v12, v36, v27                                // 000000000460: 10183724
	s_waitcnt vmcnt(1)                                         // 000000000464: BF8C3F71
	v_mul_f32_e32 v16, v39, v27                                // 000000000468: 10203727
	s_waitcnt vmcnt(0)                                         // 00000000046C: BF8C3F70
	v_mul_f32_e32 v36, v42, v27                                // 000000000470: 1048372A
	v_fmac_f32_e32 v12, v37, v28                               // 000000000474: 56183925
	v_fmac_f32_e32 v16, v40, v28                               // 000000000478: 56203928
	v_fmac_f32_e32 v36, v43, v28                               // 00000000047C: 5648392B
	v_fmac_f32_e32 v12, v38, v29                               // 000000000480: 56183B26
	v_fmac_f32_e32 v16, v41, v29                               // 000000000484: 56203B29
	v_fmac_f32_e32 v36, v44, v29                               // 000000000488: 56483B2C
	v_mul_f32_e32 v20, v12, v32                                // 00000000048C: 1028410C
	v_mul_f32_e32 v16, v16, v32                                // 000000000490: 10204110
	v_mul_f32_e32 v12, v36, v32                                // 000000000494: 10184124
_L7:
	s_or_b32 exec_lo, exec_lo, s4                              // 000000000498: 887E047E
	s_and_saveexec_b32 s4, s1                                  // 00000000049C: BE843C01
	s_cbranch_execz _L8                                        // 0000000004A0: BF880017
	s_waitcnt lgkmcnt(0)                                       // 0000000004A4: BF8CC07F
	s_clause 0x2                                               // 0000000004A8: BFA10002
	buffer_load_dwordx3 v[36:38], v34, s[20:23], 0 offen       // 0000000004AC: E03C1000 80052422
	buffer_load_dwordx3 v[39:41], v34, s[20:23], 0 offen offset:16// 0000000004B4: E03C1010 80052722
	buffer_load_dwordx3 v[42:44], v34, s[20:23], 0 offen offset:32// 0000000004BC: E03C1020 80052A22
	s_waitcnt vmcnt(2)                                         // 0000000004C4: BF8C3F72
	v_mul_f32_e32 v36, v36, v27                                // 0000000004C8: 10483724
	s_waitcnt vmcnt(1)                                         // 0000000004CC: BF8C3F71
	v_mul_f32_e32 v39, v39, v27                                // 0000000004D0: 104E3727
	s_waitcnt vmcnt(0)                                         // 0000000004D4: BF8C3F70
	v_mul_f32_e32 v42, v42, v27                                // 0000000004D8: 1054372A
	v_fmac_f32_e32 v36, v37, v28                               // 0000000004DC: 56483925
	v_fmac_f32_e32 v39, v40, v28                               // 0000000004E0: 564E3928
	v_fmac_f32_e32 v42, v43, v28                               // 0000000004E4: 5654392B
	v_fmac_f32_e32 v36, v38, v29                               // 0000000004E8: 56483B26
	v_fmac_f32_e32 v39, v41, v29                               // 0000000004EC: 564E3B29
	v_fmac_f32_e32 v42, v44, v29                               // 0000000004F0: 56543B2C
	v_fmac_f32_e32 v20, v36, v5                                // 0000000004F4: 56280B24
	v_fmac_f32_e32 v16, v39, v5                                // 0000000004F8: 56200B27
	v_fmac_f32_e32 v12, v42, v5                                // 0000000004FC: 56180B2A
_L8:
	s_or_b32 exec_lo, exec_lo, s4                              // 000000000500: 887E047E
	s_and_saveexec_b32 s4, s2                                  // 000000000504: BE843C02
	s_cbranch_execz _L9                                        // 000000000508: BF880017
	s_waitcnt lgkmcnt(0)                                       // 00000000050C: BF8CC07F
	s_clause 0x2                                               // 000000000510: BFA10002
	buffer_load_dwordx3 v[36:38], v35, s[20:23], 0 offen       // 000000000514: E03C1000 80052423
	buffer_load_dwordx3 v[39:41], v35, s[20:23], 0 offen offset:16// 00000000051C: E03C1010 80052723
	buffer_load_dwordx3 v[42:44], v35, s[20:23], 0 offen offset:32// 000000000524: E03C1020 80052A23
	s_waitcnt vmcnt(2)                                         // 00000000052C: BF8C3F72
	v_mul_f32_e32 v36, v36, v27                                // 000000000530: 10483724
	s_waitcnt vmcnt(1)                                         // 000000000534: BF8C3F71
	v_mul_f32_e32 v39, v39, v27                                // 000000000538: 104E3727
	s_waitcnt vmcnt(0)                                         // 00000000053C: BF8C3F70
	v_mul_f32_e32 v42, v42, v27                                // 000000000540: 1054372A
	v_fmac_f32_e32 v36, v37, v28                               // 000000000544: 56483925
	v_fmac_f32_e32 v39, v40, v28                               // 000000000548: 564E3928
	v_fmac_f32_e32 v42, v43, v28                               // 00000000054C: 5654392B
	v_fmac_f32_e32 v36, v38, v29                               // 000000000550: 56483B26
	v_fmac_f32_e32 v39, v41, v29                               // 000000000554: 564E3B29
	v_fmac_f32_e32 v42, v44, v29                               // 000000000558: 56543B2C
	v_fmac_f32_e32 v20, v36, v6                                // 00000000055C: 56280D24
	v_fmac_f32_e32 v16, v39, v6                                // 000000000560: 56200D27
	v_fmac_f32_e32 v12, v42, v6                                // 000000000564: 56180D2A
_L9:
	s_or_b32 exec_lo, exec_lo, s4                              // 000000000568: 887E047E
	s_and_saveexec_b32 s4, s3                                  // 00000000056C: BE843C03
	s_cbranch_execz _L10                                       // 000000000570: BF880017
	s_waitcnt lgkmcnt(0)                                       // 000000000574: BF8CC07F
	s_clause 0x2                                               // 000000000578: BFA10002
	buffer_load_dwordx3 v[36:38], v7, s[20:23], 0 offen        // 00000000057C: E03C1000 80052407
	buffer_load_dwordx3 v[39:41], v7, s[20:23], 0 offen offset:16// 000000000584: E03C1010 80052707
	buffer_load_dwordx3 v[42:44], v7, s[20:23], 0 offen offset:32// 00000000058C: E03C1020 80052A07
	s_waitcnt vmcnt(2)                                         // 000000000594: BF8C3F72
	v_mul_f32_e32 v36, v36, v27                                // 000000000598: 10483724
	s_waitcnt vmcnt(1)                                         // 00000000059C: BF8C3F71
	v_mul_f32_e32 v39, v39, v27                                // 0000000005A0: 104E3727
	s_waitcnt vmcnt(0)                                         // 0000000005A4: BF8C3F70
	v_mul_f32_e32 v27, v42, v27                                // 0000000005A8: 1036372A
	v_fmac_f32_e32 v36, v37, v28                               // 0000000005AC: 56483925
	v_fmac_f32_e32 v39, v40, v28                               // 0000000005B0: 564E3928
	v_fmac_f32_e32 v27, v43, v28                               // 0000000005B4: 5636392B
	v_fmac_f32_e32 v36, v38, v29                               // 0000000005B8: 56483B26
	v_fmac_f32_e32 v39, v41, v29                               // 0000000005BC: 564E3B29
	v_fmac_f32_e32 v27, v44, v29                               // 0000000005C0: 56363B2C
	v_fmac_f32_e32 v20, v36, v4                                // 0000000005C4: 56280924
	v_fmac_f32_e32 v16, v39, v4                                // 0000000005C8: 56200927
	v_fmac_f32_e32 v12, v27, v4                                // 0000000005CC: 5618091B
_L10:
	s_or_b32 exec_lo, exec_lo, s4                              // 0000000005D0: 887E047E
	v_mov_b32_e32 v27, 0                                       // 0000000005D4: 7E360280
	v_mov_b32_e32 v28, 0                                       // 0000000005D8: 7E380280
	v_mov_b32_e32 v29, 0                                       // 0000000005DC: 7E3A0280
	s_and_saveexec_b32 s4, s0                                  // 0000000005E0: BE843C00
	s_cbranch_execz _L11                                       // 0000000005E4: BF880017
	s_waitcnt lgkmcnt(0)                                       // 0000000005E8: BF8CC07F
	s_clause 0x2                                               // 0000000005EC: BFA10002
	buffer_load_dwordx3 v[27:29], v33, s[20:23], 0 offen       // 0000000005F0: E03C1000 80051B21
	buffer_load_dwordx3 v[36:38], v33, s[20:23], 0 offen offset:16// 0000000005F8: E03C1010 80052421
	buffer_load_dwordx3 v[39:41], v33, s[20:23], 0 offen offset:32// 000000000600: E03C1020 80052721
	s_waitcnt vmcnt(2)                                         // 000000000608: BF8C3F72
	v_mul_f32_e32 v27, v27, v21                                // 00000000060C: 10362B1B
	s_waitcnt vmcnt(1)                                         // 000000000610: BF8C3F71
	v_mul_f32_e32 v33, v36, v21                                // 000000000614: 10422B24
	s_waitcnt vmcnt(0)                                         // 000000000618: BF8C3F70
	v_mul_f32_e32 v36, v39, v21                                // 00000000061C: 10482B27
	v_fmac_f32_e32 v27, v28, v22                               // 000000000620: 56362D1C
	v_fmac_f32_e32 v33, v37, v22                               // 000000000624: 56422D25
	v_fmac_f32_e32 v36, v40, v22                               // 000000000628: 56482D28
	v_fmac_f32_e32 v27, v29, v23                               // 00000000062C: 56362F1D
	v_fmac_f32_e32 v33, v38, v23                               // 000000000630: 56422F26
	v_fmac_f32_e32 v36, v41, v23                               // 000000000634: 56482F29
	v_mul_f32_e32 v27, v27, v32                                // 000000000638: 1036411B
	v_mul_f32_e32 v28, v33, v32                                // 00000000063C: 10384121
	v_mul_f32_e32 v29, v36, v32                                // 000000000640: 103A4124
_L11:
	s_or_b32 exec_lo, exec_lo, s4                              // 000000000644: 887E047E
	s_and_saveexec_b32 s0, s1                                  // 000000000648: BE803C01
	s_cbranch_execz _L12                                       // 00000000064C: BF880017
	s_waitcnt lgkmcnt(0)                                       // 000000000650: BF8CC07F
	s_clause 0x2                                               // 000000000654: BFA10002
	buffer_load_dwordx3 v[36:38], v34, s[20:23], 0 offen       // 000000000658: E03C1000 80052422
	buffer_load_dwordx3 v[39:41], v34, s[20:23], 0 offen offset:16// 000000000660: E03C1010 80052722
	buffer_load_dwordx3 v[32:34], v34, s[20:23], 0 offen offset:32// 000000000668: E03C1020 80052022
	s_waitcnt vmcnt(2)                                         // 000000000670: BF8C3F72
	v_mul_f32_e32 v36, v36, v21                                // 000000000674: 10482B24
	s_waitcnt vmcnt(1)                                         // 000000000678: BF8C3F71
	v_mul_f32_e32 v39, v39, v21                                // 00000000067C: 104E2B27
	s_waitcnt vmcnt(0)                                         // 000000000680: BF8C3F70
	v_mul_f32_e32 v32, v32, v21                                // 000000000684: 10402B20
	v_fmac_f32_e32 v36, v37, v22                               // 000000000688: 56482D25
	v_fmac_f32_e32 v39, v40, v22                               // 00000000068C: 564E2D28
	v_fmac_f32_e32 v32, v33, v22                               // 000000000690: 56402D21
	v_fmac_f32_e32 v36, v38, v23                               // 000000000694: 56482F26
	v_fmac_f32_e32 v39, v41, v23                               // 000000000698: 564E2F29
	v_fmac_f32_e32 v32, v34, v23                               // 00000000069C: 56402F22
	v_fmac_f32_e32 v27, v36, v5                                // 0000000006A0: 56360B24
	v_fmac_f32_e32 v28, v39, v5                                // 0000000006A4: 56380B27
	v_fmac_f32_e32 v29, v32, v5                                // 0000000006A8: 563A0B20
_L12:
	s_or_b32 exec_lo, exec_lo, s0                              // 0000000006AC: 887E007E
	s_and_saveexec_b32 s0, s2                                  // 0000000006B0: BE803C02
	s_cbranch_execz _L13                                       // 0000000006B4: BF880017
	s_waitcnt lgkmcnt(0)                                       // 0000000006B8: BF8CC07F
	s_clause 0x2                                               // 0000000006BC: BFA10002
	buffer_load_dwordx3 v[32:34], v35, s[20:23], 0 offen       // 0000000006C0: E03C1000 80052023
	buffer_load_dwordx3 v[36:38], v35, s[20:23], 0 offen offset:16// 0000000006C8: E03C1010 80052423
	buffer_load_dwordx3 v[39:41], v35, s[20:23], 0 offen offset:32// 0000000006D0: E03C1020 80052723
	s_waitcnt vmcnt(2)                                         // 0000000006D8: BF8C3F72
	v_mul_f32_e32 v5, v32, v21                                 // 0000000006DC: 100A2B20
	s_waitcnt vmcnt(1)                                         // 0000000006E0: BF8C3F71
	v_mul_f32_e32 v32, v36, v21                                // 0000000006E4: 10402B24
	s_waitcnt vmcnt(0)                                         // 0000000006E8: BF8C3F70
	v_mul_f32_e32 v35, v39, v21                                // 0000000006EC: 10462B27
	v_fmac_f32_e32 v5, v33, v22                                // 0000000006F0: 560A2D21
	v_fmac_f32_e32 v32, v37, v22                               // 0000000006F4: 56402D25
	v_fmac_f32_e32 v35, v40, v22                               // 0000000006F8: 56462D28
	v_fmac_f32_e32 v5, v34, v23                                // 0000000006FC: 560A2F22
	v_fmac_f32_e32 v32, v38, v23                               // 000000000700: 56402F26
	v_fmac_f32_e32 v35, v41, v23                               // 000000000704: 56462F29
	v_fmac_f32_e32 v27, v5, v6                                 // 000000000708: 56360D05
	v_fmac_f32_e32 v28, v32, v6                                // 00000000070C: 56380D20
	v_fmac_f32_e32 v29, v35, v6                                // 000000000710: 563A0D23
_L13:
	s_or_b32 exec_lo, exec_lo, s0                              // 000000000714: 887E007E
	v_fma_f32 v17, v17, v25, v1                                // 000000000718: D54B0011 04063311
	v_fma_f32 v18, v18, v25, v2                                // 000000000720: D54B0012 040A3312
	v_fmac_f32_e32 v3, v19, v25                                // 000000000728: 56063313
	v_fmac_f32_e32 v17, v9, v24                                // 00000000072C: 56223109
	v_fmac_f32_e32 v18, v10, v24                               // 000000000730: 5624310A
	v_fmac_f32_e32 v3, v11, v24                                // 000000000734: 5606310B
	v_fmac_f32_e32 v17, v13, v26                               // 000000000738: 5622350D
	v_fmac_f32_e32 v18, v14, v26                               // 00000000073C: 5624350E
	v_fmac_f32_e32 v3, v15, v26                                // 000000000740: 5606350F
	s_and_saveexec_b32 s0, s3                                  // 000000000744: BE803C03
	s_cbranch_execz _L14                                       // 000000000748: BF880017
	s_waitcnt lgkmcnt(0)                                       // 00000000074C: BF8CC07F
	s_clause 0x2                                               // 000000000750: BFA10002
	buffer_load_dwordx3 v[9:11], v7, s[20:23], 0 offen         // 000000000754: E03C1000 80050907
	buffer_load_dwordx3 v[13:15], v7, s[20:23], 0 offen offset:16// 00000000075C: E03C1010 80050D07
	buffer_load_dwordx3 v[5:7], v7, s[20:23], 0 offen offset:32// 000000000764: E03C1020 80050507
	s_waitcnt vmcnt(2)                                         // 00000000076C: BF8C3F72
	v_mul_f32_e32 v1, v9, v21                                  // 000000000770: 10022B09
	s_waitcnt vmcnt(1)                                         // 000000000774: BF8C3F71
	v_mul_f32_e32 v2, v13, v21                                 // 000000000778: 10042B0D
	s_waitcnt vmcnt(0)                                         // 00000000077C: BF8C3F70
	v_mul_f32_e32 v5, v5, v21                                  // 000000000780: 100A2B05
	v_fmac_f32_e32 v1, v10, v22                                // 000000000784: 56022D0A
	v_fmac_f32_e32 v2, v14, v22                                // 000000000788: 56042D0E
	v_fmac_f32_e32 v5, v6, v22                                 // 00000000078C: 560A2D06
	v_fmac_f32_e32 v1, v11, v23                                // 000000000790: 56022F0B
	v_fmac_f32_e32 v2, v15, v23                                // 000000000794: 56042F0F
	v_fmac_f32_e32 v5, v7, v23                                 // 000000000798: 560A2F07
	v_fmac_f32_e32 v27, v1, v4                                 // 00000000079C: 56360901
	v_fmac_f32_e32 v28, v2, v4                                 // 0000000007A0: 56380902
	v_fmac_f32_e32 v29, v5, v4                                 // 0000000007A4: 563A0905
_L14:
	s_or_b32 exec_lo, exec_lo, s0                              // 0000000007A8: 887E007E
	v_mov_b32_e32 v23, v29                                     // 0000000007AC: 7E2E031D
	v_mov_b32_e32 v22, v28                                     // 0000000007B0: 7E2C031C
	v_mov_b32_e32 v21, v27                                     // 0000000007B4: 7E2A031B
_L2:
	s_or_b32 exec_lo, exec_lo, s8                              // 0000000007B8: 887E087E
	v_add_lshl_u32 v1, v8, s12, 6                              // 0000000007BC: D7470001 02181908
	v_mov_b32_e32 v28, 2.0                                     // 0000000007C4: 7E3802F4
	s_waitcnt lgkmcnt(0)                                       // 0000000007C8: BF8CC07F
	s_clause 0x1                                               // 0000000007CC: BFA10001
	buffer_load_dwordx4 v[24:27], v1, s[28:31], 0 offen offset:32// 0000000007D0: E0381020 80071801
	buffer_load_dwordx4 v[32:35], v1, s[28:31], 0 offen offset:48// 0000000007D8: E0381030 80072001
	s_load_dwordx16 s[0:15], s[10:11], 0x30                    // 0000000007E0: F4100005 FA000030
	s_clause 0x1                                               // 0000000007E8: BFA10001
	buffer_load_dwordx4 v[36:39], v1, s[28:31], 0 offen offset:16// 0000000007EC: E0381010 80072401
	buffer_load_dwordx4 v[40:43], v1, s[28:31], 0 offen        // 0000000007F4: E0381000 80072801
	s_waitcnt lgkmcnt(0)                                       // 0000000007FC: BF8CC07F
	tbuffer_load_format_xyz v[44:46], v31, s[0:3], 0 format:[BUF_FMT_32_32_32_FLOAT] idxen// 000000000800: EA522000 80002C1F
	tbuffer_load_format_xy v[1:2], v31, s[4:7], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000808: EA012000 8001011F
	tbuffer_load_format_xy v[8:9], v31, s[8:11], 0 format:[BUF_FMT_32_32_FLOAT] idxen// 000000000810: EA012000 8002081F
	tbuffer_load_format_xyzw v[4:7], v31, s[12:15], 0 format:[BUF_FMT_32_32_32_32_FLOAT] idxen// 000000000818: EA6B2000 8003041F
	s_clause 0x9                                               // 000000000820: BFA10009
	s_buffer_load_dwordx2 s[34:35], s[20:23], 0x50             // 000000000824: F424088A FA000050
	s_buffer_load_dwordx2 s[62:63], s[20:23], 0x40             // 00000000082C: F4240F8A FA000040
	s_buffer_load_dwordx2 s[60:61], s[20:23], 0x60             // 000000000834: F4240F0A FA000060
	s_buffer_load_dword s65, s[20:23], 0x58                    // 00000000083C: F420104A FA000058
	s_buffer_load_dword s68, s[20:23], 0x48                    // 000000000844: F420110A FA000048
	s_buffer_load_dword s69, s[20:23], 0x68                    // 00000000084C: F420114A FA000068
	s_buffer_load_dwordx8 s[36:43], s[20:23], null             // 000000000854: F42C090A FA000000
	s_buffer_load_dwordx8 s[24:31], s[20:23], 0x20             // 00000000085C: F42C060A FA000020
	s_buffer_load_dwordx8 s[52:59], s[20:23], 0x80             // 000000000864: F42C0D0A FA000080
	s_buffer_load_dwordx8 s[44:51], s[20:23], 0xa0             // 00000000086C: F42C0B0A FA0000A0
	s_clause 0x3                                               // 000000000874: BFA10003
	s_buffer_load_dwordx8 s[0:7], s[16:19], 0x80               // 000000000878: F42C0008 FA000080
	s_buffer_load_dwordx8 s[8:15], s[16:19], 0xa0              // 000000000880: F42C0208 FA0000A0
	s_buffer_load_dwordx2 s[66:67], s[16:19], 0xc0             // 000000000888: F4241088 FA0000C0
	s_buffer_load_dword s16, s[16:19], 0xc8                    // 000000000890: F4200408 FA0000C8
	s_buffer_load_dword s17, s[20:23], 0xc0                    // 000000000898: F420044A FA0000C0
	s_waitcnt lgkmcnt(0)                                       // 0000000008A0: BF8CC07F
	v_mul_f32_e32 v10, s34, v20                                // 0000000008A4: 10142822
	v_mul_f32_e32 v14, s34, v21                                // 0000000008A8: 101C2A22
	v_mul_f32_e32 v11, s62, v20                                // 0000000008AC: 1016283E
	v_mul_f32_e32 v13, s60, v20                                // 0000000008B0: 101A283C
	v_mul_f32_e32 v20, s62, v21                                // 0000000008B4: 10282A3E
	v_fmac_f32_e32 v10, s35, v16                               // 0000000008B8: 56142023
	v_fmac_f32_e32 v14, s35, v22                               // 0000000008BC: 561C2C23
	v_mul_f32_e32 v21, s60, v21                                // 0000000008C0: 102A2A3C
	v_fmac_f32_e32 v11, s63, v16                               // 0000000008C4: 5616203F
	v_fmac_f32_e32 v13, s61, v16                               // 0000000008C8: 561A203D
	v_fmac_f32_e32 v10, s65, v12                               // 0000000008CC: 56141841
	v_fmac_f32_e32 v20, s63, v22                               // 0000000008D0: 56282C3F
	v_fmac_f32_e32 v14, s65, v23                               // 0000000008D4: 561C2E41
	v_fmac_f32_e32 v11, s68, v12                               // 0000000008D8: 56161844
	v_fmac_f32_e32 v21, s61, v22                               // 0000000008DC: 562A2C3D
	v_mul_f32_e32 v15, v10, v10                                // 0000000008E0: 101E150A
	v_fmac_f32_e32 v13, s69, v12                               // 0000000008E4: 561A1845
	v_fmac_f32_e32 v20, s68, v23                               // 0000000008E8: 56282E44
	v_mul_f32_e32 v12, v14, v14                                // 0000000008EC: 10181D0E
	v_fmac_f32_e32 v21, s69, v23                               // 0000000008F0: 562A2E45
	v_fmac_f32_e32 v15, v11, v11                               // 0000000008F4: 561E170B
	v_fmac_f32_e32 v12, v20, v20                               // 0000000008F8: 56182914
	v_fmac_f32_e32 v15, v13, v13                               // 0000000008FC: 561E1B0D
	v_fmac_f32_e32 v12, v21, v21                               // 000000000900: 56182B15
	v_rsq_f32_e32 v15, v15                                     // 000000000904: 7E1E5D0F
	v_rsq_f32_e32 v12, v12                                     // 000000000908: 7E185D0C
	v_mul_legacy_f32_e32 v16, v10, v15                         // 00000000090C: 0E201F0A
	v_mul_legacy_f32_e32 v19, v11, v15                         // 000000000910: 0E261F0B
	v_mul_legacy_f32_e32 v15, v13, v15                         // 000000000914: 0E1E1F0D
	v_mul_legacy_f32_e32 v10, v20, v12                         // 000000000918: 0E141914
	s_waitcnt vmcnt(6)                                         // 00000000091C: BF8C3F76
	v_fma_f32 v22, v24, v3, v32                                // 000000000920: D54B0016 04820718
	v_fma_f32 v20, v25, v3, v33                                // 000000000928: D54B0014 04860719
	v_fma_f32 v23, v26, v3, v34                                // 000000000930: D54B0017 048A071A
	v_fmac_f32_e32 v35, v27, v3                                // 000000000938: 5646071B
	v_mul_legacy_f32_e32 v3, v14, v12                          // 00000000093C: 0E06190E
	s_waitcnt vmcnt(5)                                         // 000000000940: BF8C3F75
	v_fmac_f32_e32 v22, v36, v18                               // 000000000944: 562C2524
	v_fmac_f32_e32 v20, v37, v18                               // 000000000948: 56282525
	v_fmac_f32_e32 v23, v38, v18                               // 00000000094C: 562E2526
	v_fmac_f32_e32 v35, v39, v18                               // 000000000950: 56462527
	v_mul_legacy_f32_e32 v14, v21, v12                         // 000000000954: 0E1C1915
	s_waitcnt vmcnt(4)                                         // 000000000958: BF8C3F74
	v_fmac_f32_e32 v22, v40, v17                               // 00000000095C: 562C2328
	v_fmac_f32_e32 v20, v41, v17                               // 000000000960: 56282329
	v_fmac_f32_e32 v23, v42, v17                               // 000000000964: 562E232A
	v_fmac_f32_e32 v35, v43, v17                               // 000000000968: 5646232B
	v_mul_f32_e32 v12, s40, v22                                // 00000000096C: 10182C28
	v_mul_f32_e32 v17, s52, v22                                // 000000000970: 10222C34
	v_mul_f32_e32 v13, s36, v22                                // 000000000974: 101A2C24
	v_mul_f32_e32 v27, s56, v22                                // 000000000978: 10362C38
	v_mul_f32_e32 v11, s24, v22                                // 00000000097C: 10162C18
	v_fmac_f32_e32 v12, s41, v20                               // 000000000980: 56182829
	v_fmac_f32_e32 v17, s53, v20                               // 000000000984: 56222835
	v_mul_f32_e32 v26, s28, v22                                // 000000000988: 10342C1C
	v_mul_f32_e32 v29, s44, v22                                // 00000000098C: 103A2C2C
	v_mul_f32_e32 v31, s48, v22                                // 000000000990: 103E2C30
	s_waitcnt vmcnt(3)                                         // 000000000994: BF8C3F73
	v_mul_f32_e32 v33, s34, v44                                // 000000000998: 10425822
	v_fmac_f32_e32 v13, s37, v20                               // 00000000099C: 561A2825
	v_fmac_f32_e32 v27, s57, v20                               // 0000000009A0: 56362839
	v_fmac_f32_e32 v12, s42, v23                               // 0000000009A4: 56182E2A
	v_fmac_f32_e32 v17, s54, v23                               // 0000000009A8: 56222E36
	v_mul_f32_e32 v32, s62, v44                                // 0000000009AC: 1040583E
	v_fmac_f32_e32 v11, s25, v20                               // 0000000009B0: 56162819
	v_fmac_f32_e32 v26, s29, v20                               // 0000000009B4: 5634281D
	v_fmac_f32_e32 v29, s45, v20                               // 0000000009B8: 563A282D
	v_fmac_f32_e32 v31, s49, v20                               // 0000000009BC: 563E2831
	v_fmac_f32_e32 v33, s35, v45                               // 0000000009C0: 56425A23
	v_fmac_f32_e32 v13, s38, v23                               // 0000000009C4: 561A2E26
	v_fmac_f32_e32 v27, s58, v23                               // 0000000009C8: 56362E3A
	v_fmac_f32_e32 v12, s43, v35                               // 0000000009CC: 5618462B
	v_fmac_f32_e32 v17, s55, v35                               // 0000000009D0: 56224637
	v_mul_f32_e32 v34, s60, v44                                // 0000000009D4: 1044583C
	v_fmac_f32_e32 v32, s63, v45                               // 0000000009D8: 56405A3F
	v_fmac_f32_e32 v11, s26, v23                               // 0000000009DC: 56162E1A
	v_fmac_f32_e32 v26, s30, v23                               // 0000000009E0: 56342E1E
	v_fmac_f32_e32 v29, s46, v23                               // 0000000009E4: 563A2E2E
	v_fmac_f32_e32 v31, s50, v23                               // 0000000009E8: 563E2E32
	v_fmac_f32_e32 v33, s65, v46                               // 0000000009EC: 56425C41
	v_fmac_f32_e32 v13, s39, v35                               // 0000000009F0: 561A4627
	v_fmac_f32_e32 v27, s59, v35                               // 0000000009F4: 5636463B
	v_mul_f32_e32 v21, s0, v17                                 // 0000000009F8: 102A2200
	v_mul_f32_e32 v20, s4, v17                                 // 0000000009FC: 10282204
	v_mul_f32_e32 v18, s8, v17                                 // 000000000A00: 10242208
	v_mul_f32_e32 v17, s12, v17                                // 000000000A04: 1022220C
	v_sub_f32_e32 v37, s67, v12                                // 000000000A08: 084A1843
	v_fmac_f32_e32 v34, s61, v45                               // 000000000A0C: 56445A3D
	v_fmac_f32_e32 v32, s68, v46                               // 000000000A10: 56405C44
	v_fmac_f32_e32 v11, s27, v35                               // 000000000A14: 5616461B
	v_fmac_f32_e32 v26, s31, v35                               // 000000000A18: 5634461F
	v_fmac_f32_e32 v29, s47, v35                               // 000000000A1C: 563A462F
	v_fmac_f32_e32 v31, s51, v35                               // 000000000A20: 563E4633
	v_mul_f32_e32 v35, v33, v33                                // 000000000A24: 10464321
	v_sub_f32_e32 v36, s66, v13                                // 000000000A28: 08481A42
	v_fmac_f32_e32 v21, s1, v27                                // 000000000A2C: 562A3601
	v_fmac_f32_e32 v20, s5, v27                                // 000000000A30: 56283605
	v_fmac_f32_e32 v18, s9, v27                                // 000000000A34: 56243609
	v_fmac_f32_e32 v17, s13, v27                               // 000000000A38: 5622360D
	v_mul_f32_e32 v27, v37, v37                                // 000000000A3C: 10364B25
	v_fmac_f32_e32 v34, s69, v46                               // 000000000A40: 56445C45
	v_fmac_f32_e32 v35, v32, v32                               // 000000000A44: 56464120
	v_sub_f32_e32 v38, s16, v11                                // 000000000A48: 084C1610
	v_mul_f32_e32 v25, s0, v13                                 // 000000000A4C: 10321A00
	v_fmac_f32_e32 v27, v36, v36                               // 000000000A50: 56364924
	v_mul_f32_e32 v24, s4, v13                                 // 000000000A54: 10301A04
	v_fmac_f32_e32 v35, v34, v34                               // 000000000A58: 56464522
	v_mul_f32_e32 v23, s8, v13                                 // 000000000A5C: 102E1A08
	v_mul_f32_e32 v22, s12, v13                                // 000000000A60: 102C1A0C
	v_fmac_f32_e32 v27, v38, v38                               // 000000000A64: 56364D26
	v_fmac_f32_e32 v21, s2, v29                                // 000000000A68: 562A3A02
	v_fmac_f32_e32 v20, s6, v29                                // 000000000A6C: 56283A06
	v_fmac_f32_e32 v18, s10, v29                               // 000000000A70: 56243A0A
	v_fmac_f32_e32 v17, s14, v29                               // 000000000A74: 56223A0E
	v_rsq_f32_e32 v29, v35                                     // 000000000A78: 7E3A5D23
	v_sqrt_f32_e32 v35, v27                                    // 000000000A7C: 7E46671B
	v_fmac_f32_e32 v25, s1, v12                                // 000000000A80: 56321801
	v_fmac_f32_e32 v24, s5, v12                                // 000000000A84: 56301805
	v_fmac_f32_e32 v23, s9, v12                                // 000000000A88: 562E1809
	v_fmac_f32_e32 v22, s13, v12                               // 000000000A8C: 562C180D
	v_rsq_f32_e32 v39, v27                                     // 000000000A90: 7E4E5D1B
	v_fmac_f32_e32 v25, s2, v11                                // 000000000A94: 56321602
	v_fmac_f32_e32 v24, s6, v11                                // 000000000A98: 56301606
	v_fmac_f32_e32 v23, s10, v11                               // 000000000A9C: 562E160A
	v_fmac_f32_e32 v22, s14, v11                               // 000000000AA0: 562C160E
	v_fmamk_f32 v35, v35, 0xbca3d70a, v28                      // 000000000AA4: 58463923 BCA3D70A
	v_fmac_f32_e32 v21, s3, v31                                // 000000000AAC: 562A3E03
	v_fmac_f32_e32 v20, s7, v31                                // 000000000AB0: 56283E07
	v_fmac_f32_e32 v18, s11, v31                               // 000000000AB4: 56243E0B
	v_fmac_f32_e32 v17, s15, v31                               // 000000000AB8: 56223E0F
	v_fmac_f32_e32 v25, s3, v26                                // 000000000ABC: 56323403
	v_fmac_f32_e32 v24, s7, v26                                // 000000000AC0: 56303407
	v_fmac_f32_e32 v23, s11, v26                               // 000000000AC4: 562E340B
	v_fmac_f32_e32 v22, s15, v26                               // 000000000AC8: 562C340F
	v_mul_legacy_f32_e32 v32, v32, v29                         // 000000000ACC: 0E403B20
	v_mul_legacy_f32_e32 v31, v33, v29                         // 000000000AD0: 0E3E3B21
	v_mul_legacy_f32_e32 v33, v34, v29                         // 000000000AD4: 0E423B22
	v_mul_legacy_f32_e32 v28, v36, v39                         // 000000000AD8: 0E384F24
	v_mul_legacy_f32_e32 v27, v37, v39                         // 000000000ADC: 0E364F25
	v_mul_legacy_f32_e32 v26, v38, v39                         // 000000000AE0: 0E344F26
	v_max_f32_e64 v29, v35, v35 clamp                          // 000000000AE4: D510801D 00024723
	v_mov_b32_e32 v34, s17                                     // 000000000AEC: 7E440211
_L1:
	s_or_b32 exec_lo, exec_lo, s64                             // 000000000AF0: 887E407E
	s_mov_b32 s1, exec_lo                                      // 000000000AF4: BE81037E
	v_cmpx_gt_u32_e64 s33, v30                                 // 000000000AF8: D4D4007E 00023C21
	s_cbranch_execz _L15                                       // 000000000B00: BF880002
	exp prim v0, off, off, off done                            // 000000000B04: F8000941 00000000
_L15:
	s_waitcnt expcnt(0)                                        // 000000000B0C: BF8CFF0F
	s_or_b32 exec_lo, exec_lo, s1                              // 000000000B10: 887E017E
	s_and_saveexec_b32 s0, vcc_lo                              // 000000000B14: BE803C6A
	s_cbranch_execz _L16                                       // 000000000B18: BF88001C
	exp pos0 v25, v24, v23, v22 done                           // 000000000B1C: F80008CF 16171819
	s_waitcnt vmcnt(1)                                         // 000000000B24: BF8C3F71
	exp param5 v8, v9, off, off                                // 000000000B28: F8000253 00000908
	exp param10 v34, off, off, off                             // 000000000B30: F80002A1 00000022
	exp param3 v32, v31, v33, off                              // 000000000B38: F8000237 00211F20
	exp param8 v21, v20, v18, v17                              // 000000000B40: F800028F 11121415
	exp param1 v19, v16, v15, off                              // 000000000B48: F8000217 000F1013
	s_waitcnt vmcnt(0)                                         // 000000000B50: BF8C3F70
	exp param6 v4, v5, v6, v7                                  // 000000000B54: F800026F 07060504
	exp param11 v29, off, off, off                             // 000000000B5C: F80002B1 0000001D
	exp param4 v1, v2, off, off                                // 000000000B64: F8000243 00000201
	exp param9 v25, v24, v23, v22                              // 000000000B6C: F800029F 16171819
	exp param2 v10, v3, v14, off                               // 000000000B74: F8000227 000E030A
	exp param7 v28, v27, v26, off                              // 000000000B7C: F8000277 001A1B1C
	exp param0 v13, v12, v11, off                              // 000000000B84: F8000207 000B0C0D
_L16:
	s_endpgm                                                   // 000000000B8C: BF810000
