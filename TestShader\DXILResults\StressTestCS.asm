;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; no parameters
; shader hash: 416315ad9a6035dabe36e4dbcc9cef38
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Compute Shader
; NumThreads=(64,1,1)
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 0
; SigOutputElements: 0
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 0
; SigOutputVectors[0]: 0
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Buffer Definitions:
;
; cbuffer StressTestParams
; {
;
;   struct StressTestParams
;   {
;
;       uint WorkGroupSize;                           ; Offset:    0
;       uint TotalWorkItems;                          ; Offset:    4
;       uint ComplexityLevel;                         ; Offset:    8
;       uint TestType;                                ; Offset:   12
;       float Time;                                   ; Offset:   16
;       float3 Padding;                               ; Offset:   20
;   
;   } StressTestParams;                               ; Offset:    0 Size:    32
;
; }
;
; Resource bind info for InputBuffer
; {
;
;   float4 $Element;                                  ; Offset:    0 Size:    16
;
; }
;
; Resource bind info for OutputBuffer
; {
;
;   float4 $Element;                                  ; Offset:    0 Size:    16
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; StressTestParams                  cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; InputTexture                      texture     f32          2d      T0             t0     1
; InputBuffer                           UAV  struct         r/w      U0             u0     1
; OutputBuffer                          UAV  struct         r/w      U1             u1     1
; OutputTexture                         UAV     f32          2d      U2             u2     1
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.Dimensions = type { i32, i32, i32, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%"class.RWStructuredBuffer<vector<float, 4> >" = type { <4 x float> }
%"class.RWTexture2D<vector<float, 4> >" = type { <4 x float> }
%StressTestParams = type { i32, i32, i32, i32, float, <3 x float> }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 1, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call i32 @dx.op.threadId.i32(i32 93, i32 0)  ; ThreadId(component)
  %8 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %9 = extractvalue %dx.types.CBufRet.i32 %8, 1
  %10 = icmp ult i32 %7, %9
  br i1 %10, label %11, label %388

; <label>:11                                      ; preds = %0
  %12 = extractvalue %dx.types.CBufRet.i32 %8, 3
  %13 = icmp eq i32 %12, 0
  br i1 %13, label %14, label %109

; <label>:14                                      ; preds = %11
  %15 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %3, i32 %7, i32 0)  ; BufferLoad(srv,index,wot)
  %16 = extractvalue %dx.types.CBufRet.i32 %8, 2
  %17 = icmp eq i32 %16, 0
  br i1 %17, label %383, label %18

; <label>:18                                      ; preds = %14
  %19 = extractvalue %dx.types.ResRet.f32 %15, 3
  %20 = extractvalue %dx.types.ResRet.f32 %15, 2
  %21 = extractvalue %dx.types.ResRet.f32 %15, 1
  %22 = extractvalue %dx.types.ResRet.f32 %15, 0
  br label %23

; <label>:23                                      ; preds = %23, %18
  %24 = phi i32 [ %105, %23 ], [ 0, %18 ]
  %25 = phi float [ %101, %23 ], [ %22, %18 ]
  %26 = phi float [ %102, %23 ], [ %21, %18 ]
  %27 = phi float [ %103, %23 ], [ %20, %18 ]
  %28 = phi float [ %104, %23 ], [ %19, %18 ]
  %29 = phi float [ %87, %23 ], [ 0.000000e+00, %18 ]
  %30 = phi float [ %88, %23 ], [ 0.000000e+00, %18 ]
  %31 = phi float [ %89, %23 ], [ 0.000000e+00, %18 ]
  %32 = phi float [ %92, %23 ], [ 0.000000e+00, %18 ]
  %33 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %34 = extractvalue %dx.types.CBufRet.f32 %33, 0
  %35 = uitofp i32 %24 to float
  %36 = fmul fast float %35, 0x3F847AE140000000
  %37 = fadd fast float %34, %36
  %38 = fmul fast float %37, %25
  %39 = call float @dx.op.unary.f32(i32 13, float %38)  ; Sin(value)
  %40 = fmul fast float %37, %26
  %41 = call float @dx.op.unary.f32(i32 12, float %40)  ; Cos(value)
  %42 = fmul fast float %41, %39
  %43 = fadd fast float %42, %29
  %44 = fmul fast float %37, %27
  %45 = call float @dx.op.unary.f32(i32 14, float %44)  ; Tan(value)
  %46 = fmul fast float %37, %28
  %47 = call float @dx.op.unary.f32(i32 17, float %46)  ; Atan(value)
  %48 = fmul fast float %47, %45
  %49 = fadd fast float %48, %30
  %50 = fmul fast float %25, 0x3FC2776C60000000
  %51 = call float @dx.op.unary.f32(i32 21, float %50)  ; Exp(value)
  %52 = call float @dx.op.unary.f32(i32 6, float %26)  ; FAbs(value)
  %53 = fadd fast float %52, 1.000000e+00
  %54 = call float @dx.op.unary.f32(i32 23, float %53)  ; Log(value)
  %55 = fmul fast float %51, 0x3FE62E4300000000
  %56 = fmul fast float %55, %54
  %57 = fadd fast float %56, %31
  %58 = call float @dx.op.unary.f32(i32 6, float %27)  ; FAbs(value)
  %59 = call float @dx.op.unary.f32(i32 23, float %58)  ; Log(value)
  %60 = fmul fast float %59, 2.500000e+00
  %61 = call float @dx.op.unary.f32(i32 21, float %60)  ; Exp(value)
  %62 = call float @dx.op.unary.f32(i32 6, float %28)  ; FAbs(value)
  %63 = fadd fast float %62, 1.000000e+00
  %64 = call float @dx.op.unary.f32(i32 24, float %63)  ; Sqrt(value)
  %65 = fmul fast float %64, %61
  %66 = fadd fast float %37, %25
  %67 = call float @dx.op.unary.f32(i32 13, float %66)  ; Sin(value)
  %68 = fadd fast float %37, %26
  %69 = call float @dx.op.unary.f32(i32 12, float %68)  ; Cos(value)
  %70 = fmul fast float %69, %67
  %71 = fmul fast float %27, 0xBFC2776C60000000
  %72 = call float @dx.op.unary.f32(i32 21, float %71)  ; Exp(value)
  %73 = fmul fast float %70, %72
  %74 = fadd fast float %37, %28
  %75 = call float @dx.op.unary.f32(i32 13, float %74)  ; Sin(value)
  %76 = call float @dx.op.unary.f32(i32 6, float %75)  ; FAbs(value)
  %77 = call float @dx.op.unary.f32(i32 23, float %76)  ; Log(value)
  %78 = fmul fast float %77, 3.000000e+00
  %79 = call float @dx.op.unary.f32(i32 21, float %78)  ; Exp(value)
  %80 = fmul fast float %26, %25
  %81 = call float @dx.op.unary.f32(i32 6, float %80)  ; FAbs(value)
  %82 = fadd fast float %81, 1.000000e+00
  %83 = call float @dx.op.unary.f32(i32 23, float %82)  ; Log(value)
  %84 = fmul fast float %79, 0x3FE62E4300000000
  %85 = fmul fast float %84, %83
  %86 = fmul fast float %85, %73
  %87 = fadd fast float %43, %73
  %88 = fadd fast float %49, %85
  %89 = fadd fast float %57, %86
  %90 = fadd fast float %65, %32
  %91 = fadd fast float %90, %73
  %92 = fadd fast float %91, %85
  %93 = fmul fast float %25, 0x3FF028F5********
  %94 = fmul fast float %26, 0x3FF028F5********
  %95 = fmul fast float %27, 0x3FF028F5********
  %96 = fmul fast float %28, 0x3FF028F5********
  %97 = fmul fast float %87, 0x3F847AE140000000
  %98 = fmul fast float %88, 0x3F847AE140000000
  %99 = fmul fast float %89, 0x3F847AE140000000
  %100 = fmul fast float %92, 0x3F847AE140000000
  %101 = fadd fast float %97, %93
  %102 = fadd fast float %98, %94
  %103 = fadd fast float %99, %95
  %104 = fadd fast float %100, %96
  %105 = add i32 %24, 1
  %106 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %107 = extractvalue %dx.types.CBufRet.i32 %106, 2
  %108 = icmp ult i32 %105, %107
  br i1 %108, label %23, label %381

; <label>:109                                     ; preds = %11
  %110 = icmp eq i32 %12, 1
  br i1 %110, label %111, label %156

; <label>:111                                     ; preds = %109
  %112 = extractvalue %dx.types.CBufRet.i32 %8, 2
  %113 = icmp eq i32 %112, 0
  br i1 %113, label %383, label %114

; <label>:114                                     ; preds = %111
  br label %115

; <label>:115                                     ; preds = %115, %114
  %116 = phi i32 [ %152, %115 ], [ 0, %114 ]
  %117 = phi float [ %141, %115 ], [ 0.000000e+00, %114 ]
  %118 = phi float [ %142, %115 ], [ 0.000000e+00, %114 ]
  %119 = phi float [ %143, %115 ], [ 0.000000e+00, %114 ]
  %120 = phi float [ %144, %115 ], [ 0.000000e+00, %114 ]
  %121 = mul i32 %7, 1664525
  %122 = add i32 %121, 1013904223
  %123 = add i32 %122, %116
  %124 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %125 = extractvalue %dx.types.CBufRet.i32 %124, 1
  %126 = urem i32 %123, %125
  %127 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %3, i32 %126, i32 0)  ; BufferLoad(srv,index,wot)
  %128 = extractvalue %dx.types.ResRet.f32 %127, 0
  %129 = extractvalue %dx.types.ResRet.f32 %127, 1
  %130 = extractvalue %dx.types.ResRet.f32 %127, 2
  %131 = extractvalue %dx.types.ResRet.f32 %127, 3
  %132 = uitofp i32 %116 to float
  %133 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %134 = extractvalue %dx.types.CBufRet.f32 %133, 0
  %135 = fadd fast float %134, %132
  %136 = call float @dx.op.unary.f32(i32 13, float %135)  ; Sin(value)
  %137 = fmul fast float %136, %128
  %138 = fmul fast float %136, %129
  %139 = fmul fast float %136, %130
  %140 = fmul fast float %136, %131
  %141 = fadd fast float %137, %117
  %142 = fadd fast float %138, %118
  %143 = fadd fast float %139, %119
  %144 = fadd fast float %140, %120
  %145 = mul i32 %126, 1103515245
  %146 = add i32 %145, 12345
  %147 = urem i32 %146, %125
  %148 = fmul fast float %141, 0x3FB99999A0000000
  %149 = fmul fast float %142, 0x3FB99999A0000000
  %150 = fmul fast float %143, 0x3FB99999A0000000
  %151 = fmul fast float %144, 0x3FB99999A0000000
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %147, i32 0, float %148, float %149, float %150, float %151, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  %152 = add i32 %116, 1
  %153 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %154 = extractvalue %dx.types.CBufRet.i32 %153, 2
  %155 = icmp ult i32 %152, %154
  br i1 %155, label %115, label %382

; <label>:156                                     ; preds = %109
  %157 = icmp eq i32 %12, 2
  br i1 %157, label %158, label %262

; <label>:158                                     ; preds = %156
  %159 = call %dx.types.Dimensions @dx.op.getDimensions(i32 72, %dx.types.Handle %4, i32 0)  ; GetDimensions(handle,mipLevel)
  %160 = extractvalue %dx.types.Dimensions %159, 0
  %161 = extractvalue %dx.types.Dimensions %159, 1
  %162 = extractvalue %dx.types.CBufRet.i32 %8, 2
  %163 = icmp eq i32 %162, 0
  br i1 %163, label %245, label %164

; <label>:164                                     ; preds = %158
  br label %165

; <label>:165                                     ; preds = %165, %164
  %166 = phi i32 [ %241, %165 ], [ 0, %164 ]
  %167 = phi float [ %237, %165 ], [ 0.000000e+00, %164 ]
  %168 = phi float [ %238, %165 ], [ 0.000000e+00, %164 ]
  %169 = phi float [ %239, %165 ], [ 0.000000e+00, %164 ]
  %170 = phi float [ %240, %165 ], [ 0.000000e+00, %164 ]
  %171 = add i32 %166, %7
  %172 = uitofp i32 %171 to float
  %173 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %174 = extractvalue %dx.types.CBufRet.i32 %173, 1
  %175 = uitofp i32 %174 to float
  %176 = fdiv fast float %172, %175
  %177 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %178 = extractvalue %dx.types.CBufRet.f32 %177, 0
  %179 = uitofp i32 %166 to float
  %180 = fadd fast float %178, %179
  %181 = call float @dx.op.unary.f32(i32 13, float %180)  ; Sin(value)
  %182 = fmul fast float %181, 5.000000e-01
  %183 = fadd fast float %182, 5.000000e-01
  %184 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %4, %dx.types.Handle %5, float %176, float %183, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %185 = extractvalue %dx.types.ResRet.f32 %184, 0
  %186 = extractvalue %dx.types.ResRet.f32 %184, 1
  %187 = extractvalue %dx.types.ResRet.f32 %184, 2
  %188 = extractvalue %dx.types.ResRet.f32 %184, 3
  %189 = fadd fast float %185, %167
  %190 = fadd fast float %186, %168
  %191 = fadd fast float %187, %169
  %192 = fadd fast float %188, %170
  %193 = fmul fast float %176, 2.000000e+00
  %194 = fadd fast float %181, 1.000000e+00
  %195 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %4, %dx.types.Handle %5, float %193, float %194, float undef, float undef, i32 0, i32 0, i32 undef, float 1.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %196 = extractvalue %dx.types.ResRet.f32 %195, 0
  %197 = extractvalue %dx.types.ResRet.f32 %195, 1
  %198 = extractvalue %dx.types.ResRet.f32 %195, 2
  %199 = extractvalue %dx.types.ResRet.f32 %195, 3
  %200 = fadd fast float %189, %196
  %201 = fadd fast float %190, %197
  %202 = fadd fast float %191, %198
  %203 = fadd fast float %192, %199
  %204 = fmul fast float %176, 4.000000e+00
  %205 = fmul fast float %181, 2.000000e+00
  %206 = fadd fast float %205, 2.000000e+00
  %207 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %4, %dx.types.Handle %5, float %204, float %206, float undef, float undef, i32 0, i32 0, i32 undef, float 2.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %208 = extractvalue %dx.types.ResRet.f32 %207, 0
  %209 = extractvalue %dx.types.ResRet.f32 %207, 1
  %210 = extractvalue %dx.types.ResRet.f32 %207, 2
  %211 = extractvalue %dx.types.ResRet.f32 %207, 3
  %212 = fadd fast float %200, %208
  %213 = fadd fast float %201, %209
  %214 = fadd fast float %202, %210
  %215 = fadd fast float %203, %211
  %216 = fmul fast float %176, 8.000000e+00
  %217 = fmul fast float %181, 4.000000e+00
  %218 = fadd fast float %217, 4.000000e+00
  %219 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %4, %dx.types.Handle %5, float %216, float %218, float undef, float undef, i32 0, i32 0, i32 undef, float 3.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %220 = extractvalue %dx.types.ResRet.f32 %219, 0
  %221 = extractvalue %dx.types.ResRet.f32 %219, 1
  %222 = extractvalue %dx.types.ResRet.f32 %219, 2
  %223 = extractvalue %dx.types.ResRet.f32 %219, 3
  %224 = fadd fast float %212, %220
  %225 = fadd fast float %213, %221
  %226 = fadd fast float %214, %222
  %227 = fadd fast float %215, %223
  %228 = uitofp i32 %160 to float
  %229 = fdiv fast float 1.000000e+00, %228
  %230 = uitofp i32 %161 to float
  %231 = fdiv fast float 1.000000e+00, %230
  %232 = call %dx.types.ResRet.f32 @dx.op.sampleGrad.f32(i32 63, %dx.types.Handle %4, %dx.types.Handle %5, float %176, float %183, float undef, float undef, i32 0, i32 0, i32 undef, float %229, float 0.000000e+00, float undef, float 0.000000e+00, float %231, float undef, float undef)  ; SampleGrad(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,ddx0,ddx1,ddx2,ddy0,ddy1,ddy2,clamp)
  %233 = extractvalue %dx.types.ResRet.f32 %232, 0
  %234 = extractvalue %dx.types.ResRet.f32 %232, 1
  %235 = extractvalue %dx.types.ResRet.f32 %232, 2
  %236 = extractvalue %dx.types.ResRet.f32 %232, 3
  %237 = fadd fast float %224, %233
  %238 = fadd fast float %225, %234
  %239 = fadd fast float %226, %235
  %240 = fadd fast float %227, %236
  %241 = add i32 %166, 1
  %242 = extractvalue %dx.types.CBufRet.i32 %173, 2
  %243 = icmp ult i32 %241, %242
  br i1 %243, label %165, label %244

; <label>:244                                     ; preds = %165
  br label %245

; <label>:245                                     ; preds = %244, %158
  %246 = phi float [ 0.000000e+00, %158 ], [ %237, %244 ]
  %247 = phi float [ 0.000000e+00, %158 ], [ %238, %244 ]
  %248 = phi float [ 0.000000e+00, %158 ], [ %239, %244 ]
  %249 = phi float [ 0.000000e+00, %158 ], [ %240, %244 ]
  %250 = udiv i32 %7, %160
  %251 = icmp ult i32 %250, %161
  br i1 %251, label %252, label %383

; <label>:252                                     ; preds = %245
  %253 = urem i32 %7, %160
  %254 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %255 = extractvalue %dx.types.CBufRet.i32 %254, 2
  %256 = mul i32 %255, 5
  %257 = uitofp i32 %256 to float
  %258 = fdiv fast float %246, %257
  %259 = fdiv fast float %247, %257
  %260 = fdiv fast float %248, %257
  %261 = fdiv fast float %249, %257
  call void @dx.op.textureStore.f32(i32 67, %dx.types.Handle %1, i32 %253, i32 %250, i32 undef, float %258, float %259, float %260, float %261, i8 15)  ; TextureStore(srv,coord0,coord1,coord2,value0,value1,value2,value3,mask)
  br label %383

; <label>:262                                     ; preds = %156
  %263 = icmp eq i32 %12, 3
  br i1 %263, label %264, label %383

; <label>:264                                     ; preds = %262
  %265 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %3, i32 %7, i32 0)  ; BufferLoad(srv,index,wot)
  %266 = extractvalue %dx.types.ResRet.f32 %265, 0
  %267 = extractvalue %dx.types.ResRet.f32 %265, 1
  %268 = extractvalue %dx.types.ResRet.f32 %265, 2
  %269 = extractvalue %dx.types.ResRet.f32 %265, 3
  %270 = extractvalue %dx.types.CBufRet.i32 %8, 2
  %271 = icmp eq i32 %270, 0
  br i1 %271, label %345, label %272

; <label>:272                                     ; preds = %264
  br label %273

; <label>:273                                     ; preds = %273, %272
  %274 = phi float [ %326, %273 ], [ 0.000000e+00, %272 ]
  %275 = phi float [ %327, %273 ], [ 0.000000e+00, %272 ]
  %276 = phi float [ %328, %273 ], [ 0.000000e+00, %272 ]
  %277 = phi float [ %329, %273 ], [ 0.000000e+00, %272 ]
  %278 = phi float [ %338, %273 ], [ %266, %272 ]
  %279 = phi float [ %339, %273 ], [ %267, %272 ]
  %280 = phi float [ %340, %273 ], [ %268, %272 ]
  %281 = phi float [ %341, %273 ], [ %269, %272 ]
  %282 = phi i32 [ %342, %273 ], [ 0, %272 ]
  %283 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %284 = extractvalue %dx.types.CBufRet.f32 %283, 0
  %285 = fadd fast float %284, %278
  %286 = call float @dx.op.unary.f32(i32 13, float %285)  ; Sin(value)
  %287 = fadd fast float %284, %279
  %288 = call float @dx.op.unary.f32(i32 12, float %287)  ; Cos(value)
  %289 = fmul fast float %288, %286
  %290 = call float @dx.op.unary.f32(i32 6, float %280)  ; FAbs(value)
  %291 = fmul fast float %290, %290
  %292 = fmul fast float %281, 0xBFC2776C60000000
  %293 = call float @dx.op.unary.f32(i32 21, float %292)  ; Exp(value)
  %294 = fmul fast float %291, %293
  %295 = fadd fast float %294, %289
  %296 = mul i32 %282, 17
  %297 = add i32 %296, %7
  %298 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %299 = extractvalue %dx.types.CBufRet.i32 %298, 1
  %300 = urem i32 %297, %299
  %301 = call %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32 68, %dx.types.Handle %3, i32 %300, i32 0)  ; BufferLoad(srv,index,wot)
  %302 = extractvalue %dx.types.ResRet.f32 %301, 0
  %303 = extractvalue %dx.types.ResRet.f32 %301, 1
  %304 = extractvalue %dx.types.ResRet.f32 %301, 2
  %305 = extractvalue %dx.types.ResRet.f32 %301, 3
  %306 = fmul fast float %295, 5.000000e-01
  %307 = fadd fast float %306, 5.000000e-01
  %308 = uitofp i32 %282 to float
  %309 = extractvalue %dx.types.CBufRet.i32 %298, 2
  %310 = uitofp i32 %309 to float
  %311 = fdiv fast float %308, %310
  %312 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %4, %dx.types.Handle %5, float %307, float %311, float undef, float undef, i32 0, i32 0, i32 undef, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %313 = extractvalue %dx.types.ResRet.f32 %312, 0
  %314 = extractvalue %dx.types.ResRet.f32 %312, 1
  %315 = extractvalue %dx.types.ResRet.f32 %312, 2
  %316 = extractvalue %dx.types.ResRet.f32 %312, 3
  %317 = fadd fast float %314, %303
  %318 = fmul fast float %317, 0x3FB99999A0000000
  %319 = fadd fast float %315, %304
  %320 = fmul fast float %319, 0x3FB99999A0000000
  %321 = fadd fast float %316, %305
  %322 = fmul fast float %321, 0x3FB99999A0000000
  %323 = fadd fast float %313, %302
  %324 = fmul fast float %323, 0x3FB99999A0000000
  %325 = fadd fast float %295, %274
  %326 = fadd fast float %325, %324
  %327 = fadd fast float %318, %275
  %328 = fadd fast float %320, %276
  %329 = fadd fast float %322, %277
  %330 = fsub fast float %326, %278
  %331 = fsub fast float %327, %279
  %332 = fsub fast float %328, %280
  %333 = fsub fast float %329, %281
  %334 = fmul fast float %330, 0x3FB99999A0000000
  %335 = fmul fast float %331, 0x3FB99999A0000000
  %336 = fmul fast float %332, 0x3FB99999A0000000
  %337 = fmul fast float %333, 0x3FB99999A0000000
  %338 = fadd fast float %334, %278
  %339 = fadd fast float %335, %279
  %340 = fadd fast float %336, %280
  %341 = fadd fast float %337, %281
  %342 = add i32 %282, 1
  %343 = icmp ult i32 %342, %309
  br i1 %343, label %273, label %344

; <label>:344                                     ; preds = %273
  br label %345

; <label>:345                                     ; preds = %344, %264
  %346 = phi float [ 0.000000e+00, %264 ], [ %326, %344 ]
  %347 = phi float [ 0.000000e+00, %264 ], [ %327, %344 ]
  %348 = phi float [ 0.000000e+00, %264 ], [ %328, %344 ]
  %349 = phi float [ 0.000000e+00, %264 ], [ %329, %344 ]
  %350 = phi float [ %266, %264 ], [ %338, %344 ]
  %351 = phi float [ %267, %264 ], [ %339, %344 ]
  %352 = phi float [ %268, %264 ], [ %340, %344 ]
  %353 = phi float [ %269, %264 ], [ %341, %344 ]
  %354 = call float @dx.op.dot4.f32(i32 56, float %346, float %347, float %348, float %349, float %346, float %347, float %348, float %349)  ; Dot4(ax,ay,az,aw,bx,by,bz,bw)
  %355 = call float @dx.op.unary.f32(i32 25, float %354)  ; Rsqrt(value)
  %356 = fmul fast float %350, %350
  %357 = fmul fast float %351, %351
  %358 = fadd fast float %357, %356
  %359 = fmul fast float %352, %352
  %360 = fadd fast float %358, %359
  %361 = fmul fast float %353, %353
  %362 = fadd fast float %360, %361
  %363 = call float @dx.op.unary.f32(i32 24, float %362)  ; Sqrt(value)
  %364 = fmul fast float %363, %355
  %365 = fmul fast float %364, %346
  %366 = fmul fast float %364, %347
  %367 = fmul fast float %364, %348
  %368 = fmul fast float %364, %349
  %369 = fmul fast float %365, 1.000000e+01
  %370 = call float @dx.op.unary.f32(i32 13, float %369)  ; Sin(value)
  %371 = fmul fast float %366, 1.000000e+01
  %372 = call float @dx.op.unary.f32(i32 12, float %371)  ; Cos(value)
  %373 = fmul fast float %367, 5.000000e+00
  %374 = call float @dx.op.unary.f32(i32 14, float %373)  ; Tan(value)
  %375 = fmul fast float %368, 5.000000e+00
  %376 = call float @dx.op.unary.f32(i32 17, float %375)  ; Atan(value)
  %377 = fadd fast float %365, %370
  %378 = fadd fast float %372, %366
  %379 = fadd fast float %374, %367
  %380 = fadd fast float %376, %368
  br label %383

; <label>:381                                     ; preds = %23
  br label %383

; <label>:382                                     ; preds = %115
  br label %383

; <label>:383                                     ; preds = %382, %381, %345, %262, %252, %245, %111, %14
  %384 = phi float [ %246, %252 ], [ %246, %245 ], [ %377, %345 ], [ 0.000000e+00, %262 ], [ 0.000000e+00, %14 ], [ 0.000000e+00, %111 ], [ %87, %381 ], [ %141, %382 ]
  %385 = phi float [ %247, %252 ], [ %247, %245 ], [ %378, %345 ], [ 0.000000e+00, %262 ], [ 0.000000e+00, %14 ], [ 0.000000e+00, %111 ], [ %88, %381 ], [ %142, %382 ]
  %386 = phi float [ %248, %252 ], [ %248, %245 ], [ %379, %345 ], [ 0.000000e+00, %262 ], [ 0.000000e+00, %14 ], [ 0.000000e+00, %111 ], [ %89, %381 ], [ %143, %382 ]
  %387 = phi float [ %249, %252 ], [ %249, %245 ], [ %380, %345 ], [ 0.000000e+00, %262 ], [ 0.000000e+00, %14 ], [ 0.000000e+00, %111 ], [ %92, %381 ], [ %144, %382 ]
  call void @dx.op.bufferStore.f32(i32 69, %dx.types.Handle %2, i32 %7, i32 0, float %384, float %385, float %386, float %387, i8 15)  ; BufferStore(uav,coord0,coord1,value0,value1,value2,value3,mask)
  br label %388

; <label>:388                                     ; preds = %383, %0
  ret void
}

; Function Attrs: nounwind readnone
declare i32 @dx.op.threadId.i32(i32, i32) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Dimensions @dx.op.getDimensions(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleGrad.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float, float, float, float, float, float, float) #1

; Function Attrs: nounwind
declare void @dx.op.textureStore.f32(i32, %dx.types.Handle, i32, i32, i32, float, float, float, float, i8) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot4.f32(i32, float, float, float, float, float, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #1

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.bufferLoad.f32(i32, %dx.types.Handle, i32, i32) #1

; Function Attrs: nounwind
declare void @dx.op.bufferStore.f32(i32, %dx.types.Handle, i32, i32, float, float, float, float, i8) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind readonly }
attributes #2 = { nounwind }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.entryPoints = !{!17}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"cs", i32 6, i32 1}
!4 = !{!5, !8, !13, !15}
!5 = !{!6}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{!9, !11, !12}
!9 = !{i32 0, %"class.RWStructuredBuffer<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 12, i1 false, i1 false, i1 false, !10}
!10 = !{i32 1, i32 16}
!11 = !{i32 1, %"class.RWStructuredBuffer<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 12, i1 false, i1 false, i1 false, !10}
!12 = !{i32 2, %"class.RWTexture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i1 false, i1 false, i1 false, !7}
!13 = !{!14}
!14 = !{i32 0, %StressTestParams* undef, !"", i32 0, i32 0, i32 1, i32 32, null}
!15 = !{!16}
!16 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!17 = !{void ()* @main, !"main", null, !4, !18}
!18 = !{i32 0, i64 16, i32 4, !19}
!19 = !{i32 64, i32 1, i32 1}
