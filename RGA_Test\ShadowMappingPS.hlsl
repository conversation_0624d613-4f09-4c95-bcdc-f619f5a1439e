// Shadow Mapping Pixel Shader
// Tests shadow map sampling and PCF filtering

cbuffer Material : register(b0)
{
    float4 DiffuseColor;
    float4 SpecularColor;
    float SpecularPower;
    float3 AmbientColor;
    float ShadowBias;
    float ShadowStrength;
    int PCFSamples;
};

Texture2D DiffuseTexture : register(t0);
Texture2D ShadowMap : register(t1);
SamplerState LinearSampler : register(s0);
SamplerComparisonState ShadowSampler : register(s1);

struct PSInput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float4 Color : TEXCOORD3;
    float4 LightSpacePos : TEXCOORD4;
    float3 LightDir : TEXCOORD5;
    float3 ViewDir : TEXCOORD6;
    float Depth : TEXCOORD7;
};

float calculateShadow(float4 lightSpacePos, float bias)
{
    // Perspective divide
    float3 projCoords = lightSpacePos.xyz / lightSpacePos.w;
    
    // Transform to [0,1] range
    projCoords.xy = projCoords.xy * 0.5 + 0.5;
    projCoords.y = 1.0 - projCoords.y; // Flip Y for DirectX
    
    // Check if position is outside shadow map
    if (projCoords.x < 0.0 || projCoords.x > 1.0 || 
        projCoords.y < 0.0 || projCoords.y > 1.0)
        return 0.0;
    
    float currentDepth = projCoords.z;
    
    // PCF (Percentage Closer Filtering)
    float shadow = 0.0;
    float2 texelSize = 1.0 / float2(2048.0, 2048.0); // Assuming 2048x2048 shadow map
    
    int halfSamples = PCFSamples / 2;
    for(int x = -halfSamples; x <= halfSamples; ++x)
    {
        for(int y = -halfSamples; y <= halfSamples; ++y)
        {
            float2 offset = float2(x, y) * texelSize;
            shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, 
                projCoords.xy + offset, currentDepth - bias);
        }
    }
    
    shadow /= float((PCFSamples + 1) * (PCFSamples + 1));
    return shadow;
}

float4 main(PSInput input) : SV_TARGET
{
    // Normalize interpolated vectors
    float3 normal = normalize(input.Normal);
    float3 lightDir = normalize(input.LightDir);
    float3 viewDir = normalize(input.ViewDir);
    
    // Sample diffuse texture
    float4 texColor = DiffuseTexture.Sample(LinearSampler, input.TexCoord);
    
    // Calculate shadow factor
    float shadow = calculateShadow(input.LightSpacePos, ShadowBias);
    shadow = lerp(ShadowStrength, 1.0, shadow);
    
    // Ambient lighting
    float3 ambient = AmbientColor * texColor.rgb;
    
    // Diffuse lighting
    float NdotL = max(0.0, dot(normal, lightDir));
    float3 diffuse = DiffuseColor.rgb * texColor.rgb * NdotL * shadow;
    
    // Specular lighting (Blinn-Phong)
    float3 halfDir = normalize(lightDir + viewDir);
    float NdotH = max(0.0, dot(normal, halfDir));
    float3 specular = SpecularColor.rgb * pow(NdotH, SpecularPower) * shadow;
    
    // Combine lighting
    float3 finalColor = ambient + diffuse + specular;
    
    return float4(finalColor, texColor.a * DiffuseColor.a);
}
