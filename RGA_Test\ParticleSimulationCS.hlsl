// Particle Simulation Compute Shader
// Tests compute shader performance with particle physics

cbuffer SimulationParams : register(b0)
{
    float DeltaTime;
    float3 Gravity;
    float3 WindForce;
    float Damping;
    uint ParticleCount;
    float3 BoundsMin;
    float3 BoundsMax;
    float GroundHeight;
    float Restitution;
};

struct Particle
{
    float3 Position;
    float Mass;
    float3 Velocity;
    float Life;
    float3 Force;
    float Size;
    float4 Color;
};

RWStructuredBuffer<Particle> ParticleBuffer : register(u0);

[numthreads(64, 1, 1)]
void main(uint3 id : SV_DispatchThreadID)
{
    uint index = id.x;
    if (index >= ParticleCount)
        return;
    
    Particle particle = ParticleBuffer[index];
    
    // Skip dead particles
    if (particle.Life <= 0.0)
        return;
    
    // Apply forces
    float3 totalForce = particle.Force;
    totalForce += Gravity * particle.Mass;
    totalForce += WindForce;
    
    // Update velocity using Verlet integration
    float3 acceleration = totalForce / particle.Mass;
    particle.Velocity += acceleration * DeltaTime;
    particle.Velocity *= Damping;
    
    // Update position
    particle.Position += particle.Velocity * DeltaTime;
    
    // Collision detection with ground
    if (particle.Position.y <= GroundHeight)
    {
        particle.Position.y = GroundHeight;
        particle.Velocity.y = -particle.Velocity.y * Restitution;
        
        // Apply friction
        particle.Velocity.x *= 0.8;
        particle.Velocity.z *= 0.8;
    }
    
    // Collision detection with bounds
    if (particle.Position.x < BoundsMin.x || particle.Position.x > BoundsMax.x)
    {
        particle.Velocity.x = -particle.Velocity.x * Restitution;
        particle.Position.x = clamp(particle.Position.x, BoundsMin.x, BoundsMax.x);
    }
    
    if (particle.Position.z < BoundsMin.z || particle.Position.z > BoundsMax.z)
    {
        particle.Velocity.z = -particle.Velocity.z * Restitution;
        particle.Position.z = clamp(particle.Position.z, BoundsMin.z, BoundsMax.z);
    }
    
    // Update life
    particle.Life -= DeltaTime;
    
    // Update color based on life (fade out)
    float lifeRatio = saturate(particle.Life / 5.0); // Assuming max life is 5 seconds
    particle.Color.a = lifeRatio;
    
    // Update size based on life
    particle.Size = lerp(0.1, 1.0, lifeRatio);
    
    // Reset force for next frame
    particle.Force = float3(0, 0, 0);
    
    // Write back to buffer
    ParticleBuffer[index] = particle;
}
