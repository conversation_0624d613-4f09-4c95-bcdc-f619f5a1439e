"""
DXIL Analyzer - A library for analyzing DXIL assembly and binary files.

This package provides tools to parse DXIL files, extract computational instructions,
and analyze data types used in shader computations.
"""

__version__ = "1.0.0"
__author__ = "DXIL Analyzer Team"

from .dxil_parser import DXILParser
from .instruction_analyzer import InstructionAnalyzer
from .output_formatter import OutputFormatter

__all__ = [
    "DXILParser",
    "InstructionAnalyzer", 
    "OutputFormatter"
]
