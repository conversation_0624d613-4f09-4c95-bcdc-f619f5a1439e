struct SAMPLER_1D { Texture1D tex; SamplerState smp; };
struct SAMPLER_2D { Texture2D tex; SamplerState smp; };
struct SAMPLER_3D { Texture3D tex; SamplerState smp; };
struct SAMPLER_Cube { TextureCube tex; SamplerState smp; };
struct SAMPLER_1D_CMP { Texture1D tex; SamplerComparisonState smp; };
struct SAMPLER_2D_CMP { Texture2D tex; SamplerComparisonState smp; };
struct SAMPLER_Cube_CMP { TextureCube tex; SamplerComparisonState smp; };
struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float3 Tangent : TANGENT;
  float3 Bitangent : BITANGENT;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float4 Color : TEXCOORD5;
  float3 ViewDir : TEXCOORD6;
  float4 LightSpacePos : TEXCOORD7;
};

cbuffer PerFrame : register(b0)
{
  uniform float4x4 ViewProjectionMatrix;
  uniform float4x4 WorldMatrix;
  uniform float4x4 NormalMatrix;
  uniform float4x4 LightSpaceMatrix;
  uniform float3 CameraPosition;
  uniform float Time;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float4 worldPos = mul(float4(input.Position, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  output.Position = mul(worldPos, ViewProjectionMatrix);
  output.Normal = normalize(mul(input.Normal, (float3x3)NormalMatrix));
  output.Tangent = normalize(mul(input.Tangent, (float3x3)NormalMatrix));
  output.Bitangent = normalize(mul(input.Bitangent, (float3x3)NormalMatrix));
  output.TexCoord = input.TexCoord;
  output.Color = input.Color;
  output.ViewDir = normalize(CameraPosition - output.WorldPos);
  output.LightSpacePos = mul(worldPos, LightSpaceMatrix);
  return output;
}

