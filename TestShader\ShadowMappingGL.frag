#version 320 es

// Shadow Mapping Fragment Shader - OpenGL ES Version
// Tests shadow map sampling and PCF filtering

precision highp float;
precision highp sampler2DShadow;

in vec3 vWorldPos;
in vec3 vNormal;
in vec2 vTexCoord;
in vec4 vColor;
in vec4 vLightSpacePos;
in vec3 vLightDir;
in vec3 vViewDir;
in float vDepth;

out vec4 fragColor;

uniform vec4 uDiffuseColor;
uniform vec4 uSpecularColor;
uniform float uSpecularPower;
uniform vec3 uAmbientColor;
uniform float uShadowBias;
uniform float uShadowStrength;
uniform int uPCFSamples;

uniform sampler2D uDiffuseTexture;
uniform sampler2DShadow uShadowMap;

float calculateShadow(vec4 lightSpacePos, float bias)
{
    // Perspective divide
    vec3 projCoords = lightSpacePos.xyz / lightSpacePos.w;
    
    // Transform to [0,1] range
    projCoords = projCoords * 0.5 + 0.5;
    
    // Check if position is outside shadow map
    if (projCoords.x < 0.0 || projCoords.x > 1.0 || 
        projCoords.y < 0.0 || projCoords.y > 1.0)
        return 0.0;
    
    float currentDepth = projCoords.z;
    
    // PCF (Percentage Closer Filtering)
    float shadow = 0.0;
    vec2 texelSize = 1.0 / vec2(2048.0); // Assuming 2048x2048 shadow map
    
    int halfSamples = uPCFSamples / 2;
    for(int x = -halfSamples; x <= halfSamples; ++x)
    {
        for(int y = -halfSamples; y <= halfSamples; ++y)
        {
            vec2 offset = vec2(float(x), float(y)) * texelSize;
            shadow += texture(uShadowMap, vec3(projCoords.xy + offset, currentDepth - bias));
        }
    }
    
    shadow /= float((uPCFSamples + 1) * (uPCFSamples + 1));
    return shadow;
}

void main()
{
    // Normalize interpolated vectors
    vec3 normal = normalize(vNormal);
    vec3 lightDir = normalize(vLightDir);
    vec3 viewDir = normalize(vViewDir);
    
    // Sample diffuse texture
    vec4 texColor = texture(uDiffuseTexture, vTexCoord);
    
    // Calculate shadow factor
    float shadow = calculateShadow(vLightSpacePos, uShadowBias);
    shadow = mix(uShadowStrength, 1.0, shadow);
    
    // Ambient lighting
    vec3 ambient = uAmbientColor * texColor.rgb;
    
    // Diffuse lighting
    float NdotL = max(0.0, dot(normal, lightDir));
    vec3 diffuse = uDiffuseColor.rgb * texColor.rgb * NdotL * shadow;
    
    // Specular lighting (Blinn-Phong)
    vec3 halfDir = normalize(lightDir + viewDir);
    float NdotH = max(0.0, dot(normal, halfDir));
    vec3 specular = uSpecularColor.rgb * pow(NdotH, uSpecularPower) * shadow;
    
    // Combine lighting
    vec3 finalColor = ambient + diffuse + specular;
    
    fragColor = vec4(finalColor, texColor.a * uDiffuseColor.a);
}
