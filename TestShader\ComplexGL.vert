#version 320 es

// Vertex attributes
layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;
layout(location = 3) in vec3 aTangent;
layout(location = 4) in vec3 aBitangent;
layout(location = 5) in vec4 aColor;

// Uniform matrices
uniform mat4 uModelMatrix;
uniform mat4 uViewMatrix;
uniform mat4 uProjectionMatrix;
uniform mat4 uNormalMatrix;
uniform mat4 uLightSpaceMatrix;

// Material uniforms
uniform float uTime;
uniform vec3 uCameraPos;

// Vertex shader outputs
out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;
out vec3 vTangent;
out vec3 vBitangent;
out vec4 vColor;
out vec3 vViewDir;
out vec4 vLightSpacePos;
out float vFogFactor;

// Animation uniforms
uniform float uWaveAmplitude;
uniform float uWaveFrequency;
uniform vec3 uWindDirection;

void main() {
    // Calculate world position
    vec4 worldPos = uModelMatrix * vec4(aPosition, 1.0);
    
    // Apply vertex animation (wave effect)
    float wave = sin(uTime * uWaveFrequency + worldPos.x * 0.1 + worldPos.z * 0.1) * uWaveAmplitude;
    worldPos.y += wave;
    
    // Wind effect on vertices
    vec3 windOffset = uWindDirection * sin(uTime * 2.0 + worldPos.x * 0.05) * 0.1;
    worldPos.xyz += windOffset;
    
    vWorldPos = worldPos.xyz;
    
    // Transform normal, tangent, and bitangent to world space
    vNormal = normalize((uNormalMatrix * vec4(aNormal, 0.0)).xyz);
    vTangent = normalize((uNormalMatrix * vec4(aTangent, 0.0)).xyz);
    vBitangent = normalize((uNormalMatrix * vec4(aBitangent, 0.0)).xyz);
    
    // Pass through texture coordinates and vertex color
    vTexCoord = aTexCoord;
    vColor = aColor;
    
    // Calculate view direction
    vViewDir = normalize(uCameraPos - vWorldPos);
    
    // Calculate position in light space for shadow mapping
    vLightSpacePos = uLightSpaceMatrix * worldPos;
    
    // Calculate fog factor based on distance from camera
    float distance = length(uCameraPos - vWorldPos);
    vFogFactor = exp(-distance * 0.01);
    vFogFactor = clamp(vFogFactor, 0.0, 1.0);
    
    // Final position transformation
    gl_Position = uProjectionMatrix * uViewMatrix * worldPos;
}
