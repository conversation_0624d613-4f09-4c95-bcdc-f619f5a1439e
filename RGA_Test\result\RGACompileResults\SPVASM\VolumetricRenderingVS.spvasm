; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 54
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Vertex %main "main" %in_var_POSITION %in_var_TEXCOORD0 %gl_Position %out_var_TEXCOORD0 %out_var_TEXCOORD1 %out_var_TEXCOORD2 %out_var_TEXCOORD3
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "WorldMatrix"
               OpMemberName %type_PerFrame 1 "ViewMatrix"
               OpMemberName %type_PerFrame 2 "ProjectionMatrix"
               OpMemberName %type_PerFrame 3 "InverseViewProjectionMatrix"
               OpMemberName %type_PerFrame 4 "CameraPosition"
               OpMemberName %type_PerFrame 5 "VolumeMin"
               OpMemberName %type_PerFrame 6 "VolumeMax"
               OpMemberName %type_PerFrame 7 "Time"
               OpName %PerFrame "PerFrame"
               OpName %in_var_POSITION "in.var.POSITION"
               OpName %in_var_TEXCOORD0 "in.var.TEXCOORD0"
               OpName %out_var_TEXCOORD0 "out.var.TEXCOORD0"
               OpName %out_var_TEXCOORD1 "out.var.TEXCOORD1"
               OpName %out_var_TEXCOORD2 "out.var.TEXCOORD2"
               OpName %out_var_TEXCOORD3 "out.var.TEXCOORD3"
               OpName %main "main"
               OpDecorate %gl_Position BuiltIn Position
               OpDecorate %in_var_POSITION Location 0
               OpDecorate %in_var_TEXCOORD0 Location 1
               OpDecorate %out_var_TEXCOORD0 Location 0
               OpDecorate %out_var_TEXCOORD1 Location 1
               OpDecorate %out_var_TEXCOORD2 Location 2
               OpDecorate %out_var_TEXCOORD3 Location 3
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 3 MatrixStride 16
               OpMemberDecorate %type_PerFrame 3 RowMajor
               OpMemberDecorate %type_PerFrame 4 Offset 256
               OpMemberDecorate %type_PerFrame 5 Offset 272
               OpMemberDecorate %type_PerFrame 6 Offset 288
               OpMemberDecorate %type_PerFrame 7 Offset 300
               OpDecorate %type_PerFrame Block
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
      %int_2 = OpConstant %int 2
      %int_1 = OpConstant %int 1
      %int_4 = OpConstant %int 4
    %v4float = OpTypeVector %float 4
%mat4v4float = OpTypeMatrix %v4float 4
    %v3float = OpTypeVector %float 3
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %mat4v4float %v3float %v3float %v3float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%_ptr_Input_v3float = OpTypePointer Input %v3float
    %v2float = OpTypeVector %float 2
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Output_v4float = OpTypePointer Output %v4float
%_ptr_Output_v2float = OpTypePointer Output %v2float
%_ptr_Output_v3float = OpTypePointer Output %v3float
       %void = OpTypeVoid
         %30 = OpTypeFunction %void
%_ptr_Uniform_mat4v4float = OpTypePointer Uniform %mat4v4float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%in_var_POSITION = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD0 = OpVariable %_ptr_Input_v2float Input
%gl_Position = OpVariable %_ptr_Output_v4float Output
%out_var_TEXCOORD0 = OpVariable %_ptr_Output_v2float Output
%out_var_TEXCOORD1 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD2 = OpVariable %_ptr_Output_v3float Output
%out_var_TEXCOORD3 = OpVariable %_ptr_Output_v3float Output
       %main = OpFunction %void None %30
         %33 = OpLabel
         %34 = OpLoad %v3float %in_var_POSITION
         %35 = OpLoad %v2float %in_var_TEXCOORD0
         %36 = OpCompositeExtract %float %34 0
         %37 = OpCompositeExtract %float %34 1
         %38 = OpCompositeExtract %float %34 2
         %39 = OpCompositeConstruct %v4float %36 %37 %38 %float_1
         %40 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_0
         %41 = OpLoad %mat4v4float %40
         %42 = OpMatrixTimesVector %v4float %41 %39
         %43 = OpVectorShuffle %v3float %42 %42 0 1 2
         %44 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_1
         %45 = OpLoad %mat4v4float %44
         %46 = OpMatrixTimesVector %v4float %45 %42
         %47 = OpAccessChain %_ptr_Uniform_mat4v4float %PerFrame %int_2
         %48 = OpLoad %mat4v4float %47
         %49 = OpMatrixTimesVector %v4float %48 %46
         %50 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_4
         %51 = OpLoad %v3float %50
         %52 = OpFSub %v3float %43 %51
         %53 = OpExtInst %v3float %1 Normalize %52
               OpStore %gl_Position %49
               OpStore %out_var_TEXCOORD0 %35
               OpStore %out_var_TEXCOORD1 %43
               OpStore %out_var_TEXCOORD2 %53
               OpStore %out_var_TEXCOORD3 %51
               OpReturn
               OpFunctionEnd
