// Unnamed technique, shader MeshViewerCS
/*$(ShaderResources)*/

float3 AdjustVertexColor(float3 color, float brightness)
{
    // 简单亮度调整，可自定义
    return saturate(color * brightness);
}

/*$(_compute:csmain)*/(uint3 DTid : SV_DispatchThreadID)
{
    uint vertexID = DTid.x;
    Struct_VertexBuffer vertex = VertexBuffer[vertexID];

    // 调用颜色调整函数，亮度系数可修改
    vertex.Color = AdjustVertexColor(vertex.Color, 1.2f);

    VertexBuffer[vertexID] = vertex;
}

/*
Shader Resources:
	Buffer VertexBuffer (as UAV)
*/
