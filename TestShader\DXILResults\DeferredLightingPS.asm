;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xy          1     NONE   float   xy  
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: e829904aa502a5799eee8382a1bf6ea2
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 2
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 2
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer LightingParams
; {
;
;   struct LightingParams
;   {
;
;       float3 CameraPosition;                        ; Offset:    0
;       int NumLights;                                ; Offset:   12
;       float3 AmbientColor;                          ; Offset:   16
;       float AmbientStrength;                        ; Offset:   28
;   
;   } LightingParams;                                 ; Offset:    0 Size:    32
;
; }
;
; cbuffer LightData
; {
;
;   struct LightData
;   {
;
;       float4 LightPositions[32];                    ; Offset:    0
;       float4 LightColors[32];                       ; Offset:  512
;       float4 LightDirections[32];                   ; Offset: 1024
;       int4 LightTypes[8];                           ; Offset: 1536
;   
;   } LightData;                                      ; Offset:    0 Size:  1664
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; LightingParams                    cbuffer      NA          NA     CB0            cb0     1
; LightData                         cbuffer      NA          NA     CB1            cb1     1
; PointSampler                      sampler      NA          NA      S0             s0     1
; GBufferAlbedo                     texture     f32          2d      T0             t0     1
; GBufferNormal                     texture     f32          2d      T1             t1     1
; GBufferWorldPos                   texture     f32          2d      T2             t2     1
; GBufferEmissive                   texture     f32          2d      T3             t3     1
;
;
; ViewId state:
;
; Number of inputs: 6, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 4, 5 }
;   output 1 depends on inputs: { 4, 5 }
;   output 2 depends on inputs: { 4, 5 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%"class.Texture2D<vector<float, 4> >" = type { <4 x float>, %"class.Texture2D<vector<float, 4> >::mips_type" }
%"class.Texture2D<vector<float, 4> >::mips_type" = type { i32 }
%LightingParams = type { <3 x float>, i32, <3 x float>, float }
%LightData = type { [32 x <4 x float>], [32 x <4 x float>], [32 x <4 x float>], [8 x <4 x i32>] }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 3, i32 3, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 2, i32 2, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %6 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %7 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %8 = alloca [4 x i32], align 4
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 1, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %4, %dx.types.Handle %5, float %9, float %10, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %12 = extractvalue %dx.types.ResRet.f32 %11, 0
  %13 = extractvalue %dx.types.ResRet.f32 %11, 1
  %14 = extractvalue %dx.types.ResRet.f32 %11, 2
  %15 = extractvalue %dx.types.ResRet.f32 %11, 3
  %16 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %3, %dx.types.Handle %5, float %9, float %10, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %17 = extractvalue %dx.types.ResRet.f32 %16, 0
  %18 = extractvalue %dx.types.ResRet.f32 %16, 1
  %19 = extractvalue %dx.types.ResRet.f32 %16, 2
  %20 = extractvalue %dx.types.ResRet.f32 %16, 3
  %21 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %2, %dx.types.Handle %5, float %9, float %10, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %22 = extractvalue %dx.types.ResRet.f32 %21, 0
  %23 = extractvalue %dx.types.ResRet.f32 %21, 1
  %24 = extractvalue %dx.types.ResRet.f32 %21, 2
  %25 = extractvalue %dx.types.ResRet.f32 %21, 3
  %26 = call %dx.types.ResRet.f32 @dx.op.sample.f32(i32 60, %dx.types.Handle %1, %dx.types.Handle %5, float %9, float %10, float undef, float undef, i32 0, i32 0, i32 undef, float undef)  ; Sample(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,clamp)
  %27 = extractvalue %dx.types.ResRet.f32 %26, 0
  %28 = extractvalue %dx.types.ResRet.f32 %26, 1
  %29 = extractvalue %dx.types.ResRet.f32 %26, 2
  %30 = fmul fast float %17, 2.000000e+00
  %31 = fmul fast float %18, 2.000000e+00
  %32 = fmul fast float %19, 2.000000e+00
  %33 = fadd fast float %30, -1.000000e+00
  %34 = fadd fast float %31, -1.000000e+00
  %35 = fadd fast float %32, -1.000000e+00
  %36 = call float @dx.op.dot3.f32(i32 55, float %33, float %34, float %35, float %33, float %34, float %35)  ; Dot3(ax,ay,az,bx,by,bz)
  %37 = call float @dx.op.unary.f32(i32 25, float %36)  ; Rsqrt(value)
  %38 = fmul fast float %37, %33
  %39 = fmul fast float %37, %34
  %40 = fmul fast float %37, %35
  %41 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %42 = extractvalue %dx.types.CBufRet.f32 %41, 0
  %43 = extractvalue %dx.types.CBufRet.f32 %41, 1
  %44 = extractvalue %dx.types.CBufRet.f32 %41, 2
  %45 = fsub fast float %42, %22
  %46 = fsub fast float %43, %23
  %47 = fsub fast float %44, %24
  %48 = call float @dx.op.dot3.f32(i32 55, float %45, float %46, float %47, float %45, float %46, float %47)  ; Dot3(ax,ay,az,bx,by,bz)
  %49 = call float @dx.op.unary.f32(i32 25, float %48)  ; Rsqrt(value)
  %50 = fmul fast float %45, %49
  %51 = fmul fast float %46, %49
  %52 = fmul fast float %47, %49
  %53 = fadd fast float %12, 0xBFA47AE140000000
  %54 = fadd fast float %13, 0xBFA47AE140000000
  %55 = fadd fast float %14, 0xBFA47AE140000000
  %56 = fmul fast float %53, %15
  %57 = fmul fast float %54, %15
  %58 = fmul fast float %55, %15
  %59 = fadd fast float %56, 0x3FA47AE140000000
  %60 = fadd fast float %57, 0x3FA47AE140000000
  %61 = fadd fast float %58, 0x3FA47AE140000000
  %62 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %63 = extractvalue %dx.types.CBufRet.i32 %62, 3
  %64 = icmp sgt i32 %63, 0
  br i1 %64, label %65, label %280

; <label>:65                                      ; preds = %0
  br label %66

; <label>:66                                      ; preds = %182, %65
  %67 = phi float [ %185, %182 ], [ undef, %65 ]
  %68 = phi float [ %184, %182 ], [ undef, %65 ]
  %69 = phi float [ %183, %182 ], [ undef, %65 ]
  %70 = phi i32 [ %273, %182 ], [ 0, %65 ]
  %71 = phi float [ %272, %182 ], [ 0.000000e+00, %65 ]
  %72 = phi float [ %271, %182 ], [ 0.000000e+00, %65 ]
  %73 = phi float [ %270, %182 ], [ 0.000000e+00, %65 ]
  %74 = sdiv i32 %70, 4
  %75 = srem i32 %70, 4
  %76 = add nsw i32 %74, 96
  %77 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %6, i32 %76)  ; CBufferLoadLegacy(handle,regIndex)
  %78 = extractvalue %dx.types.CBufRet.i32 %77, 0
  %79 = extractvalue %dx.types.CBufRet.i32 %77, 1
  %80 = extractvalue %dx.types.CBufRet.i32 %77, 2
  %81 = extractvalue %dx.types.CBufRet.i32 %77, 3
  %82 = getelementptr inbounds [4 x i32], [4 x i32]* %8, i32 0, i32 0
  store i32 %78, i32* %82, align 4
  %83 = getelementptr inbounds [4 x i32], [4 x i32]* %8, i32 0, i32 1
  store i32 %79, i32* %83, align 4
  %84 = getelementptr inbounds [4 x i32], [4 x i32]* %8, i32 0, i32 2
  store i32 %80, i32* %84, align 4
  %85 = getelementptr inbounds [4 x i32], [4 x i32]* %8, i32 0, i32 3
  store i32 %81, i32* %85, align 4
  %86 = getelementptr inbounds [4 x i32], [4 x i32]* %8, i32 0, i32 %75
  %87 = load i32, i32* %86, align 4, !tbaa !27
  %88 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 %70)  ; CBufferLoadLegacy(handle,regIndex)
  %89 = extractvalue %dx.types.CBufRet.f32 %88, 0
  %90 = extractvalue %dx.types.CBufRet.f32 %88, 1
  %91 = extractvalue %dx.types.CBufRet.f32 %88, 2
  %92 = extractvalue %dx.types.CBufRet.f32 %88, 3
  %93 = add nuw nsw i32 %70, 32
  %94 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 %93)  ; CBufferLoadLegacy(handle,regIndex)
  %95 = extractvalue %dx.types.CBufRet.f32 %94, 0
  %96 = extractvalue %dx.types.CBufRet.f32 %94, 1
  %97 = extractvalue %dx.types.CBufRet.f32 %94, 2
  %98 = extractvalue %dx.types.CBufRet.f32 %94, 3
  %99 = icmp eq i32 %87, 0
  br i1 %99, label %100, label %120

; <label>:100                                     ; preds = %66
  %101 = fsub fast float %89, %22
  %102 = fsub fast float %90, %23
  %103 = fsub fast float %91, %24
  %104 = call float @dx.op.dot3.f32(i32 55, float %101, float %102, float %103, float %101, float %102, float %103)  ; Dot3(ax,ay,az,bx,by,bz)
  %105 = call float @dx.op.unary.f32(i32 25, float %104)  ; Rsqrt(value)
  %106 = fmul fast float %105, %101
  %107 = fmul fast float %105, %102
  %108 = fmul fast float %105, %103
  %109 = fmul fast float %101, %101
  %110 = fmul fast float %102, %102
  %111 = fadd fast float %109, %110
  %112 = fmul fast float %103, %103
  %113 = fadd fast float %111, %112
  %114 = call float @dx.op.unary.f32(i32 24, float %113)  ; Sqrt(value)
  %115 = fmul fast float %114, %114
  %116 = fmul fast float %92, %92
  %117 = fdiv fast float %115, %116
  %118 = fadd fast float %117, 1.000000e+00
  %119 = fdiv fast float 1.000000e+00, %118
  br label %182

; <label>:120                                     ; preds = %66
  %121 = icmp eq i32 %87, 1
  br i1 %121, label %122, label %136

; <label>:122                                     ; preds = %120
  %123 = add nuw nsw i32 %70, 64
  %124 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 %123)  ; CBufferLoadLegacy(handle,regIndex)
  %125 = extractvalue %dx.types.CBufRet.f32 %124, 0
  %126 = extractvalue %dx.types.CBufRet.f32 %124, 1
  %127 = extractvalue %dx.types.CBufRet.f32 %124, 2
  %128 = fsub fast float -0.000000e+00, %125
  %129 = fsub fast float -0.000000e+00, %126
  %130 = fsub fast float -0.000000e+00, %127
  %131 = call float @dx.op.dot3.f32(i32 55, float %128, float %129, float %130, float %128, float %129, float %130)  ; Dot3(ax,ay,az,bx,by,bz)
  %132 = call float @dx.op.unary.f32(i32 25, float %131)  ; Rsqrt(value)
  %133 = fmul fast float %132, %128
  %134 = fmul fast float %132, %129
  %135 = fmul fast float %132, %130
  br label %182

; <label>:136                                     ; preds = %120
  %137 = icmp eq i32 %87, 2
  br i1 %137, label %138, label %182

; <label>:138                                     ; preds = %136
  %139 = fsub fast float %89, %22
  %140 = fsub fast float %90, %23
  %141 = fsub fast float %91, %24
  %142 = call float @dx.op.dot3.f32(i32 55, float %139, float %140, float %141, float %139, float %140, float %141)  ; Dot3(ax,ay,az,bx,by,bz)
  %143 = call float @dx.op.unary.f32(i32 25, float %142)  ; Rsqrt(value)
  %144 = fmul fast float %143, %139
  %145 = fmul fast float %143, %140
  %146 = fmul fast float %143, %141
  %147 = fmul fast float %139, %139
  %148 = fmul fast float %140, %140
  %149 = fadd fast float %147, %148
  %150 = fmul fast float %141, %141
  %151 = fadd fast float %149, %150
  %152 = call float @dx.op.unary.f32(i32 24, float %151)  ; Sqrt(value)
  %153 = fmul fast float %152, %152
  %154 = fmul fast float %92, %92
  %155 = fdiv fast float %153, %154
  %156 = fadd fast float %155, 1.000000e+00
  %157 = fdiv fast float 1.000000e+00, %156
  %158 = add nuw nsw i32 %70, 64
  %159 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %6, i32 %158)  ; CBufferLoadLegacy(handle,regIndex)
  %160 = extractvalue %dx.types.CBufRet.f32 %159, 0
  %161 = extractvalue %dx.types.CBufRet.f32 %159, 1
  %162 = extractvalue %dx.types.CBufRet.f32 %159, 2
  %163 = call float @dx.op.dot3.f32(i32 55, float %160, float %161, float %162, float %160, float %161, float %162)  ; Dot3(ax,ay,az,bx,by,bz)
  %164 = call float @dx.op.unary.f32(i32 25, float %163)  ; Rsqrt(value)
  %165 = extractvalue %dx.types.CBufRet.f32 %159, 3
  %166 = fmul fast float %160, %164
  %167 = fsub fast float -0.000000e+00, %166
  %168 = fmul fast float %161, %164
  %169 = fsub fast float -0.000000e+00, %168
  %170 = fmul fast float %162, %164
  %171 = fsub fast float -0.000000e+00, %170
  %172 = call float @dx.op.dot3.f32(i32 55, float %144, float %145, float %146, float %167, float %169, float %171)  ; Dot3(ax,ay,az,bx,by,bz)
  %173 = call float @dx.op.unary.f32(i32 12, float %165)  ; Cos(value)
  %174 = fmul fast float %165, 0x3FF3333340000000
  %175 = call float @dx.op.unary.f32(i32 12, float %174)  ; Cos(value)
  %176 = fsub fast float %173, %175
  %177 = fsub fast float %172, %175
  %178 = fdiv fast float %177, %176
  %179 = call float @dx.op.binary.f32(i32 35, float %178, float 0.000000e+00)  ; FMax(a,b)
  %180 = call float @dx.op.binary.f32(i32 36, float %179, float 1.000000e+00)  ; FMin(a,b)
  %181 = fmul fast float %180, %157
  br label %182

; <label>:182                                     ; preds = %138, %136, %122, %100
  %183 = phi float [ %106, %100 ], [ %133, %122 ], [ %144, %138 ], [ %69, %136 ]
  %184 = phi float [ %107, %100 ], [ %134, %122 ], [ %145, %138 ], [ %68, %136 ]
  %185 = phi float [ %108, %100 ], [ %135, %122 ], [ %146, %138 ], [ %67, %136 ]
  %186 = phi float [ %119, %100 ], [ 1.000000e+00, %122 ], [ %181, %138 ], [ 1.000000e+00, %136 ]
  %187 = fadd fast float %183, %50
  %188 = fadd fast float %184, %51
  %189 = fadd fast float %185, %52
  %190 = call float @dx.op.dot3.f32(i32 55, float %187, float %188, float %189, float %187, float %188, float %189)  ; Dot3(ax,ay,az,bx,by,bz)
  %191 = call float @dx.op.unary.f32(i32 25, float %190)  ; Rsqrt(value)
  %192 = fmul fast float %191, %187
  %193 = fmul fast float %191, %188
  %194 = fmul fast float %191, %189
  %195 = fmul fast float %20, %20
  %196 = fmul fast float %195, %195
  %197 = call float @dx.op.dot3.f32(i32 55, float %38, float %39, float %40, float %192, float %193, float %194)  ; Dot3(ax,ay,az,bx,by,bz)
  %198 = call float @dx.op.binary.f32(i32 35, float %197, float 0.000000e+00)  ; FMax(a,b)
  %199 = fadd fast float %196, -1.000000e+00
  %200 = fmul fast float %198, %198
  %201 = fmul fast float %200, %199
  %202 = fadd fast float %201, 1.000000e+00
  %203 = fmul fast float %202, %202
  %204 = fmul fast float %203, 0x400921FB60000000
  %205 = fdiv fast float %196, %204
  %206 = call float @dx.op.dot3.f32(i32 55, float %38, float %39, float %40, float %50, float %51, float %52)  ; Dot3(ax,ay,az,bx,by,bz)
  %207 = call float @dx.op.binary.f32(i32 35, float %206, float 0.000000e+00)  ; FMax(a,b)
  %208 = call float @dx.op.dot3.f32(i32 55, float %38, float %39, float %40, float %183, float %184, float %185)  ; Dot3(ax,ay,az,bx,by,bz)
  %209 = call float @dx.op.binary.f32(i32 35, float %208, float 0.000000e+00)  ; FMax(a,b)
  %210 = fadd fast float %20, 1.000000e+00
  %211 = fmul fast float %210, %210
  %212 = fmul fast float %211, 1.250000e-01
  %213 = fsub fast float 1.000000e+00, %212
  %214 = fmul fast float %207, %213
  %215 = fadd fast float %214, %212
  %216 = fdiv fast float %207, %215
  %217 = fmul fast float %209, %213
  %218 = fadd fast float %217, %212
  %219 = fdiv fast float %209, %218
  %220 = call float @dx.op.dot3.f32(i32 55, float %192, float %193, float %194, float %50, float %51, float %52)  ; Dot3(ax,ay,az,bx,by,bz)
  %221 = call float @dx.op.binary.f32(i32 35, float %220, float 0.000000e+00)  ; FMax(a,b)
  %222 = fsub fast float 0x3FEEB851E0000000, %56
  %223 = fsub fast float 0x3FEEB851E0000000, %57
  %224 = fsub fast float 0x3FEEB851E0000000, %58
  %225 = fsub fast float 1.000000e+00, %221
  %226 = call float @dx.op.binary.f32(i32 35, float %225, float 0.000000e+00)  ; FMax(a,b)
  %227 = call float @dx.op.binary.f32(i32 36, float %226, float 1.000000e+00)  ; FMin(a,b)
  %228 = call float @dx.op.unary.f32(i32 23, float %227)  ; Log(value)
  %229 = fmul fast float %228, 5.000000e+00
  %230 = call float @dx.op.unary.f32(i32 21, float %229)  ; Exp(value)
  %231 = fmul fast float %230, %222
  %232 = fmul fast float %230, %223
  %233 = fmul fast float %230, %224
  %234 = fadd fast float %59, %231
  %235 = fadd fast float %60, %232
  %236 = fadd fast float %61, %233
  %237 = fsub fast float 1.000000e+00, %234
  %238 = fsub fast float 1.000000e+00, %235
  %239 = fsub fast float 1.000000e+00, %236
  %240 = fsub fast float 1.000000e+00, %15
  %241 = fmul fast float %216, %205
  %242 = fmul fast float %241, %219
  %243 = fmul fast float %234, %242
  %244 = fmul fast float %235, %242
  %245 = fmul fast float %236, %242
  %246 = fmul fast float %207, 4.000000e+00
  %247 = fmul fast float %246, %209
  %248 = fadd fast float %247, 0x3F1A36E2E0000000
  %249 = fdiv fast float %243, %248
  %250 = fdiv fast float %244, %248
  %251 = fdiv fast float %245, %248
  %252 = fmul fast float %240, 0x3FD45F3060000000
  %253 = fmul fast float %252, %12
  %254 = fmul fast float %253, %237
  %255 = fmul fast float %252, %13
  %256 = fmul fast float %255, %238
  %257 = fmul fast float %252, %14
  %258 = fmul fast float %257, %239
  %259 = fadd fast float %254, %249
  %260 = fadd fast float %256, %250
  %261 = fadd fast float %258, %251
  %262 = fmul fast float %186, %98
  %263 = fmul fast float %209, %262
  %264 = fmul fast float %263, %95
  %265 = fmul fast float %264, %259
  %266 = fmul fast float %263, %96
  %267 = fmul fast float %266, %260
  %268 = fmul fast float %263, %97
  %269 = fmul fast float %268, %261
  %270 = fadd fast float %265, %73
  %271 = fadd fast float %267, %72
  %272 = fadd fast float %269, %71
  %273 = add nuw nsw i32 %70, 1
  %274 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %7, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %275 = extractvalue %dx.types.CBufRet.i32 %274, 3
  %276 = icmp slt i32 %273, %275
  %277 = icmp slt i32 %273, 32
  %278 = and i1 %277, %276
  br i1 %278, label %66, label %279

; <label>:279                                     ; preds = %182
  br label %280

; <label>:280                                     ; preds = %279, %0
  %281 = phi float [ 0.000000e+00, %0 ], [ %272, %279 ]
  %282 = phi float [ 0.000000e+00, %0 ], [ %271, %279 ]
  %283 = phi float [ 0.000000e+00, %0 ], [ %270, %279 ]
  %284 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %7, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %285 = extractvalue %dx.types.CBufRet.f32 %284, 0
  %286 = extractvalue %dx.types.CBufRet.f32 %284, 1
  %287 = extractvalue %dx.types.CBufRet.f32 %284, 2
  %288 = extractvalue %dx.types.CBufRet.f32 %284, 3
  %289 = fmul fast float %288, %25
  %290 = fmul fast float %285, %12
  %291 = fmul fast float %290, %289
  %292 = fmul fast float %286, %13
  %293 = fmul fast float %292, %289
  %294 = fmul fast float %287, %14
  %295 = fmul fast float %294, %289
  %296 = fadd fast float %283, %27
  %297 = fadd fast float %296, %291
  %298 = fadd fast float %282, %28
  %299 = fadd fast float %298, %293
  %300 = fadd fast float %281, %29
  %301 = fadd fast float %300, %295
  %302 = fadd fast float %297, 1.000000e+00
  %303 = fadd fast float %299, 1.000000e+00
  %304 = fadd fast float %301, 1.000000e+00
  %305 = fdiv fast float %297, %302
  %306 = fdiv fast float %299, %303
  %307 = fdiv fast float %301, %304
  %308 = call float @dx.op.unary.f32(i32 23, float %305)  ; Log(value)
  %309 = call float @dx.op.unary.f32(i32 23, float %306)  ; Log(value)
  %310 = call float @dx.op.unary.f32(i32 23, float %307)  ; Log(value)
  %311 = fmul fast float %308, 0x3FDD1745E0000000
  %312 = fmul fast float %309, 0x3FDD1745E0000000
  %313 = fmul fast float %310, 0x3FDD1745E0000000
  %314 = call float @dx.op.unary.f32(i32 21, float %311)  ; Exp(value)
  %315 = call float @dx.op.unary.f32(i32 21, float %312)  ; Exp(value)
  %316 = call float @dx.op.unary.f32(i32 21, float %313)  ; Exp(value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %314)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %315)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %316)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float 1.000000e+00)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sample.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!16}
!dx.entryPoints = !{!17}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !11, !14}
!5 = !{!6, !8, !9, !10}
!6 = !{i32 0, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 2, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 2, i32 0, !7}
!9 = !{i32 2, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 2, i32 1, i32 2, i32 0, !7}
!10 = !{i32 3, %"class.Texture2D<vector<float, 4> >"* undef, !"", i32 0, i32 3, i32 1, i32 2, i32 0, !7}
!11 = !{!12, !13}
!12 = !{i32 0, %LightingParams* undef, !"", i32 0, i32 0, i32 1, i32 32, null}
!13 = !{i32 1, %LightData* undef, !"", i32 0, i32 1, i32 1, i32 1664, null}
!14 = !{!15}
!15 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!16 = !{[8 x i32] [i32 6, i32 4, i32 0, i32 0, i32 0, i32 0, i32 7, i32 7]}
!17 = !{void ()* @main, !"main", !18, !4, null}
!18 = !{!19, !24, null}
!19 = !{!20, !22}
!20 = !{i32 0, !"SV_Position", i8 9, i8 3, !21, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!21 = !{i32 0}
!22 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 1, i8 2, i32 1, i8 0, !23}
!23 = !{i32 3, i32 3}
!24 = !{!25}
!25 = !{i32 0, !"SV_Target", i8 9, i8 16, !21, i8 0, i32 1, i8 4, i32 0, i8 0, !26}
!26 = !{i32 3, i32 15}
!27 = !{!28, !28, i64 0}
!28 = !{!"int", !29, i64 0}
!29 = !{!"omnipotent char", !30, i64 0}
!30 = !{!"Simple C/C++ TBAA"}
