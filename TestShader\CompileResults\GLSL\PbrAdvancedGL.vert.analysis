﻿MALI ANALYSIS SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Vertex  Main shader ===========  Position variant ----------------  Work registers: 32 (100% used at 100% occupancy) Uniform registers: 102 (79% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    4.13   23.00    0.00       LS Shortest path cycles:        1.29    1.00    0.00        A Longest path cycles:         4.13   22.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Varying variant ---------------  Work registers: 55 (85% used at 50% occupancy) Uniform registers: 94 (73% used) Stack use: false 16-bit arithmetic: 0%                                  A      LS       T    Bound Total instruction cycles:    6.13   46.00    0.00       LS Shortest path cycles:        2.47   22.00    0.00       LS Longest path cycles:         6.08   42.00    0.00       LS  A = Arithmetic, LS = Load/Store, T = Texture  Shader properties =================  Has uniform computation: true  Recommended attribute streams =============================  Position attributes  - aPosition (location=0)  - aNormal (location=1)  - aTangent (location=2)  - aBitangent (location=3)  - aBoneWeights (location=6)  - aBoneIndices (location=7)  Non-position attributes  - aTexCoord (location=4)  - aColor (location=5) 
