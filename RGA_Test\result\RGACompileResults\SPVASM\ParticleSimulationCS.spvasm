; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 168
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint GLCompute %main "main" %gl_GlobalInvocationID
               OpExecutionMode %main LocalSize 8 8 1
               OpSource HLSL 600
               OpName %type_SimulationParams "type.SimulationParams"
               OpMemberName %type_SimulationParams 0 "DeltaTime"
               OpMemberName %type_SimulationParams 1 "Gravity"
               OpMemberName %type_SimulationParams 2 "WindForce"
               OpMemberName %type_SimulationParams 3 "Damping"
               OpMemberName %type_SimulationParams 4 "ParticleCount"
               OpMemberName %type_SimulationParams 5 "BoundsMin"
               OpMemberName %type_SimulationParams 6 "BoundsMax"
               OpMemberName %type_SimulationParams 7 "GroundHeight"
               OpMemberName %type_SimulationParams 8 "Restitution"
               OpName %SimulationParams "SimulationParams"
               OpName %type_RWStructuredBuffer_Particle "type.RWStructuredBuffer.Particle"
               OpName %Particle "Particle"
               OpMemberName %Particle 0 "Position"
               OpMemberName %Particle 1 "Mass"
               OpMemberName %Particle 2 "Velocity"
               OpMemberName %Particle 3 "Life"
               OpMemberName %Particle 4 "Force"
               OpMemberName %Particle 5 "Size"
               OpMemberName %Particle 6 "Color"
               OpName %ParticleBuffer "ParticleBuffer"
               OpName %main "main"
               OpDecorate %gl_GlobalInvocationID BuiltIn GlobalInvocationId
               OpDecorate %SimulationParams DescriptorSet 0
               OpDecorate %SimulationParams Binding 0
               OpDecorate %ParticleBuffer DescriptorSet 0
               OpDecorate %ParticleBuffer Binding 0
               OpMemberDecorate %type_SimulationParams 0 Offset 0
               OpMemberDecorate %type_SimulationParams 1 Offset 4
               OpMemberDecorate %type_SimulationParams 2 Offset 16
               OpMemberDecorate %type_SimulationParams 3 Offset 28
               OpMemberDecorate %type_SimulationParams 4 Offset 32
               OpMemberDecorate %type_SimulationParams 5 Offset 36
               OpMemberDecorate %type_SimulationParams 6 Offset 48
               OpMemberDecorate %type_SimulationParams 7 Offset 60
               OpMemberDecorate %type_SimulationParams 8 Offset 64
               OpDecorate %type_SimulationParams Block
               OpMemberDecorate %Particle 0 Offset 0
               OpMemberDecorate %Particle 1 Offset 12
               OpMemberDecorate %Particle 2 Offset 16
               OpMemberDecorate %Particle 3 Offset 28
               OpMemberDecorate %Particle 4 Offset 32
               OpMemberDecorate %Particle 5 Offset 44
               OpMemberDecorate %Particle 6 Offset 48
               OpDecorate %_runtimearr_Particle ArrayStride 64
               OpMemberDecorate %type_RWStructuredBuffer_Particle 0 Offset 0
               OpDecorate %type_RWStructuredBuffer_Particle BufferBlock
        %int = OpTypeInt 32 1
      %int_0 = OpConstant %int 0
      %int_4 = OpConstant %int 4
      %int_3 = OpConstant %int 3
      %float = OpTypeFloat 32
    %float_0 = OpConstant %float 0
      %int_1 = OpConstant %int 1
      %int_2 = OpConstant %int 2
      %int_7 = OpConstant %int 7
       %uint = OpTypeInt 32 0
      %int_8 = OpConstant %int 8
%float_0_800000012 = OpConstant %float 0.800000012
     %uint_0 = OpConstant %uint 0
      %int_5 = OpConstant %int 5
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
      %int_6 = OpConstant %int 6
    %float_1 = OpConstant %float 1
%float_0_100000001 = OpConstant %float 0.100000001
    %v3float = OpTypeVector %float 3
         %30 = OpConstantComposite %v3float %float_0 %float_0 %float_0
%type_SimulationParams = OpTypeStruct %float %v3float %v3float %float %uint %v3float %v3float %float %float
%_ptr_Uniform_type_SimulationParams = OpTypePointer Uniform %type_SimulationParams
    %v4float = OpTypeVector %float 4
   %Particle = OpTypeStruct %v3float %float %v3float %float %v3float %float %v4float
%_runtimearr_Particle = OpTypeRuntimeArray %Particle
%type_RWStructuredBuffer_Particle = OpTypeStruct %_runtimearr_Particle
%_ptr_Uniform_type_RWStructuredBuffer_Particle = OpTypePointer Uniform %type_RWStructuredBuffer_Particle
     %v3uint = OpTypeVector %uint 3
%_ptr_Input_v3uint = OpTypePointer Input %v3uint
       %void = OpTypeVoid
         %37 = OpTypeFunction %void
%_ptr_Uniform_uint = OpTypePointer Uniform %uint
%_ptr_Uniform_Particle = OpTypePointer Uniform %Particle
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_float = OpTypePointer Uniform %float
%SimulationParams = OpVariable %_ptr_Uniform_type_SimulationParams Uniform
%ParticleBuffer = OpVariable %_ptr_Uniform_type_RWStructuredBuffer_Particle Uniform
%gl_GlobalInvocationID = OpVariable %_ptr_Input_v3uint Input
%float_0_200000003 = OpConstant %float 0.200000003
     %uint_1 = OpConstant %uint 1
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
     %uint_4 = OpConstant %uint 4
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
     %uint_6 = OpConstant %uint 6
       %main = OpFunction %void None %37
         %49 = OpLabel
         %50 = OpLoad %v3uint %gl_GlobalInvocationID
               OpSelectionMerge %51 None
               OpSwitch %uint_0 %52
         %52 = OpLabel
         %53 = OpCompositeExtract %uint %50 0
         %54 = OpAccessChain %_ptr_Uniform_uint %SimulationParams %int_4
         %55 = OpLoad %uint %54
         %56 = OpUGreaterThanEqual %bool %53 %55
               OpSelectionMerge %57 None
               OpBranchConditional %56 %58 %57
         %58 = OpLabel
               OpBranch %51
         %57 = OpLabel
         %59 = OpAccessChain %_ptr_Uniform_Particle %ParticleBuffer %int_0 %53
         %60 = OpAccessChain %_ptr_Uniform_v3float %59 %uint_0
         %61 = OpLoad %v3float %60
         %62 = OpAccessChain %_ptr_Uniform_float %59 %uint_1
         %63 = OpLoad %float %62
         %64 = OpAccessChain %_ptr_Uniform_v3float %59 %uint_2
         %65 = OpLoad %v3float %64
         %66 = OpAccessChain %_ptr_Uniform_float %59 %uint_3
         %67 = OpLoad %float %66
         %68 = OpAccessChain %_ptr_Uniform_v3float %59 %uint_4
         %69 = OpLoad %v3float %68
         %70 = OpAccessChain %_ptr_Uniform_v4float %59 %uint_6
         %71 = OpLoad %v4float %70
         %72 = OpFOrdLessThanEqual %bool %67 %float_0
               OpSelectionMerge %73 None
               OpBranchConditional %72 %74 %73
         %74 = OpLabel
               OpBranch %51
         %73 = OpLabel
         %75 = OpAccessChain %_ptr_Uniform_v3float %SimulationParams %int_1
         %76 = OpLoad %v3float %75
         %77 = OpVectorTimesScalar %v3float %76 %63
         %78 = OpFAdd %v3float %69 %77
         %79 = OpAccessChain %_ptr_Uniform_v3float %SimulationParams %int_2
         %80 = OpLoad %v3float %79
         %81 = OpFAdd %v3float %78 %80
         %82 = OpCompositeConstruct %v3float %63 %63 %63
         %83 = OpFDiv %v3float %81 %82
         %84 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_0
         %85 = OpLoad %float %84
         %86 = OpVectorTimesScalar %v3float %83 %85
         %87 = OpFAdd %v3float %65 %86
         %88 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_3
         %89 = OpLoad %float %88
         %90 = OpVectorTimesScalar %v3float %87 %89
         %91 = OpVectorTimesScalar %v3float %90 %85
         %92 = OpFAdd %v3float %61 %91
         %93 = OpCompositeExtract %float %92 1
         %94 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_7
         %95 = OpLoad %float %94
         %96 = OpFOrdLessThanEqual %bool %93 %95
               OpSelectionMerge %97 None
               OpBranchConditional %96 %98 %97
         %98 = OpLabel
         %99 = OpCompositeInsert %v3float %95 %92 1
        %100 = OpCompositeExtract %float %90 1
        %101 = OpFNegate %float %100
        %102 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_8
        %103 = OpLoad %float %102
        %104 = OpFMul %float %101 %103
        %105 = OpCompositeExtract %float %90 0
        %106 = OpFMul %float %105 %float_0_800000012
        %107 = OpCompositeExtract %float %90 2
        %108 = OpFMul %float %107 %float_0_800000012
        %109 = OpCompositeConstruct %v3float %106 %104 %108
               OpBranch %97
         %97 = OpLabel
        %110 = OpPhi %v3float %90 %73 %109 %98
        %111 = OpPhi %v3float %92 %73 %99 %98
        %112 = OpCompositeExtract %float %111 0
        %113 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_5 %int_0
        %114 = OpLoad %float %113
        %115 = OpFOrdLessThan %bool %112 %114
        %116 = OpLogicalNot %bool %115
               OpSelectionMerge %117 None
               OpBranchConditional %116 %118 %117
        %118 = OpLabel
        %119 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_6 %int_0
        %120 = OpLoad %float %119
        %121 = OpFOrdGreaterThan %bool %112 %120
               OpBranch %117
        %117 = OpLabel
        %122 = OpPhi %bool %true %97 %121 %118
               OpSelectionMerge %123 None
               OpBranchConditional %122 %124 %123
        %124 = OpLabel
        %125 = OpCompositeExtract %float %110 0
        %126 = OpFNegate %float %125
        %127 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_8
        %128 = OpLoad %float %127
        %129 = OpFMul %float %126 %128
        %130 = OpCompositeInsert %v3float %129 %110 0
        %131 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_6 %int_0
        %132 = OpLoad %float %131
        %133 = OpExtInst %float %1 FClamp %112 %114 %132
        %134 = OpCompositeInsert %v3float %133 %111 0
               OpBranch %123
        %123 = OpLabel
        %135 = OpPhi %v3float %110 %117 %130 %124
        %136 = OpPhi %v3float %111 %117 %134 %124
        %137 = OpCompositeExtract %float %136 2
        %138 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_5 %int_2
        %139 = OpLoad %float %138
        %140 = OpFOrdLessThan %bool %137 %139
        %141 = OpLogicalNot %bool %140
               OpSelectionMerge %142 None
               OpBranchConditional %141 %143 %142
        %143 = OpLabel
        %144 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_6 %int_2
        %145 = OpLoad %float %144
        %146 = OpFOrdGreaterThan %bool %137 %145
               OpBranch %142
        %142 = OpLabel
        %147 = OpPhi %bool %true %123 %146 %143
               OpSelectionMerge %148 None
               OpBranchConditional %147 %149 %148
        %149 = OpLabel
        %150 = OpCompositeExtract %float %135 2
        %151 = OpFNegate %float %150
        %152 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_8
        %153 = OpLoad %float %152
        %154 = OpFMul %float %151 %153
        %155 = OpCompositeInsert %v3float %154 %135 2
        %156 = OpAccessChain %_ptr_Uniform_float %SimulationParams %int_6 %int_2
        %157 = OpLoad %float %156
        %158 = OpExtInst %float %1 FClamp %137 %139 %157
        %159 = OpCompositeInsert %v3float %158 %136 2
               OpBranch %148
        %148 = OpLabel
        %160 = OpPhi %v3float %136 %142 %159 %149
        %161 = OpPhi %v3float %135 %142 %155 %149
        %162 = OpFSub %float %67 %85
        %163 = OpFMul %float %162 %float_0_200000003
        %164 = OpExtInst %float %1 FClamp %163 %float_0 %float_1
        %165 = OpCompositeInsert %v4float %164 %71 3
        %166 = OpExtInst %float %1 FMix %float_0_100000001 %float_1 %164
        %167 = OpCompositeConstruct %Particle %160 %63 %161 %162 %30 %166 %165
               OpStore %59 %167
               OpBranch %51
         %51 = OpLabel
               OpReturn
               OpFunctionEnd
