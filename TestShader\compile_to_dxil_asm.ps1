# Compile all HLSL shaders to DXIL format
Write-Host "Compiling all HLSL shaders to DXIL..." -ForegroundColor Green

$dxilOutputDir = "DXILResults"
$logFile = "DXILResults\compilation.log"

# Create output directory if it doesn't exist
if (-not (Test-Path $dxilOutputDir)) {
    New-Item -ItemType Directory -Path $dxilOutputDir -Force | Out-Null
}

# Initialize log file
"=== DXIL Compilation Log ===" | Out-File -FilePath $logFile -Encoding UTF8
"Start time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Get all HLSL shader files from current directory
$hlslFiles = Get-ChildItem -Path "." -Filter "*.hlsl"

Write-Host "Found $($hlslFiles.Count) HLSL files" -ForegroundColor Cyan

# Initialize counters
$totalFiles = $hlslFiles.Count
$successCount = 0
$failureCount = 0

# Compile HLSL files to DXIL
Write-Host "`nCompiling HLSL files to DXIL..." -ForegroundColor Yellow
foreach ($file in $hlslFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor White
    
    # Log current file processing
    "Processing: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8

    # Determine shader profile and entry point based on filename
    $profile = ""
    $entryPoint = "main"

    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $profile = "vs_6_1"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $profile = "ps_6_1"
        $entryPoint = "psmain"  # Use psmain for pixel shaders as in your example
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $profile = "cs_6_1"
    } elseif ($file.Name -like "*GS*" -or $file.Name -like "*Geometry*") {
        $profile = "gs_6_1"
    } elseif ($file.Name -like "*HS*" -or $file.Name -like "*Hull*") {
        $profile = "hs_6_1"
    } elseif ($file.Name -like "*DS*" -or $file.Name -like "*Domain*") {
        $profile = "ds_6_1"
    } else {
        $profile = "vs_6_1"  # Default fallback to vertex shader
    }

    # Define output files
    $dxilFile = Join-Path $dxilOutputDir "$($file.BaseName).dxil"
    $asmFile = Join-Path $dxilOutputDir "$($file.BaseName).asm"

    try {
        # Compile to DXIL with assembly output
        # Using the command format: dxc.exe -T ps_6_1 -E psmain -Fo output.dxil -Fc output.asm YourShader.hlsl
        $result = & dxc -T $profile -E $entryPoint -Fo $dxilFile -Fc $asmFile $file.FullName 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - DXIL saved to $dxilFile" -ForegroundColor Green
            "  DXIL Compilation: SUCCESS (Profile: $profile, Entry: $entryPoint)" | Out-File -FilePath $logFile -Append -Encoding UTF8
            $successCount++
        } else {
            # Try with default main entry point if failed
            Write-Host "  Retrying with 'main' entry point..." -ForegroundColor Yellow
            $result2 = & dxc -T $profile -E "main" -Fo $dxilFile -Fc $asmFile $file.FullName 2>&1

            if ($LASTEXITCODE -eq 0) {
                Write-Host "  [OK] SUCCESS (main entry point) - DXIL saved to $dxilFile" -ForegroundColor Green
                "  DXIL Compilation: SUCCESS (Profile: $profile, Entry: main)" | Out-File -FilePath $logFile -Append -Encoding UTF8
                $successCount++
            } else {
                # Try without entry point specification
                Write-Host "  Retrying without entry point..." -ForegroundColor Yellow
                $result3 = & dxc -T $profile -Fo $dxilFile -Fc $asmFile $file.FullName 2>&1

                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  [OK] SUCCESS (no entry point) - DXIL saved to $dxilFile" -ForegroundColor Green
                    "  DXIL Compilation: SUCCESS (Profile: $profile, no entry point)" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    $successCount++
                } else {
                    Write-Host "  [FAIL] FAILED" -ForegroundColor Red
                    "  DXIL Compilation: FAILED (Profile: $profile)" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    "    Error details: Multiple entry point attempts failed" | Out-File -FilePath $logFile -Append -Encoding UTF8
                    $failureCount++
                }
            }
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "  DXIL Compilation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        $failureCount++
    }
    
    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Final log entry with statistics
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Statistics ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Total files processed: $totalFiles" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Successful compilations: $successCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Failed compilations: $failureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Success rate: $(if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"End time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Complete ===" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Display statistics
Write-Host "`n=== Compilation Statistics ===" -ForegroundColor Yellow
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "Successful compilations: $successCount" -ForegroundColor Green
Write-Host "Failed compilations: $failureCount" -ForegroundColor $(if ($failureCount -gt 0) { "Red" } else { "Green" })
$successRate = if ($totalFiles -gt 0) { [math]::Round(($successCount / $totalFiles) * 100, 2) } else { 0 }
Write-Host "Success rate: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host "`n[DONE] DXIL compilation complete! Results saved in DXILResults\" -ForegroundColor Green
Write-Host "  - DXIL binary files (.dxil): DXILResults\" -ForegroundColor Cyan
Write-Host "  - Assembly files (.asm): DXILResults\" -ForegroundColor Cyan
Write-Host "  - Compilation log: DXILResults\compilation.log" -ForegroundColor Cyan

Write-Host "`nGenerated files:" -ForegroundColor Yellow
Write-Host "  - .dxil files: DirectX Intermediate Language binary format" -ForegroundColor White
Write-Host "  - .asm files: Human-readable assembly disassembly" -ForegroundColor White
Write-Host "`nNote: DXIL files can be used with DirectX 12 and are compatible with GPU debugging tools." -ForegroundColor Green
