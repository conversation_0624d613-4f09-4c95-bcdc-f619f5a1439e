// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct MaterialData
{
  float4 Albedo;
  float Metallic;
  float Roughness;
  float AO;
  float Emission;
  float4 TilingOffset;
  uint TextureFlags;
  float _padding;
};

struct LightData
{
  float3 Position;
  float Range;
  float3 Direction;
  float SpotAngle;
  float3 Color;
  float Intensity;
  uint Type;
  float3 _padding;
};

struct PSInput
{
  float4 Position : SV_POSITION;
  float3 WorldPos : TEXCOORD0;
  float3 Normal : TEXCOORD1;
  float3 Tangent : TEXCOORD2;
  float3 Bitangent : TEXCOORD3;
  float2 TexCoord : TEXCOORD4;
  float2 LightmapUV : TEXCOORD5;
  float4 Color : TEXCOORD6;
  float3 ViewDir : TEXCOORD7;
  float4 PrevClipPos : TEXCOORD8;
  float4 CurrClipPos : TEXCOORD9;
  nointerpolation uint MaterialID : TEXCOORD10;
  float LODFade : TEXCOORD11;
};

struct PSOutput
{
  float4 Color : SV_Target0;
  float4 Normal : SV_Target1;
  float4 MotionVector : SV_Target2;
  float4 MaterialProps : SV_Target3;
};

cbuffer PerFrame : register(b0)
{
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 ViewProjectionMatrix;
  float3 CameraPosition;
  float Time;
  float DeltaTime;
  uint FrameCount;
  float2 ScreenResolution;
}

cbuffer LightingParams : register(b1)
{
  uint NumLights;
  float AmbientIntensity;
  float3 AmbientColor;
  float ShadowBias;
  float ShadowNormalBias;
  float2 ShadowMapSize;
  float PCFRadius;
}

StructuredBuffer<MaterialData> Materials : register(t0);
StructuredBuffer<LightData> Lights : register(t1);
Texture2DArray AlbedoTextures : register(t2);
Texture2DArray NormalTextures : register(t3);
Texture2DArray MetallicRoughnessTextures : register(t4);
Texture2DArray AOTextures : register(t5);
Texture2DArray EmissionTextures : register(t6);
Texture2D LightmapTexture : register(t7);
Texture2D ShadowMap : register(t9);
SamplerState LinearSampler : register(s0);
SamplerComparisonState ShadowSampler : register(s2);
float3 GetNormalFromMap(float3 normalMap, float3 normal, float3 tangent, float3 bitangent)
{
  normalMap = (normalMap * 2.0f) - 1.0f;
  float3x3 TBN = float3x3(tangent, bitangent, normal);
  return normalize(mul(normalMap, TBN));
}

float DistributionGGX(float3 N, float3 H, float roughness)
{
  float a = (roughness * roughness);
  float a2 = (a * a);
  float NdotH = max(dot(N, H), 0.0f);
  float NdotH2 = (NdotH * NdotH);
  float num = a2;
  float denom = (NdotH2 * a2 - 1.0f) + 1.0f;
  denom = ((3.14159265359f * denom) * denom);
  return num / denom;
}

float GeometrySchlickGGX(float NdotV, float roughness)
{
  float r = roughness + 1.0f;
  float k = (r * r) / 8.0f;
  float num = NdotV;
  float denom = (NdotV * 1.0f - k) + k;
  return num / denom;
}

float GeometrySmith(float3 N, float3 V, float3 L, float roughness)
{
  float NdotV = max(dot(N, V), 0.0f);
  float NdotL = max(dot(N, L), 0.0f);
  float ggx2 = GeometrySchlickGGX(NdotV, roughness);
  float ggx1 = GeometrySchlickGGX(NdotL, roughness);
  return (ggx1 * ggx2);
}

float3 FresnelSchlick(float cosTheta, float3 F0)
{
  return F0 + (1.0f - F0 * pow(clamp(1.0f - cosTheta, 0.0f, 1.0f), 5.0f));
}

float SampleShadowMap(float3 worldPos, float3 normal, float3 lightDir)
{
  float4 lightSpacePos = mul(float4(worldPos, 1.0f), ViewProjectionMatrix);
  float3 projCoords = lightSpacePos.xyz / lightSpacePos.w;
  projCoords = (projCoords * 0.5f) + 0.5f;
  if (projCoords.x < 0.0f || projCoords.x > 1.0f || projCoords.y < 0.0f || projCoords.y > 1.0f)
    return 1.0f;
  float bias = max((ShadowNormalBias * 1.0f - dot(normal, lightDir)), ShadowBias);
  float currentDepth = projCoords.z - bias;
  float shadow = 0.0f;
  float2 texelSize = 1.0f / ShadowMapSize;
  for (int x = -1; x <= 1; (++x))
  {
    for (int y = -1; y <= 1; (++y))
    {
      float2 offset = ((float2(x, y) * texelSize) * PCFRadius);
      shadow += ShadowMap.SampleCmpLevelZero(ShadowSampler, projCoords.xy + offset, currentDepth);
    }
  }
  return shadow / 9.0f;
}

float3 CalculatePBRLighting(PSInput input, MaterialData material, float3 albedo, float3 normal, float metallic, float roughness, float ao)
{
  float3 V = normalize(input.ViewDir);
  float3 N = normal;
  float3 F0 = lerp(float3(0.04f, 0.04f, 0.04f), albedo, metallic);
  float3 Lo = float3(0.0f, 0.0f, 0.0f);
  for (uint i = 0; i < NumLights; (++i))
  {
    LightData light = Lights[i];
    float3 L;
    float attenuation = 1.0f;
    if (light.Type == 0)
    {
      L = normalize((-light.Direction));
    }
    else if (light.Type == 1)
    {
      L = normalize(light.Position - input.WorldPos);
      float distance = length(light.Position - input.WorldPos);
      attenuation = 1.0f / 1.0f + (0.09f * distance) + ((0.032f * distance) * distance);
    }
    else if (light.Type == 2)
    {
      L = normalize(light.Position - input.WorldPos);
      float distance = length(light.Position - input.WorldPos);
      float theta = dot(L, normalize((-light.Direction)));
      float epsilon = cos(light.SpotAngle) - cos((light.SpotAngle * 1.2f));
      float intensity = clamp(theta - cos((light.SpotAngle * 1.2f)) / epsilon, 0.0f, 1.0f);
      attenuation = intensity / 1.0f + (0.09f * distance) + ((0.032f * distance) * distance);
    }
    float3 H = normalize(V + L);
    float3 radiance = ((light.Color * light.Intensity) * attenuation);
    float shadow = SampleShadowMap(input.WorldPos, N, L);
    radiance *= shadow;
    float NDF = DistributionGGX(N, H, roughness);
    float G = GeometrySmith(N, V, L, roughness);
    float3 F = FresnelSchlick(max(dot(H, V), 0.0f), F0);
    float3 kS = F;
    float3 kD = float3(1.0f, 1.0f, 1.0f) - kS;
    kD *= 1.0f - metallic;
    float3 numerator = ((NDF * G) * F);
    float denominator = ((4.0f * max(dot(N, V), 0.0f)) * max(dot(N, L), 0.0f)) + 0.0001f;
    float3 specular = numerator / denominator;
    float NdotL = max(dot(N, L), 0.0f);
    Lo += (((kD * albedo) / 3.14159265359f + specular * radiance) * NdotL);
  }
  float3 ambient = (((AmbientColor * AmbientIntensity) * albedo) * ao);
  return ambient + Lo;
}

PSOutput main(PSInput input)
{
  PSOutput output;
  MaterialData material = Materials[input.MaterialID];
  float2 tiledUV = (input.TexCoord * material.TilingOffset.xy) + material.TilingOffset.zw;
  float3 albedo = material.Albedo.rgb;
  if (material.TextureFlags & 1)
  {
    albedo *= AlbedoTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb;
  }
  albedo *= input.Color.rgb;
  float3 normal = input.Normal;
  if (material.TextureFlags & 2)
  {
    float3 normalMap = NormalTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb;
    normal = GetNormalFromMap(normalMap, input.Normal, input.Tangent, input.Bitangent);
  }
  float metallic = material.Metallic;
  float roughness = material.Roughness;
  if (material.TextureFlags & 4)
  {
    float2 metallicRoughness = MetallicRoughnessTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rg;
    metallic *= metallicRoughness.r;
    roughness *= metallicRoughness.g;
  }
  float ao = material.AO;
  if (material.TextureFlags & 8)
  {
    ao *= AOTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).r;
  }
  float3 emission = float3(0, 0, 0);
  if (material.TextureFlags & 16)
  {
    emission = (EmissionTextures.Sample(LinearSampler, float3(tiledUV, input.MaterialID)).rgb * material.Emission);
  }
  float3 lightmap = LightmapTexture.Sample(LinearSampler, input.LightmapUV).rgb;
  float3 color = CalculatePBRLighting(input, material, albedo, normal, metallic, roughness, ao);
  color += emission + (lightmap * albedo);
  color *= input.LODFade;
  float2 currentPos = (input.CurrClipPos.xy / input.CurrClipPos.w * 0.5f) + 0.5f;
  float2 prevPos = (input.PrevClipPos.xy / input.PrevClipPos.w * 0.5f) + 0.5f;
  float2 motionVector = currentPos - prevPos;
  output.Color = float4(color, material.Albedo.a);
  output.Normal = float4((normal * 0.5f) + 0.5f, roughness);
  output.MotionVector = float4(motionVector, input.Position.z, 1.0f);
  output.MaterialProps = float4(metallic, roughness, ao, material.Emission);
  return output;
}

