// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct VSInput
{
  float3 Position : POSITION;
  float2 TexCoord : TEXCOORD0;
};

struct VSOutput
{
  float4 Position : SV_POSITION;
  float2 TexCoord : TEXCOORD0;
};

VSOutput main(VSInput input)
{
  VSOutput output;
  output.Position = float4(input.Position, 1.0f);
  output.TexCoord = input.TexCoord;
  return output;
}

