// Volumetric Rendering Pixel Shader
// Tests ray marching and volume sampling techniques

cbuffer VolumeParams : register(b0)
{
    float3 VolumeMin;
    float StepSize;
    float3 VolumeMax;
    int MaxSteps;
    float3 LightDirection;
    float Density;
    float3 LightColor;
    float Absorption;
    float3 ScatteringColor;
    float Scattering;
    float Time;
    float NoiseScale;
    float NoiseStrength;
};

Texture3D VolumeTexture : register(t0);
Texture3D NoiseTexture : register(t1);
SamplerState LinearSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float2 TexCoord : TEXCOORD0;
    float3 WorldPos : TEXCOORD1;
    float3 RayDirection : TEXCOORD2;
    float3 CameraPos : TEXCOORD3;
};

// Ray-box intersection
bool rayBoxIntersection(float3 rayOrigin, float3 rayDir, float3 boxMin, float3 boxMax, out float tNear, out float tFar)
{
    float3 invDir = 1.0 / rayDir;
    float3 t1 = (boxMin - rayOrigin) * invDir;
    float3 t2 = (boxMax - rayOrigin) * invDir;
    
    float3 tMin = min(t1, t2);
    float3 tMax = max(t1, t2);
    
    tNear = max(max(tMin.x, tMin.y), tMin.z);
    tFar = min(min(tMax.x, tMax.y), tMax.z);
    
    return tFar > tNear && tFar > 0.0;
}

// 3D noise function
float noise3D(float3 p)
{
    return NoiseTexture.SampleLevel(LinearSampler, p, 0).r;
}

// Fractal Brownian Motion
float fbm(float3 p, int octaves)
{
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for(int i = 0; i < octaves; i++)
    {
        value += amplitude * noise3D(p * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

// Volume density function
float sampleVolume(float3 worldPos)
{
    // Convert world position to volume texture coordinates
    float3 volumeCoord = (worldPos - VolumeMin) / (VolumeMax - VolumeMin);
    
    // Sample base volume texture
    float baseDensity = VolumeTexture.SampleLevel(LinearSampler, volumeCoord, 0).r;
    
    // Add animated noise
    float3 noiseCoord = worldPos * NoiseScale + float3(Time * 0.1, Time * 0.05, Time * 0.08);
    float noiseDensity = fbm(noiseCoord, 4) * NoiseStrength;
    
    // Combine densities
    float finalDensity = (baseDensity + noiseDensity) * Density;
    
    return max(0.0, finalDensity);
}

// Phase function for scattering
float phaseFunction(float3 lightDir, float3 viewDir)
{
    float cosTheta = dot(lightDir, viewDir);
    float g = 0.3; // Anisotropy parameter
    
    // Henyey-Greenstein phase function
    float g2 = g * g;
    return (1.0 - g2) / (4.0 * 3.14159265 * pow(1.0 + g2 - 2.0 * g * cosTheta, 1.5));
}

float4 main(PSInput input) : SV_TARGET
{
    float3 rayOrigin = input.CameraPos;
    float3 rayDir = normalize(input.RayDirection);
    
    // Find intersection with volume bounds
    float tNear, tFar;
    if (!rayBoxIntersection(rayOrigin, rayDir, VolumeMin, VolumeMax, tNear, tFar))
    {
        discard;
    }
    
    // Ensure we start from the camera or volume entry point
    tNear = max(0.0, tNear);
    
    // Ray marching variables
    float3 color = float3(0, 0, 0);
    float transmittance = 1.0;
    float t = tNear;
    
    // Ray marching loop
    for(int step = 0; step < MaxSteps && t < tFar; step++)
    {
        float3 currentPos = rayOrigin + rayDir * t;
        float density = sampleVolume(currentPos);
        
        if (density > 0.001)
        {
            // Calculate lighting
            float3 lightDir = normalize(-LightDirection);
            
            // In-scattering
            float phase = phaseFunction(lightDir, -rayDir);
            float3 scatteredLight = LightColor * ScatteringColor * density * Scattering * phase;
            
            // Beer's law for absorption
            float extinction = density * (Absorption + Scattering);
            float stepTransmittance = exp(-extinction * StepSize);
            
            // Accumulate color
            color += scatteredLight * transmittance * (1.0 - stepTransmittance) / extinction;
            
            // Update transmittance
            transmittance *= stepTransmittance;
            
            // Early termination if transmittance is very low
            if (transmittance < 0.01)
                break;
        }
        
        t += StepSize;
    }
    
    // Apply final transmittance as alpha
    float alpha = 1.0 - transmittance;
    
    return float4(color, alpha);
}
