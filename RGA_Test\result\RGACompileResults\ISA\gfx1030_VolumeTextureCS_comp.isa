_amdgpu_cs_main:
	s_mov_b32 s6, s1                                           // 000000000000: BE860301
	s_getpc_b64 s[0:1]                                         // 000000000004: BE801F00
	v_lshl_add_u32 v24, s2, 3, v0                              // 000000000008: D7460018 04010602
	s_mov_b32 s7, s1                                           // 000000000010: BE870301
	v_lshl_add_u32 v25, s3, 3, v1                              // 000000000014: D7460019 04050603
	s_load_dwordx8 s[8:15], s[6:7], null                       // 00000000001C: F40C0203 FA000000
	s_waitcnt lgkmcnt(0)                                       // 000000000024: BF8CC07F
	v_alignbit_b32 v2, s10, s9, 30                             // 000000000028: D54E0002 0278120A
	s_bfe_u32 s1, s10, 0xe000e                                 // 000000000030: 9381FF0A 000E000E
	s_and_b32 s2, s12, 0x1fff                                  // 000000000038: 8702FF0C 00001FFF
	s_bfe_u32 s3, s11, 0x4000c                                 // 000000000040: 9383FF0B 0004000C
	s_add_i32 s1, s1, 1                                        // 000000000048: 81018101
	v_readfirstlane_b32 s16, v2                                // 00000000004C: 7E200502
	s_bfe_u32 s5, s12, 0xd0010                                 // 000000000050: 9385FF0C 000D0010
	s_add_i32 s2, s2, 1                                        // 000000000058: 81028102
	s_lshr_b32 s1, s1, s3                                      // 00000000005C: 90010301
	s_lshr_b32 s17, s2, s3                                     // 000000000060: 90110302
	s_and_b32 s16, s16, 0x3fff                                 // 000000000064: 8710FF10 00003FFF
	s_sub_i32 s5, s2, s5                                       // 00000000006C: 81850502
	s_add_i32 s16, s16, 1                                      // 000000000070: 81108110
	s_max_u32 s2, s1, 1                                        // 000000000074: 84828101
	s_lshr_b32 s1, s16, s3                                     // 000000000078: 90010310
	s_and_b32 s0, 1, s13                                       // 00000000007C: 87000D81
	s_max_u32 s17, s17, 1                                      // 000000000080: 84918111
	s_max_u32 s3, s1, 1                                        // 000000000084: 84838101
	s_cmp_eq_u32 s0, 1                                         // 000000000088: BF068100
	v_cmp_le_u32_e32 vcc_lo, s2, v25                           // 00000000008C: 7D863202
	v_cmp_le_u32_e64 s0, s3, v24                               // 000000000090: D4C30000 00023003
	s_cselect_b32 s5, s5, s17                                  // 000000000098: 85051105
	s_cmp_ge_u32 s4, s5                                        // 00000000009C: BF090504
	s_cselect_b64 s[16:17], -1, 0                              // 0000000000A0: 859080C1
	s_or_b64 s[0:1], s[0:1], vcc                               // 0000000000A4: 88806A00
	s_or_b64 s[0:1], s[16:17], s[0:1]                          // 0000000000A8: 88800010
	s_xor_b64 s[0:1], s[0:1], -1                               // 0000000000AC: 8980C100
	s_and_saveexec_b64 s[16:17], s[0:1]                        // 0000000000B0: BE902400
	s_cbranch_execz _L0                                        // 0000000000B4: BF880157
	s_load_dwordx4 s[16:19], s[6:7], null                      // 0000000000B8: F4080403 FA000000
	s_add_i32 s3, s3, -1                                       // 0000000000C0: 8103C103
	s_add_i32 s2, s2, -1                                       // 0000000000C4: 8102C102
	v_cvt_f32_u32_e32 v2, s3                                   // 0000000000C8: 7E040C03
	v_cvt_f32_u32_e32 v3, s2                                   // 0000000000CC: 7E060C02
	s_add_i32 s5, s5, -1                                       // 0000000000D0: 8105C105
	v_cvt_f32_u32_e32 v5, v24                                  // 0000000000D4: 7E0A0D18
	v_cvt_f32_u32_e32 v4, s5                                   // 0000000000D8: 7E080C05
	v_rcp_iflag_f32_e32 v2, v2                                 // 0000000000DC: 7E045702
	v_rcp_iflag_f32_e32 v3, v3                                 // 0000000000E0: 7E065703
	v_cvt_f32_u32_e32 v6, v25                                  // 0000000000E4: 7E0C0D19
	v_cvt_f32_u32_e32 v7, s4                                   // 0000000000E8: 7E0E0C04
	v_rcp_iflag_f32_e32 v4, v4                                 // 0000000000EC: 7E085704
	v_mov_b32_e32 v26, s4                                      // 0000000000F0: 7E340204
	v_fmaak_f32 v2, v5, v2, 0xc2480000                         // 0000000000F4: 5A040505 C2480000
	v_fmaak_f32 v3, v6, v3, 0xc2480000                         // 0000000000FC: 5A060706 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000104: BF8CC07F
	s_buffer_load_dword s0, s[16:19], 0xc                      // 000000000108: F4200008 FA00000C
	v_fmaak_f32 v4, v7, v4, 0xc2480000                         // 000000000110: 5A080907 C2480000
	s_waitcnt lgkmcnt(0)                                       // 000000000118: BF8CC07F
	v_mul_f32_e64 v5, 0x3dcccccd, s0                           // 00000000011C: D5080005 000000FF 3DCCCCCD
	v_mul_f32_e64 v6, 0x3d4ccccd, s0                           // 000000000128: D5080006 000000FF 3D4CCCCD
	v_fmamk_f32 v7, v2, 0x3c23d70a, v5                         // 000000000134: 580E0B02 3C23D70A
	v_fmamk_f32 v8, v3, 0x3c23d70a, v5                         // 00000000013C: 58100B03 3C23D70A
	v_fmac_f32_e32 v5, 0x3c23d70a, v4                          // 000000000144: 560A08FF 3C23D70A
	v_fmamk_f32 v9, v2, 0x3ca3d70a, v6                         // 00000000014C: 58120D02 3CA3D70A
	v_fmamk_f32 v10, v3, 0x3ca3d70a, v6                        // 000000000154: 58140D03 3CA3D70A
	v_floor_f32_e32 v11, v7                                    // 00000000015C: 7E164907
	v_floor_f32_e32 v12, v8                                    // 000000000160: 7E184908
	v_fmac_f32_e32 v6, 0x3ca3d70a, v4                          // 000000000164: 560C08FF 3CA3D70A
	v_floor_f32_e32 v13, v5                                    // 00000000016C: 7E1A4905
	v_fract_f32_e32 v7, v7                                     // 000000000170: 7E0E4107
	v_fract_f32_e32 v8, v8                                     // 000000000174: 7E104108
	v_fract_f32_e32 v5, v5                                     // 000000000178: 7E0A4105
	v_floor_f32_e32 v14, v9                                    // 00000000017C: 7E1C4909
	v_floor_f32_e32 v15, v10                                   // 000000000180: 7E1E490A
	v_fmac_f32_e32 v11, 0x42640000, v12                        // 000000000184: 561618FF 42640000
	v_floor_f32_e32 v16, v6                                    // 00000000018C: 7E204906
	v_mul_f32_e32 v17, v7, v7                                  // 000000000190: 10220F07
	v_mul_f32_e32 v18, v8, v8                                  // 000000000194: 10241108
	v_mul_f32_e32 v19, v5, v5                                  // 000000000198: 10260B05
	v_add_f32_e32 v7, v7, v7                                   // 00000000019C: 060E0F07
	v_add_f32_e32 v8, v8, v8                                   // 0000000001A0: 06101108
	v_add_f32_e32 v5, v5, v5                                   // 0000000001A4: 060A0B05
	v_fmac_f32_e32 v14, 0x42640000, v15                        // 0000000001A8: 561C1EFF 42640000
	v_fmac_f32_e32 v11, 0x42e20000, v13                        // 0000000001B0: 56161AFF 42E20000
	v_fma_f32 v7, 0x40400000, v17, -v7                         // 0000000001B8: D54B0007 841E22FF 40400000
	v_fma_f32 v8, 0x40400000, v18, -v8                         // 0000000001C4: D54B0008 842224FF 40400000
	v_fma_f32 v5, 0x40400000, v19, -v5                         // 0000000001D0: D54B0005 841626FF 40400000
	v_fmac_f32_e32 v14, 0x42e20000, v16                        // 0000000001DC: 561C20FF 42E20000
	v_mul_f32_e32 v12, 0.15915494, v11                         // 0000000001E4: 101816F8
	v_add_f32_e32 v13, 1.0, v11                                // 0000000001E8: 061A16F2
	v_add_f32_e32 v15, 0x42640000, v11                         // 0000000001EC: 061E16FF 42640000
	v_add_f32_e32 v16, 0x42680000, v11                         // 0000000001F4: 062016FF 42680000
	v_add_f32_e32 v17, 0x42e20000, v11                         // 0000000001FC: 062216FF 42E20000
	v_add_f32_e32 v18, 0x42e40000, v11                         // 000000000204: 062416FF 42E40000
	v_add_f32_e32 v19, 0x432a0000, v11                         // 00000000020C: 062616FF 432A0000
	v_add_f32_e32 v11, 0x432b0000, v11                         // 000000000214: 061616FF 432B0000
	v_mul_f32_e32 v13, 0.15915494, v13                         // 00000000021C: 101A1AF8
	v_mul_f32_e32 v15, 0.15915494, v15                         // 000000000220: 101E1EF8
	v_mul_f32_e32 v16, 0.15915494, v16                         // 000000000224: 102020F8
	v_mul_f32_e32 v17, 0.15915494, v17                         // 000000000228: 102222F8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 00000000022C: 102424F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000230: 102626F8
	v_mul_f32_e32 v11, 0.15915494, v11                         // 000000000234: 101616F8
	v_sin_f32_e32 v12, v12                                     // 000000000238: 7E186B0C
	v_sin_f32_e32 v13, v13                                     // 00000000023C: 7E1A6B0D
	v_sin_f32_e32 v15, v15                                     // 000000000240: 7E1E6B0F
	v_sin_f32_e32 v16, v16                                     // 000000000244: 7E206B10
	v_sin_f32_e32 v17, v17                                     // 000000000248: 7E226B11
	v_sin_f32_e32 v18, v18                                     // 00000000024C: 7E246B12
	v_sin_f32_e32 v19, v19                                     // 000000000250: 7E266B13
	v_sin_f32_e32 v11, v11                                     // 000000000254: 7E166B0B
	v_add_f32_e32 v21, 1.0, v14                                // 000000000258: 062A1CF2
	v_mul_f32_e32 v20, 0.15915494, v14                         // 00000000025C: 10281CF8
	v_mul_f32_e32 v12, 0x472aee8c, v12                         // 000000000260: 101818FF 472AEE8C
	v_mul_f32_e32 v13, 0x472aee8c, v13                         // 000000000268: 101A1AFF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v15                         // 000000000270: 101E1EFF 472AEE8C
	v_mul_f32_e32 v16, 0x472aee8c, v16                         // 000000000278: 102020FF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 000000000280: 102222FF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 000000000288: 102424FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 000000000290: 102626FF 472AEE8C
	v_mul_f32_e32 v11, 0x472aee8c, v11                         // 000000000298: 101616FF 472AEE8C
	v_fract_f32_e32 v12, v12                                   // 0000000002A0: 7E18410C
	v_fract_f32_e32 v13, v13                                   // 0000000002A4: 7E1A410D
	v_fract_f32_e32 v15, v15                                   // 0000000002A8: 7E1E410F
	v_fract_f32_e32 v16, v16                                   // 0000000002AC: 7E204110
	v_fract_f32_e32 v17, v17                                   // 0000000002B0: 7E224111
	v_fract_f32_e32 v18, v18                                   // 0000000002B4: 7E244112
	v_fract_f32_e32 v19, v19                                   // 0000000002B8: 7E264113
	v_fract_f32_e32 v11, v11                                   // 0000000002BC: 7E16410B
	v_sub_f32_e32 v13, v13, v12                                // 0000000002C0: 081A190D
	v_sub_f32_e32 v16, v16, v15                                // 0000000002C4: 08201F10
	v_sub_f32_e32 v18, v18, v17                                // 0000000002C8: 08242312
	v_add_f32_e32 v22, 0x42640000, v14                         // 0000000002CC: 062C1CFF 42640000
	v_sub_f32_e32 v11, v11, v19                                // 0000000002D4: 0816270B
	v_add_f32_e32 v23, 0x42680000, v14                         // 0000000002D8: 062E1CFF 42680000
	v_fmac_f32_e32 v12, v13, v7                                // 0000000002E0: 56180F0D
	v_fmac_f32_e32 v15, v16, v7                                // 0000000002E4: 561E0F10
	v_fmac_f32_e32 v17, v18, v7                                // 0000000002E8: 56220F12
	v_fmac_f32_e32 v19, v11, v7                                // 0000000002EC: 56260F0B
	v_mul_f32_e32 v11, 0.15915494, v21                         // 0000000002F0: 10162AF8
	v_fract_f32_e32 v9, v9                                     // 0000000002F4: 7E124109
	v_fract_f32_e32 v10, v10                                   // 0000000002F8: 7E14410A
	v_sin_f32_e32 v7, v20                                      // 0000000002FC: 7E0E6B14
	v_mul_f32_e32 v13, 0.15915494, v22                         // 000000000300: 101A2CF8
	v_sub_f32_e32 v15, v15, v12                                // 000000000304: 081E190F
	v_sub_f32_e32 v16, v19, v17                                // 000000000308: 08202313
	v_mul_f32_e32 v18, 0.15915494, v23                         // 00000000030C: 10242EF8
	v_sin_f32_e32 v11, v11                                     // 000000000310: 7E166B0B
	v_sin_f32_e32 v13, v13                                     // 000000000314: 7E1A6B0D
	v_fmac_f32_e32 v12, v15, v8                                // 000000000318: 5618110F
	v_fmac_f32_e32 v17, v16, v8                                // 00000000031C: 56221110
	v_sin_f32_e32 v8, v18                                      // 000000000320: 7E106B12
	v_mul_f32_e32 v15, v9, v9                                  // 000000000324: 101E1309
	v_mul_f32_e32 v16, v10, v10                                // 000000000328: 1020150A
	v_add_f32_e32 v9, v9, v9                                   // 00000000032C: 06121309
	v_add_f32_e32 v10, v10, v10                                // 000000000330: 0614150A
	v_mul_f32_e32 v7, 0x472aee8c, v7                           // 000000000334: 100E0EFF 472AEE8C
	v_mul_f32_e32 v11, 0x472aee8c, v11                         // 00000000033C: 101616FF 472AEE8C
	v_add_f32_e32 v19, 0x432a0000, v14                         // 000000000344: 06261CFF 432A0000
	v_fma_f32 v9, 0x40400000, v15, -v9                         // 00000000034C: D54B0009 84261EFF 40400000
	v_add_f32_e32 v15, 0x42e20000, v14                         // 000000000358: 061E1CFF 42E20000
	v_fma_f32 v10, 0x40400000, v16, -v10                       // 000000000360: D54B000A 842A20FF 40400000
	v_add_f32_e32 v16, 0x42e40000, v14                         // 00000000036C: 06201CFF 42E40000
	v_add_f32_e32 v14, 0x432b0000, v14                         // 000000000374: 061C1CFF 432B0000
	v_mul_f32_e64 v20, 0x3ca3d70a, s0                          // 00000000037C: D5080014 000000FF 3CA3D70A
	v_mul_f32_e32 v13, 0x472aee8c, v13                         // 000000000388: 101A1AFF 472AEE8C
	v_mul_f32_e32 v8, 0x472aee8c, v8                           // 000000000390: 101010FF 472AEE8C
	v_fract_f32_e32 v7, v7                                     // 000000000398: 7E0E4107
	v_fract_f32_e32 v11, v11                                   // 00000000039C: 7E16410B
	v_mul_f32_e32 v15, 0.15915494, v15                         // 0000000003A0: 101E1EF8
	v_mul_f32_e32 v16, 0.15915494, v16                         // 0000000003A4: 102020F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000003A8: 102626F8
	v_mul_f32_e32 v14, 0.15915494, v14                         // 0000000003AC: 101C1CF8
	v_fmamk_f32 v2, v2, 0x3d23d70a, v20                        // 0000000003B0: 58042902 3D23D70A
	v_fmamk_f32 v3, v3, 0x3d23d70a, v20                        // 0000000003B8: 58062903 3D23D70A
	v_fract_f32_e32 v13, v13                                   // 0000000003C0: 7E1A410D
	v_fract_f32_e32 v8, v8                                     // 0000000003C4: 7E104108
	v_sub_f32_e32 v11, v11, v7                                 // 0000000003C8: 08160F0B
	v_sin_f32_e32 v15, v15                                     // 0000000003CC: 7E1E6B0F
	v_sin_f32_e32 v16, v16                                     // 0000000003D0: 7E206B10
	v_sin_f32_e32 v19, v19                                     // 0000000003D4: 7E266B13
	v_sin_f32_e32 v14, v14                                     // 0000000003D8: 7E1C6B0E
	v_fmac_f32_e32 v20, 0x3d23d70a, v4                         // 0000000003DC: 562808FF 3D23D70A
	v_floor_f32_e32 v4, v2                                     // 0000000003E4: 7E084902
	v_floor_f32_e32 v21, v3                                    // 0000000003E8: 7E2A4903
	v_sub_f32_e32 v8, v8, v13                                  // 0000000003EC: 08101B08
	v_fmac_f32_e32 v7, v11, v9                                 // 0000000003F0: 560E130B
	v_floor_f32_e32 v11, v20                                   // 0000000003F4: 7E164914
	v_fract_f32_e32 v6, v6                                     // 0000000003F8: 7E0C4106
	v_fmac_f32_e32 v4, 0x42640000, v21                         // 0000000003FC: 56082AFF 42640000
	v_fmac_f32_e32 v13, v8, v9                                 // 000000000404: 561A1308
	v_mul_f32_e32 v8, 0x472aee8c, v15                          // 000000000408: 10101EFF 472AEE8C
	v_mul_f32_e32 v15, 0x472aee8c, v16                         // 000000000410: 101E20FF 472AEE8C
	v_mul_f32_e32 v16, 0x472aee8c, v19                         // 000000000418: 102026FF 472AEE8C
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 000000000420: 101C1CFF 472AEE8C
	v_fmac_f32_e32 v4, 0x42e20000, v11                         // 000000000428: 560816FF 42E20000
	v_fract_f32_e32 v8, v8                                     // 000000000430: 7E104108
	v_fract_f32_e32 v11, v15                                   // 000000000434: 7E16410F
	v_fract_f32_e32 v15, v16                                   // 000000000438: 7E1E4110
	v_fract_f32_e32 v14, v14                                   // 00000000043C: 7E1C410E
	v_add_f32_e32 v16, 1.0, v4                                 // 000000000440: 062008F2
	v_add_f32_e32 v19, 0x42640000, v4                          // 000000000444: 062608FF 42640000
	v_mul_f32_e32 v18, v6, v6                                  // 00000000044C: 10240D06
	v_add_f32_e32 v6, v6, v6                                   // 000000000450: 060C0D06
	v_sub_f32_e32 v11, v11, v8                                 // 000000000454: 0816110B
	v_sub_f32_e32 v14, v14, v15                                // 000000000458: 081C1F0E
	v_mul_f32_e32 v21, 0.15915494, v4                          // 00000000045C: 102A08F8
	v_mul_f32_e32 v16, 0.15915494, v16                         // 000000000460: 102020F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 000000000464: 102626F8
	v_fmac_f32_e32 v8, v11, v9                                 // 000000000468: 5610130B
	v_fmac_f32_e32 v15, v14, v9                                // 00000000046C: 561E130E
	v_sin_f32_e32 v9, v21                                      // 000000000470: 7E126B15
	v_sin_f32_e32 v11, v16                                     // 000000000474: 7E166B10
	v_sin_f32_e32 v14, v19                                     // 000000000478: 7E1C6B13
	v_sub_f32_e32 v16, v17, v12                                // 00000000047C: 08201911
	v_fma_f32 v6, 0x40400000, v18, -v6                         // 000000000480: D54B0006 841A24FF 40400000
	v_add_f32_e32 v17, 0x42680000, v4                          // 00000000048C: 062208FF 42680000
	v_add_f32_e32 v18, 0x42e20000, v4                          // 000000000494: 062408FF 42E20000
	v_add_f32_e32 v19, 0x42e40000, v4                          // 00000000049C: 062608FF 42E40000
	v_add_f32_e32 v21, 0x432a0000, v4                          // 0000000004A4: 062A08FF 432A0000
	v_add_f32_e32 v4, 0x432b0000, v4                           // 0000000004AC: 060808FF 432B0000
	v_mul_f32_e32 v17, 0.15915494, v17                         // 0000000004B4: 102222F8
	v_mul_f32_e32 v18, 0.15915494, v18                         // 0000000004B8: 102424F8
	v_mul_f32_e32 v19, 0.15915494, v19                         // 0000000004BC: 102626F8
	v_mul_f32_e32 v21, 0.15915494, v21                         // 0000000004C0: 102A2AF8
	v_mul_f32_e32 v4, 0.15915494, v4                           // 0000000004C4: 100808F8
	v_sin_f32_e32 v17, v17                                     // 0000000004C8: 7E226B11
	v_sin_f32_e32 v18, v18                                     // 0000000004CC: 7E246B12
	v_sin_f32_e32 v19, v19                                     // 0000000004D0: 7E266B13
	v_sin_f32_e32 v21, v21                                     // 0000000004D4: 7E2A6B15
	v_sin_f32_e32 v4, v4                                       // 0000000004D8: 7E086B04
	v_sub_f32_e32 v13, v13, v7                                 // 0000000004DC: 081A0F0D
	v_fract_f32_e32 v2, v2                                     // 0000000004E0: 7E044102
	v_mul_f32_e32 v9, 0x472aee8c, v9                           // 0000000004E4: 101212FF 472AEE8C
	v_mul_f32_e32 v11, 0x472aee8c, v11                         // 0000000004EC: 101616FF 472AEE8C
	v_mul_f32_e32 v14, 0x472aee8c, v14                         // 0000000004F4: 101C1CFF 472AEE8C
	v_mul_f32_e32 v17, 0x472aee8c, v17                         // 0000000004FC: 102222FF 472AEE8C
	v_mul_f32_e32 v18, 0x472aee8c, v18                         // 000000000504: 102424FF 472AEE8C
	v_mul_f32_e32 v19, 0x472aee8c, v19                         // 00000000050C: 102626FF 472AEE8C
	v_mul_f32_e32 v21, 0x472aee8c, v21                         // 000000000514: 102A2AFF 472AEE8C
	v_mul_f32_e32 v4, 0x472aee8c, v4                           // 00000000051C: 100808FF 472AEE8C
	v_fmac_f32_e32 v7, v13, v10                                // 000000000524: 560E150D
	v_sub_f32_e32 v13, v15, v8                                 // 000000000528: 081A110F
	v_mul_f32_e32 v15, v2, v2                                  // 00000000052C: 101E0502
	v_add_f32_e32 v2, v2, v2                                   // 000000000530: 06040502
	v_fract_f32_e32 v9, v9                                     // 000000000534: 7E124109
	v_fract_f32_e32 v11, v11                                   // 000000000538: 7E16410B
	v_fract_f32_e32 v14, v14                                   // 00000000053C: 7E1C410E
	v_fract_f32_e32 v17, v17                                   // 000000000540: 7E224111
	v_fract_f32_e32 v18, v18                                   // 000000000544: 7E244112
	v_fract_f32_e32 v19, v19                                   // 000000000548: 7E264113
	v_fract_f32_e32 v21, v21                                   // 00000000054C: 7E2A4115
	v_fract_f32_e32 v4, v4                                     // 000000000550: 7E084104
	v_fract_f32_e32 v3, v3                                     // 000000000554: 7E064103
	v_fma_f32 v2, 0x40400000, v15, -v2                         // 000000000558: D54B0002 840A1EFF 40400000
	v_sub_f32_e32 v11, v11, v9                                 // 000000000564: 0816130B
	v_sub_f32_e32 v15, v17, v14                                // 000000000568: 081E1D11
	v_sub_f32_e32 v17, v19, v18                                // 00000000056C: 08222513
	v_sub_f32_e32 v4, v4, v21                                  // 000000000570: 08082B04
	v_mul_f32_e32 v22, v3, v3                                  // 000000000574: 102C0703
	v_add_f32_e32 v3, v3, v3                                   // 000000000578: 06060703
	v_fmac_f32_e32 v9, v11, v2                                 // 00000000057C: 5612050B
	v_fmac_f32_e32 v14, v15, v2                                // 000000000580: 561C050F
	v_fmac_f32_e32 v18, v17, v2                                // 000000000584: 56240511
	v_fmac_f32_e32 v21, v4, v2                                 // 000000000588: 562A0504
	v_fmac_f32_e32 v8, v13, v10                                // 00000000058C: 5610150D
	v_fract_f32_e32 v2, v20                                    // 000000000590: 7E044114
	v_fma_f32 v3, 0x40400000, v22, -v3                         // 000000000594: D54B0003 840E2CFF 40400000
	v_sub_f32_e32 v4, v14, v9                                  // 0000000005A0: 0808130E
	v_sub_f32_e32 v10, v21, v18                                // 0000000005A4: 08142515
	v_sub_f32_e32 v8, v8, v7                                   // 0000000005A8: 08100F08
	v_mul_f32_e32 v11, v2, v2                                  // 0000000005AC: 10160502
	v_add_f32_e32 v2, v2, v2                                   // 0000000005B0: 06040502
	v_fmac_f32_e32 v9, v4, v3                                  // 0000000005B4: 56120704
	v_fmac_f32_e32 v18, v10, v3                                // 0000000005B8: 5624070A
	v_fmac_f32_e32 v7, v8, v6                                  // 0000000005BC: 560E0D08
	s_mov_b32 s0, 0x3e99999a                                   // 0000000005C0: BE8003FF 3E99999A
	v_fmac_f32_e32 v12, v16, v5                                // 0000000005C8: 56180B10
	v_fma_f32 v2, 0x40400000, v11, -v2                         // 0000000005CC: D54B0002 840A16FF 40400000
	v_sub_f32_e32 v3, v18, v9                                  // 0000000005D8: 08061312
	v_fmaak_f32 v4, s0, v7, 0xbe99999a                         // 0000000005DC: 5A080E00 BE99999A
	v_fmac_f32_e32 v9, v3, v2                                  // 0000000005E4: 56120503
	v_fmac_f32_e32 v4, 0.5, v12                                // 0000000005E8: 560818F0
	v_fmamk_f32 v2, v9, 0x3e4ccccd, v4                         // 0000000005EC: 58040909 3E4CCCCD
	v_max_f32_e64 v3, v2, v2 clamp                             // 0000000005F4: D5108003 00020502
	v_mov_b32_e32 v2, 1.0                                      // 0000000005FC: 7E0402F2
	v_add_f32_e32 v5, v3, v3                                   // 000000000600: 060A0703
	v_mov_b32_e32 v3, v2                                       // 000000000604: 7E060302
	v_mov_b32_e32 v4, v2                                       // 000000000608: 7E080302
	image_store v[2:5], v[24:26], s[8:15] dmask:0xf dim:SQ_RSRC_IMG_3D unorm// 00000000060C: F0201F10 00020218
_L0:
	s_endpgm                                                   // 000000000614: BF810000
