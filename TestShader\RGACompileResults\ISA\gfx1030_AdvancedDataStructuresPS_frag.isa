_amdgpu_ps_main:
	s_mov_b64 s[4:5], exec                                     // 000000000000: BE84047E
	s_wqm_b64 exec, exec                                       // 000000000004: BEFE0A7E
	s_mov_b32 s24, s1                                          // 000000000008: BE980301
	s_getpc_b64 s[0:1]                                         // 00000000000C: BE801F00
	s_mov_b32 m0, s2                                           // 000000000010: BEFC0302
	s_mov_b32 s25, s1                                          // 000000000014: BE990301
	v_interp_mov_f32_e32 v21, p0, attr10.x                     // 000000000018: C8562802
	s_load_dwordx4 s[12:15], s[24:25], null                    // 00000000001C: F408030C FA000000
	v_interp_p1_f32_e32 v2, v0, attr4.x                        // 000000000024: C8081000
	v_interp_p1_f32_e32 v3, v0, attr4.y                        // 000000000028: C80C1100
	s_mov_b64 s[0:1], exec                                     // 00000000002C: BE80047E
	v_lshlrev_b32_e32 v17, 6, v21                              // 000000000030: 34222A86
	s_waitcnt lgkmcnt(0)                                       // 000000000034: BF8CC07F
	s_clause 0x2                                               // 000000000038: BFA10002
	buffer_load_dword v18, v17, s[12:15], 0 offen offset:48    // 00000000003C: E0301030 80031211
	buffer_load_dwordx4 v[9:12], v17, s[12:15], 0 offen offset:32// 000000000044: E0381020 80030911
	buffer_load_dwordx4 v[5:8], v17, s[12:15], 0 offen         // 00000000004C: E0381000 80030511
	v_interp_p2_f32_e32 v2, v1, attr4.x                        // 000000000054: C8091001
	v_interp_p2_f32_e32 v3, v1, attr4.y                        // 000000000058: C80D1101
	s_waitcnt vmcnt(2)                                         // 00000000005C: BF8C3F72
	v_and_b32_e32 v13, 1, v18                                  // 000000000060: 361A2481
	s_waitcnt vmcnt(1)                                         // 000000000064: BF8C3F71
	v_fma_f32 v9, v9, v2, v11                                  // 000000000068: D54B0009 042E0509
	v_fmac_f32_e32 v12, v10, v3                                // 000000000070: 5618070A
	v_cmpx_eq_u32_e32 1, v13                                   // 000000000074: 7DA41A81
	s_cbranch_execz _L0                                        // 000000000078: BF88000E
	s_clause 0x1                                               // 00000000007C: BFA10001
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 000000000080: F408020C FA000020
	s_load_dwordx8 s[16:23], s[24:25], 0x40                    // 000000000088: F40C040C FA000040
	v_cvt_f32_u32_e32 v2, v21                                  // 000000000090: 7E040D15
	s_waitcnt lgkmcnt(0)                                       // 000000000094: BF8CC07F
	image_sample  v[13:15], [v9, v12, v2], s[16:23], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 000000000098: F080072A 00440D09 0000020C
	s_waitcnt vmcnt(0)                                         // 0000000000A4: BF8C3F70
	v_mul_f32_e32 v5, v13, v5                                  // 0000000000A8: 100A0B0D
	v_mul_f32_e32 v6, v14, v6                                  // 0000000000AC: 100C0D0E
	v_mul_f32_e32 v7, v15, v7                                  // 0000000000B0: 100E0F0F
_L0:
	s_or_b64 exec, exec, s[0:1]                                // 0000000000B4: 88FE007E
	s_mov_b32 m0, s2                                           // 0000000000B8: BEFC0302
	v_and_b32_e32 v2, 2, v18                                   // 0000000000BC: 36042482
	v_interp_p1_f32_e32 v13, v0, attr1.x                       // 0000000000C0: C8340400
	v_interp_p1_f32_e32 v15, v0, attr1.y                       // 0000000000C4: C83C0500
	v_interp_p1_f32_e32 v14, v0, attr1.z                       // 0000000000C8: C8380600
	s_mov_b64 s[0:1], exec                                     // 0000000000CC: BE80047E
	v_interp_p2_f32_e32 v13, v1, attr1.x                       // 0000000000D0: C8350401
	v_interp_p2_f32_e32 v15, v1, attr1.y                       // 0000000000D4: C83D0501
	v_interp_p2_f32_e32 v14, v1, attr1.z                       // 0000000000D8: C8390601
	v_cmpx_ne_u32_e32 0, v2                                    // 0000000000DC: 7DAA0480
	s_cbranch_execz _L1                                        // 0000000000E0: BF880030
	s_clause 0x1                                               // 0000000000E4: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0x70                    // 0000000000E8: F40C040C FA000070
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000000F0: F408020C FA000020
	v_cvt_f32_u32_e32 v2, v21                                  // 0000000000F8: 7E040D15
	s_mov_b32 m0, s2                                           // 0000000000FC: BEFC0302
	v_interp_p1_f32_e32 v3, v0, attr3.x                        // 000000000100: C80C0C00
	v_interp_p1_f32_e32 v16, v0, attr2.y                       // 000000000104: C8400900
	v_interp_p1_f32_e32 v10, v0, attr3.z                       // 000000000108: C8280E00
	v_interp_p1_f32_e32 v11, v0, attr2.x                       // 00000000010C: C82C0800
	v_interp_p1_f32_e32 v20, v0, attr2.z                       // 000000000110: C8500A00
	v_interp_p2_f32_e32 v3, v1, attr3.x                        // 000000000114: C80D0C01
	v_interp_p2_f32_e32 v16, v1, attr2.y                       // 000000000118: C8410901
	v_interp_p2_f32_e32 v10, v1, attr3.z                       // 00000000011C: C8290E01
	v_interp_p2_f32_e32 v11, v1, attr2.x                       // 000000000120: C82D0801
	v_interp_p2_f32_e32 v20, v1, attr2.z                       // 000000000124: C8510A01
	s_waitcnt lgkmcnt(0)                                       // 000000000128: BF8CC07F
	image_sample  v[22:24], [v9, v12, v2], s[16:23], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 00000000012C: F080072A 00441609 0000020C
	v_interp_p1_f32_e32 v2, v0, attr3.y                        // 000000000138: C8080D00
	v_interp_p2_f32_e32 v2, v1, attr3.y                        // 00000000013C: C8090D01
	s_waitcnt vmcnt(0)                                         // 000000000140: BF8C3F70
	v_fma_f32 v19, v23, 2.0, -1.0                              // 000000000144: D54B0013 03CDE917
	v_fma_f32 v22, v22, 2.0, -1.0                              // 00000000014C: D54B0016 03CDE916
	v_fma_f32 v23, v24, 2.0, -1.0                              // 000000000154: D54B0017 03CDE918
	v_mul_f32_e32 v2, v19, v2                                  // 00000000015C: 10040513
	v_mul_f32_e32 v3, v19, v3                                  // 000000000160: 10060713
	v_mul_f32_e32 v10, v19, v10                                // 000000000164: 10141513
	v_fmac_f32_e32 v2, v22, v16                                // 000000000168: 56042116
	v_fmac_f32_e32 v3, v22, v11                                // 00000000016C: 56061716
	v_fmac_f32_e32 v10, v22, v20                               // 000000000170: 56142916
	v_fmac_f32_e32 v2, v23, v15                                // 000000000174: 56041F17
	v_fmac_f32_e32 v3, v23, v13                                // 000000000178: 56061B17
	v_fmac_f32_e32 v10, v23, v14                               // 00000000017C: 56141D17
	v_mul_f32_e32 v11, v2, v2                                  // 000000000180: 10160502
	v_fmac_f32_e32 v11, v3, v3                                 // 000000000184: 56160703
	v_fmac_f32_e32 v11, v10, v10                               // 000000000188: 5616150A
	v_rsq_f32_e32 v13, v11                                     // 00000000018C: 7E1A5D0B
	v_cmp_neq_f32_e32 vcc_lo, 0, v11                           // 000000000190: 7C1A1680
	v_cndmask_b32_e32 v11, 0, v13, vcc_lo                      // 000000000194: 02161A80
	v_mul_f32_e32 v13, v11, v3                                 // 000000000198: 101A070B
	v_mul_f32_e32 v15, v11, v2                                 // 00000000019C: 101E050B
	v_mul_f32_e32 v14, v11, v10                                // 0000000001A0: 101C150B
_L1:
	s_or_b64 exec, exec, s[0:1]                                // 0000000001A4: 88FE007E
	buffer_load_dwordx2 v[2:3], v17, s[12:15], 0 offen offset:16// 0000000001A8: E0341010 80030211
	v_and_b32_e32 v10, 4, v18                                  // 0000000001B0: 36142484
	s_mov_b64 s[0:1], exec                                     // 0000000001B4: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 0000000001B8: 7DAA1480
	s_cbranch_execz _L2                                        // 0000000001BC: BF88000D
	s_clause 0x1                                               // 0000000001C0: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0x90                    // 0000000001C4: F40C040C FA000090
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000001CC: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v21                                 // 0000000001D4: 7E140D15
	s_waitcnt lgkmcnt(0)                                       // 0000000001D8: BF8CC07F
	image_sample  v[10:11], [v9, v12, v10], s[16:23], s[8:11] dmask:0x3 dim:SQ_RSRC_IMG_2D_ARRAY// 0000000001DC: F080032A 00440A09 00000A0C
	s_waitcnt vmcnt(0)                                         // 0000000001E8: BF8C3F70
	v_mul_f32_e32 v2, v10, v2                                  // 0000000001EC: 1004050A
	v_mul_f32_e32 v3, v11, v3                                  // 0000000001F0: 1006070B
_L2:
	s_or_b64 exec, exec, s[0:1]                                // 0000000001F4: 88FE007E
	buffer_load_dword v16, v17, s[12:15], 0 offen offset:24    // 0000000001F8: E0301018 80031011
	v_and_b32_e32 v10, 8, v18                                  // 000000000200: 36142488
	s_mov_b64 s[0:1], exec                                     // 000000000204: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 000000000208: 7DAA1480
	s_cbranch_execz _L3                                        // 00000000020C: BF88000C
	s_clause 0x1                                               // 000000000210: BFA10001
	s_load_dwordx8 s[16:23], s[24:25], 0xb0                    // 000000000214: F40C040C FA0000B0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 00000000021C: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v21                                 // 000000000224: 7E140D15
	s_waitcnt lgkmcnt(0)                                       // 000000000228: BF8CC07F
	image_sample  v10, [v9, v12, v10], s[16:23], s[8:11] dmask:0x1 dim:SQ_RSRC_IMG_2D_ARRAY// 00000000022C: F080012A 00440A09 00000A0C
	s_waitcnt vmcnt(0)                                         // 000000000238: BF8C3F70
	v_mul_f32_e32 v16, v10, v16                                // 00000000023C: 1020210A
_L3:
	s_or_b64 exec, exec, s[0:1]                                // 000000000240: 88FE007E
	buffer_load_dword v17, v17, s[12:15], 0 offen offset:28    // 000000000244: E030101C 80031111
	s_load_dwordx4 s[20:23], s[24:25], 0x30                    // 00000000024C: F408050C FA000030
	v_and_b32_e32 v10, 16, v18                                 // 000000000254: 36142490
	v_mov_b32_e32 v23, 0                                       // 000000000258: 7E2E0280
	v_mov_b32_e32 v18, 0                                       // 00000000025C: 7E240280
	v_mov_b32_e32 v19, 0                                       // 000000000260: 7E260280
	v_mov_b32_e32 v20, 0                                       // 000000000264: 7E280280
	s_mov_b64 s[0:1], exec                                     // 000000000268: BE80047E
	v_cmpx_ne_u32_e32 0, v10                                   // 00000000026C: 7DAA1480
	s_cbranch_execz _L4                                        // 000000000270: BF88000E
	s_clause 0x1                                               // 000000000274: BFA10001
	s_load_dwordx8 s[36:43], s[24:25], 0xd0                    // 000000000278: F40C090C FA0000D0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 000000000280: F408020C FA000020
	v_cvt_f32_u32_e32 v10, v21                                 // 000000000288: 7E140D15
	s_waitcnt lgkmcnt(0)                                       // 00000000028C: BF8CC07F
	image_sample  v[9:11], [v9, v12, v10], s[36:43], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D_ARRAY// 000000000290: F080072A 00490909 00000A0C
	s_waitcnt vmcnt(0)                                         // 00000000029C: BF8C3F70
	v_mul_f32_e32 v18, v9, v17                                 // 0000000002A0: 10242309
	v_mul_f32_e32 v19, v10, v17                                // 0000000002A4: 1026230A
	v_mul_f32_e32 v20, v11, v17                                // 0000000002A8: 1028230B
_L4:
	s_or_b64 exec, exec, s[0:1]                                // 0000000002AC: 88FE007E
	s_clause 0x1                                               // 0000000002B0: BFA10001
	s_load_dwordx8 s[36:43], s[24:25], 0xf0                    // 0000000002B4: F40C090C FA0000F0
	s_load_dwordx4 s[8:11], s[24:25], 0x20                     // 0000000002BC: F408020C FA000020
	s_mov_b32 m0, s2                                           // 0000000002C4: BEFC0302
	v_interp_p1_f32_e32 v9, v0, attr5.x                        // 0000000002C8: C8241400
	v_interp_p1_f32_e32 v10, v0, attr5.y                       // 0000000002CC: C8281500
	v_interp_p2_f32_e32 v9, v1, attr5.x                        // 0000000002D0: C8251401
	v_interp_p2_f32_e32 v10, v1, attr5.y                       // 0000000002D4: C8291501
	s_and_b64 exec, exec, s[4:5]                               // 0000000002D8: 87FE047E
	s_waitcnt lgkmcnt(0)                                       // 0000000002DC: BF8CC07F
	image_sample v[9:11], v[9:10], s[36:43], s[8:11] dmask:0x7 dim:SQ_RSRC_IMG_2D// 0000000002E0: F0800708 00490909
	s_buffer_load_dword s3, s[20:23], null                     // 0000000002E8: F42000CA FA000000
	v_interp_p1_f32_e32 v12, v0, attr6.x                       // 0000000002F0: C8301800
	v_interp_p1_f32_e32 v21, v0, attr6.y                       // 0000000002F4: C8541900
	v_interp_p1_f32_e32 v22, v0, attr6.z                       // 0000000002F8: C8581A00
	v_mov_b32_e32 v28, 0                                       // 0000000002FC: 7E380280
	v_mov_b32_e32 v29, 0                                       // 000000000300: 7E3A0280
	v_interp_p2_f32_e32 v12, v1, attr6.x                       // 000000000304: C8311801
	v_interp_p2_f32_e32 v21, v1, attr6.y                       // 000000000308: C8551901
	v_interp_p2_f32_e32 v22, v1, attr6.z                       // 00000000030C: C8591A01
	s_waitcnt vmcnt(4)                                         // 000000000310: BF8C3F74
	v_mul_f32_e32 v5, v5, v12                                  // 000000000314: 100A1905
	v_mul_f32_e32 v6, v6, v21                                  // 000000000318: 100C2B06
	v_mul_f32_e32 v7, v7, v22                                  // 00000000031C: 100E2D07
	s_waitcnt lgkmcnt(0)                                       // 000000000320: BF8CC07F
	s_cmp_eq_u32 s3, 0                                         // 000000000324: BF068003
	s_cbranch_scc1 _L5                                         // 000000000328: BF85019F
	s_mov_b32 m0, s2                                           // 00000000032C: BEFC0302
	s_clause 0x1                                               // 000000000330: BFA10001
	s_buffer_load_dwordx8 s[4:11], s[12:15], 0xa0              // 000000000334: F42C0106 FA0000A0
	s_buffer_load_dwordx8 s[12:19], s[12:15], 0x80             // 00000000033C: F42C0306 FA000080
	v_interp_p1_f32_e32 v23, v0, attr7.y                       // 000000000344: C85C1D00
	v_interp_p1_f32_e32 v29, v0, attr7.x                       // 000000000348: C8741C00
	v_interp_p1_f32_e32 v12, v0, attr0.x                       // 00000000034C: C8300000
	v_interp_p1_f32_e32 v35, v0, attr7.z                       // 000000000350: C88C1E00
	v_interp_p1_f32_e32 v21, v0, attr0.y                       // 000000000354: C8540100
	v_interp_p2_f32_e32 v23, v1, attr7.y                       // 000000000358: C85D1D01
	v_interp_p2_f32_e32 v29, v1, attr7.x                       // 00000000035C: C8751C01
	v_interp_p2_f32_e32 v12, v1, attr0.x                       // 000000000360: C8310001
	v_interp_p2_f32_e32 v35, v1, attr7.z                       // 000000000364: C88D1E01
	v_interp_p1_f32_e32 v22, v0, attr0.z                       // 000000000368: C8580200
	v_mul_f32_e32 v30, v23, v23                                // 00000000036C: 103C2F17
	v_interp_p2_f32_e32 v21, v1, attr0.y                       // 000000000370: C8550101
	v_add_f32_e32 v28, 0xbd23d70a, v6                          // 000000000374: 06380CFF BD23D70A
	s_waitcnt vmcnt(3)                                         // 00000000037C: BF8C3F73
	v_add_f32_e32 v37, 1.0, v3                                 // 000000000380: 064A06F2
	v_interp_p2_f32_e32 v22, v1, attr0.z                       // 000000000384: C8590201
	v_fmac_f32_e32 v30, v29, v29                               // 000000000388: 563C3B1D
	v_mov_b32_e32 v38, 1.0                                     // 00000000038C: 7E4C02F2
	v_fmaak_f32 v25, v2, v28, 0x3d23d70a                       // 000000000390: 5A323902 3D23D70A
	s_waitcnt lgkmcnt(0)                                       // 000000000398: BF8CC07F
	v_fma_f32 v26, v12, s8, s11                                // 00000000039C: D54B001A 002C110C
	v_fma_f32 v31, v12, s12, s15                               // 0000000003A4: D54B001F 003C190C
	v_fmac_f32_e32 v30, v35, v35                               // 0000000003AC: 563C4723
	v_fma_f32 v33, v12, s16, s19                               // 0000000003B0: D54B0021 004C210C
	v_fma_f32 v42, v12, s4, s7                                 // 0000000003B8: D54B002A 001C090C
	v_fmac_f32_e32 v26, s9, v21                                // 0000000003C0: 56342A09
	v_fmac_f32_e32 v31, s13, v21                               // 0000000003C4: 563E2A0D
	v_rsq_f32_e32 v32, v30                                     // 0000000003C8: 7E405D1E
	v_cmp_neq_f32_e32 vcc_lo, 0, v30                           // 0000000003CC: 7C1A3C80
	v_fmac_f32_e32 v33, s17, v21                               // 0000000003D0: 56422A11
	v_fmac_f32_e32 v26, s10, v22                               // 0000000003D4: 56342C0A
	v_fma_f32 v31, v22, s14, v31 div:2                         // 0000000003D8: D54B001F 1C7C1D16
	v_fmac_f32_e32 v42, s5, v21                                // 0000000003E0: 56542A05
	v_fma_f32 v34, -v2, v28, 0x3f75c28f                        // 0000000003E4: D54B0022 23FE3902 3F75C28F
	s_mov_b32 s0, 0xbea2f983                                   // 0000000003F0: BE8003FF BEA2F983
	v_rcp_f32_e32 v39, v26                                     // 0000000003F8: 7E4E551A
	v_fmaak_f32 v41, s0, v2, 0x3ea2f983                        // 0000000003FC: 5A520400 3EA2F983
	v_cndmask_b32_e32 v40, 0, v32, vcc_lo                      // 000000000404: 02504080
	v_fma_f32 v32, v22, s18, v33 div:2                         // 000000000408: D54B0020 1C842516
	v_add_f32_e32 v27, 0xbd23d70a, v5                          // 000000000410: 06360AFF BD23D70A
	v_add_f32_e32 v36, 0xbd23d70a, v7                          // 000000000418: 06480EFF BD23D70A
	s_mov_b32 s7, 0x3d03126f                                   // 000000000420: BE8703FF 3D03126F
	v_mul_f32_e32 v30, v40, v23                                // 000000000428: 103C2F28
	v_mul_f32_e32 v35, v40, v35                                // 00000000042C: 10464728
	v_fmaak_f32 v24, v2, v27, 0x3d23d70a                       // 000000000430: 5A303702 3D23D70A
	v_mul_f32_e32 v23, v31, v39                                // 000000000438: 102E4F1F
	v_mul_f32_e32 v33, v32, v39                                // 00000000043C: 10424F20
	v_mul_f32_e32 v31, v40, v29                                // 000000000440: 103E3B28
	v_mul_f32_e32 v29, v15, v30                                // 000000000444: 103A3D0F
	v_mul_f32_e32 v40, v37, v37                                // 000000000448: 10504B25
	v_add_f32_e32 v32, 0.5, v23                                // 00000000044C: 06402EF0
	v_add_f32_e32 v33, 0.5, v33                                // 000000000450: 064242F0
	v_fma_f32 v23, v22, s6, v42 div:2                          // 000000000454: D54B0017 1CA80D16
	v_fmac_f32_e32 v29, v13, v31                               // 00000000045C: 563A3F0D
	v_fmaak_f32 v26, v2, v36, 0x3d23d70a                       // 000000000460: 5A344902 3D23D70A
	v_fma_f32 v27, -v2, v27, 0x3f75c28f                        // 000000000468: D54B001B 23FE3702 3F75C28F
	v_min_f32_e32 v28, v32, v33                                // 000000000474: 1E384320
	v_max_f32_e32 v37, v32, v33                                // 000000000478: 204A4320
	v_fmac_f32_e32 v29, v14, v35                               // 00000000047C: 563A470E
	v_mul_f32_e32 v23, v23, v39                                // 000000000480: 102E4F17
	v_fma_f32 v36, -v2, v36, 0x3f75c28f                        // 000000000484: D54B0024 23FE4902 3F75C28F
	v_cmp_ngt_f32_e32 vcc_lo, 0, v28                           // 000000000490: 7C163880
	v_cmp_nlt_f32_e64 s0, 1.0, v37                             // 000000000494: D40E0000 00024AF2
	v_max_f32_e32 v28, 0, v29                                  // 00000000049C: 20383A80
	v_mul_f32_e32 v29, v3, v3                                  // 0000000004A0: 103A0703
	v_fmamk_f32 v37, v40, 0xbe000000, v38                      // 0000000004A4: 584A4D28 BE000000
	v_mul_f32_e32 v38, 0x3e000000, v40                         // 0000000004AC: 104C50FF 3E000000
	v_mul_f32_e32 v39, v41, v5                                 // 0000000004B4: 104E0B29
	v_mul_f32_e32 v40, v41, v6                                 // 0000000004B8: 10500D29
	v_mul_f32_e32 v43, v29, v29                                // 0000000004BC: 10563B1D
	v_mul_f32_e32 v41, v41, v7                                 // 0000000004C0: 10520F29
	v_fma_f32 v45, v37, v28, v38                               // 0000000004C4: D54B002D 049A3925
	v_add_f32_e32 v42, 0.5, v23                                // 0000000004CC: 06542EF0
	v_fma_f32 v44, v29, v29, -1.0                              // 0000000004D0: D54B002C 03CE3B1D
	v_mul_f32_e32 v43, v28, v43                                // 0000000004D8: 1056571C
	v_mul_f32_e32 v46, 4.0, v28                                // 0000000004DC: 105C38F6
	v_mul_f32_e32 v45, 0x40490fdb, v45                         // 0000000004E0: 105A5AFF 40490FDB
	v_mov_b32_e32 v29, 0                                       // 0000000004E8: 7E3A0280
	v_mov_b32_e32 v28, 0                                       // 0000000004EC: 7E380280
	v_mov_b32_e32 v23, 0                                       // 0000000004F0: 7E2E0280
	s_and_b64 s[4:5], s[0:1], vcc                              // 0000000004F4: 87846A00
	s_mov_b32 s6, 0                                            // 0000000004F8: BE860380
	s_mov_b32 s8, 0                                            // 0000000004FC: BE880380
	s_branch _L6                                               // 000000000500: BF82005A
_L12:
	s_or_b64 exec, exec, s[0:1]                                // 000000000504: 88FE007E
	v_add_f32_e32 v52, v47, v30                                // 000000000508: 06683D2F
	v_add_f32_e32 v53, v48, v31                                // 00000000050C: 066A3F30
	v_add_f32_e32 v55, v49, v35                                // 000000000510: 066E4731
	v_mul_f32_e32 v57, v47, v15                                // 000000000514: 10721F2F
	s_add_i32 s0, s6, 44                                       // 000000000518: 8100AC06
	v_mul_f32_e32 v54, v52, v52                                // 00000000051C: 106C6934
	s_add_i32 s9, s6, 36                                       // 000000000520: 8109A406
	s_add_i32 s1, s6, 32                                       // 000000000524: 8101A006
	v_fmac_f32_e32 v57, v48, v13                               // 000000000528: 56721B30
	s_add_i32 s10, s6, 40                                      // 00000000052C: 810AA806
	v_fmac_f32_e32 v54, v53, v53                               // 000000000530: 566C6B35
	s_clause 0x3                                               // 000000000534: BFA10003
	s_buffer_load_dword s0, s[20:23], s0                       // 000000000538: F420000A 00000000
	s_buffer_load_dword s1, s[20:23], s1                       // 000000000540: F420004A 02000000
	s_buffer_load_dword s9, s[20:23], s9                       // 000000000548: F420024A 12000000
	s_buffer_load_dword s10, s[20:23], s10                     // 000000000550: F420028A 14000000
	v_fmac_f32_e32 v57, v49, v14                               // 000000000558: 56721D31
	s_add_i32 s8, s8, 1                                        // 00000000055C: 81088108
	v_fmac_f32_e32 v54, v55, v55                               // 000000000560: 566C6F37
	s_add_i32 s6, s6, 64                                       // 000000000564: 8106C006
	s_cmp_lt_u32 s8, s3                                        // 000000000568: BF0A0308
	v_rsq_f32_e32 v56, v54                                     // 00000000056C: 7E705D36
	v_cmp_neq_f32_e32 vcc_lo, 0, v54                           // 000000000570: 7C1A6C80
	s_waitcnt lgkmcnt(0)                                       // 000000000574: BF8CC07F
	v_mul_f32_e32 v50, s0, v50                                 // 000000000578: 10646400
	v_cndmask_b32_e32 v54, 0, v56, vcc_lo                      // 00000000057C: 026C7080
	v_mul_f32_e32 v50, v50, v51                                // 000000000580: 10646732
	v_mul_f32_e32 v52, v54, v52                                // 000000000584: 10686936
	v_mul_f32_e32 v53, v54, v53                                // 000000000588: 106A6B36
	v_mul_f32_e32 v54, v54, v55                                // 00000000058C: 106C6F36
	v_mul_f32_e32 v56, v52, v15                                // 000000000590: 10701F34
	v_mul_f32_e32 v52, v52, v30                                // 000000000594: 10683D34
	v_fmac_f32_e32 v56, v53, v13                               // 000000000598: 56701B35
	v_fmac_f32_e32 v52, v53, v31                               // 00000000059C: 56683F35
	v_max_f32_e32 v53, 0, v57                                  // 0000000005A0: 206A7280
	v_fmac_f32_e32 v56, v54, v14                               // 0000000005A4: 56701D36
	v_fmac_f32_e32 v52, v54, v35                               // 0000000005A8: 56684736
	v_fma_f32 v54, v53, v37, v38                               // 0000000005AC: D54B0036 049A4B35
	v_max_f32_e32 v55, 0, v56                                  // 0000000005B4: 206E7080
	v_max_f32_e32 v52, 0, v52                                  // 0000000005B8: 20686880
	v_mul_f32_e32 v54, v45, v54                                // 0000000005BC: 106C6D2D
	v_fmaak_f32 v56, v53, v46, 0x38d1b717                      // 0000000005C0: 5A705D35 38D1B717
	v_mul_f32_e32 v55, v55, v55                                // 0000000005C8: 106E6F37
	v_sub_f32_e64 v52, 1.0, v52 clamp                          // 0000000005CC: D5048034 000268F2
	v_fma_f32 v55, v55, v44, 1.0                               // 0000000005D4: D54B0037 03CA5937
	v_mul_f32_e32 v55, v55, v55                                // 0000000005DC: 106E6F37
	v_mul_f32_e32 v54, v55, v54                                // 0000000005E0: 106C6D37
	v_mul_f32_e32 v55, v52, v52                                // 0000000005E4: 106E6934
	v_mul_f32_e32 v54, v54, v56                                // 0000000005E8: 106C7136
	v_mul_f32_e32 v55, v55, v55                                // 0000000005EC: 106E6F37
	v_mul_f32_e32 v56, s1, v53                                 // 0000000005F0: 10706A01
	v_rcp_f32_e32 v54, v54                                     // 0000000005F4: 7E6C5536
	v_mul_f32_e32 v52, v52, v55                                // 0000000005F8: 10686F34
	v_mul_f32_e32 v55, v53, v43                                // 0000000005FC: 106E5735
	v_fma_f32 v57, v52, v27, v24                               // 000000000600: D54B0039 04623734
	v_fma_f32 v58, v52, v34, v25                               // 000000000608: D54B003A 04664534
	v_fma_f32 v52, v52, v36, v26                               // 000000000610: D54B0034 046A4934
	v_fma_f32 v59, v55, v54, -v39                              // 000000000618: D54B003B 849E6D37
	v_fma_f32 v60, v55, v54, -v40                              // 000000000620: D54B003C 84A26D37
	v_fma_f32 v54, v55, v54, -v41                              // 000000000628: D54B0036 84A66D37
	v_mul_f32_e32 v55, s9, v53                                 // 000000000630: 106E6A09
	v_mul_f32_e32 v53, s10, v53                                // 000000000634: 106A6A0A
	v_fma_f32 v57, v57, v59, v39                               // 000000000638: D54B0039 049E7739
	v_fma_f32 v58, v58, v60, v40                               // 000000000640: D54B003A 04A2793A
	v_fma_f32 v52, v52, v54, v41                               // 000000000648: D54B0034 04A66D34
	v_mul_f32_e32 v51, v56, v57                                // 000000000650: 10667338
	v_mul_f32_e32 v54, v55, v58                                // 000000000654: 106C7537
	v_mul_f32_e32 v52, v53, v52                                // 000000000658: 10686935
	v_fmac_f32_e32 v29, v51, v50                               // 00000000065C: 563A6533
	v_fmac_f32_e32 v28, v54, v50                               // 000000000660: 56386536
	v_fmac_f32_e32 v23, v52, v50                               // 000000000664: 562E6534
	s_cbranch_scc0 _L5                                         // 000000000668: BF8400CF
_L6:
	s_add_i32 s9, s6, 48                                       // 00000000066C: 8109B006
	s_add_i32 s0, s6, 16                                       // 000000000670: 81009006
	s_add_i32 s11, s6, 24                                      // 000000000674: 810B9806
	s_add_i32 s1, s6, 20                                       // 000000000678: 81019406
	s_clause 0x3                                               // 00000000067C: BFA10003
	s_buffer_load_dword s15, s[20:23], s9                      // 000000000680: F42003CA 12000000
	s_buffer_load_dword s9, s[20:23], s0                       // 000000000688: F420024A 00000000
	s_buffer_load_dword s10, s[20:23], s1                      // 000000000690: F420028A 02000000
	s_buffer_load_dword s11, s[20:23], s11                     // 000000000698: F42002CA 16000000
	s_waitcnt lgkmcnt(0)                                       // 0000000006A0: BF8CC07F
	s_cmp_lg_u32 s15, 0                                        // 0000000006A4: BF07800F
	s_cbranch_scc0 _L7                                         // 0000000006A8: BF8400BE
	s_add_i32 s0, s6, 4                                        // 0000000006AC: 81008406
	s_add_i32 s1, s6, 8                                        // 0000000006B0: 81018806
	s_clause 0x2                                               // 0000000006B4: BFA10002
	s_buffer_load_dword s13, s[20:23], s6                      // 0000000006B8: F420034A 0C000000
	s_buffer_load_dword s14, s[20:23], s0                      // 0000000006C0: F420038A 00000000
	s_buffer_load_dword s12, s[20:23], s1                      // 0000000006C8: F420030A 02000000
	s_cmp_lg_u32 s15, 1                                        // 0000000006D0: BF07810F
	s_mov_b64 s[0:1], -1                                       // 0000000006D4: BE8004C1
	s_cbranch_scc0 _L8                                         // 0000000006D8: BF840038
	v_mov_b32_e32 v50, 1.0                                     // 0000000006DC: 7E6402F2
	s_cmp_lg_u32 s15, 2                                        // 0000000006E0: BF07820F
	s_cbranch_scc1 _L9                                         // 0000000006E4: BF850034
	s_waitcnt lgkmcnt(0)                                       // 0000000006E8: BF8CC07F
	v_sub_f32_e32 v47, s14, v21                                // 0000000006EC: 085E2A0E
	s_add_i32 s0, s6, 28                                       // 0000000006F0: 81009C06
	v_mul_f32_e64 v48, s10, s10                                // 0000000006F4: D5080030 0000140A
	s_buffer_load_dword s0, s[20:23], s0                       // 0000000006FC: F420000A 00000000
	v_sub_f32_e32 v49, s13, v12                                // 000000000704: 0862180D
	v_mul_f32_e32 v50, v47, v47                                // 000000000708: 10645F2F
	v_sub_f32_e32 v51, s12, v22                                // 00000000070C: 08662C0C
	v_fmac_f32_e64 v48, s9, s9                                 // 000000000710: D52B0030 00001209
	v_fmac_f32_e32 v50, v49, v49                               // 000000000718: 56646331
	v_fmac_f32_e64 v48, s11, s11                               // 00000000071C: D52B0030 0000160B
	v_fmac_f32_e32 v50, v51, v51                               // 000000000724: 56646733
	v_rsq_f32_e32 v52, v48                                     // 000000000728: 7E685D30
	v_cmp_neq_f32_e32 vcc_lo, 0, v48                           // 00000000072C: 7C1A6080
	v_rsq_f32_e32 v53, v50                                     // 000000000730: 7E6A5D32
	s_waitcnt lgkmcnt(0)                                       // 000000000734: BF8CC07F
	v_mul_f32_e64 v48, s0, 0.15915494                          // 000000000738: D5080030 0001F000
	v_mul_f32_e64 v54, 0x3e4391d1, s0                          // 000000000740: D5080036 000000FF 3E4391D1
	v_cmp_neq_f32_e64 s0, 0, v50                               // 00000000074C: D40D0000 00026480
	v_sqrt_f32_e32 v50, v50                                    // 000000000754: 7E646732
	v_cndmask_b32_e32 v52, 0, v52, vcc_lo                      // 000000000758: 02686880
	v_cos_f32_e32 v55, v48                                     // 00000000075C: 7E6E6D30
	v_cos_f32_e32 v54, v54                                     // 000000000760: 7E6C6D36
	v_cndmask_b32_e64 v53, 0, v53, s0                          // 000000000764: D5010035 00026A80
	v_mul_f32_e32 v56, s10, v52                                // 00000000076C: 1070680A
	v_mul_f32_e32 v57, s9, v52                                 // 000000000770: 10726809
	v_mul_f32_e32 v52, s11, v52                                // 000000000774: 1068680B
	v_mul_f32_e32 v47, v53, v47                                // 000000000778: 105E5F35
	v_fmaak_f32 v58, s7, v50, 0x3db851ec                       // 00000000077C: 5A746407 3DB851EC
	v_mul_f32_e32 v48, v53, v49                                // 000000000784: 10606335
	v_mul_f32_e32 v49, v53, v51                                // 000000000788: 10626735
	v_sub_f32_e32 v55, v54, v55                                // 00000000078C: 086E6F36
	v_fmac_f32_e32 v54, v47, v56                               // 000000000790: 566C712F
	v_fma_f32 v50, v58, v50, 1.0                               // 000000000794: D54B0032 03CA653A
	v_rcp_f32_e32 v51, v55                                     // 00000000079C: 7E665537
	v_fmac_f32_e32 v54, v48, v57                               // 0000000007A0: 566C7330
	v_rcp_f32_e32 v50, v50                                     // 0000000007A4: 7E645532
	v_fmac_f32_e32 v54, v49, v52                               // 0000000007A8: 566C6931
	v_mul_f32_e64 v51, v54, v51 clamp                          // 0000000007AC: D5088033 00026736
	v_mul_f32_e32 v50, v51, v50                                // 0000000007B4: 10646533
_L9:
	s_mov_b64 s[0:1], 0                                        // 0000000007B8: BE800480
_L8:
	s_andn2_b64 vcc, exec, s[0:1]                              // 0000000007BC: 8AEA007E
	s_cbranch_vccnz _L10                                       // 0000000007C0: BF870013
	s_waitcnt lgkmcnt(0)                                       // 0000000007C4: BF8CC07F
	v_sub_f32_e32 v47, s14, v21                                // 0000000007C8: 085E2A0E
	v_sub_f32_e32 v48, s13, v12                                // 0000000007CC: 0860180D
	v_sub_f32_e32 v51, s12, v22                                // 0000000007D0: 08662C0C
	v_mul_f32_e32 v49, v47, v47                                // 0000000007D4: 10625F2F
	v_fmac_f32_e32 v49, v48, v48                               // 0000000007D8: 56626130
	v_fmac_f32_e32 v49, v51, v51                               // 0000000007DC: 56626733
	v_sqrt_f32_e32 v50, v49                                    // 0000000007E0: 7E646731
	v_rsq_f32_e32 v52, v49                                     // 0000000007E4: 7E685D31
	v_cmp_neq_f32_e32 vcc_lo, 0, v49                           // 0000000007E8: 7C1A6280
	v_fmaak_f32 v53, s7, v50, 0x3db851ec                       // 0000000007EC: 5A6A6407 3DB851EC
	v_cndmask_b32_e32 v49, 0, v52, vcc_lo                      // 0000000007F4: 02626880
	v_fma_f32 v50, v53, v50, 1.0                               // 0000000007F8: D54B0032 03CA6535
	v_mul_f32_e32 v48, v49, v48                                // 000000000800: 10606131
	v_mul_f32_e32 v47, v49, v47                                // 000000000804: 105E5F31
	v_mul_f32_e32 v49, v49, v51                                // 000000000808: 10626731
	v_rcp_f32_e32 v50, v50                                     // 00000000080C: 7E645532
_L10:
	s_cbranch_execnz _L11                                      // 000000000810: BF890010
_L13:
	v_mul_f32_e64 v47, s10, s10                                // 000000000814: D508002F 0000140A
	v_mov_b32_e32 v50, 1.0                                     // 00000000081C: 7E6402F2
	v_fmac_f32_e64 v47, s9, s9                                 // 000000000820: D52B002F 00001209
	v_fmac_f32_e64 v47, s11, s11                               // 000000000828: D52B002F 0000160B
	v_rsq_f32_e32 v48, v47                                     // 000000000830: 7E605D2F
	v_cmp_neq_f32_e32 vcc_lo, 0, v47                           // 000000000834: 7C1A5E80
	v_cndmask_b32_e32 v49, 0, v48, vcc_lo                      // 000000000838: 02626080
	v_mul_f32_e64 v48, v49, -s9                                // 00000000083C: D5080030 40001331
	v_mul_f32_e64 v47, v49, -s10                               // 000000000844: D508002F 40001531
	v_mul_f32_e64 v49, v49, -s11                               // 00000000084C: D5080031 40001731
_L11:
	v_mov_b32_e32 v51, 1.0                                     // 000000000854: 7E6602F2
	s_and_saveexec_b64 s[0:1], s[4:5]                          // 000000000858: BE802404
	s_cbranch_execz _L12                                       // 00000000085C: BF88FF29
	v_mul_f32_e32 v51, v48, v13                                // 000000000860: 10661B30
	s_clause 0x1                                               // 000000000864: BFA10001
	s_buffer_load_dwordx4 s[28:31], s[20:23], 0x1c             // 000000000868: F428070A FA00001C
	s_buffer_load_dword s9, s[20:23], 0x2c                     // 000000000870: F420024A FA00002C
	s_waitcnt lgkmcnt(0)                                       // 000000000878: BF8CC07F
	s_clause 0x1                                               // 00000000087C: BFA10001
	s_load_dwordx8 s[12:19], s[24:25], 0x110                   // 000000000880: F40C030C FA000110
	s_load_dwordx4 s[36:39], s[24:25], 0x60                    // 000000000888: F408090C FA000060
	v_fmac_f32_e32 v51, v47, v15                               // 000000000890: 56661F2F
	v_fmac_f32_e32 v51, v49, v14                               // 000000000894: 56661D31
	v_sub_f32_e32 v51, 1.0, v51                                // 000000000898: 086666F2
	v_mul_f32_e32 v51, s29, v51                                // 00000000089C: 1066661D
	v_rcp_f32_e32 v52, s31                                     // 0000000008A0: 7E68541F
	v_rcp_f32_e32 v53, s30                                     // 0000000008A4: 7E6A541E
	v_mul_f32_e64 v54, s9, 0                                   // 0000000008A8: D5080036 00010009
	v_max_f32_e32 v51, s28, v51                                // 0000000008B0: 2066661C
	v_sub_f32_e32 v51, v42, v51                                // 0000000008B4: 0866672A
	v_fma_f32 v55, -s9, v52, v33                               // 0000000008B8: D54B0037 24866809
	v_fma_f32 v56, s9, v53, v32                                // 0000000008C0: D54B0038 04826A09
	v_fma_f32 v57, v54, v52, v33                               // 0000000008C8: D54B0039 04866936
	v_fma_f32 v58, -s9, v53, v32                               // 0000000008D0: D54B003A 24826A09
	v_fma_f32 v52, s9, v52, v33                                // 0000000008D8: D54B0034 04866809
	s_waitcnt lgkmcnt(0)                                       // 0000000008E0: BF8CC07F
	s_clause 0x4                                               // 0000000008E4: BFA10004
	image_sample_c_lz  v59, [v51, v56, v55], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008E8: F0BC010A 01233B33 00003738
	image_sample_c_lz  v60, [v51, v56, v57], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 0000000008F4: F0BC010A 01233C33 00003938
	image_sample_c_lz  v61, [v51, v58, v57], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000900: F0BC010A 01233D33 0000393A
	image_sample_c_lz  v62, [v51, v58, v55], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000090C: F0BC010A 01233E33 0000373A
	image_sample_c_lz  v58, [v51, v58, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000918: F0BC010A 01233A33 0000343A
	v_fma_f32 v53, v54, v53, v32                               // 000000000924: D54B0035 04826B36
	s_clause 0x3                                               // 00000000092C: BFA10003
	image_sample_c_lz  v54, [v51, v53, v55], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000930: F0BC010A 01233633 00003735
	image_sample_c_lz  v55, [v51, v53, v57], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 00000000093C: F0BC010A 01233733 00003935
	image_sample_c_lz  v53, [v51, v53, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000948: F0BC010A 01233533 00003435
	image_sample_c_lz  v51, [v51, v56, v52], s[12:19], s[36:39] dmask:0x1 dim:SQ_RSRC_IMG_2D// 000000000954: F0BC010A 01233333 00003438
	s_waitcnt vmcnt(5)                                         // 000000000960: BF8C3F75
	v_add_f32_e32 v52, v61, v62                                // 000000000964: 06687D3D
	s_waitcnt vmcnt(4)                                         // 000000000968: BF8C3F74
	v_add_f32_e32 v52, v58, v52                                // 00000000096C: 0668693A
	s_waitcnt vmcnt(3)                                         // 000000000970: BF8C3F73
	v_add_f32_e32 v52, v54, v52                                // 000000000974: 06686936
	s_waitcnt vmcnt(2)                                         // 000000000978: BF8C3F72
	v_add_f32_e32 v52, v55, v52                                // 00000000097C: 06686937
	s_waitcnt vmcnt(1)                                         // 000000000980: BF8C3F71
	v_add_f32_e32 v52, v53, v52                                // 000000000984: 06686935
	v_add_f32_e32 v52, v59, v52                                // 000000000988: 0668693B
	v_add_f32_e32 v52, v60, v52                                // 00000000098C: 0668693C
	s_waitcnt vmcnt(0)                                         // 000000000990: BF8C3F70
	v_add_f32_e32 v51, v51, v52                                // 000000000994: 06666933
	v_mul_f32_e32 v51, 0x3de38e39, v51                         // 000000000998: 106666FF 3DE38E39
	s_branch _L12                                              // 0000000009A0: BF82FED8
_L7:
	s_branch _L13                                              // 0000000009A4: BF82FF9B
_L5:
	s_mov_b32 m0, s2                                           // 0000000009A8: BEFC0302
	s_clause 0x2                                               // 0000000009AC: BFA10002
	s_buffer_load_dword s2, s[20:23], 0x18                     // 0000000009B0: F420008A FA000018
	s_buffer_load_dword s3, s[20:23], 0x4                      // 0000000009B8: F42000CA FA000004
	s_buffer_load_dwordx2 s[0:1], s[20:23], 0x10               // 0000000009C0: F424000A FA000010
	v_add_f32_e32 v12, v29, v18                                // 0000000009C8: 0618251D
	v_add_f32_e32 v18, v28, v19                                // 0000000009CC: 0624271C
	v_add_f32_e32 v19, v23, v20                                // 0000000009D0: 06262917
	v_interp_p1_f32_e32 v20, v0, attr8.w                       // 0000000009D4: C8502300
	v_interp_p1_f32_e32 v24, v0, attr9.w                       // 0000000009D8: C8602700
	v_interp_p1_f32_e32 v21, v0, attr8.x                       // 0000000009DC: C8542000
	v_interp_p1_f32_e32 v22, v0, attr8.y                       // 0000000009E0: C8582100
	v_interp_p1_f32_e32 v23, v0, attr9.x                       // 0000000009E4: C85C2400
	v_interp_p2_f32_e32 v20, v1, attr8.w                       // 0000000009E8: C8512301
	v_interp_p2_f32_e32 v24, v1, attr9.w                       // 0000000009EC: C8612701
	v_interp_p1_f32_e32 v25, v0, attr9.y                       // 0000000009F0: C8642500
	v_interp_p1_f32_e32 v0, v0, attr11.x                       // 0000000009F4: C8002C00
	v_interp_p2_f32_e32 v21, v1, attr8.x                       // 0000000009F8: C8552001
	v_rcp_f32_e64 v20, v20 div:2                               // 0000000009FC: D5AA0014 18000114
	v_interp_p2_f32_e32 v22, v1, attr8.y                       // 000000000A04: C8592101
	v_rcp_f32_e64 v24, v24 div:2                               // 000000000A08: D5AA0018 18000118
	v_interp_p2_f32_e32 v23, v1, attr9.x                       // 000000000A10: C85D2401
	v_interp_p2_f32_e32 v25, v1, attr9.y                       // 000000000A14: C8652501
	s_waitcnt vmcnt(2) lgkmcnt(0)                              // 000000000A18: BF8C0072
	v_mul_f32_e32 v26, s3, v16                                 // 000000000A1C: 10342003
	v_interp_p2_f32_e32 v0, v1, attr11.x                       // 000000000A20: C8012C01
	v_mul_f32_e32 v1, v21, v20                                 // 000000000A24: 10022915
	s_waitcnt vmcnt(0)                                         // 000000000A28: BF8C3F70
	v_fma_f32 v9, s0, v26, v9                                  // 000000000A2C: D54B0009 04263400
	v_fma_f32 v10, s1, v26, v10                                // 000000000A34: D54B000A 042A3401
	v_fmac_f32_e32 v11, s2, v26                                // 000000000A3C: 56163402
	v_fma_f32 v1, v24, v23, -v1                                // 000000000A40: D54B0001 84062F18
	v_fmac_f32_e32 v12, v9, v5                                 // 000000000A48: 56180B09
	v_fmac_f32_e32 v18, v10, v6                                // 000000000A4C: 56240D0A
	v_fmac_f32_e32 v19, v11, v7                                // 000000000A50: 56260F0B
	v_mul_f32_e32 v5, v22, v20                                 // 000000000A54: 100A2916
	v_fma_f32 v9, v13, 0.5, 0.5                                // 000000000A58: D54B0009 03C1E10D
	v_mul_f32_e32 v6, v12, v0                                  // 000000000A60: 100C010C
	v_mul_f32_e32 v7, v18, v0                                  // 000000000A64: 100E0112
	v_mul_f32_e32 v0, v19, v0                                  // 000000000A68: 10000113
	v_fma_f32 v5, v24, v25, -v5                                // 000000000A6C: D54B0005 84163318
	v_fma_f32 v10, v15, 0.5, 0.5                               // 000000000A74: D54B000A 03C1E10F
	v_fma_f32 v11, v14, 0.5, 0.5                               // 000000000A7C: D54B000B 03C1E10E
	v_mov_b32_e32 v12, 1.0                                     // 000000000A84: 7E1802F2
	exp mrt0 v6, v7, v0, v8 vm                                 // 000000000A88: F800100F 08000706
	exp mrt1 v9, v10, v11, v3 vm                               // 000000000A90: F800101F 030B0A09
	exp mrt2 v1, v5, v4, v12 vm                                // 000000000A98: F800102F 0C040501
	exp mrt3 v2, v3, v16, v17 done vm                          // 000000000AA0: F800183F 11100302
	s_endpgm                                                   // 000000000AA8: BF810000
