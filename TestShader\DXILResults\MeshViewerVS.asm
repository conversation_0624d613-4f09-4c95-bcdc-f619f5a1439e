;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; POSITION                 0   xyz         0     NONE   float   xyz 
; SV_VertexID              0   x           1   VERTID    uint   x   
; SV_InstanceID            0   x           2   INSTID    uint   x   
; COLOR                    0   xyz         3     NONE   float   xyz 
; NORMAL                   0   xyz         4     NONE   float   xyz 
; TANGENT                  0   xyzw        5     NONE   float   xyz 
; TEXCOORD                 0   xy          6     NONE   float   xy  
; TEXCOORD                 1   x           7     NONE     int   x   
; TEXCOORD                 2   x           8     NONE     int   x   
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float   xyzw
; TEXCOORD                 0   xyzw        1     NONE   float   xyzw
; NORMAL                   0   xyz         2     NONE   float   xyz 
; POSITION                 0   xyz         3     NONE   float   xyz 
;
; shader hash: 739f9778a4a850a153b4c5912694c385
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Vertex Shader
; OutputPositionPresent=1
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 9
; SigOutputElements: 4
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 9
; SigOutputVectors[0]: 4
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; POSITION                 0                              
; SV_VertexID              0                              
; SV_InstanceID            0                              
; COLOR                    0                              
; NORMAL                   0                              
; TANGENT                  0                              
; TEXCOORD                 0                              
; TEXCOORD                 1                              
; TEXCOORD                 2                              
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; NORMAL                   0                 linear       
; POSITION                 0                 linear       
;
; Buffer Definitions:
;
; cbuffer _MeshViewerVSCB
; {
;
;   struct hostlayout._MeshViewerVSCB
;   {
;
;       struct hostlayout.struct.Struct__MeshViewerVSCB
;       {
;
;           uint RemapRanges;                         ; Offset:    0
;           int ViewMode;                             ; Offset:    4
;           float2 _padding0;                         ; Offset:    8
;           column_major float4x4 ViewProjMtx;        ; Offset:   16
;           float4 ViewerColor;                       ; Offset:   80
;       
;       } _MeshViewerVSCB;                            ; Offset:    0
;
;   
;   } _MeshViewerVSCB;                                ; Offset:    0 Size:    96
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; _MeshViewerVSCB                   cbuffer      NA          NA     CB0            cb0     1
;
;
; ViewId state:
;
; Number of inputs: 33, outputs: 15
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 0, 1, 2 }
;   output 1 depends on inputs: { 0, 1, 2 }
;   output 2 depends on inputs: { 0, 1, 2 }
;   output 3 depends on inputs: { 0, 1, 2 }
;   output 4 depends on inputs: { 0, 1, 2, 4, 8, 12, 16, 17, 18, 20, 21, 22, 24, 28, 32 }
;   output 5 depends on inputs: { 0, 1, 2, 13, 16, 17, 18, 20, 21, 22, 25 }
;   output 6 depends on inputs: { 0, 1, 2, 14, 16, 17, 18, 20, 21, 22 }
;   output 8 depends on inputs: { 16 }
;   output 9 depends on inputs: { 17 }
;   output 10 depends on inputs: { 18 }
;   output 12 depends on inputs: { 0 }
;   output 13 depends on inputs: { 1 }
;   output 14 depends on inputs: { 2 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%hostlayout._MeshViewerVSCB = type { %hostlayout.struct.Struct__MeshViewerVSCB }
%hostlayout.struct.Struct__MeshViewerVSCB = type { i32, i32, <2 x float>, [4 x <4 x float>], <4 x float> }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %3 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %4 = call float @dx.op.loadInput.f32(i32 4, i32 5, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 0, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %12 = extractvalue %dx.types.CBufRet.f32 %11, 0
  %13 = extractvalue %dx.types.CBufRet.f32 %11, 1
  %14 = extractvalue %dx.types.CBufRet.f32 %11, 2
  %15 = extractvalue %dx.types.CBufRet.f32 %11, 3
  %16 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %17 = extractvalue %dx.types.CBufRet.f32 %16, 0
  %18 = extractvalue %dx.types.CBufRet.f32 %16, 1
  %19 = extractvalue %dx.types.CBufRet.f32 %16, 2
  %20 = extractvalue %dx.types.CBufRet.f32 %16, 3
  %21 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %22 = extractvalue %dx.types.CBufRet.f32 %21, 0
  %23 = extractvalue %dx.types.CBufRet.f32 %21, 1
  %24 = extractvalue %dx.types.CBufRet.f32 %21, 2
  %25 = extractvalue %dx.types.CBufRet.f32 %21, 3
  %26 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %27 = extractvalue %dx.types.CBufRet.f32 %26, 0
  %28 = extractvalue %dx.types.CBufRet.f32 %26, 1
  %29 = extractvalue %dx.types.CBufRet.f32 %26, 2
  %30 = extractvalue %dx.types.CBufRet.f32 %26, 3
  %31 = fmul fast float %12, %8
  %32 = call float @dx.op.tertiary.f32(i32 46, float %9, float %13, float %31)  ; FMad(a,b,c)
  %33 = call float @dx.op.tertiary.f32(i32 46, float %10, float %14, float %32)  ; FMad(a,b,c)
  %34 = fadd fast float %33, %15
  %35 = fmul fast float %17, %8
  %36 = call float @dx.op.tertiary.f32(i32 46, float %9, float %18, float %35)  ; FMad(a,b,c)
  %37 = call float @dx.op.tertiary.f32(i32 46, float %10, float %19, float %36)  ; FMad(a,b,c)
  %38 = fadd fast float %37, %20
  %39 = fmul fast float %22, %8
  %40 = call float @dx.op.tertiary.f32(i32 46, float %9, float %23, float %39)  ; FMad(a,b,c)
  %41 = call float @dx.op.tertiary.f32(i32 46, float %10, float %24, float %40)  ; FMad(a,b,c)
  %42 = fadd fast float %41, %25
  %43 = fmul fast float %27, %8
  %44 = call float @dx.op.tertiary.f32(i32 46, float %9, float %28, float %43)  ; FMad(a,b,c)
  %45 = call float @dx.op.tertiary.f32(i32 46, float %10, float %29, float %44)  ; FMad(a,b,c)
  %46 = fadd fast float %45, %30
  %47 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %1, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %48 = extractvalue %dx.types.CBufRet.i32 %47, 1
  switch i32 %48, label %112 [
    i32 0, label %118
    i32 1, label %49
    i32 2, label %53
    i32 3, label %56
    i32 4, label %59
    i32 5, label %63
    i32 6, label %80
    i32 7, label %97
    i32 8, label %100
    i32 9, label %103
    i32 10, label %106
  ]

; <label>:49                                      ; preds = %0
  %50 = fdiv fast float %34, %46
  %51 = fdiv fast float %38, %46
  %52 = fdiv fast float %42, %46
  br label %118

; <label>:53                                      ; preds = %0
  %54 = call i32 @dx.op.loadInput.i32(i32 4, i32 1, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %55 = uitofp i32 %54 to float
  br label %118

; <label>:56                                      ; preds = %0
  %57 = call i32 @dx.op.loadInput.i32(i32 4, i32 2, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %58 = uitofp i32 %57 to float
  br label %118

; <label>:59                                      ; preds = %0
  %60 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %61 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %62 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  br label %118

; <label>:63                                      ; preds = %0
  %64 = call float @dx.op.dot3.f32(i32 55, float %5, float %6, float %7, float %5, float %6, float %7)  ; Dot3(ax,ay,az,bx,by,bz)
  %65 = call float @dx.op.unary.f32(i32 25, float %64)  ; Rsqrt(value)
  %66 = fmul fast float %65, %5
  %67 = fmul fast float %65, %6
  %68 = fmul fast float %65, %7
  %69 = extractvalue %dx.types.CBufRet.i32 %47, 0
  %70 = icmp ne i32 %69, 0
  %71 = fadd fast float %66, 1.000000e+00
  %72 = fadd fast float %67, 1.000000e+00
  %73 = fadd fast float %68, 1.000000e+00
  %74 = fmul fast float %71, 5.000000e-01
  %75 = fmul fast float %72, 5.000000e-01
  %76 = fmul fast float %73, 5.000000e-01
  %77 = select i1 %70, float %74, float %66
  %78 = select i1 %70, float %75, float %67
  %79 = select i1 %70, float %76, float %68
  br label %118

; <label>:80                                      ; preds = %0
  %81 = call float @dx.op.dot3.f32(i32 55, float %2, float %3, float %4, float %2, float %3, float %4)  ; Dot3(ax,ay,az,bx,by,bz)
  %82 = call float @dx.op.unary.f32(i32 25, float %81)  ; Rsqrt(value)
  %83 = fmul fast float %82, %2
  %84 = fmul fast float %82, %3
  %85 = fmul fast float %82, %4
  %86 = extractvalue %dx.types.CBufRet.i32 %47, 0
  %87 = icmp ne i32 %86, 0
  %88 = fadd fast float %83, 1.000000e+00
  %89 = fadd fast float %84, 1.000000e+00
  %90 = fadd fast float %85, 1.000000e+00
  %91 = fmul fast float %88, 5.000000e-01
  %92 = fmul fast float %89, 5.000000e-01
  %93 = fmul fast float %90, 5.000000e-01
  %94 = select i1 %87, float %91, float %83
  %95 = select i1 %87, float %92, float %84
  %96 = select i1 %87, float %93, float %85
  br label %118

; <label>:97                                      ; preds = %0
  %98 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %99 = call float @dx.op.loadInput.f32(i32 4, i32 6, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  br label %118

; <label>:100                                     ; preds = %0
  %101 = call i32 @dx.op.loadInput.i32(i32 4, i32 7, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %102 = sitofp i32 %101 to float
  br label %118

; <label>:103                                     ; preds = %0
  %104 = call i32 @dx.op.loadInput.i32(i32 4, i32 8, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %105 = sitofp i32 %104 to float
  br label %118

; <label>:106                                     ; preds = %0
  %107 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %108 = extractvalue %dx.types.CBufRet.f32 %107, 0
  %109 = extractvalue %dx.types.CBufRet.f32 %107, 1
  %110 = extractvalue %dx.types.CBufRet.f32 %107, 2
  %111 = extractvalue %dx.types.CBufRet.f32 %107, 3
  br label %118

; <label>:112                                     ; preds = %0
  %113 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %1, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %114 = extractvalue %dx.types.CBufRet.f32 %113, 0
  %115 = extractvalue %dx.types.CBufRet.f32 %113, 1
  %116 = extractvalue %dx.types.CBufRet.f32 %113, 2
  %117 = extractvalue %dx.types.CBufRet.f32 %113, 3
  br label %118

; <label>:118                                     ; preds = %112, %106, %103, %100, %97, %80, %63, %59, %56, %53, %49, %0
  %119 = phi float [ %114, %112 ], [ %108, %106 ], [ %105, %103 ], [ %102, %100 ], [ %99, %97 ], [ %94, %80 ], [ %77, %63 ], [ %62, %59 ], [ %58, %56 ], [ %55, %53 ], [ %50, %49 ], [ %8, %0 ]
  %120 = phi float [ %115, %112 ], [ %109, %106 ], [ 0.000000e+00, %103 ], [ 0.000000e+00, %100 ], [ %98, %97 ], [ %95, %80 ], [ %78, %63 ], [ %61, %59 ], [ 0.000000e+00, %56 ], [ 0.000000e+00, %53 ], [ %51, %49 ], [ %9, %0 ]
  %121 = phi float [ %116, %112 ], [ %110, %106 ], [ 0.000000e+00, %103 ], [ 0.000000e+00, %100 ], [ 0.000000e+00, %97 ], [ %96, %80 ], [ %79, %63 ], [ %60, %59 ], [ 0.000000e+00, %56 ], [ 0.000000e+00, %53 ], [ %52, %49 ], [ %10, %0 ]
  %122 = phi float [ %117, %112 ], [ %111, %106 ], [ 1.000000e+00, %103 ], [ 1.000000e+00, %100 ], [ 1.000000e+00, %97 ], [ 1.000000e+00, %80 ], [ 1.000000e+00, %63 ], [ 1.000000e+00, %59 ], [ 1.000000e+00, %56 ], [ 1.000000e+00, %53 ], [ 1.000000e+00, %49 ], [ 1.000000e+00, %0 ]
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %34)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %38)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %42)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %46)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 0, float %119)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 1, float %120)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 2, float %121)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 1, i32 0, i8 3, float %122)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 0, float %5)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 1, float %6)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 2, i32 0, i8 2, float %7)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 0, float %8)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 1, float %9)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 3, i32 0, i8 2, float %10)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind readnone
declare i32 @dx.op.loadInput.i32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readnone
declare float @dx.op.tertiary.f32(i32, float, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!7}
!dx.entryPoints = !{!8}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"vs", i32 6, i32 1}
!4 = !{null, null, !5, null}
!5 = !{!6}
!6 = !{i32 0, %hostlayout._MeshViewerVSCB* undef, !"", i32 0, i32 0, i32 1, i32 96, null}
!7 = !{[35 x i32] [i32 33, i32 15, i32 4223, i32 8319, i32 16511, i32 0, i32 16, i32 0, i32 0, i32 0, i32 16, i32 0, i32 0, i32 0, i32 16, i32 32, i32 64, i32 0, i32 368, i32 624, i32 1136, i32 0, i32 112, i32 112, i32 112, i32 0, i32 16, i32 32, i32 0, i32 0, i32 16, i32 0, i32 0, i32 0, i32 16]}
!8 = !{void ()* @main, !"main", !9, !4, null}
!9 = !{!10, !26, null}
!10 = !{!11, !14, !16, !17, !18, !19, !20, !22, !24}
!11 = !{i32 0, !"POSITION", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 0, i8 0, !13}
!12 = !{i32 0}
!13 = !{i32 3, i32 7}
!14 = !{i32 1, !"SV_VertexID", i8 5, i8 1, !12, i8 0, i32 1, i8 1, i32 1, i8 0, !15}
!15 = !{i32 3, i32 1}
!16 = !{i32 2, !"SV_InstanceID", i8 5, i8 2, !12, i8 0, i32 1, i8 1, i32 2, i8 0, !15}
!17 = !{i32 3, !"COLOR", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 3, i8 0, !13}
!18 = !{i32 4, !"NORMAL", i8 9, i8 0, !12, i8 0, i32 1, i8 3, i32 4, i8 0, !13}
!19 = !{i32 5, !"TANGENT", i8 9, i8 0, !12, i8 0, i32 1, i8 4, i32 5, i8 0, !13}
!20 = !{i32 6, !"TEXCOORD", i8 9, i8 0, !12, i8 0, i32 1, i8 2, i32 6, i8 0, !21}
!21 = !{i32 3, i32 3}
!22 = !{i32 7, !"TEXCOORD", i8 4, i8 0, !23, i8 0, i32 1, i8 1, i32 7, i8 0, !15}
!23 = !{i32 1}
!24 = !{i32 8, !"TEXCOORD", i8 4, i8 0, !25, i8 0, i32 1, i8 1, i32 8, i8 0, !15}
!25 = !{i32 2}
!26 = !{!27, !29, !30, !31}
!27 = !{i32 0, !"SV_Position", i8 9, i8 3, !12, i8 4, i32 1, i8 4, i32 0, i8 0, !28}
!28 = !{i32 3, i32 15}
!29 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !12, i8 2, i32 1, i8 4, i32 1, i8 0, !28}
!30 = !{i32 2, !"NORMAL", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 2, i8 0, !13}
!31 = !{i32 3, !"POSITION", i8 9, i8 0, !12, i8 2, i32 1, i8 3, i32 3, i8 0, !13}
