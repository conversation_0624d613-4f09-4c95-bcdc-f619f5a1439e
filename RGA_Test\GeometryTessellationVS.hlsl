// Geometry and Tessellation Vertex Shader
// Tests vertex shader for geometry and tessellation pipeline

// Vertex data structure
struct TerrainVertex
{
    float3 Position;
    float3 Normal;
    float2 TexCoord;
    float4 Color;
    float Height;
    float3 Tangent;
    float2 DetailUV;
    float Slope;
};

// Control point structure for tessellation
struct ControlPoint
{
    float3 Position;
    float3 Normal;
    float2 TexCoord;
    float4 Color;
    float TessellationFactor;
    float3 Tangent;
    float3 Bitangent;
    float2 DetailUV;
};

// Constant buffers
cbuffer PerFrame : register(b0)
{
    float4x4 ViewMatrix;
    float4x4 ProjectionMatrix;
    float4x4 ViewProjectionMatrix;
    float3 CameraPosition;
    float Time;
    float3 LightDirection;
    float TessellationLevel;
    float2 HeightmapSize;
    float HeightScale;
    float DetailScale;
    float LODDistance;
};

cbuffer PerObject : register(b1)
{
    float4x4 WorldMatrix;
    float4x4 NormalMatrix;
    float3 BoundingBoxMin;
    float3 BoundingBoxMax;
    float2 TextureTiling;
    float DisplacementStrength;
    float _padding;
};

// Textures
Texture2D HeightmapTexture : register(t0);
Texture2D NormalTexture : register(t1);
Texture2D DetailHeightTexture : register(t2);

SamplerState LinearSampler : register(s0);
SamplerState PointSampler : register(s1);

// Input structure
struct VSInput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float3 Tangent : TANGENT;
    float2 TexCoord : TEXCOORD0;
    float4 Color : COLOR0;
    uint VertexID : SV_VertexID;
};

// Output structure (control points for tessellation)
struct VSOutput
{
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float3 Tangent : TANGENT;
    float3 Bitangent : BITANGENT;
    float2 TexCoord : TEXCOORD0;
    float2 DetailUV : TEXCOORD1;
    float4 Color : COLOR0;
    float3 WorldPos : TEXCOORD2;
    float TessellationFactor : TEXCOORD3;
    float Height : TEXCOORD4;
    float Slope : TEXCOORD5;
};

// Utility functions
float CalculateDistanceTessellation(float3 worldPos)
{
    float distance = length(CameraPosition - worldPos);
    float tessLevel = TessellationLevel * (LODDistance / max(distance, 1.0));
    return clamp(tessLevel, 1.0, 64.0);
}

float SampleHeightmap(float2 uv)
{
    // Sample heightmap with proper filtering
    float height = HeightmapTexture.SampleLevel(LinearSampler, uv, 0).r;
    
    // Add detail height
    float2 detailUV = uv * DetailScale;
    float detailHeight = DetailHeightTexture.SampleLevel(LinearSampler, detailUV, 0).r;
    
    return height * HeightScale + (detailHeight - 0.5) * DisplacementStrength;
}

float3 CalculateNormalFromHeightmap(float2 uv)
{
    float2 texelSize = 1.0 / HeightmapSize;
    
    // Sample neighboring heights
    float hL = SampleHeightmap(uv + float2(-texelSize.x, 0));
    float hR = SampleHeightmap(uv + float2(texelSize.x, 0));
    float hD = SampleHeightmap(uv + float2(0, -texelSize.y));
    float hU = SampleHeightmap(uv + float2(0, texelSize.y));
    
    // Calculate normal using finite differences
    float3 normal;
    normal.x = (hL - hR) / (2.0 * texelSize.x);
    normal.z = (hD - hU) / (2.0 * texelSize.y);
    normal.y = 1.0;
    
    return normalize(normal);
}

float CalculateSlope(float3 normal)
{
    // Calculate slope as angle from vertical
    return 1.0 - dot(normal, float3(0, 1, 0));
}

float3 CalculateTangent(float2 uv, float3 normal)
{
    float2 texelSize = 1.0 / HeightmapSize;
    
    // Sample heights for tangent calculation
    float h0 = SampleHeightmap(uv);
    float h1 = SampleHeightmap(uv + float2(texelSize.x, 0));
    
    float3 tangent = float3(texelSize.x, h1 - h0, 0);
    tangent = normalize(tangent);
    
    // Gram-Schmidt orthogonalization
    tangent = normalize(tangent - dot(tangent, normal) * normal);
    
    return tangent;
}

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Apply texture tiling to UV coordinates
    float2 tiledUV = input.TexCoord * TextureTiling;
    
    // Sample heightmap to displace vertex
    float height = SampleHeightmap(tiledUV);
    float3 displacedPosition = input.Position;
    displacedPosition.y += height;
    
    // Transform to world space
    float4 worldPos = mul(float4(displacedPosition, 1.0), WorldMatrix);
    output.WorldPos = worldPos.xyz;
    output.Position = displacedPosition; // Keep local position for tessellation
    
    // Calculate normal from heightmap
    float3 heightmapNormal = CalculateNormalFromHeightmap(tiledUV);
    
    // Blend with vertex normal
    float3 blendedNormal = normalize(lerp(input.Normal, heightmapNormal, 0.8));
    output.Normal = normalize(mul(blendedNormal, (float3x3)NormalMatrix));
    
    // Calculate tangent and bitangent
    output.Tangent = CalculateTangent(tiledUV, blendedNormal);
    output.Tangent = normalize(mul(output.Tangent, (float3x3)NormalMatrix));
    output.Bitangent = normalize(cross(output.Normal, output.Tangent));
    
    // Pass through texture coordinates
    output.TexCoord = tiledUV;
    output.DetailUV = tiledUV * DetailScale;
    
    // Calculate slope for material blending
    output.Slope = CalculateSlope(output.Normal);
    
    // Modify color based on height and slope
    float4 heightColor = input.Color;
    
    // Height-based coloring
    float normalizedHeight = saturate(height / HeightScale);
    heightColor.rgb = lerp(float3(0.2, 0.4, 0.1), float3(0.8, 0.8, 0.9), normalizedHeight);
    
    // Slope-based coloring (rocky areas)
    if (output.Slope > 0.5)
    {
        heightColor.rgb = lerp(heightColor.rgb, float3(0.5, 0.4, 0.3), (output.Slope - 0.5) * 2.0);
    }
    
    output.Color = heightColor;
    output.Height = height;
    
    // Calculate tessellation factor based on distance and slope
    float distanceTess = CalculateDistanceTessellation(output.WorldPos);
    float slopeTess = 1.0 + output.Slope * 2.0; // More tessellation on slopes
    output.TessellationFactor = distanceTess * slopeTess;
    
    return output;
}
