﻿COMPILATION SUCCESSFUL
Mali Offline Compiler v8.7.0 (Build cbd520) Copyright (c) 2007-2025 Arm Limited. All rights reserved.  Configuration =============  Hardware: Mali-G76 r0p0 Architecture: Bifrost Driver: r51p0-00rel0 Shader type: OpenGL ES Fragment  Main shader ===========  Work registers: 26 (81% used at 100% occupancy) Uniform registers: 20 (15% used) Stack use: false 16-bit arithmetic: 76%                                  A      LS       V       T    Bound Total instruction cycles:    1.29    0.00    0.25    5.00        T Shortest path cycles:        0.88    0.00    0.25    5.00        T Longest path cycles:         1.29    0.00    0.25    5.00        T  A = Arithmetic, LS = Load/Store, V = Varying, T = Texture  Shader properties =================  Has uniform computation: false Has side-effects: false Modifies coverage: false Uses late ZS test: false Uses late ZS update: false Reads color buffer: false  Note: This tool shows only the shader-visible property state. API configuration may also impact the value of some properties. 
