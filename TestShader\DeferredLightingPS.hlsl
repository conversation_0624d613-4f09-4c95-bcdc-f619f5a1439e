// Deferred Lighting Pixel Shader
// Tests G-Buffer reconstruction and lighting calculations

cbuffer LightingParams : register(b0)
{
    float3 CameraPosition;
    int NumLights;
    float3 AmbientColor;
    float AmbientStrength;
};

cbuffer LightData : register(b1)
{
    float4 LightPositions[32];   // xyz: position, w: range
    float4 LightColors[32];      // rgb: color, a: intensity
    float4 LightDirections[32];  // xyz: direction, w: spot angle
    int4 LightTypes[8];          // 0: point, 1: directional, 2: spot
};

Texture2D GBufferAlbedo : register(t0);    // RGB: Albedo, A: Metallic
Texture2D GBufferNormal : register(t1);    // RGB: Normal, A: Roughness
Texture2D GBufferWorldPos : register(t2);  // RGB: WorldPos, A: AO
Texture2D GBufferEmissive : register(t3);  // RGB: Emissive, A: Depth
SamplerState PointSampler : register(s0);

struct PSInput
{
    float4 Position : SV_POSITION;
    float2 TexCoord : TEXCOORD0;
};

static const float PI = 3.14159265359;

float DistributionGGX(float3 N, float3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;
    
    return num / denom;
}

float GeometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r * r) / 8.0;
    
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    
    return num / denom;
}

float GeometrySmith(float3 N, float3 V, float3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);
    
    return ggx1 * ggx2;
}

float3 fresnelSchlick(float cosTheta, float3 F0)
{
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

float4 main(PSInput input) : SV_TARGET
{
    // Sample G-Buffer
    float4 albedoMetallic = GBufferAlbedo.Sample(PointSampler, input.TexCoord);
    float4 normalRoughness = GBufferNormal.Sample(PointSampler, input.TexCoord);
    float4 worldPosAO = GBufferWorldPos.Sample(PointSampler, input.TexCoord);
    float4 emissiveDepth = GBufferEmissive.Sample(PointSampler, input.TexCoord);
    
    // Reconstruct material properties
    float3 albedo = albedoMetallic.rgb;
    float metallic = albedoMetallic.a;
    float3 normal = normalize(normalRoughness.rgb * 2.0 - 1.0);
    float roughness = normalRoughness.a;
    float3 worldPos = worldPosAO.rgb;
    float ao = worldPosAO.a;
    float3 emissive = emissiveDepth.rgb;
    
    // Calculate view direction
    float3 V = normalize(CameraPosition - worldPos);
    
    // Calculate reflectance at normal incidence
    float3 F0 = lerp(float3(0.04, 0.04, 0.04), albedo, metallic);
    
    // Reflectance equation
    float3 Lo = float3(0.0, 0.0, 0.0);
    
    // Calculate lighting for each light source
    for(int i = 0; i < NumLights && i < 32; ++i)
    {
        int lightType = LightTypes[i / 4][i % 4];
        float3 lightPos = LightPositions[i].xyz;
        float lightRange = LightPositions[i].w;
        float3 lightColor = LightColors[i].rgb;
        float lightIntensity = LightColors[i].a;
        
        float3 L;
        float attenuation = 1.0;
        
        if (lightType == 0) // Point light
        {
            L = normalize(lightPos - worldPos);
            float distance = length(lightPos - worldPos);
            attenuation = 1.0 / (1.0 + distance * distance / (lightRange * lightRange));
        }
        else if (lightType == 1) // Directional light
        {
            L = normalize(-LightDirections[i].xyz);
        }
        else if (lightType == 2) // Spot light
        {
            L = normalize(lightPos - worldPos);
            float distance = length(lightPos - worldPos);
            attenuation = 1.0 / (1.0 + distance * distance / (lightRange * lightRange));
            
            float3 spotDir = normalize(LightDirections[i].xyz);
            float spotAngle = LightDirections[i].w;
            float theta = dot(L, -spotDir);
            float epsilon = cos(spotAngle) - cos(spotAngle * 1.2);
            float intensity = clamp((theta - cos(spotAngle * 1.2)) / epsilon, 0.0, 1.0);
            attenuation *= intensity;
        }
        
        float3 H = normalize(V + L);
        float3 radiance = lightColor * lightIntensity * attenuation;
        
        // Cook-Torrance BRDF
        float NDF = DistributionGGX(normal, H, roughness);
        float G = GeometrySmith(normal, V, L, roughness);
        float3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);
        
        float3 kS = F;
        float3 kD = float3(1.0, 1.0, 1.0) - kS;
        kD *= 1.0 - metallic;
        
        float3 numerator = NDF * G * F;
        float denominator = 4.0 * max(dot(normal, V), 0.0) * max(dot(normal, L), 0.0) + 0.0001;
        float3 specular = numerator / denominator;
        
        float NdotL = max(dot(normal, L), 0.0);
        Lo += (kD * albedo / PI + specular) * radiance * NdotL;
    }
    
    // Ambient lighting
    float3 ambient = AmbientColor * AmbientStrength * albedo * ao;
    
    // Final color
    float3 color = ambient + Lo + emissive;
    
    // HDR tonemapping
    color = color / (color + float3(1.0, 1.0, 1.0));
    
    // Gamma correction
    color = pow(color, 1.0/2.2);
    
    return float4(color, 1.0);
}
