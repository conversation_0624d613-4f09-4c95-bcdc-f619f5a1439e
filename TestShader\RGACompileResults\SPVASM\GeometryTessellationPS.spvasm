; SPIR-V
; Version: 1.0
; Generator: Google spiregg; 0
; Bound: 535
; Schema: 0
               OpCapability Shader
          %1 = OpExtInstImport "GLSL.std.450"
               OpMemoryModel Logical GLSL450
               OpEntryPoint Fragment %main "main" %gl_FragCoord %in_var_TEXCOORD1 %in_var_TEXCOORD2 %in_var_TEXCOORD3 %in_var_TEXCOORD4 %in_var_TEXCOORD5 %in_var_TEXCOORD6 %in_var_TEXCOORD7 %in_var_TEXCOORD8 %in_var_TEXCOORD9 %in_var_TEXCOORD10 %out_var_SV_Target0 %out_var_SV_Target1 %out_var_SV_Target2 %out_var_SV_Target3
               OpExecutionMode %main OriginUpperLeft
               OpSource HLSL 600
               OpName %type_PerFrame "type.PerFrame"
               OpMemberName %type_PerFrame 0 "ViewMatrix"
               OpMemberName %type_PerFrame 1 "ProjectionMatrix"
               OpMemberName %type_PerFrame 2 "ViewProjectionMatrix"
               OpMemberName %type_PerFrame 3 "CameraPosition"
               OpMemberName %type_PerFrame 4 "Time"
               OpMemberName %type_PerFrame 5 "LightDirection"
               OpMemberName %type_PerFrame 6 "TessellationLevel"
               OpMemberName %type_PerFrame 7 "HeightmapSize"
               OpMemberName %type_PerFrame 8 "HeightScale"
               OpMemberName %type_PerFrame 9 "DetailScale"
               OpMemberName %type_PerFrame 10 "LODDistance"
               OpName %PerFrame "PerFrame"
               OpName %type_MaterialParams "type.MaterialParams"
               OpMemberName %type_MaterialParams 0 "GrassLayer"
               OpMemberName %type_MaterialParams 1 "RockLayer"
               OpMemberName %type_MaterialParams 2 "SnowLayer"
               OpMemberName %type_MaterialParams 3 "SandLayer"
               OpMemberName %type_MaterialParams 4 "FogColor"
               OpMemberName %type_MaterialParams 5 "FogDensity"
               OpMemberName %type_MaterialParams 6 "FogStart"
               OpMemberName %type_MaterialParams 7 "FogEnd"
               OpMemberName %type_MaterialParams 8 "WindDirection"
               OpMemberName %type_MaterialParams 9 "WindStrength"
               OpMemberName %type_MaterialParams 10 "_padding2"
               OpName %MaterialLayer "MaterialLayer"
               OpMemberName %MaterialLayer 0 "Albedo"
               OpMemberName %MaterialLayer 1 "Metallic"
               OpMemberName %MaterialLayer 2 "Roughness"
               OpMemberName %MaterialLayer 3 "NormalStrength"
               OpMemberName %MaterialLayer 4 "TilingScale"
               OpMemberName %MaterialLayer 5 "Offset"
               OpMemberName %MaterialLayer 6 "BlendSharpness"
               OpMemberName %MaterialLayer 7 "_padding"
               OpName %MaterialParams "MaterialParams"
               OpName %type_2d_image_array "type.2d.image.array"
               OpName %AlbedoTextures "AlbedoTextures"
               OpName %NormalTextures "NormalTextures"
               OpName %RoughnessTextures "RoughnessTextures"
               OpName %type_2d_image "type.2d.image"
               OpName %SplatmapTexture "SplatmapTexture"
               OpName %DetailNormalTexture "DetailNormalTexture"
               OpName %type_cube_image "type.cube.image"
               OpName %SkyboxTexture "SkyboxTexture"
               OpName %ShadowMap "ShadowMap"
               OpName %type_sampler "type.sampler"
               OpName %LinearSampler "LinearSampler"
               OpName %TrilinearSampler "TrilinearSampler"
               OpName %ShadowSampler "ShadowSampler"
               OpName %in_var_TEXCOORD1 "in.var.TEXCOORD1"
               OpName %in_var_TEXCOORD2 "in.var.TEXCOORD2"
               OpName %in_var_TEXCOORD3 "in.var.TEXCOORD3"
               OpName %in_var_TEXCOORD4 "in.var.TEXCOORD4"
               OpName %in_var_TEXCOORD5 "in.var.TEXCOORD5"
               OpName %in_var_TEXCOORD6 "in.var.TEXCOORD6"
               OpName %in_var_TEXCOORD7 "in.var.TEXCOORD7"
               OpName %in_var_TEXCOORD8 "in.var.TEXCOORD8"
               OpName %in_var_TEXCOORD9 "in.var.TEXCOORD9"
               OpName %in_var_TEXCOORD10 "in.var.TEXCOORD10"
               OpName %out_var_SV_Target0 "out.var.SV_Target0"
               OpName %out_var_SV_Target1 "out.var.SV_Target1"
               OpName %out_var_SV_Target2 "out.var.SV_Target2"
               OpName %out_var_SV_Target3 "out.var.SV_Target3"
               OpName %main "main"
               OpName %type_sampled_image "type.sampled.image"
               OpName %type_sampled_image_0 "type.sampled.image"
               OpName %type_sampled_image_1 "type.sampled.image"
               OpDecorate %gl_FragCoord BuiltIn FragCoord
               OpDecorate %in_var_TEXCOORD1 Location 1
               OpDecorate %in_var_TEXCOORD2 Location 2
               OpDecorate %in_var_TEXCOORD3 Location 3
               OpDecorate %in_var_TEXCOORD4 Location 4
               OpDecorate %in_var_TEXCOORD5 Location 5
               OpDecorate %in_var_TEXCOORD6 Location 7
               OpDecorate %in_var_TEXCOORD7 Location 8
               OpDecorate %in_var_TEXCOORD8 Location 9
               OpDecorate %in_var_TEXCOORD9 Location 10
               OpDecorate %in_var_TEXCOORD10 Location 11
               OpDecorate %out_var_SV_Target0 Location 0
               OpDecorate %out_var_SV_Target1 Location 1
               OpDecorate %out_var_SV_Target2 Location 2
               OpDecorate %out_var_SV_Target3 Location 3
               OpDecorate %PerFrame DescriptorSet 0
               OpDecorate %PerFrame Binding 0
               OpDecorate %MaterialParams DescriptorSet 0
               OpDecorate %MaterialParams Binding 1
               OpDecorate %AlbedoTextures DescriptorSet 0
               OpDecorate %AlbedoTextures Binding 0
               OpDecorate %NormalTextures DescriptorSet 0
               OpDecorate %NormalTextures Binding 1
               OpDecorate %RoughnessTextures DescriptorSet 0
               OpDecorate %RoughnessTextures Binding 2
               OpDecorate %SplatmapTexture DescriptorSet 0
               OpDecorate %SplatmapTexture Binding 5
               OpDecorate %DetailNormalTexture DescriptorSet 0
               OpDecorate %DetailNormalTexture Binding 6
               OpDecorate %SkyboxTexture DescriptorSet 0
               OpDecorate %SkyboxTexture Binding 7
               OpDecorate %ShadowMap DescriptorSet 0
               OpDecorate %ShadowMap Binding 8
               OpDecorate %LinearSampler DescriptorSet 0
               OpDecorate %LinearSampler Binding 0
               OpDecorate %TrilinearSampler DescriptorSet 0
               OpDecorate %TrilinearSampler Binding 1
               OpDecorate %ShadowSampler DescriptorSet 0
               OpDecorate %ShadowSampler Binding 2
               OpMemberDecorate %type_PerFrame 0 Offset 0
               OpMemberDecorate %type_PerFrame 0 MatrixStride 16
               OpMemberDecorate %type_PerFrame 0 RowMajor
               OpMemberDecorate %type_PerFrame 1 Offset 64
               OpMemberDecorate %type_PerFrame 1 MatrixStride 16
               OpMemberDecorate %type_PerFrame 1 RowMajor
               OpMemberDecorate %type_PerFrame 2 Offset 128
               OpMemberDecorate %type_PerFrame 2 MatrixStride 16
               OpMemberDecorate %type_PerFrame 2 RowMajor
               OpMemberDecorate %type_PerFrame 3 Offset 192
               OpMemberDecorate %type_PerFrame 4 Offset 204
               OpMemberDecorate %type_PerFrame 5 Offset 208
               OpMemberDecorate %type_PerFrame 6 Offset 220
               OpMemberDecorate %type_PerFrame 7 Offset 224
               OpMemberDecorate %type_PerFrame 8 Offset 232
               OpMemberDecorate %type_PerFrame 9 Offset 236
               OpMemberDecorate %type_PerFrame 10 Offset 240
               OpDecorate %type_PerFrame Block
               OpMemberDecorate %MaterialLayer 0 Offset 0
               OpMemberDecorate %MaterialLayer 1 Offset 16
               OpMemberDecorate %MaterialLayer 2 Offset 20
               OpMemberDecorate %MaterialLayer 3 Offset 24
               OpMemberDecorate %MaterialLayer 4 Offset 28
               OpMemberDecorate %MaterialLayer 5 Offset 32
               OpMemberDecorate %MaterialLayer 6 Offset 40
               OpMemberDecorate %MaterialLayer 7 Offset 44
               OpMemberDecorate %type_MaterialParams 0 Offset 0
               OpMemberDecorate %type_MaterialParams 1 Offset 48
               OpMemberDecorate %type_MaterialParams 2 Offset 96
               OpMemberDecorate %type_MaterialParams 3 Offset 144
               OpMemberDecorate %type_MaterialParams 4 Offset 192
               OpMemberDecorate %type_MaterialParams 5 Offset 204
               OpMemberDecorate %type_MaterialParams 6 Offset 208
               OpMemberDecorate %type_MaterialParams 7 Offset 212
               OpMemberDecorate %type_MaterialParams 8 Offset 216
               OpMemberDecorate %type_MaterialParams 9 Offset 224
               OpMemberDecorate %type_MaterialParams 10 Offset 228
               OpDecorate %type_MaterialParams Block
        %int = OpTypeInt 32 1
      %int_2 = OpConstant %int 2
      %int_3 = OpConstant %int 3
      %int_4 = OpConstant %int 4
      %int_5 = OpConstant %int 5
      %int_0 = OpConstant %int 0
      %int_8 = OpConstant %int 8
      %int_9 = OpConstant %int 9
       %uint = OpTypeInt 32 0
     %uint_0 = OpConstant %uint 0
      %int_1 = OpConstant %int 1
      %float = OpTypeFloat 32
    %float_1 = OpConstant %float 1
    %v4float = OpTypeVector %float 4
         %54 = OpConstantComposite %v4float %float_1 %float_1 %float_1 %float_1
    %float_0 = OpConstant %float 0
         %56 = OpConstantComposite %v4float %float_1 %float_0 %float_0 %float_0
         %57 = OpConstantComposite %v4float %float_0 %float_0 %float_0 %float_0
    %v3float = OpTypeVector %float 3
         %59 = OpConstantComposite %v3float %float_0 %float_0 %float_0
      %int_6 = OpConstant %int 6
  %float_0_5 = OpConstant %float 0.5
%float_0_100000001 = OpConstant %float 0.100000001
%float_0_150000006 = OpConstant %float 0.150000006
         %64 = OpConstantComposite %v3float %float_0_100000001 %float_0_100000001 %float_0_150000006
      %int_7 = OpConstant %int 7
         %66 = OpConstantComposite %v3float %float_0_5 %float_0_5 %float_0_5
%float_0_699999988 = OpConstant %float 0.699999988
%float_0_300000012 = OpConstant %float 0.300000012
   %float_10 = OpConstant %float 10
%float_0_00999999978 = OpConstant %float 0.00999999978
    %float_2 = OpConstant %float 2
         %72 = OpConstantComposite %v3float %float_1 %float_1 %float_1
         %73 = OpConstantComposite %v3float %float_0 %float_0 %float_1
   %float_n1 = OpConstant %float -1
         %75 = OpConstantComposite %v3float %float_n1 %float_n1 %float_1
       %bool = OpTypeBool
       %true = OpConstantTrue %bool
    %v2float = OpTypeVector %float 2
     %int_n1 = OpConstant %int -1
    %float_7 = OpConstant %float 7
%mat4v4float = OpTypeMatrix %v4float 4
%type_PerFrame = OpTypeStruct %mat4v4float %mat4v4float %mat4v4float %v3float %float %v3float %float %v2float %float %float %float
%_ptr_Uniform_type_PerFrame = OpTypePointer Uniform %type_PerFrame
%MaterialLayer = OpTypeStruct %v4float %float %float %float %float %v2float %float %float
%type_MaterialParams = OpTypeStruct %MaterialLayer %MaterialLayer %MaterialLayer %MaterialLayer %v3float %float %float %float %v2float %float %float
%_ptr_Uniform_type_MaterialParams = OpTypePointer Uniform %type_MaterialParams
%type_2d_image_array = OpTypeImage %float 2D 2 1 0 1 Unknown
%_ptr_UniformConstant_type_2d_image_array = OpTypePointer UniformConstant %type_2d_image_array
%type_2d_image = OpTypeImage %float 2D 2 0 0 1 Unknown
%_ptr_UniformConstant_type_2d_image = OpTypePointer UniformConstant %type_2d_image
%type_cube_image = OpTypeImage %float Cube 2 0 0 1 Unknown
%_ptr_UniformConstant_type_cube_image = OpTypePointer UniformConstant %type_cube_image
%type_sampler = OpTypeSampler
%_ptr_UniformConstant_type_sampler = OpTypePointer UniformConstant %type_sampler
%_ptr_Input_v4float = OpTypePointer Input %v4float
%_ptr_Input_v3float = OpTypePointer Input %v3float
%_ptr_Input_v2float = OpTypePointer Input %v2float
%_ptr_Input_float = OpTypePointer Input %float
%_ptr_Output_v4float = OpTypePointer Output %v4float
       %void = OpTypeVoid
         %94 = OpTypeFunction %void
%mat3v3float = OpTypeMatrix %v3float 3
%type_sampled_image = OpTypeSampledImage %type_2d_image
%_ptr_Uniform_MaterialLayer = OpTypePointer Uniform %MaterialLayer
%_ptr_Uniform_float = OpTypePointer Uniform %float
%_ptr_Uniform_v3float = OpTypePointer Uniform %v3float
%_ptr_Uniform_v2float = OpTypePointer Uniform %v2float
%type_sampled_image_0 = OpTypeSampledImage %type_2d_image_array
%type_sampled_image_1 = OpTypeSampledImage %type_cube_image
   %PerFrame = OpVariable %_ptr_Uniform_type_PerFrame Uniform
%MaterialParams = OpVariable %_ptr_Uniform_type_MaterialParams Uniform
%AlbedoTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%NormalTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%RoughnessTextures = OpVariable %_ptr_UniformConstant_type_2d_image_array UniformConstant
%SplatmapTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%DetailNormalTexture = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%SkyboxTexture = OpVariable %_ptr_UniformConstant_type_cube_image UniformConstant
  %ShadowMap = OpVariable %_ptr_UniformConstant_type_2d_image UniformConstant
%LinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%TrilinearSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%ShadowSampler = OpVariable %_ptr_UniformConstant_type_sampler UniformConstant
%gl_FragCoord = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD1 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD2 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD3 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD4 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD5 = OpVariable %_ptr_Input_v2float Input
%in_var_TEXCOORD6 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD7 = OpVariable %_ptr_Input_float Input
%in_var_TEXCOORD8 = OpVariable %_ptr_Input_v3float Input
%in_var_TEXCOORD9 = OpVariable %_ptr_Input_v4float Input
%in_var_TEXCOORD10 = OpVariable %_ptr_Input_float Input
%out_var_SV_Target0 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target1 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target2 = OpVariable %_ptr_Output_v4float Output
%out_var_SV_Target3 = OpVariable %_ptr_Output_v4float Output
    %float_3 = OpConstant %float 3
%float_0_00048828125 = OpConstant %float 0.00048828125
        %102 = OpConstantComposite %v2float %float_0_00048828125 %float_0_00048828125
  %float_2_5 = OpConstant %float 2.5
%float_0_111111112 = OpConstant %float 0.111111112
%_ptr_Uniform_v4float = OpTypePointer Uniform %v4float
     %uint_2 = OpConstant %uint 2
     %uint_3 = OpConstant %uint 3
     %uint_4 = OpConstant %uint 4
     %uint_5 = OpConstant %uint 5
       %main = OpFunction %void None %94
        %110 = OpLabel
        %111 = OpLoad %v4float %gl_FragCoord
        %112 = OpLoad %v3float %in_var_TEXCOORD1
        %113 = OpLoad %v3float %in_var_TEXCOORD2
        %114 = OpLoad %v3float %in_var_TEXCOORD3
        %115 = OpLoad %v2float %in_var_TEXCOORD4
        %116 = OpLoad %v2float %in_var_TEXCOORD5
        %117 = OpLoad %float %in_var_TEXCOORD6
        %118 = OpLoad %float %in_var_TEXCOORD7
        %119 = OpLoad %v3float %in_var_TEXCOORD8
        %120 = OpLoad %v4float %in_var_TEXCOORD9
        %121 = OpLoad %float %in_var_TEXCOORD10
        %122 = OpExtInst %v3float %1 Normalize %112
        %123 = OpExtInst %v3float %1 Normalize %113
        %124 = OpExtInst %v3float %1 Normalize %114
        %125 = OpExtInst %v3float %1 Normalize %119
        %126 = OpLoad %type_2d_image %SplatmapTexture
        %127 = OpLoad %type_sampler %LinearSampler
        %128 = OpSampledImage %type_sampled_image %126 %127
        %129 = OpImageSampleImplicitLod %v4float %128 %115 None
        %130 = OpCompositeExtract %float %129 0
        %131 = OpFMul %float %118 %float_2
        %132 = OpFSub %float %float_1 %131
        %133 = OpExtInst %float %1 FClamp %132 %float_0 %float_1
        %134 = OpFMul %float %130 %133
        %135 = OpCompositeExtract %float %129 1
        %136 = OpFSub %float %118 %float_0_300000012
        %137 = OpFMul %float %136 %float_2_5
        %138 = OpExtInst %float %1 FClamp %137 %float_0 %float_1
        %139 = OpFMul %float %135 %138
        %140 = OpCompositeExtract %float %129 2
        %141 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_8
        %142 = OpLoad %float %141
        %143 = OpFMul %float %142 %float_0_699999988
        %144 = OpFSub %float %117 %143
        %145 = OpFMul %float %142 %float_0_300000012
        %146 = OpFDiv %float %144 %145
        %147 = OpExtInst %float %1 FClamp %146 %float_0 %float_1
        %148 = OpFMul %float %140 %147
        %149 = OpCompositeExtract %float %129 3
        %150 = OpFDiv %float %117 %145
        %151 = OpFSub %float %float_1 %150
        %152 = OpExtInst %float %1 FClamp %151 %float_0 %float_1
        %153 = OpFMul %float %149 %152
        %154 = OpCompositeConstruct %v4float %134 %139 %148 %153
        %155 = OpDot %float %154 %54
        %156 = OpFOrdGreaterThan %bool %155 %float_0
               OpSelectionMerge %157 None
               OpBranchConditional %156 %158 %159
        %159 = OpLabel
               OpBranch %157
        %158 = OpLabel
        %160 = OpCompositeConstruct %v4float %155 %155 %155 %155
        %161 = OpFDiv %v4float %154 %160
               OpBranch %157
        %157 = OpLabel
        %162 = OpPhi %v4float %161 %158 %56 %159
        %163 = OpCompositeExtract %float %162 0
        %164 = OpFOrdGreaterThan %bool %163 %float_0
               OpSelectionMerge %165 None
               OpBranchConditional %164 %166 %165
        %166 = OpLabel
        %167 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_0
        %168 = OpAccessChain %_ptr_Uniform_v4float %167 %uint_0
        %169 = OpLoad %v4float %168
        %170 = OpAccessChain %_ptr_Uniform_float %167 %uint_2
        %171 = OpLoad %float %170
        %172 = OpAccessChain %_ptr_Uniform_float %167 %uint_3
        %173 = OpLoad %float %172
        %174 = OpAccessChain %_ptr_Uniform_float %167 %uint_4
        %175 = OpLoad %float %174
        %176 = OpAccessChain %_ptr_Uniform_v2float %167 %uint_5
        %177 = OpLoad %v2float %176
        %178 = OpVectorTimesScalar %v2float %115 %175
        %179 = OpFAdd %v2float %178 %177
        %180 = OpAccessChain %_ptr_Uniform_v2float %MaterialParams %int_8
        %181 = OpLoad %v2float %180
        %182 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_9
        %183 = OpLoad %float %182
        %184 = OpVectorTimesScalar %v2float %181 %183
        %185 = OpAccessChain %_ptr_Uniform_float %PerFrame %int_4
        %186 = OpLoad %float %185
        %187 = OpCompositeExtract %float %179 0
        %188 = OpFMul %float %187 %float_10
        %189 = OpFAdd %float %186 %188
        %190 = OpExtInst %float %1 Sin %189
        %191 = OpVectorTimesScalar %v2float %184 %190
        %192 = OpVectorTimesScalar %v2float %191 %float_0_00999999978
        %193 = OpFAdd %v2float %179 %192
        %194 = OpLoad %type_2d_image_array %AlbedoTextures
        %195 = OpLoad %type_sampler %TrilinearSampler
        %196 = OpCompositeExtract %float %193 0
        %197 = OpCompositeExtract %float %193 1
        %198 = OpCompositeConstruct %v3float %196 %197 %float_0
        %199 = OpSampledImage %type_sampled_image_0 %194 %195
        %200 = OpImageSampleImplicitLod %v4float %199 %198 None
        %201 = OpFMul %v4float %200 %169
        %202 = OpLoad %type_2d_image_array %NormalTextures
        %203 = OpLoad %type_sampler %TrilinearSampler
        %204 = OpCompositeExtract %float %179 1
        %205 = OpCompositeConstruct %v3float %187 %204 %float_0
        %206 = OpSampledImage %type_sampled_image_0 %202 %203
        %207 = OpImageSampleImplicitLod %v4float %206 %205 None
        %208 = OpVectorShuffle %v3float %207 %207 0 1 2
        %209 = OpVectorTimesScalar %v3float %208 %float_2
        %210 = OpFSub %v3float %209 %72
        %211 = OpVectorTimesScalar %v3float %210 %173
        %212 = OpLoad %type_2d_image_array %RoughnessTextures
        %213 = OpLoad %type_sampler %TrilinearSampler
        %214 = OpSampledImage %type_sampled_image_0 %212 %213
        %215 = OpImageSampleImplicitLod %v4float %214 %205 None
        %216 = OpCompositeExtract %float %215 0
        %217 = OpFMul %float %171 %216
        %218 = OpCompositeExtract %float %215 1
        %219 = OpVectorTimesScalar %v4float %201 %163
        %220 = OpVectorTimesScalar %v3float %211 %163
        %221 = OpFMul %float %217 %163
        %222 = OpFMul %float %218 %163
        %223 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_0 %int_1
        %224 = OpLoad %float %223
        %225 = OpFMul %float %224 %163
               OpBranch %165
        %165 = OpLabel
        %226 = OpPhi %float %float_0 %157 %225 %166
        %227 = OpPhi %float %float_0 %157 %222 %166
        %228 = OpPhi %float %float_0 %157 %221 %166
        %229 = OpPhi %v3float %59 %157 %220 %166
        %230 = OpPhi %v4float %57 %157 %219 %166
        %231 = OpCompositeExtract %float %162 1
        %232 = OpFOrdGreaterThan %bool %231 %float_0
               OpSelectionMerge %233 None
               OpBranchConditional %232 %234 %233
        %234 = OpLabel
        %235 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_1
        %236 = OpAccessChain %_ptr_Uniform_v4float %235 %uint_0
        %237 = OpLoad %v4float %236
        %238 = OpAccessChain %_ptr_Uniform_float %235 %uint_2
        %239 = OpLoad %float %238
        %240 = OpAccessChain %_ptr_Uniform_float %235 %uint_3
        %241 = OpLoad %float %240
        %242 = OpAccessChain %_ptr_Uniform_float %235 %uint_4
        %243 = OpLoad %float %242
        %244 = OpAccessChain %_ptr_Uniform_v2float %235 %uint_5
        %245 = OpLoad %v2float %244
        %246 = OpVectorTimesScalar %v2float %115 %243
        %247 = OpFAdd %v2float %246 %245
        %248 = OpLoad %type_2d_image_array %AlbedoTextures
        %249 = OpLoad %type_sampler %TrilinearSampler
        %250 = OpCompositeExtract %float %247 0
        %251 = OpCompositeExtract %float %247 1
        %252 = OpCompositeConstruct %v3float %250 %251 %float_1
        %253 = OpSampledImage %type_sampled_image_0 %248 %249
        %254 = OpImageSampleImplicitLod %v4float %253 %252 None
        %255 = OpFMul %v4float %254 %237
        %256 = OpLoad %type_2d_image_array %NormalTextures
        %257 = OpLoad %type_sampler %TrilinearSampler
        %258 = OpSampledImage %type_sampled_image_0 %256 %257
        %259 = OpImageSampleImplicitLod %v4float %258 %252 None
        %260 = OpVectorShuffle %v3float %259 %259 0 1 2
        %261 = OpVectorTimesScalar %v3float %260 %float_2
        %262 = OpFSub %v3float %261 %72
        %263 = OpVectorTimesScalar %v3float %262 %241
        %264 = OpLoad %type_2d_image_array %RoughnessTextures
        %265 = OpLoad %type_sampler %TrilinearSampler
        %266 = OpSampledImage %type_sampled_image_0 %264 %265
        %267 = OpImageSampleImplicitLod %v4float %266 %252 None
        %268 = OpCompositeExtract %float %267 0
        %269 = OpFMul %float %239 %268
        %270 = OpCompositeExtract %float %267 1
        %271 = OpVectorTimesScalar %v4float %255 %231
        %272 = OpFAdd %v4float %230 %271
        %273 = OpVectorTimesScalar %v3float %263 %231
        %274 = OpFAdd %v3float %229 %273
        %275 = OpFMul %float %269 %231
        %276 = OpFAdd %float %228 %275
        %277 = OpFMul %float %270 %231
        %278 = OpFAdd %float %227 %277
        %279 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_1 %int_1
        %280 = OpLoad %float %279
        %281 = OpFMul %float %280 %231
        %282 = OpFAdd %float %226 %281
               OpBranch %233
        %233 = OpLabel
        %283 = OpPhi %float %226 %165 %282 %234
        %284 = OpPhi %float %227 %165 %278 %234
        %285 = OpPhi %float %228 %165 %276 %234
        %286 = OpPhi %v3float %229 %165 %274 %234
        %287 = OpPhi %v4float %230 %165 %272 %234
        %288 = OpCompositeExtract %float %162 2
        %289 = OpFOrdGreaterThan %bool %288 %float_0
               OpSelectionMerge %290 None
               OpBranchConditional %289 %291 %290
        %291 = OpLabel
        %292 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_2
        %293 = OpAccessChain %_ptr_Uniform_v4float %292 %uint_0
        %294 = OpLoad %v4float %293
        %295 = OpAccessChain %_ptr_Uniform_float %292 %uint_2
        %296 = OpLoad %float %295
        %297 = OpAccessChain %_ptr_Uniform_float %292 %uint_3
        %298 = OpLoad %float %297
        %299 = OpAccessChain %_ptr_Uniform_float %292 %uint_4
        %300 = OpLoad %float %299
        %301 = OpAccessChain %_ptr_Uniform_v2float %292 %uint_5
        %302 = OpLoad %v2float %301
        %303 = OpVectorTimesScalar %v2float %115 %300
        %304 = OpFAdd %v2float %303 %302
        %305 = OpLoad %type_2d_image_array %AlbedoTextures
        %306 = OpLoad %type_sampler %TrilinearSampler
        %307 = OpCompositeExtract %float %304 0
        %308 = OpCompositeExtract %float %304 1
        %309 = OpCompositeConstruct %v3float %307 %308 %float_2
        %310 = OpSampledImage %type_sampled_image_0 %305 %306
        %311 = OpImageSampleImplicitLod %v4float %310 %309 None
        %312 = OpFMul %v4float %311 %294
        %313 = OpLoad %type_2d_image_array %NormalTextures
        %314 = OpLoad %type_sampler %TrilinearSampler
        %315 = OpSampledImage %type_sampled_image_0 %313 %314
        %316 = OpImageSampleImplicitLod %v4float %315 %309 None
        %317 = OpVectorShuffle %v3float %316 %316 0 1 2
        %318 = OpVectorTimesScalar %v3float %317 %float_2
        %319 = OpFSub %v3float %318 %72
        %320 = OpVectorTimesScalar %v3float %319 %298
        %321 = OpLoad %type_2d_image_array %RoughnessTextures
        %322 = OpLoad %type_sampler %TrilinearSampler
        %323 = OpSampledImage %type_sampled_image_0 %321 %322
        %324 = OpImageSampleImplicitLod %v4float %323 %309 None
        %325 = OpCompositeExtract %float %324 0
        %326 = OpFMul %float %296 %325
        %327 = OpCompositeExtract %float %324 1
        %328 = OpVectorTimesScalar %v4float %312 %288
        %329 = OpFAdd %v4float %287 %328
        %330 = OpVectorTimesScalar %v3float %320 %288
        %331 = OpFAdd %v3float %286 %330
        %332 = OpFMul %float %326 %288
        %333 = OpFAdd %float %285 %332
        %334 = OpFMul %float %327 %288
        %335 = OpFAdd %float %284 %334
        %336 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_2 %int_1
        %337 = OpLoad %float %336
        %338 = OpFMul %float %337 %288
        %339 = OpFAdd %float %283 %338
               OpBranch %290
        %290 = OpLabel
        %340 = OpPhi %float %283 %233 %339 %291
        %341 = OpPhi %float %284 %233 %335 %291
        %342 = OpPhi %float %285 %233 %333 %291
        %343 = OpPhi %v3float %286 %233 %331 %291
        %344 = OpPhi %v4float %287 %233 %329 %291
        %345 = OpCompositeExtract %float %162 3
        %346 = OpFOrdGreaterThan %bool %345 %float_0
               OpSelectionMerge %347 None
               OpBranchConditional %346 %348 %347
        %348 = OpLabel
        %349 = OpAccessChain %_ptr_Uniform_MaterialLayer %MaterialParams %int_3
        %350 = OpAccessChain %_ptr_Uniform_v4float %349 %uint_0
        %351 = OpLoad %v4float %350
        %352 = OpAccessChain %_ptr_Uniform_float %349 %uint_2
        %353 = OpLoad %float %352
        %354 = OpAccessChain %_ptr_Uniform_float %349 %uint_3
        %355 = OpLoad %float %354
        %356 = OpAccessChain %_ptr_Uniform_float %349 %uint_4
        %357 = OpLoad %float %356
        %358 = OpAccessChain %_ptr_Uniform_v2float %349 %uint_5
        %359 = OpLoad %v2float %358
        %360 = OpVectorTimesScalar %v2float %115 %357
        %361 = OpFAdd %v2float %360 %359
        %362 = OpLoad %type_2d_image_array %AlbedoTextures
        %363 = OpLoad %type_sampler %TrilinearSampler
        %364 = OpCompositeExtract %float %361 0
        %365 = OpCompositeExtract %float %361 1
        %366 = OpCompositeConstruct %v3float %364 %365 %float_3
        %367 = OpSampledImage %type_sampled_image_0 %362 %363
        %368 = OpImageSampleImplicitLod %v4float %367 %366 None
        %369 = OpFMul %v4float %368 %351
        %370 = OpLoad %type_2d_image_array %NormalTextures
        %371 = OpLoad %type_sampler %TrilinearSampler
        %372 = OpSampledImage %type_sampled_image_0 %370 %371
        %373 = OpImageSampleImplicitLod %v4float %372 %366 None
        %374 = OpVectorShuffle %v3float %373 %373 0 1 2
        %375 = OpVectorTimesScalar %v3float %374 %float_2
        %376 = OpFSub %v3float %375 %72
        %377 = OpVectorTimesScalar %v3float %376 %355
        %378 = OpLoad %type_2d_image_array %RoughnessTextures
        %379 = OpLoad %type_sampler %TrilinearSampler
        %380 = OpSampledImage %type_sampled_image_0 %378 %379
        %381 = OpImageSampleImplicitLod %v4float %380 %366 None
        %382 = OpCompositeExtract %float %381 0
        %383 = OpFMul %float %353 %382
        %384 = OpCompositeExtract %float %381 1
        %385 = OpVectorTimesScalar %v4float %369 %345
        %386 = OpFAdd %v4float %344 %385
        %387 = OpVectorTimesScalar %v3float %377 %345
        %388 = OpFAdd %v3float %343 %387
        %389 = OpFMul %float %383 %345
        %390 = OpFAdd %float %342 %389
        %391 = OpFMul %float %384 %345
        %392 = OpFAdd %float %341 %391
        %393 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_3 %int_1
        %394 = OpLoad %float %393
        %395 = OpFMul %float %394 %345
        %396 = OpFAdd %float %340 %395
               OpBranch %347
        %347 = OpLabel
        %397 = OpPhi %float %340 %290 %396 %348
        %398 = OpPhi %v4float %344 %290 %386 %348
        %399 = OpPhi %float %341 %290 %392 %348
        %400 = OpPhi %float %342 %290 %390 %348
        %401 = OpPhi %v3float %343 %290 %388 %348
        %402 = OpLoad %type_2d_image %DetailNormalTexture
        %403 = OpLoad %type_sampler %TrilinearSampler
        %404 = OpSampledImage %type_sampled_image %402 %403
        %405 = OpImageSampleImplicitLod %v4float %404 %116 None
        %406 = OpVectorShuffle %v3float %405 %405 0 1 2
        %407 = OpVectorTimesScalar %v3float %406 %float_2
        %408 = OpFSub %v3float %407 %72
        %409 = OpFAdd %v3float %401 %73
        %410 = OpFMul %v3float %408 %75
        %411 = OpCompositeExtract %float %409 2
        %412 = OpCompositeConstruct %v3float %411 %411 %411
        %413 = OpFDiv %v3float %409 %412
        %414 = OpDot %float %409 %410
        %415 = OpVectorTimesScalar %v3float %413 %414
        %416 = OpFSub %v3float %415 %410
        %417 = OpExtInst %v3float %1 FMix %401 %416 %66
        %418 = OpExtInst %v3float %1 Normalize %417
        %419 = OpCompositeConstruct %mat3v3float %123 %124 %122
        %420 = OpMatrixTimesVector %v3float %419 %418
        %421 = OpExtInst %v3float %1 Normalize %420
        %422 = OpAccessChain %_ptr_Uniform_v3float %PerFrame %int_5
        %423 = OpLoad %v3float %422
        %424 = OpFNegate %v3float %423
        %425 = OpExtInst %v3float %1 Normalize %424
        %426 = OpDot %float %421 %425
        %427 = OpExtInst %float %1 NMax %426 %float_0
               OpSelectionMerge %428 None
               OpSwitch %uint_0 %429
        %429 = OpLabel
        %430 = OpVectorShuffle %v3float %120 %120 0 1 2
        %431 = OpCompositeExtract %float %120 3
        %432 = OpCompositeConstruct %v3float %431 %431 %431
        %433 = OpFDiv %v3float %430 %432
        %434 = OpVectorTimesScalar %v3float %433 %float_0_5
        %435 = OpFAdd %v3float %434 %66
        %436 = OpCompositeExtract %float %435 0
        %437 = OpFOrdLessThan %bool %436 %float_0
        %438 = OpLogicalNot %bool %437
               OpSelectionMerge %439 None
               OpBranchConditional %438 %440 %439
        %440 = OpLabel
        %441 = OpFOrdGreaterThan %bool %436 %float_1
               OpBranch %439
        %439 = OpLabel
        %442 = OpPhi %bool %true %429 %441 %440
        %443 = OpLogicalNot %bool %442
               OpSelectionMerge %444 None
               OpBranchConditional %443 %445 %444
        %445 = OpLabel
        %446 = OpCompositeExtract %float %435 1
        %447 = OpFOrdLessThan %bool %446 %float_0
               OpBranch %444
        %444 = OpLabel
        %448 = OpPhi %bool %true %439 %447 %445
        %449 = OpLogicalNot %bool %448
               OpSelectionMerge %450 None
               OpBranchConditional %449 %451 %450
        %451 = OpLabel
        %452 = OpCompositeExtract %float %435 1
        %453 = OpFOrdGreaterThan %bool %452 %float_1
               OpBranch %450
        %450 = OpLabel
        %454 = OpPhi %bool %true %444 %453 %451
               OpSelectionMerge %455 None
               OpBranchConditional %454 %456 %455
        %456 = OpLabel
               OpBranch %428
        %455 = OpLabel
               OpBranch %457
        %457 = OpLabel
        %458 = OpPhi %float %float_0 %455 %459 %460
        %461 = OpPhi %int %int_n1 %455 %462 %460
        %463 = OpSLessThanEqual %bool %461 %int_1
               OpLoopMerge %464 %460 None
               OpBranchConditional %463 %465 %464
        %465 = OpLabel
               OpBranch %466
        %466 = OpLabel
        %459 = OpPhi %float %458 %465 %467 %468
        %469 = OpPhi %int %int_n1 %465 %470 %468
        %471 = OpSLessThanEqual %bool %469 %int_1
               OpLoopMerge %472 %468 None
               OpBranchConditional %471 %468 %472
        %468 = OpLabel
        %473 = OpConvertSToF %float %461
        %474 = OpConvertSToF %float %469
        %475 = OpCompositeConstruct %v2float %473 %474
        %476 = OpFMul %v2float %475 %102
        %477 = OpLoad %type_2d_image %ShadowMap
        %478 = OpLoad %type_sampler %ShadowSampler
        %479 = OpVectorShuffle %v2float %435 %435 0 1
        %480 = OpFAdd %v2float %479 %476
        %481 = OpCompositeExtract %float %435 2
        %482 = OpSampledImage %type_sampled_image %477 %478
        %483 = OpImageSampleDrefExplicitLod %float %482 %480 %481 Lod %float_0
        %467 = OpFAdd %float %459 %483
        %470 = OpIAdd %int %469 %int_1
               OpBranch %466
        %472 = OpLabel
               OpBranch %460
        %460 = OpLabel
        %462 = OpIAdd %int %461 %int_1
               OpBranch %457
        %464 = OpLabel
        %484 = OpFMul %float %458 %float_0_111111112
               OpBranch %428
        %428 = OpLabel
        %485 = OpPhi %float %float_1 %456 %484 %464
        %486 = OpFNegate %v3float %125
        %487 = OpExtInst %v3float %1 Reflect %486 %421
        %488 = OpFMul %float %400 %float_7
        %489 = OpLoad %type_cube_image %SkyboxTexture
        %490 = OpLoad %type_sampler %TrilinearSampler
        %491 = OpSampledImage %type_sampled_image_1 %489 %490
        %492 = OpImageSampleExplicitLod %v4float %491 %487 Lod %488
        %493 = OpVectorShuffle %v3float %492 %492 0 1 2
        %494 = OpVectorTimesScalar %v3float %64 %399
        %495 = OpVectorShuffle %v3float %398 %398 0 1 2
        %496 = OpVectorTimesScalar %v3float %495 %427
        %497 = OpVectorTimesScalar %v3float %496 %485
        %498 = OpFSub %float %float_1 %400
        %499 = OpVectorTimesScalar %v3float %493 %498
        %500 = OpVectorTimesScalar %v3float %499 %397
        %501 = OpFAdd %v3float %494 %497
        %502 = OpFAdd %v3float %501 %500
        %503 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_6
        %504 = OpLoad %float %503
        %505 = OpFSub %float %121 %504
        %506 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_7
        %507 = OpLoad %float %506
        %508 = OpFSub %float %507 %504
        %509 = OpFDiv %float %505 %508
        %510 = OpExtInst %float %1 FClamp %509 %float_0 %float_1
        %511 = OpAccessChain %_ptr_Uniform_v3float %MaterialParams %int_4
        %512 = OpLoad %v3float %511
        %513 = OpAccessChain %_ptr_Uniform_float %MaterialParams %int_5
        %514 = OpLoad %float %513
        %515 = OpFMul %float %510 %514
        %516 = OpCompositeConstruct %v3float %515 %515 %515
        %517 = OpExtInst %v3float %1 FMix %502 %512 %516
        %518 = OpCompositeExtract %float %398 3
        %519 = OpCompositeExtract %float %517 0
        %520 = OpCompositeExtract %float %517 1
        %521 = OpCompositeExtract %float %517 2
        %522 = OpCompositeConstruct %v4float %519 %520 %521 %518
        %523 = OpVectorTimesScalar %v3float %421 %float_0_5
        %524 = OpFAdd %v3float %523 %66
        %525 = OpCompositeExtract %float %524 0
        %526 = OpCompositeExtract %float %524 1
        %527 = OpCompositeExtract %float %524 2
        %528 = OpCompositeConstruct %v4float %525 %526 %527 %400
        %529 = OpFDiv %float %117 %142
        %530 = OpCompositeConstruct %v4float %397 %400 %399 %529
        %531 = OpCompositeExtract %float %111 2
        %532 = OpCompositeExtract %float %111 3
        %533 = OpFDiv %float %531 %532
        %534 = OpCompositeConstruct %v4float %float_0 %float_0 %533 %float_1
               OpStore %out_var_SV_Target0 %522
               OpStore %out_var_SV_Target1 %528
               OpStore %out_var_SV_Target2 %530
               OpStore %out_var_SV_Target3 %534
               OpReturn
               OpFunctionEnd
