// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct Particle
{
  float3 Position;
  float Life;
  float3 Velocity;
  float Size;
  float4 Color;
  float3 Acceleration;
  float Mass;
  uint Type;
  float3 _padding;
};

cbuffer ComputeParams : register(b0)
{
  uint ParticleCount;
  uint MaxParticles;
  float DeltaTime;
  float Time;
  float3 Gravity;
  float Damping;
  float3 EmitterPosition;
  float EmissionRate;
  float3 EmitterDirection;
  float EmissionSpeed;
  float2 LifetimeRange;
  float2 SizeRange;
  uint FrameCount;
  float NoiseScale;
  float NoiseStrength;
  uint _padding;
}

RWStructuredBuffer<Particle> ParticleBuffer : register(u0);
AppendStructuredBuffer<Particle> DeadParticleBuffer : register(u1);
groupshared float3 SharedPositions[64];
groupshared float SharedDistances[64];
groupshared uint SharedIndices[64];
float Hash(float n)
{
  return frac((sin(n) * 43758.5453f));
}

float Noise(float3 x)
{
  float3 p = floor(x);
  float3 f = frac(x);
  f = ((f * f) * 3.0f - (2.0f * f));
  float n = p.x + (p.y * 57.0f) + (113.0f * p.z);
  return lerp(lerp(lerp(Hash(n + 0.0f), Hash(n + 1.0f), f.x), lerp(Hash(n + 57.0f), Hash(n + 58.0f), f.x), f.y), lerp(lerp(Hash(n + 113.0f), Hash(n + 114.0f), f.x), lerp(Hash(n + 170.0f), Hash(n + 171.0f), f.x), f.y), f.z);
}

float3 CurlNoise(float3 p)
{
  const float e = 0.1f;
  float3 dx = float3(e, 0.0f, 0.0f);
  float3 dy = float3(0.0f, e, 0.0f);
  float3 dz = float3(0.0f, 0.0f, e);
  float3 p_x0 = Noise(p - dx);
  float3 p_x1 = Noise(p + dx);
  float3 p_y0 = Noise(p - dy);
  float3 p_y1 = Noise(p + dy);
  float3 p_z0 = Noise(p - dz);
  float3 p_z1 = Noise(p + dz);
  float x = p_y1.z - p_y0.z - p_z1.y + p_z0.y;
  float y = p_z1.x - p_z0.x - p_x1.z + p_x0.z;
  float z = p_x1.y - p_x0.y - p_y1.x + p_y0.x;
  return float3(x, y, z) / (2.0f * e);
}

[numthreads(8, 8, 1)]
void main(uint3 id : SV_DispatchThreadID, uint3 groupId : SV_GroupID, uint3 localId : SV_GroupThreadID)
{
  uint index = id.x;
  if (index >= ParticleCount)
    return ;
  Particle particle = ParticleBuffer[index];
  particle.Life -= DeltaTime;
  if (particle.Life <= 0.0f)
  {
    DeadParticleBuffer.Append(particle);
    particle.Type = 0;
    ParticleBuffer[index] = particle;
    return ;
  }
  float3 force = (Gravity * particle.Mass);
  float3 noisePos = (particle.Position * NoiseScale) + (Time * 0.1f);
  float3 curlForce = (CurlNoise(noisePos) * NoiseStrength);
  force += curlForce;
  particle.Acceleration = force / particle.Mass;
  particle.Velocity += (particle.Acceleration * DeltaTime);
  particle.Velocity *= Damping;
  particle.Position += (particle.Velocity * DeltaTime);
  float lifeRatio = particle.Life / 2.0f;
  particle.Size = lerp(SizeRange.x, SizeRange.y, lifeRatio);
  particle.Color.a = lifeRatio;
  SharedPositions[localId.x] = particle.Position;
  SharedDistances[localId.x] = length(particle.Position - EmitterPosition);
  SharedIndices[localId.x] = index;
  GroupMemoryBarrierWithGroupSync();
  float3 separation = float3(0, 0, 0);
  float3 alignment = float3(0, 0, 0);
  float3 cohesion = float3(0, 0, 0);
  uint neighborCount = 0;
  const float neighborRadius = 5.0f;
  for (uint i = 0; i < 64; (++i))
  {
    if (i == localId.x)
      continue;
    float3 neighborPos = SharedPositions[i];
    float distance = length(particle.Position - neighborPos);
    if (distance < neighborRadius && distance > 0.0f)
    {
      float3 diff = particle.Position - neighborPos;
      separation += normalize(diff) / distance;
      cohesion += neighborPos;
      (neighborCount++);
    }
  }
  if (neighborCount > 0)
  {
    cohesion /= neighborCount;
    cohesion = normalize(cohesion - particle.Position);
    particle.Velocity += ((separation * 0.1f) * DeltaTime);
    particle.Velocity += ((cohesion * 0.05f) * DeltaTime);
  }
  ParticleBuffer[index] = particle;
}

