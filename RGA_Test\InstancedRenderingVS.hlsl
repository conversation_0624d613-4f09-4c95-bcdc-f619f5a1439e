// Instanced Rendering Vertex Shader
// Tests hardware instancing and per-instance data

cbuffer PerFrame : register(b0)
{
    float4x4 ViewProjectionMatrix;
    float3 CameraPosition;
    float Time;
    float3 WindDirection;
    float WindStrength;
};

struct VSInput
{
    // Per-vertex data
    float3 Position : POSITION;
    float3 Normal : NORMAL;
    float2 TexCoord : TEXCOORD0;
    float4 Color : COLOR0;
    
    // Per-instance data
    float4 InstancePosition : TEXCOORD1;    // xyz: position, w: scale
    float4 InstanceRotation : TEXCOORD2;    // quaternion
    float4 InstanceColor : TEXCOORD3;       // rgba: color tint
    float4 InstanceData : TEXCOORD4;        // x: animation phase, y: health, z: type, w: age
};

struct VSOutput
{
    float4 Position : SV_POSITION;
    float3 WorldPos : TEXCOORD0;
    float3 Normal : TEXCOORD1;
    float2 TexCoord : TEXCOORD2;
    float4 Color : TEXCOORD3;
    float3 ViewDir : TEXCOORD4;
    float InstanceType : TEXCOORD5;
    float Health : TEXCOORD6;
};

// Quaternion rotation function
float3 rotateByQuaternion(float3 v, float4 q)
{
    float3 qvec = q.xyz;
    float3 uv = cross(qvec, v);
    float3 uuv = cross(qvec, uv);
    return v + 2.0 * (uv * q.w + uuv);
}

// Animation functions
float3 applyWindAnimation(float3 position, float3 worldPos, float animPhase, float age)
{
    float windEffect = sin(Time * 2.0 + animPhase + worldPos.x * 0.1) * WindStrength;
    float heightFactor = position.y; // More wind effect at top
    return position + WindDirection * windEffect * heightFactor * 0.1;
}

float3 applyGrowthAnimation(float3 position, float age, float health)
{
    float growthFactor = saturate(age * 0.1) * health;
    return position * growthFactor;
}

VSOutput main(VSInput input)
{
    VSOutput output;
    
    // Extract instance data
    float3 instancePos = input.InstancePosition.xyz;
    float instanceScale = input.InstancePosition.w;
    float4 instanceRot = input.InstanceRotation;
    float animPhase = input.InstanceData.x;
    float health = input.InstanceData.y;
    float instanceType = input.InstanceData.z;
    float age = input.InstanceData.w;
    
    // Apply animations based on instance type
    float3 animatedPosition = input.Position;
    
    if (instanceType < 1.0) // Vegetation
    {
        animatedPosition = applyWindAnimation(animatedPosition, instancePos, animPhase, age);
        animatedPosition = applyGrowthAnimation(animatedPosition, age, health);
    }
    else if (instanceType < 2.0) // Rocks/Static objects
    {
        // No animation for static objects
    }
    else // Dynamic objects
    {
        // Bobbing animation
        float bobOffset = sin(Time * 3.0 + animPhase) * 0.1;
        animatedPosition.y += bobOffset;
    }
    
    // Apply instance scale
    animatedPosition *= instanceScale;
    
    // Apply instance rotation
    animatedPosition = rotateByQuaternion(animatedPosition, instanceRot);
    float3 worldNormal = rotateByQuaternion(input.Normal, instanceRot);
    
    // Transform to world space
    float3 worldPos = animatedPosition + instancePos;
    output.WorldPos = worldPos;
    
    // Transform to clip space
    output.Position = mul(float4(worldPos, 1.0), ViewProjectionMatrix);
    
    // Transform normal
    output.Normal = normalize(worldNormal);
    
    // Pass through and modify texture coordinates
    output.TexCoord = input.TexCoord;
    
    // Combine vertex color with instance color
    output.Color = input.Color * input.InstanceColor;
    
    // Apply health-based color modification
    if (health < 0.5)
    {
        output.Color.rgb = lerp(float3(0.5, 0.3, 0.1), output.Color.rgb, health * 2.0);
    }
    
    // Calculate view direction
    output.ViewDir = normalize(CameraPosition - worldPos);
    
    // Pass instance data
    output.InstanceType = instanceType;
    output.Health = health;
    
    return output;
}
