# Compile all shaders and save results to CompileResults folder
Write-Host "Compiling all shaders and saving results..." -ForegroundColor Green

$hlslOutputDir = "CompileResults\HLSL"
$glslOutputDir = "CompileResults\GLSL"
$logFile = "CompileResults\compilation.log"

# Create output directories if they don't exist
if (-not (Test-Path $hlslOutputDir)) {
    New-Item -ItemType Directory -Path $hlslOutputDir -Force | Out-Null
}
if (-not (Test-Path $glslOutputDir)) {
    New-Item -ItemType Directory -Path $glslOutputDir -Force | Out-Null
}

# Initialize log file
"=== Shader Compilation Log ===" | Out-File -FilePath $logFile -Encoding UTF8
"Start time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Get all shader files from current directory (TestShader)
$hlslFiles = Get-ChildItem -Path "." -Filter "*.hlsl"
$glslFiles = Get-ChildItem -Path "." -Filter "*.*" | Where-Object { $_.Extension -in @(".vert", ".frag", ".geom", ".comp", ".tesc", ".tese") }

Write-Host "Found $($hlslFiles.Count) HLSL files and $($glslFiles.Count) GLSL files" -ForegroundColor Cyan

# Initialize counters
$totalFiles = $hlslFiles.Count + $glslFiles.Count
$hlslSuccessCount = 0
$hlslFailureCount = 0
$glslSuccessCount = 0
$glslFailureCount = 0

# Compile HLSL files to SPIR-V
Write-Host "`nCompiling HLSL files to SPIR-V..." -ForegroundColor Yellow
foreach ($file in $hlslFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor White

    # Log current file processing
    "Processing HLSL: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8

    # Determine shader profile and entry point based on filename
    $profile = ""
    $entryPoint = "main"

    if ($file.Name -like "*VS*" -or $file.Name -like "*Vert*") {
        $profile = "vs_6_0"
    } elseif ($file.Name -like "*PS*" -or $file.Name -like "*Pixel*") {
        $profile = "ps_6_0"
    } elseif ($file.Name -like "*CS*" -or $file.Name -like "*Compute*") {
        $profile = "cs_6_0"
    } else {
        $profile = "vs_6_0"  # Default fallback
    }

    $outputFile = Join-Path $hlslOutputDir "$($file.BaseName).spv"
    $individualLogFile = Join-Path $hlslOutputDir "$($file.BaseName).log"

    try {
        # Compile to SPIR-V for Vulkan
        $result = & dxc -T $profile -E $entryPoint -spirv $file.FullName -Fo $outputFile 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - SPIR-V saved to $outputFile" -ForegroundColor Green
            "  HLSL Compilation: SUCCESS" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "COMPILATION SUCCESSFUL (SPIR-V)`n$result" | Out-File -FilePath $individualLogFile -Encoding UTF8
            $hlslSuccessCount++
        } else {
            # Try without entry point if failed
            Write-Host "  Retrying without entry point..." -ForegroundColor Yellow
            $result2 = & dxc -T $profile -spirv $file.FullName -Fo $outputFile 2>&1

            if ($LASTEXITCODE -eq 0) {
                Write-Host "  [OK] SUCCESS (no entry point) - SPIR-V saved to $outputFile" -ForegroundColor Green
                "  HLSL Compilation: SUCCESS (no entry point)" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "COMPILATION SUCCESSFUL (SPIR-V, no entry point specified)`n$result2" | Out-File -FilePath $individualLogFile -Encoding UTF8
                $hlslSuccessCount++
            } else {
                Write-Host "  [FAIL] FAILED" -ForegroundColor Red
                "  HLSL Compilation: FAILED" | Out-File -FilePath $logFile -Append -Encoding UTF8
                "COMPILATION FAILED`nFirst attempt with entry point '$entryPoint':`n$result`n`nSecond attempt without entry point:`n$result2" | Out-File -FilePath $individualLogFile -Encoding UTF8
                $hlslFailureCount++
            }
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "  HLSL Compilation: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        "COMPILATION EXCEPTION: $($_.Exception.Message)" | Out-File -FilePath $individualLogFile -Encoding UTF8
        $hlslFailureCount++
    }

    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Compile GLSL files with Mali analysis
Write-Host "`nCompiling GLSL files with Mali analysis..." -ForegroundColor Yellow
foreach ($file in $glslFiles) {
    Write-Host "Compiling: $($file.Name)" -ForegroundColor White

    # Log current file processing
    "Processing GLSL: $($file.Name)" | Out-File -FilePath $logFile -Append -Encoding UTF8

    # Create unique output filename including extension to avoid conflicts
    $outputFile = Join-Path $glslOutputDir "$($file.Name).analysis"
    $individualGlslLogFile = Join-Path $glslOutputDir "$($file.Name).log"

    try {
        # Use malioc to analyze shader performance
        $result = & malioc -c Mali-G76 $file.FullName 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Host "  [OK] SUCCESS - Analysis completed" -ForegroundColor Green
            "  GLSL Analysis: SUCCESS" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "MALI ANALYSIS SUCCESSFUL`n$result" | Out-File -FilePath $outputFile -Encoding UTF8
            "COMPILATION SUCCESSFUL`n$result" | Out-File -FilePath $individualGlslLogFile -Encoding UTF8
            $glslSuccessCount++
        } else {
            Write-Host "  [FAIL] FAILED" -ForegroundColor Red
            "  GLSL Analysis: FAILED" | Out-File -FilePath $logFile -Append -Encoding UTF8
            "MALI ANALYSIS FAILED`n$result" | Out-File -FilePath $outputFile -Encoding UTF8
            "COMPILATION FAILED`n$result" | Out-File -FilePath $individualGlslLogFile -Encoding UTF8
            $glslFailureCount++
        }
    } catch {
        Write-Host "  [ERROR] EXCEPTION: $($_.Exception.Message)" -ForegroundColor Red
        "  GLSL Analysis: EXCEPTION - $($_.Exception.Message)" | Out-File -FilePath $logFile -Append -Encoding UTF8
        "MALI ANALYSIS EXCEPTION: $($_.Exception.Message)" | Out-File -FilePath $outputFile -Encoding UTF8
        "COMPILATION EXCEPTION: $($_.Exception.Message)" | Out-File -FilePath $individualGlslLogFile -Encoding UTF8
        $glslFailureCount++
    }

    # Add separator in log
    "" | Out-File -FilePath $logFile -Append -Encoding UTF8
}

# Calculate totals
$totalSuccessCount = $hlslSuccessCount + $glslSuccessCount
$totalFailureCount = $hlslFailureCount + $glslFailureCount

# Final log entry with statistics
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Statistics ===" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Total files processed: $totalFiles" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"HLSL files: $($hlslFiles.Count)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Successful: $hlslSuccessCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Failed: $hlslFailureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Success rate: $(if ($hlslFiles.Count -gt 0) { [math]::Round(($hlslSuccessCount / $hlslFiles.Count) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"GLSL files: $($glslFiles.Count)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Successful: $glslSuccessCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Failed: $glslFailureCount" | Out-File -FilePath $logFile -Append -Encoding UTF8
"  - Success rate: $(if ($glslFiles.Count -gt 0) { [math]::Round(($glslSuccessCount / $glslFiles.Count) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"" | Out-File -FilePath $logFile -Append -Encoding UTF8
"Overall success rate: $(if ($totalFiles -gt 0) { [math]::Round(($totalSuccessCount / $totalFiles) * 100, 2) } else { 0 })%" | Out-File -FilePath $logFile -Append -Encoding UTF8
"End time: $(Get-Date)" | Out-File -FilePath $logFile -Append -Encoding UTF8
"=== Compilation Complete ===" | Out-File -FilePath $logFile -Append -Encoding UTF8

# Display statistics
Write-Host "`n=== Compilation Statistics ===" -ForegroundColor Yellow
Write-Host "Total files processed: $totalFiles" -ForegroundColor White
Write-Host "" -ForegroundColor White

# HLSL Statistics
$hlslSuccessRate = if ($hlslFiles.Count -gt 0) { [math]::Round(($hlslSuccessCount / $hlslFiles.Count) * 100, 2) } else { 0 }
Write-Host "HLSL files: $($hlslFiles.Count)" -ForegroundColor Cyan
Write-Host "  - Successful: $hlslSuccessCount" -ForegroundColor Green
Write-Host "  - Failed: $hlslFailureCount" -ForegroundColor $(if ($hlslFailureCount -gt 0) { "Red" } else { "Green" })
Write-Host "  - Success rate: $hlslSuccessRate%" -ForegroundColor $(if ($hlslSuccessRate -eq 100) { "Green" } elseif ($hlslSuccessRate -ge 80) { "Yellow" } else { "Red" })

# GLSL Statistics
$glslSuccessRate = if ($glslFiles.Count -gt 0) { [math]::Round(($glslSuccessCount / $glslFiles.Count) * 100, 2) } else { 0 }
Write-Host "GLSL files: $($glslFiles.Count)" -ForegroundColor Cyan
Write-Host "  - Successful: $glslSuccessCount" -ForegroundColor Green
Write-Host "  - Failed: $glslFailureCount" -ForegroundColor $(if ($glslFailureCount -gt 0) { "Red" } else { "Green" })
Write-Host "  - Success rate: $glslSuccessRate%" -ForegroundColor $(if ($glslSuccessRate -eq 100) { "Green" } elseif ($glslSuccessRate -ge 80) { "Yellow" } else { "Red" })

# Overall Statistics
$overallSuccessRate = if ($totalFiles -gt 0) { [math]::Round(($totalSuccessCount / $totalFiles) * 100, 2) } else { 0 }
Write-Host "" -ForegroundColor White
Write-Host "Overall success rate: $overallSuccessRate%" -ForegroundColor $(if ($overallSuccessRate -eq 100) { "Green" } elseif ($overallSuccessRate -ge 80) { "Yellow" } else { "Red" })

Write-Host "`n[DONE] Compilation complete! Results saved in CompileResults\" -ForegroundColor Green
Write-Host "  - HLSL SPIR-V files and logs: CompileResults\HLSL\" -ForegroundColor Cyan
Write-Host "  - GLSL Mali analysis and logs: CompileResults\GLSL\" -ForegroundColor Cyan
Write-Host "  - Compilation log: CompileResults\compilation.log" -ForegroundColor Cyan
