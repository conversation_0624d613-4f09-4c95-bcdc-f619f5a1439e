#version 320 es

// Geometry Expansion Vertex Shader - OpenGL ES Version
// Tests vertex processing for geometry shader input

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;
layout(location = 3) in vec4 aColor;

uniform mat4 uModelMatrix;
uniform mat4 uViewMatrix;
uniform mat4 uProjectionMatrix;
uniform mat4 uNormalMatrix;
uniform float uTime;

out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;
out vec4 vColor;
out float vTime;

void main()
{
    // Transform position to world space
    vec4 worldPos = uModelMatrix * vec4(aPosition, 1.0);
    vWorldPos = worldPos.xyz;
    
    // Transform normal to world space
    vNormal = normalize((uNormalMatrix * vec4(aNormal, 0.0)).xyz);
    
    // Pass through attributes
    vTexCoord = aTexCoord;
    vColor = aColor;
    vTime = uTime;
    
    // Transform to view space (geometry shader will handle projection)
    gl_Position = uViewMatrix * worldPos;
}
