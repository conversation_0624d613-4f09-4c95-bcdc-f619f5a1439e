#version 320 es

// Basic Lighting Fragment Shader - OpenGL Version
// Tests basic Phong lighting model

precision highp float;

in vec3 vWorldPos;
in vec3 vNormal;
in vec2 vTexCoord;
in vec4 vColor;
in vec3 vViewDir;
in vec3 vLightDir;

out vec4 fragColor;

uniform vec4 uDiffuseColor;
uniform vec4 uSpecularColor;
uniform float uSpecularPower;
uniform vec3 uAmbientColor;

uniform sampler2D uDiffuseTexture;

void main()
{
    // Normalize interpolated vectors
    vec3 normal = normalize(vNormal);
    vec3 lightDir = normalize(vLightDir);
    vec3 viewDir = normalize(vViewDir);
    
    // Sample diffuse texture
    vec4 texColor = texture(uDiffuseTexture, vTexCoord);
    
    // Ambient lighting
    vec3 ambient = uAmbientColor * texColor.rgb;
    
    // Diffuse lighting
    float NdotL = max(0.0, dot(normal, lightDir));
    vec3 diffuse = uDiffuseColor.rgb * texColor.rgb * NdotL;
    
    // Specular lighting (Phong)
    vec3 reflectDir = reflect(-lightDir, normal);
    float RdotV = max(0.0, dot(reflectDir, viewDir));
    vec3 specular = uSpecularColor.rgb * pow(RdotV, uSpecularPower);
    
    // Combine lighting
    vec3 finalColor = ambient + diffuse + specular;
    
    fragColor = vec4(finalColor, texColor.a * uDiffuseColor.a);
}
