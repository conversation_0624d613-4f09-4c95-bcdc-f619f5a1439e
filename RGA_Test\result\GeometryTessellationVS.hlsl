// Generated by hlsloptconv - Modern HLSL Output
// Target: HLSL 5.0+ with enhanced modern syntax

struct TerrainVertex
{
  float3 Position;
  float3 Normal;
  float2 TexCoord;
  float4 Color;
  float Height;
  float3 Tangent;
  float2 DetailUV;
  float Slope;
};

struct ControlPoint
{
  float3 Position;
  float3 Normal;
  float2 TexCoord;
  float4 Color;
  float TessellationFactor;
  float3 Tangent;
  float3 Bitangent;
  float2 DetailUV;
};

struct VSInput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float3 Tangent : TANGENT;
  float2 TexCoord : TEXCOORD0;
  float4 Color : COLOR0;
  uint VertexID : SV_VertexID;
};

struct VSOutput
{
  float3 Position : POSITION;
  float3 Normal : NORMAL;
  float3 Tangent : TANGENT;
  float3 Bitangent : BITANGENT;
  float2 TexCoord : TEXCOORD0;
  float2 DetailUV : TEXCOORD1;
  float4 Color : COLOR0;
  float3 WorldPos : TEXCOORD2;
  float TessellationFactor : TEXCOORD3;
  float Height : TEXCOORD4;
  float Slope : TEXCOORD5;
};

cbuffer PerFrame : register(b0)
{
  float4x4 ViewMatrix;
  float4x4 ProjectionMatrix;
  float4x4 ViewProjectionMatrix;
  float3 CameraPosition;
  float Time;
  float3 LightDirection;
  float TessellationLevel;
  float2 HeightmapSize;
  float HeightScale;
  float DetailScale;
  float LODDistance;
}

cbuffer PerObject : register(b1)
{
  float4x4 WorldMatrix;
  float4x4 NormalMatrix;
  float3 BoundingBoxMin;
  float3 BoundingBoxMax;
  float2 TextureTiling;
  float DisplacementStrength;
  float _padding;
}

Texture2D HeightmapTexture : register(t0);
Texture2D DetailHeightTexture : register(t2);
SamplerState LinearSampler : register(s0);
float CalculateDistanceTessellation(float3 worldPos)
{
  float distance = length(CameraPosition - worldPos);
  float tessLevel = (TessellationLevel * LODDistance / max(distance, 1.0f));
  return clamp(tessLevel, 1.0f, 64.0f);
}

float SampleHeightmap(float2 uv)
{
  float height = HeightmapTexture.SampleLevel(LinearSampler, uv, 0).r;
  float2 detailUV = (uv * DetailScale);
  float detailHeight = DetailHeightTexture.SampleLevel(LinearSampler, detailUV, 0).r;
  return (height * HeightScale) + (detailHeight - 0.5f * DisplacementStrength);
}

float3 CalculateNormalFromHeightmap(float2 uv)
{
  float2 texelSize = 1.0f / HeightmapSize;
  float hL = SampleHeightmap(uv + float2((-texelSize.x), 0));
  float hR = SampleHeightmap(uv + float2(texelSize.x, 0));
  float hD = SampleHeightmap(uv + float2(0, (-texelSize.y)));
  float hU = SampleHeightmap(uv + float2(0, texelSize.y));
  float3 normal;
  normal.x = hL - hR / (2.0f * texelSize.x);
  normal.z = hD - hU / (2.0f * texelSize.y);
  normal.y = 1.0f;
  return normalize(normal);
}

float CalculateSlope(float3 normal)
{
  return 1.0f - dot(normal, float3(0, 1, 0));
}

float3 CalculateTangent(float2 uv, float3 normal)
{
  float2 texelSize = 1.0f / HeightmapSize;
  float h0 = SampleHeightmap(uv);
  float h1 = SampleHeightmap(uv + float2(texelSize.x, 0));
  float3 tangent = float3(texelSize.x, h1 - h0, 0);
  tangent = normalize(tangent);
  tangent = normalize(tangent - (dot(tangent, normal) * normal));
  return tangent;
}

VSOutput main(VSInput input)
{
  VSOutput output;
  float2 tiledUV = (input.TexCoord * TextureTiling);
  float height = SampleHeightmap(tiledUV);
  float3 displacedPosition = input.Position;
  displacedPosition.y += height;
  float4 worldPos = mul(float4(displacedPosition, 1.0f), WorldMatrix);
  output.WorldPos = worldPos.xyz;
  output.Position = displacedPosition;
  float3 heightmapNormal = CalculateNormalFromHeightmap(tiledUV);
  float3 blendedNormal = normalize(lerp(input.Normal, heightmapNormal, 0.8f));
  output.Normal = normalize(mul(blendedNormal, (float3x3)NormalMatrix));
  output.Tangent = CalculateTangent(tiledUV, blendedNormal);
  output.Tangent = normalize(mul(output.Tangent, (float3x3)NormalMatrix));
  output.Bitangent = normalize(cross(output.Normal, output.Tangent));
  output.TexCoord = tiledUV;
  output.DetailUV = (tiledUV * DetailScale);
  output.Slope = CalculateSlope(output.Normal);
  float4 heightColor = input.Color;
  float normalizedHeight = saturate(height / HeightScale);
  heightColor.rgb = lerp(float3(0.2f, 0.4f, 0.1f), float3(0.8f, 0.8f, 0.9f), normalizedHeight);
  if (output.Slope > 0.5f)
  {
    heightColor.rgb = lerp(heightColor.rgb, float3(0.5f, 0.4f, 0.3f), (output.Slope - 0.5f * 2.0f));
  }
  output.Color = heightColor;
  output.Height = height;
  float distanceTess = CalculateDistanceTessellation(output.WorldPos);
  float slopeTess = 1.0f + (output.Slope * 2.0f);
  output.TessellationFactor = (distanceTess * slopeTess);
  return output;
}

