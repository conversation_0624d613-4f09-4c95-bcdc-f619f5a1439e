#version 320 es

// Performance Test Vertex Shader - OpenGL ES Version
// Tests vertex shader performance with heavy calculations

layout(location = 0) in vec3 aPosition;
layout(location = 1) in vec3 aNormal;
layout(location = 2) in vec2 aTexCoord;

uniform mat4 uModelViewProjectionMatrix;
uniform mat4 uModelMatrix;
uniform mat4 uNormalMatrix;
uniform float uTime;
uniform int uComplexityLevel;

out vec3 vWorldPos;
out vec3 vNormal;
out vec2 vTexCoord;
out float vComplexityResult;

// Heavy mathematical function for performance testing
float complexCalculation(vec3 pos, float time, int iterations)
{
    float result = 0.0;
    vec3 p = pos;
    
    for(int i = 0; i < iterations; i++)
    {
        float t = time + float(i) * 0.1;
        result += sin(p.x * t) * cos(p.y * t) * sin(p.z * t);
        result += pow(abs(sin(length(p) + t)), 2.0);
        result += exp(-length(p - vec3(sin(t), cos(t), sin(t * 0.5))) * 0.1);
        
        // Update position for next iteration
        p = p * 1.1 + vec3(sin(t), cos(t), sin(t * 1.5)) * 0.1;
    }
    
    return result / float(iterations);
}

void main()
{
    // Transform position
    vec4 worldPos = uModelMatrix * vec4(aPosition, 1.0);
    vWorldPos = worldPos.xyz;
    
    // Transform normal
    vNormal = normalize((uNormalMatrix * vec4(aNormal, 0.0)).xyz);
    
    // Pass through texture coordinates
    vTexCoord = aTexCoord;
    
    // Perform complex calculations based on complexity level
    vComplexityResult = complexCalculation(aPosition, uTime, uComplexityLevel);
    
    // Apply some vertex displacement based on calculations
    vec3 displacement = vNormal * vComplexityResult * 0.1;
    worldPos.xyz += displacement;
    
    // Final position
    gl_Position = uModelViewProjectionMatrix * vec4(aPosition + displacement, 1.0);
}
