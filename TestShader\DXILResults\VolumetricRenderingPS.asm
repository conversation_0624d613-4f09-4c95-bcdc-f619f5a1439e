;
; Input signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Position              0   xyzw        0      POS   float       
; TEXCOORD                 0   xy          1     NONE   float       
; TEXCOORD                 1   xyz         2     NONE   float       
; TEXCOORD                 2   xyz         3     NONE   float   xyz 
; TEXCOORD                 3   xyz         4     NONE   float   xyz 
;
;
; Output signature:
;
; Name                 Index   Mask Register SysValue  Format   Used
; -------------------- ----- ------ -------- -------- ------- ------
; SV_Target                0   xyzw        0   TARGET   float   xyzw
;
; shader hash: 29f6702652087d5498be23a8266a7216
;
; Pipeline Runtime Information: 
;
;PSVRuntimeInfo:
; Pixel Shader
; DepthOutput=0
; SampleFrequency=0
; MinimumExpectedWaveLaneCount: 0
; MaximumExpectedWaveLaneCount: 4294967295
; UsesViewID: false
; SigInputElements: 5
; SigOutputElements: 1
; SigPatchConstOrPrimElements: 0
; SigInputVectors: 5
; SigOutputVectors[0]: 1
; SigOutputVectors[1]: 0
; SigOutputVectors[2]: 0
; SigOutputVectors[3]: 0
; EntryFunctionName: main
;
;
; Input signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Position              0          noperspective       
; TEXCOORD                 0                 linear       
; TEXCOORD                 1                 linear       
; TEXCOORD                 2                 linear       
; TEXCOORD                 3                 linear       
;
; Output signature:
;
; Name                 Index             InterpMode DynIdx
; -------------------- ----- ---------------------- ------
; SV_Target                0                              
;
; Buffer Definitions:
;
; cbuffer VolumeParams
; {
;
;   struct VolumeParams
;   {
;
;       float3 VolumeMin;                             ; Offset:    0
;       float StepSize;                               ; Offset:   12
;       float3 VolumeMax;                             ; Offset:   16
;       int MaxSteps;                                 ; Offset:   28
;       float3 LightDirection;                        ; Offset:   32
;       float Density;                                ; Offset:   44
;       float3 LightColor;                            ; Offset:   48
;       float Absorption;                             ; Offset:   60
;       float3 ScatteringColor;                       ; Offset:   64
;       float Scattering;                             ; Offset:   76
;       float Time;                                   ; Offset:   80
;       float NoiseScale;                             ; Offset:   84
;       float NoiseStrength;                          ; Offset:   88
;   
;   } VolumeParams;                                   ; Offset:    0 Size:    92
;
; }
;
;
; Resource Bindings:
;
; Name                                 Type  Format         Dim      ID      HLSL Bind  Count
; ------------------------------ ---------- ------- ----------- ------- -------------- ------
; VolumeParams                      cbuffer      NA          NA     CB0            cb0     1
; LinearSampler                     sampler      NA          NA      S0             s0     1
; VolumeTexture                     texture     f32          3d      T0             t0     1
; NoiseTexture                      texture     f32          3d      T1             t1     1
;
;
; ViewId state:
;
; Number of inputs: 19, outputs: 4
; Outputs dependent on ViewId: {  }
; Inputs contributing to computation of Outputs:
;   output 0 depends on inputs: { 12, 13, 14, 16, 17, 18 }
;   output 1 depends on inputs: { 12, 13, 14, 16, 17, 18 }
;   output 2 depends on inputs: { 12, 13, 14, 16, 17, 18 }
;   output 3 depends on inputs: { 12, 13, 14, 16, 17, 18 }
;
target datalayout = "e-m:e-p:32:32-i1:32-i8:32-i16:32-i32:32-i64:64-f16:32-f32:32-f64:64-n8:16:32:64"
target triple = "dxil-ms-dx"

%dx.types.Handle = type { i8* }
%dx.types.CBufRet.f32 = type { float, float, float, float }
%dx.types.CBufRet.i32 = type { i32, i32, i32, i32 }
%dx.types.ResRet.f32 = type { float, float, float, float, i32 }
%"class.Texture3D<vector<float, 4> >" = type { <4 x float>, %"class.Texture3D<vector<float, 4> >::mips_type" }
%"class.Texture3D<vector<float, 4> >::mips_type" = type { i32 }
%VolumeParams = type { <3 x float>, float, <3 x float>, i32, <3 x float>, float, <3 x float>, float, <3 x float>, float, float, float, float }
%struct.SamplerState = type { i32 }

define void @main() {
  %1 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 1, i32 1, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %2 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 0, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %3 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 3, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %4 = call %dx.types.Handle @dx.op.createHandle(i32 57, i8 2, i32 0, i32 0, i1 false)  ; CreateHandle(resourceClass,rangeId,index,nonUniformIndex)
  %5 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %6 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %7 = call float @dx.op.loadInput.f32(i32 4, i32 4, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %8 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 0, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %9 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 1, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %10 = call float @dx.op.loadInput.f32(i32 4, i32 3, i32 0, i8 2, i32 undef)  ; LoadInput(inputSigId,rowIndex,colIndex,gsVertexAxis)
  %11 = call float @dx.op.dot3.f32(i32 55, float %8, float %9, float %10, float %8, float %9, float %10)  ; Dot3(ax,ay,az,bx,by,bz)
  %12 = call float @dx.op.unary.f32(i32 25, float %11)  ; Rsqrt(value)
  %13 = fmul fast float %12, %8
  %14 = fmul fast float %12, %9
  %15 = fmul fast float %12, %10
  %16 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %17 = extractvalue %dx.types.CBufRet.f32 %16, 0
  %18 = extractvalue %dx.types.CBufRet.f32 %16, 1
  %19 = extractvalue %dx.types.CBufRet.f32 %16, 2
  %20 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %21 = extractvalue %dx.types.CBufRet.f32 %20, 0
  %22 = extractvalue %dx.types.CBufRet.f32 %20, 1
  %23 = extractvalue %dx.types.CBufRet.f32 %20, 2
  %24 = fdiv fast float 1.000000e+00, %13
  %25 = fdiv fast float 1.000000e+00, %14
  %26 = fdiv fast float 1.000000e+00, %15
  %27 = fsub fast float %21, %5
  %28 = fsub fast float %22, %6
  %29 = fsub fast float %23, %7
  %30 = fmul fast float %27, %24
  %31 = fmul fast float %28, %25
  %32 = fmul fast float %26, %29
  %33 = fsub fast float %17, %5
  %34 = fsub fast float %18, %6
  %35 = fsub fast float %19, %7
  %36 = fmul fast float %33, %24
  %37 = fmul fast float %25, %34
  %38 = fmul fast float %26, %35
  %39 = call float @dx.op.binary.f32(i32 36, float %30, float %36)  ; FMin(a,b)
  %40 = call float @dx.op.binary.f32(i32 36, float %31, float %37)  ; FMin(a,b)
  %41 = call float @dx.op.binary.f32(i32 36, float %32, float %38)  ; FMin(a,b)
  %42 = call float @dx.op.binary.f32(i32 35, float %30, float %36)  ; FMax(a,b)
  %43 = call float @dx.op.binary.f32(i32 35, float %31, float %37)  ; FMax(a,b)
  %44 = call float @dx.op.binary.f32(i32 35, float %32, float %38)  ; FMax(a,b)
  %45 = call float @dx.op.binary.f32(i32 35, float %39, float %40)  ; FMax(a,b)
  %46 = call float @dx.op.binary.f32(i32 35, float %45, float %41)  ; FMax(a,b)
  %47 = call float @dx.op.binary.f32(i32 36, float %42, float %43)  ; FMin(a,b)
  %48 = call float @dx.op.binary.f32(i32 36, float %47, float %44)  ; FMin(a,b)
  %49 = fcmp fast ogt float %48, %46
  %50 = fcmp fast ogt float %48, 0.000000e+00
  %51 = and i1 %49, %50
  br i1 %51, label %53, label %52

; <label>:52                                      ; preds = %0
  call void @dx.op.discard(i32 82, i1 true)  ; Discard(condition)
  br label %53

; <label>:53                                      ; preds = %52, %0
  %54 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %46)  ; FMax(a,b)
  %55 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %56 = extractvalue %dx.types.CBufRet.i32 %55, 3
  %57 = icmp sgt i32 %56, 0
  %58 = fcmp fast olt float %54, %48
  %59 = and i1 %58, %57
  br i1 %59, label %60, label %213

; <label>:60                                      ; preds = %53
  br label %61

; <label>:61                                      ; preds = %196, %60
  %62 = phi i32 [ %202, %196 ], [ 0, %60 ]
  %63 = phi float [ %201, %196 ], [ %54, %60 ]
  %64 = phi float [ %200, %196 ], [ 1.000000e+00, %60 ]
  %65 = phi float [ %199, %196 ], [ 0.000000e+00, %60 ]
  %66 = phi float [ %198, %196 ], [ 0.000000e+00, %60 ]
  %67 = phi float [ %197, %196 ], [ 0.000000e+00, %60 ]
  %68 = fmul fast float %63, %13
  %69 = fmul fast float %63, %14
  %70 = fmul fast float %63, %15
  %71 = fadd fast float %68, %5
  %72 = fadd fast float %69, %6
  %73 = fadd fast float %70, %7
  %74 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 0)  ; CBufferLoadLegacy(handle,regIndex)
  %75 = extractvalue %dx.types.CBufRet.f32 %74, 0
  %76 = extractvalue %dx.types.CBufRet.f32 %74, 1
  %77 = extractvalue %dx.types.CBufRet.f32 %74, 2
  %78 = fsub fast float %71, %75
  %79 = fsub fast float %72, %76
  %80 = fsub fast float %73, %77
  %81 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %82 = extractvalue %dx.types.CBufRet.f32 %81, 0
  %83 = extractvalue %dx.types.CBufRet.f32 %81, 1
  %84 = extractvalue %dx.types.CBufRet.f32 %81, 2
  %85 = fsub fast float %82, %75
  %86 = fsub fast float %83, %76
  %87 = fsub fast float %84, %77
  %88 = fdiv fast float %78, %85
  %89 = fdiv fast float %79, %86
  %90 = fdiv fast float %80, %87
  %91 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %2, %dx.types.Handle %3, float %88, float %89, float %90, float undef, i32 0, i32 0, i32 0, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %92 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 5)  ; CBufferLoadLegacy(handle,regIndex)
  %93 = extractvalue %dx.types.CBufRet.f32 %92, 1
  %94 = fmul fast float %93, %71
  %95 = fmul fast float %93, %72
  %96 = fmul fast float %93, %73
  %97 = extractvalue %dx.types.CBufRet.f32 %92, 0
  %98 = fmul fast float %97, 0x3FB99999A0000000
  %99 = fmul fast float %97, 0x3FA99999A0000000
  %100 = fmul fast float %97, 0x3FB47AE140000000
  %101 = fadd fast float %98, %94
  %102 = fadd fast float %99, %95
  %103 = fadd fast float %100, %96
  %104 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %101, float %102, float %103, float undef, i32 0, i32 0, i32 0, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %105 = extractvalue %dx.types.ResRet.f32 %104, 0
  %106 = fmul fast float %105, 5.000000e-01
  %107 = fmul fast float %101, 2.000000e+00
  %108 = fmul fast float %102, 2.000000e+00
  %109 = fmul fast float %103, 2.000000e+00
  %110 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %107, float %108, float %109, float undef, i32 0, i32 0, i32 0, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %111 = extractvalue %dx.types.ResRet.f32 %110, 0
  %112 = fmul fast float %111, 2.500000e-01
  %113 = fadd fast float %112, %106
  %114 = fmul fast float %101, 4.000000e+00
  %115 = fmul fast float %102, 4.000000e+00
  %116 = fmul fast float %103, 4.000000e+00
  %117 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %114, float %115, float %116, float undef, i32 0, i32 0, i32 0, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %118 = extractvalue %dx.types.ResRet.f32 %117, 0
  %119 = fmul fast float %118, 1.250000e-01
  %120 = fadd fast float %113, %119
  %121 = fmul fast float %101, 8.000000e+00
  %122 = fmul fast float %102, 8.000000e+00
  %123 = fmul fast float %103, 8.000000e+00
  %124 = call %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32 62, %dx.types.Handle %1, %dx.types.Handle %3, float %121, float %122, float %123, float undef, i32 0, i32 0, i32 0, float 0.000000e+00)  ; SampleLevel(srv,sampler,coord0,coord1,coord2,coord3,offset0,offset1,offset2,LOD)
  %125 = extractvalue %dx.types.ResRet.f32 %124, 0
  %126 = fmul fast float %125, 6.250000e-02
  %127 = fadd fast float %120, %126
  %128 = extractvalue %dx.types.ResRet.f32 %91, 0
  %129 = extractvalue %dx.types.CBufRet.f32 %92, 2
  %130 = fmul fast float %127, %129
  %131 = fadd fast float %130, %128
  %132 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 2)  ; CBufferLoadLegacy(handle,regIndex)
  %133 = extractvalue %dx.types.CBufRet.f32 %132, 3
  %134 = fmul fast float %131, %133
  %135 = call float @dx.op.binary.f32(i32 35, float 0.000000e+00, float %134)  ; FMax(a,b)
  %136 = fcmp fast ogt float %135, 0x3F50624DE0000000
  %137 = extractvalue %dx.types.CBufRet.f32 %74, 3
  br i1 %136, label %138, label %196

; <label>:138                                     ; preds = %61
  %139 = extractvalue %dx.types.CBufRet.f32 %132, 0
  %140 = extractvalue %dx.types.CBufRet.f32 %132, 1
  %141 = extractvalue %dx.types.CBufRet.f32 %132, 2
  %142 = fsub fast float -0.000000e+00, %139
  %143 = fsub fast float -0.000000e+00, %140
  %144 = fsub fast float -0.000000e+00, %141
  %145 = call float @dx.op.dot3.f32(i32 55, float %142, float %143, float %144, float %142, float %143, float %144)  ; Dot3(ax,ay,az,bx,by,bz)
  %146 = call float @dx.op.unary.f32(i32 25, float %145)  ; Rsqrt(value)
  %147 = fmul fast float %146, %142
  %148 = fmul fast float %146, %143
  %149 = fmul fast float %146, %144
  %150 = fsub fast float -0.000000e+00, %13
  %151 = fsub fast float -0.000000e+00, %14
  %152 = fsub fast float -0.000000e+00, %15
  %153 = call float @dx.op.dot3.f32(i32 55, float %147, float %148, float %149, float %150, float %151, float %152)  ; Dot3(ax,ay,az,bx,by,bz)
  %154 = fmul fast float %153, 0x3FE3333340000000
  %155 = fsub fast float 0x3FF170A3E0000000, %154
  %156 = call float @dx.op.unary.f32(i32 23, float %155)  ; Log(value)
  %157 = fmul fast float %156, 1.500000e+00
  %158 = call float @dx.op.unary.f32(i32 21, float %157)  ; Exp(value)
  %159 = fdiv fast float 0x3FB289D260000000, %158
  %160 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 3)  ; CBufferLoadLegacy(handle,regIndex)
  %161 = extractvalue %dx.types.CBufRet.f32 %160, 0
  %162 = extractvalue %dx.types.CBufRet.f32 %160, 1
  %163 = extractvalue %dx.types.CBufRet.f32 %160, 2
  %164 = call %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32 59, %dx.types.Handle %4, i32 4)  ; CBufferLoadLegacy(handle,regIndex)
  %165 = extractvalue %dx.types.CBufRet.f32 %164, 0
  %166 = extractvalue %dx.types.CBufRet.f32 %164, 1
  %167 = extractvalue %dx.types.CBufRet.f32 %164, 2
  %168 = extractvalue %dx.types.CBufRet.f32 %164, 3
  %169 = extractvalue %dx.types.CBufRet.f32 %160, 3
  %170 = fadd fast float %168, %169
  %171 = fmul fast float %170, %135
  %172 = fmul fast float %137, 0xBFF7154760000000
  %173 = fmul fast float %172, %171
  %174 = call float @dx.op.unary.f32(i32 21, float %173)  ; Exp(value)
  %175 = fsub fast float 1.000000e+00, %174
  %176 = fmul fast float %135, %64
  %177 = fmul fast float %176, %159
  %178 = fmul fast float %168, %177
  %179 = fmul fast float %178, %161
  %180 = fmul fast float %179, %165
  %181 = fmul fast float %180, %175
  %182 = fmul fast float %178, %162
  %183 = fmul fast float %182, %166
  %184 = fmul fast float %183, %175
  %185 = fmul fast float %178, %163
  %186 = fmul fast float %185, %167
  %187 = fmul fast float %186, %175
  %188 = fdiv fast float %181, %171
  %189 = fdiv fast float %184, %171
  %190 = fdiv fast float %187, %171
  %191 = fadd fast float %188, %67
  %192 = fadd fast float %189, %66
  %193 = fadd fast float %190, %65
  %194 = fmul fast float %174, %64
  %195 = fcmp fast olt float %194, 0x3F847AE140000000
  br i1 %195, label %208, label %196

; <label>:196                                     ; preds = %138, %61
  %197 = phi float [ %191, %138 ], [ %67, %61 ]
  %198 = phi float [ %192, %138 ], [ %66, %61 ]
  %199 = phi float [ %193, %138 ], [ %65, %61 ]
  %200 = phi float [ %194, %138 ], [ %64, %61 ]
  %201 = fadd fast float %137, %63
  %202 = add nuw nsw i32 %62, 1
  %203 = call %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32 59, %dx.types.Handle %4, i32 1)  ; CBufferLoadLegacy(handle,regIndex)
  %204 = extractvalue %dx.types.CBufRet.i32 %203, 3
  %205 = icmp slt i32 %202, %204
  %206 = fcmp fast olt float %201, %48
  %207 = and i1 %206, %205
  br i1 %207, label %61, label %208

; <label>:208                                     ; preds = %196, %138
  %209 = phi float [ %197, %196 ], [ %191, %138 ]
  %210 = phi float [ %198, %196 ], [ %192, %138 ]
  %211 = phi float [ %199, %196 ], [ %193, %138 ]
  %212 = phi float [ %200, %196 ], [ %194, %138 ]
  br label %213

; <label>:213                                     ; preds = %208, %53
  %214 = phi float [ 0.000000e+00, %53 ], [ %209, %208 ]
  %215 = phi float [ 0.000000e+00, %53 ], [ %210, %208 ]
  %216 = phi float [ 0.000000e+00, %53 ], [ %211, %208 ]
  %217 = phi float [ 1.000000e+00, %53 ], [ %212, %208 ]
  %218 = fsub fast float 1.000000e+00, %217
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 0, float %214)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 1, float %215)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 2, float %216)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  call void @dx.op.storeOutput.f32(i32 5, i32 0, i32 0, i8 3, float %218)  ; StoreOutput(outputSigId,rowIndex,colIndex,value)
  ret void
}

; Function Attrs: nounwind readnone
declare float @dx.op.loadInput.f32(i32, i32, i32, i8, i32) #0

; Function Attrs: nounwind
declare void @dx.op.storeOutput.f32(i32, i32, i32, i8, float) #1

; Function Attrs: nounwind
declare void @dx.op.discard(i32, i1) #1

; Function Attrs: nounwind readnone
declare float @dx.op.dot3.f32(i32, float, float, float, float, float, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.unary.f32(i32, float) #0

; Function Attrs: nounwind readnone
declare float @dx.op.binary.f32(i32, float, float) #0

; Function Attrs: nounwind readonly
declare %dx.types.ResRet.f32 @dx.op.sampleLevel.f32(i32, %dx.types.Handle, %dx.types.Handle, float, float, float, float, i32, i32, i32, float) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.f32 @dx.op.cbufferLoadLegacy.f32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.CBufRet.i32 @dx.op.cbufferLoadLegacy.i32(i32, %dx.types.Handle, i32) #2

; Function Attrs: nounwind readonly
declare %dx.types.Handle @dx.op.createHandle(i32, i8, i32, i32, i1) #2

attributes #0 = { nounwind readnone }
attributes #1 = { nounwind }
attributes #2 = { nounwind readonly }

!llvm.ident = !{!0}
!dx.version = !{!1}
!dx.valver = !{!2}
!dx.shaderModel = !{!3}
!dx.resources = !{!4}
!dx.viewIdState = !{!13}
!dx.entryPoints = !{!14}

!0 = !{!"dxc(private) 1.8.0.4907 (06381f2d7)"}
!1 = !{i32 1, i32 1}
!2 = !{i32 1, i32 9}
!3 = !{!"ps", i32 6, i32 1}
!4 = !{!5, null, !9, !11}
!5 = !{!6, !8}
!6 = !{i32 0, %"class.Texture3D<vector<float, 4> >"* undef, !"", i32 0, i32 0, i32 1, i32 4, i32 0, !7}
!7 = !{i32 0, i32 9}
!8 = !{i32 1, %"class.Texture3D<vector<float, 4> >"* undef, !"", i32 0, i32 1, i32 1, i32 4, i32 0, !7}
!9 = !{!10}
!10 = !{i32 0, %VolumeParams* undef, !"", i32 0, i32 0, i32 1, i32 92, null}
!11 = !{!12}
!12 = !{i32 0, %struct.SamplerState* undef, !"", i32 0, i32 0, i32 1, i32 0, null}
!13 = !{[21 x i32] [i32 19, i32 4, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 0, i32 15, i32 15, i32 15, i32 0, i32 15, i32 15, i32 15]}
!14 = !{void ()* @main, !"main", !15, !4, null}
!15 = !{!16, !27, null}
!16 = !{!17, !19, !20, !22, !25}
!17 = !{i32 0, !"SV_Position", i8 9, i8 3, !18, i8 4, i32 1, i8 4, i32 0, i8 0, null}
!18 = !{i32 0}
!19 = !{i32 1, !"TEXCOORD", i8 9, i8 0, !18, i8 2, i32 1, i8 2, i32 1, i8 0, null}
!20 = !{i32 2, !"TEXCOORD", i8 9, i8 0, !21, i8 2, i32 1, i8 3, i32 2, i8 0, null}
!21 = !{i32 1}
!22 = !{i32 3, !"TEXCOORD", i8 9, i8 0, !23, i8 2, i32 1, i8 3, i32 3, i8 0, !24}
!23 = !{i32 2}
!24 = !{i32 3, i32 7}
!25 = !{i32 4, !"TEXCOORD", i8 9, i8 0, !26, i8 2, i32 1, i8 3, i32 4, i8 0, !24}
!26 = !{i32 3}
!27 = !{!28}
!28 = !{i32 0, !"SV_Target", i8 9, i8 16, !18, i8 0, i32 1, i8 4, i32 0, i8 0, !29}
!29 = !{i32 3, i32 15}
